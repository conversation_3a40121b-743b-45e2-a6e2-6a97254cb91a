syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "google/protobuf/descriptor.proto";
import "google/protobuf/struct.proto";

import "protobuf/options.proto";


// AccountConfig 池账号配置
message AccountConfig {
  map<string, string> whole = 1;
  map<string, string> combo = 2;
}

message Relation {
  string id = 1;
  string type = 2;
  repeated google.protobuf.ListValue children = 3;
  string reference_id = 4;
}

// ApiExecutionDataType API执行数据类型
enum ApiExecutionDataType {
  API_UNKNOWN = 0;

  // 外层的执行数据类型
  API_COMPONENT_GROUP = 1; // API组件组
  API_CASE = 2; // API测试用例
  API_SUITE = 3; // API测试集合
  API_PLAN = 4; // API测试计划
  INTERFACE_CASE = 5; // 接口用例
  INTERFACE_DOCUMENT = 6; // 接口文档（即接口集合）
  UI_CASE = 7;  // UI测试用例
  UI_SUITE = 8; // UI测试集合
  UI_PLAN = 9;  // UI测试计划
  API_SERVICE = 10; // API测试服务（精准测试服务）
  PERF_CASE = 91; // 压力测试用例
  PERF_SUITE = 92; // 压力测试集合
  PERF_PLAN = 93; // 压力测试计划
  STABILITY_PLAN = 81; // 稳定性测试计划

  /*
  内层的执行数据类型，即组件类型
  21 ~ 40: 框的组件（预留）
  41 ~ 60: 点的组件（预留）
   */
  START = 11; // 开始
  END = 12; // 结束

  SETUP = 21; // 前置 - 框
  TEARDOWN = 22; // 后置 - 框
  BUSINESS_SINGLE = 23; // 业务单请求 - 框
  BUSINESS_GROUP = 24; // 业务行为组 - 框
  LOOP = 25; // 循环 - 框
  PARALLEL = 26; // 并行 - 框

  HTTP = 41; // HTTP请求
  COMPONENT_GROUP = 42; // 引用组件组
  CONDITION = 43; // 条件
  WAIT = 44; // 等待
  ASSERT = 45; // 断言
  POOL_ACCOUNT = 46; // 池账号
  DATA_PROCESSING = 47; // 数据处理
  DATA_DRIVEN = 48; // 数据驱动
  SQL_EXECUTION = 49; // SQL执行
  PRECISION_TESTING_HTTP = 50; // 精准测试HTTP请求
}

// CommonState 通用状态
enum CommonState {
  CS_NULL = 0;    // NULL
  CS_ENABLE = 1;  // 生效
  CS_DISABLE = 2; // 失效
}

// ResourceState 资源状态
enum ResourceState {
  RS_NULL = 0;                                               // NULL
  RS_ENABLED = 1 [(options.enum_value_alias) = "ENABLED"];   // 生效
  RS_DISABLED = 2 [(options.enum_value_alias) = "DISABLED"]; // 失效

  RS_NEW = 11 [(options.enum_value_alias) = "NEW"];                             // 新
  RS_TO_BE_IMPLEMENTED = 12 [(options.enum_value_alias) = "TO_BE_IMPLEMENTED"]; // 待实现
  RS_TO_BE_MAINTAINED = 13 [(options.enum_value_alias) = "TO_BE_MAINTAINED"];   // 待维护
  RS_PENDING_REVIEW = 14 [(options.enum_value_alias) = "PENDING_REVIEW"];       // 待审核
  RS_PUBLISHED = 15 [(options.enum_value_alias) = "PUBLISHED"];                 // 已上线
}

// ExecutionMode 执行模式
enum ExecutionMode {
  EM_NULL = 0;     // NULL
  EM_PARALLEL = 1; // 并行
  EM_SERIAL = 2;   // 串行
}

// UIPlatformType UI平台类型
enum UIPlatformType {
  PT_NULL = 0;    // NULL
  PT_ANDROID = 1; // Android
  PT_IOS = 2;     // iOS
}

message Statistic {
  int64 total = 1;
  int64 increased = 2;
  int64 modified = 3;
  int64 unchanged = 4;
  int64 skipped = 5;
  int64 failure = 6;
}
