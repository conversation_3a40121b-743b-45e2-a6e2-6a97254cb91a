syntax = "proto3";

package manager;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb";

import "manager/base.proto";
import "manager/component.proto";
import "manager/element.proto";


message ComponentGroup {
  string project_id = 1; // 项目ID
  string category_id = 2; // 所属分类ID
  string component_group_id = 3; // 组件组ID
  string component_group_type = 4; // 组件组类型
  string name = 5; // 组件组名称
  string description = 6; // 组件组描述
  int64 priority = 7; // 优先级
  repeated string tags = 8; // 标签
  repeated Import imports = 9; // 入参
  repeated Export exports = 10; // 出参
  AccountConfig account_config = 11; // 池账号配置信息
  string version = 12; // 用例版本
  string maintained_by = 13; // 维护者
  string created_by = 14; // 创建者
  string updated_by = 15; // 更新者
  int64  created_at = 16; // 创建时间
  int64  updated_at = 17; // 更新时间
  repeated Node nodes = 18;
  repeated Edge edges = 19;
  repeated Combo combos = 20;
}

message Referenced {
  string project_id = 1;
  string reference_id = 2;
  string reference_type = 3;
  string reference_version = 4;
}

message SearchComponentGroupReferenceItem {
  string project_id = 1; // 项目ID
  string component_group_id = 2; // 组件组ID
  string reference_type = 3; // 引用对象类型
  string reference_id = 4; // 引用对象ID
  string reference_version = 5; // 引用对象版本
  string reference_parent_id = 6; // 引用对象的父ID
  string reference_component_group_type = 7; // 引用对象的组件组类型
  string name = 8; // 引用对象名称
  string description = 9; // 引用对象描述
  int64 priority = 10; // 优先级
  repeated string tags = 11; // 标签
  CommonState state = 12; // 状态
  string maintained_by = 13; // 维护者
  string created_by = 14; // 创建者
  string updated_by = 15; // 更新者
  int64  created_at = 16; // 创建时间
  int64  updated_at = 17; // 更新时间
}
