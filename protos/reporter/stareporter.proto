syntax = "proto3";

package reporter;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb";

import "common/config.proto";
import "common/enum.proto";
import "common/stability.proto";
import "common/lark.proto";
import "devicehub/device.proto";

// message StabilityPlanRecord {
//   string task_id = 1;    // 任务ID
//   string execute_id = 2; // 稳定性测试计划执行ID
//
//   string project_id = 11;       // 项目ID
//   string plan_id = 12;          // 计划ID
//   string plan_name = 13;        // 计划名称
//   common.TriggerMode type = 14; // 计划类型（手动、定时、接口）
//
//   common.AccountConfig account_config = 21; // 账号池配置
//
//   common.DeviceType device_type = 31;         // 设备类型（真机、云手机）
//   common.PlatformType platform_type = 32;     // 平台类型（Android、iOS）
//   common.StabilityCustomDevices devices = 23; // 自定义设备
//
//   string app_download_link = 41;                   // App下载地址
//   repeated string activities = 42;                 // 指定的Activity列表
//   common.StabilityCustomScript custom_script = 43; // 自定义脚本
//
//   int64 cost_time = 51;    // 执行耗时（单位为毫秒）
//   int64 started_at = 52;   // 开始时间
//   int64 ended_at = 53;     // 结束时间
//   string executed_by = 54; // 执行者
//
//   string created_by = 96; // 创建者
//   string updated_by = 97; // 更新者
//   int64  created_at = 98; // 创建时间
//   int64  updated_at = 99; // 更新时间
// }

// message StabilityCaseStep {
//   string task_id = 1;         // 任务ID
//   string step_id = 2;         // 步骤ID
//   common.TestStage stage = 3; // 阶段
//   string name = 4;            // 步骤名称
//   string status = 5;          // 执行状态（结果）
//   string content = 6;         // 步骤内容
//   int64 started_at = 7;       // 开始时间
//   int64 ended_at = 8;         // 结束时间
// }

message StabilityPlanRecordItem {
  string project_id = 1;                // 项目ID
  string plan_id = 2;                   // 计划ID
  string plan_name = 3;                 // 计划名称
  common.TriggerMode trigger_mode = 4;  // 触发方式
  string task_id = 5;                   // 任务ID
  string execute_id = 6;                // 执行ID

  string status = 11;            // 执行状态（结果）
  int64  success_device = 12;    // 成功的设备数
  int64  failure_device = 13;    // 失败的设备数
  int64  total_device = 14;     // 测试设备总数
  int64  cost_time = 15;        // 实际执行时长（单位：秒）
  int64  target_duration = 16;  // 目标运行时长（单位：分）
  string executed_by = 17;      // 执行者
  int64  started_at = 18;       // 开始时间
  int64  ended_at = 19;         // 结束时间

  int64  created_at = 51;       // 创建时间
  int64  updated_at = 52;       // 更新时间
  bool   cleaned = 53;          // 是否被清理
}

message StabilityPlanMetaData {
  common.AccountConfig account_config = 1; // 池账号配置
  repeated common.LarkChat lark_chats = 2; // 飞书群组列表

  common.DeviceType device_type = 11; // 设备类型（真机、云手机）
  common.PlatformType platform_type = 12; // 平台类型（Android、iOS）
  common.StabilityCustomDevices devices = 13; // 自定义设备

  string package_name = 21; // App包名
  string app_version = 22; // App版本
  string app_download_link = 23; // App下载地址
  repeated string activities = 24; // 指定的Activity列表
  common.StabilityCustomScript custom_script = 25; // 自定义脚本

  uint32 duration = 31; // 执行时长
}

message StabilityDeviceRecordItem {
	string project_id = 1;		  // 项目ID
	string task_id = 2;			    // 任务ID
	string execute_id = 3;		  // 执行ID
	string plan_execute_id = 4;	// 计划执行ID

	string status = 11;      // 执行状态（结果）
	int64  crash_count = 12; // crash数量
	int64  anr_count = 13;   // anr数量
	int64  cost_time = 14;   // 实际执行时长（单位：秒）
	int64  started_at = 15;  // 开始时间
	int64  ended_at = 16;    // 结束时间

	// Device
	devicehub.Device device = 99; // 设备信息
}

message StabilityDeviceStep {
  string task_id = 1;         // 任务ID
  string step_id = 2;         // 步骤ID
  common.TestStage stage = 3; // 阶段
  int64 index = 4;            // 步骤索引

  string name = 11;                        // 步骤名称
  string status = 12;                      // 执行状态（结果）
  string content = 13;                     // 步骤内容
  
  int64 started_at = 21;                   // 开始时间
  int64 ended_at = 22;                     // 结束时间
}
