syntax = "proto3";

package common;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb";

import "validate/validate.proto";
import "google/protobuf/timestamp.proto";

import "common/config.proto";
import "common/enum.proto";
import "common/node.proto";
import "common/perf.proto";
import "common/stability.proto";


message PeriodicPlanTaskInfo {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^plan|^ui_plan|^perf_plan)_id:.+?"}]; // 计划ID
  string cron_expression = 3; // Cron表达式
  PlanType plan_type = 4;     // 计划类型
}

message ParsePythonProjectTaskInfo {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  GitConfig config = 2 [(validate.rules).message.required = true]; // Git配置
  TriggerMode trigger_mode = 3 [(validate.rules).enum = {in: [1, 3]}]; // 触发方式
  string trigger_account = 4 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 触发者用户ID
  int64 trigger_time = 5 [(validate.rules).int64.gt = 0]; // 触发时间戳（单位：毫秒）
}

message ParsePythonProjectTaskResult {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string config_id = 2 [(validate.rules).string = {pattern: "(?:^git_config_id:.+?)"}]; // Git配置ID
  Node root_node = 3 [(validate.rules).message.required = true]; // Git项目根节点
  string trigger_account = 4 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 触发者用户ID
  int64 trigger_time = 5 [(validate.rules).int64.gt = 0]; // 触发时间戳（单位：毫秒）
}

message ProtobufTarget {
  string path = 1 [(validate.rules).string = {min_len: 1}]; // 路径
  repeated string import_paths = 2 [(validate.rules).repeated = {ignore_empty: true, unique: true, items: {string: {min_len: 1}}}];  // 依赖导入路径列表
  repeated string exclude_paths = 3 [(validate.rules).repeated = {ignore_empty: true, unique: true, items: {string: {min_len: 1}}}]; // 排除路径列表
  repeated string exclude_files = 4 [(validate.rules).repeated = {ignore_empty: true, unique: true, items: {string: {min_len: 1}}}]; // 排除文件列表
}

message PerfTestTaskInfo {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string task_id = 2 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
  string execute_id = 3 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 压测用例执行ID
  string suite_execute_id = 4 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 压测集合执行ID
  string plan_execute_id = 5 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 压测计划执行ID
  TriggerMode trigger_mode = 6 [(validate.rules).enum = {in: [1, 2]}]; // 触发模式

  PerfTaskExecutionMode execute_mode = 21 [(validate.rules).enum = {not_in: [0]}]; // 执行方式
  Protocol protocol = 22 [(validate.rules).enum = {not_in: [0]}]; // 协议
  repeated ProtobufTarget protobuf_targets = 23; // Protobuf相关信息
  GeneralConfig general_config = 24 [(validate.rules).message = {required: true}]; // 通用配置
  PerfKeepalive keepalive = 25 [(validate.rules).message = {skip: true}]; // Deprecated: 压测保活参数
  string perf_case_path = 26 [(validate.rules).string = {ignore_empty: true, min_len: 1}]; // Deprecated: 压测用例文件路径
  string perf_data_path = 27 [(validate.rules).string = {min_len: 1}]; // 压测数据文件路径
  uint32 duration = 28 [(validate.rules).uint32 = {gte: 0}]; // 压测时长
  uint32 times = 29 [(validate.rules).uint32 = {gte: 0}]; // 压测次数
  PerfCaseContentV2 perf_case = 30 [(validate.rules).message = {required: true}]; // 压测用例
  PerfRateLimits rate_limits = 31; // 压测相关的限流配置（包括：认证接口、心跳接口）

  uint32 timeout = 41 [(validate.rules).uint32 = {gt: 0}]; // 任务执行的超时时间
  uint32 total_of_vu = 42 [(validate.rules).uint32 = {gt: 0}]; // 总虚拟用户数
  uint32 total_of_lg = 43 [(validate.rules).uint32 = {gt: 0}]; // 总施压机数
}

message LarkChatDisbandedTaskInfo {
  string chat_id = 1; // 群组ID
  string name = 2;    // 群名称
  bool external = 3;  // 是否是外部群
}

message LarkChatUpdatedTaskInfo {
  string chat_id = 1;     // 群组ID
  string avatar = 2;      // 群头像URL
  string name = 3;        // 群名称
  string description = 4; // 群描述
  bool external = 5;      // 是否是外部群
}

message LarkChatBotDeletedTaskInfo {
  string chat_id = 1; // 群组ID
  string name = 2;    // 群名称
  bool external = 3;  // 是否是外部群
}

message SaveDevicePerfDataTaskInfo {
  string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
  string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // UI用例执行ID
  string project_id = 3 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID

  string udid = 11 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 设备编号
  DeviceUsage usage = 12 [(validate.rules).enum = {not_in: [0]}]; // 设备用途
  PerfDataType data_type = 13 [(validate.rules).enum = {not_in: [0]}]; // 数据类型
  int64 interval = 14 [(validate.rules).int64 = {gt: 0}]; // 采集间隔，单位毫秒
  string series = 15 [(validate.rules).string = {min_len: 1, max_len: 16}]; // 指标名称
  string unit = 16 [(validate.rules).string = {min_len: 0, max_len: 8}]; // 单位
  string x = 17 [(validate.rules).string = {min_len: 1, max_len: 64}]; // X轴数据
  string y = 18 [(validate.rules).string = {min_len: 1, max_len: 64}]; // Y轴数据

  string executed_by = 21 [(validate.rules).string = {min_len:1, max_len: 64}]; // 执行者的用户ID
}

message StabilityCaseTaskInfo {
  string project_id = 1 [(validate.rules).string = {pattern: "(?:^project_id:.+?)"}]; // 项目ID
  string plan_id = 2 [(validate.rules).string = {pattern: "(?:^stability_plan_id:.+?)"}]; // 计划ID
  string task_id = 3 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
  string execute_id = 4 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 稳定性测试用例执行ID
  string plan_execute_id = 6 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 稳定性测试计划执行ID
  TriggerMode trigger_mode = 7 [(validate.rules).enum = {not_in: [0]}]; // 触发模式

  reserved 5;
  reserved "suite_execute_id";

  AccountConfig account_config = 11 [(validate.rules).message = {required: true}]; // 池账号配置

  DeviceType device_type = 21 [(validate.rules).enum = {in: [1]}]; // 设备类型（真机、云手机）
  PlatformType platform_type = 22 [(validate.rules).enum = {in: [1]}]; // 平台类型（Android、iOS）
  string udid = 23 [(validate.rules).string = {min_len: 1}]; // 设备编号（固定前缀：表示随机设备，否则：表示指定设备）

  string package_name = 31 [(validate.rules).string = {min_len: 1}]; // 包名
  string app_download_link = 32 [(validate.rules).string = {ignore_empty: true, uri: true}]; // App下载地址
  repeated string activities = 33 [(validate.rules).repeated = {ignore_empty: true, unique: true, items: {string: {min_len: 1}}}]; // 指定的Activity列表
  StabilityCustomScript custom_script = 34; // 自定义脚本

  uint32 duration = 41 [(validate.rules).uint32 = {gt: 0}]; // 执行时长（单位为：分钟）

  string executed_by = 51 [(validate.rules).string = {min_len:1, max_len: 64}]; // 执行者的用户ID
}

message SaveDeviceStepTaskInfo {
  string task_id = 1 [(validate.rules).string = {pattern: "(?:^task_id:.+?)"}]; // 任务ID
  string execute_id = 2 [(validate.rules).string = {pattern: "(?:^execute_id:.+?)"}]; // 用例执行ID
  string project_id = 3 [(validate.rules).string = {pattern: "(?:^project_id:.+?|^1$)"}]; // 项目ID
  string udid =4;

  //注：步骤ID在保存时，会自动生成，无需手动设置
  DeviceUsage usage = 11 [(validate.rules).enum = {not_in: [0]}]; // 设备用途
  common.TestStage stage = 12; // 阶段
  int64 index = 13; // 步骤索引
  string name = 14; // 步骤名称
  string status = 15; // 执行状态（结果）
  string content = 16; // 步骤内容
  repeated common.Artifact artifacts = 17; // 产出物

  string executed_by = 31 [(validate.rules).string = {min_len:1, max_len: 64}]; // 执行者的用户ID

  google.protobuf.Timestamp started_at = 51; // 开始时间
	google.protobuf.Timestamp ended_at = 52; // 结束时间
}
