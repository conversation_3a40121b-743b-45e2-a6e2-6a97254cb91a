CREATE TABLE `account`.`t_products`  (
    `id` int NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品名称',
    `product_type` tinyint NOT NULL COMMENT '产品类型',
    `belong_product` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属产品',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '池账号产品配置表' ROW_FORMAT = Dynamic;

INSERT INTO `account`.`t_products` (`product_name`, `product_type`, `belong_product`) VALUES ('TT_APP', 17, 'tt');
INSERT INTO `account`.`t_products` (`product_name`, `product_type`, `belong_product`) VALUES ('声洞APP', 2, 'sd');
INSERT INTO `account`.`t_products` (`product_name`, `product_type`, `belong_product`) VALUES ('TTCHAT_APP', 9, 'tt_chat');
INSERT INTO `account`.`t_products` (`product_name`, `product_type`, `belong_product`) VALUES ('中台推荐', 12, 'rec');
INSERT INTO `account`.`t_products` (`product_name`, `product_type`, `belong_product`) VALUES ('起飞中台', 13, 'wefly');
INSERT INTO `account`.`t_products` (`product_name`, `product_type`, `belong_product`) VALUES ('企业效能', 14, 'bpm');
INSERT INTO `account`.`t_products` (`product_name`, `product_type`, `belong_product`) VALUES ('T盾', 15, 'td');
INSERT INTO `account`.`t_products` (`product_name`, `product_type`, `belong_product`) VALUES ('提提电竞', 16, 'ttesports');
INSERT INTO `account`.`t_products` (`product_name`, `product_type`, `belong_product`) VALUES ('T次元', 18, 'tcy');
INSERT INTO `account`.`t_products` (`product_name`, `product_type`, `belong_product`) VALUES ('ttui池账号', 66, 'tt_ui');
INSERT INTO `account`.`t_products` (`product_name`, `product_type`, `belong_product`) VALUES ('lyc池账号', 77, 'lyc');