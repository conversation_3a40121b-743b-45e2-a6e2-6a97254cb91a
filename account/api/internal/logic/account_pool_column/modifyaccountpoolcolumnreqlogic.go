package account_pool_column

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/internal/user"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/api/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/model"
)

type ModifyAccountPoolColumnReqLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewModifyAccountPoolColumnReqLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *ModifyAccountPoolColumnReqLogic {
	return &ModifyAccountPoolColumnReqLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func getPoolEnvName(l *ModifyAccountPoolColumnReqLogic, db *sql.DB, poolId int64) ([]string, error) {
	var err error
	var poolEnvNames []string

	selectEnvRecordSql := fmt.Sprintf(
		"" +
			"select pool_name from t_pool_table " +
			"where id in (" +
			"	select r.env_id " +
			"	from t_execute_record r, t_pool_table t " +
			"	where r.pool_id=t.id and r.pool_id=? " +
			"	and state not in (?, ?, ?, ?)" +
			")",
	)

	rows, err := db.QueryContext(
		l.ctx, selectEnvRecordSql, poolId, "FINISHING", "TERMINATED", "EXCEPTION", "NONE_VALID_DATA",
	)
	if err != nil {
		return nil, err
	}

	columns, _ := rows.Columns()
	count := len(columns)
	valueSlice := make([]any, count)
	valuePtrSlice := make([]any, count)

	for rows.Next() {
		for i := range columns {
			valuePtrSlice[i] = &valueSlice[i]
		}
		err := rows.Scan(valuePtrSlice...)
		if err == nil {
			// value := valueSlice[0].([]uint8)
			poolName := fmt.Sprintf("'%s'", cast.ToString(valueSlice[0]))
			poolEnvNames = append(poolEnvNames, poolName)
		}
	}
	return poolEnvNames, nil
}

func dealColumnLength(
	l *ModifyAccountPoolColumnReqLogic, db *sql.DB, columnId string, columnLength int64, tableNameList []string,
) error {
	var err error

	var sqlList []string
	for _, tableName := range tableNameList {
		s := fmt.Sprintf("select IFNULL(max(LENGTH(%s)), 0) m_l from %s", columnId, tableName)
		sqlList = append(sqlList, s)
	}
	sqlStr := strings.Join(sqlList, " UNION ALL ")
	if sqlStr != "" {
		selectSql := fmt.Sprintf("select max(m_l) ml from (%s) t", sqlStr)
		rows, _ := db.QueryContext(l.ctx, selectSql)
		columns, _ := rows.Columns()
		count := len(columns)
		valueSlice := make([]any, count)
		valuePtrSlice := make([]any, count)

		var existMaxLength int64
		for rows.Next() {
			for i := range columns {
				valuePtrSlice[i] = &valueSlice[i]
			}
			err := rows.Scan(valuePtrSlice...)
			if err == nil {
				// value := valueSlice[0].([]uint8)
				// valueString := string(value)
				// existMaxLength, _ = strconv.ParseInt(valueString, 0, 0)
				existMaxLength = cast.ToInt64(valueSlice[0])
			}
		}
		if existMaxLength > columnLength {
			err = errors.New(
				fmt.Sprintf(
					"字段长度：%d小于池环境中字段最大长度：%d，修改失败", columnLength, existMaxLength,
				),
			)
		}
	}
	return err
}

func dealColumnAllowNull(
	l *ModifyAccountPoolColumnReqLogic, db *sql.DB, columnId string, envNameList, tableNameList []string,
) error {
	var err error

	var wrongEnvNameList []string
	for index, tName := range tableNameList {
		selectEnvSql := fmt.Sprintf("select count(1) null_count from %s where %s is null", tName, columnId)
		rows, _ := db.QueryContext(l.ctx, selectEnvSql)
		columns, _ := rows.Columns()
		count := len(columns)
		valueSlice := make([]any, count)
		valuePtrSlice := make([]any, count)

		var nullCount int64
		for rows.Next() {
			for i := range columns {
				valuePtrSlice[i] = &valueSlice[i]
			}
			err := rows.Scan(valuePtrSlice...)
			if err == nil {
				// value := valueSlice[0].([]uint8)
				// valueString := string(value)
				// nullCount, _ = strconv.ParseInt(valueString, 0, 0)
				nullCount = cast.ToInt64(valueSlice[0])
			}
		}
		if nullCount > 0 {
			wrongEnvNameList = append(wrongEnvNameList, envNameList[index])
		}
	}
	wrongEnvNameStr := strings.Join(wrongEnvNameList, "、")
	if len(wrongEnvNameList) != 0 {
		err = errors.New(
			fmt.Sprintf(
				"字段不能由'允许为空'改为'不允许为空', 因为以下环境：%s存在字段%s为空的记录", wrongEnvNameStr, columnId,
			),
		)
	}

	return err
}

func modifyTableStruct(
	l *ModifyAccountPoolColumnReqLogic, db *sql.DB, poolTable *model.TPoolTable, tableNameList []string,
	columnId, newColumnId string,
	columnTypeStr, columnDefault, nullStr, commentStr string,
) error {
	var err error

	// 修改账户池模板表和环境表的结构
	var alterSqlList []string
	tableNameList = append([]string{poolTable.TableName}, tableNameList...)
	for _, tName := range tableNameList {
		var tableDdlSql string
		if columnId == newColumnId {
			tableDdlSql = fmt.Sprintf(
				"ALTER TABLE `%s` modify column %s %s %s %s %s",
				tName, columnId, columnTypeStr, columnDefault, nullStr, commentStr,
			)
		} else {
			tableDdlSql = fmt.Sprintf(
				"ALTER TABLE `%s` change %s %s %s %s %s %s",
				tName, columnId, newColumnId, columnTypeStr, columnDefault, nullStr, commentStr,
			)
		}
		alterSqlList = append(alterSqlList, tableDdlSql)
	}
	for _, alterSql := range alterSqlList {
		_, err := db.ExecContext(l.ctx, alterSql)
		if err != nil {
			return errors.New(fmt.Sprintf("执行alter sql:%s发生异常", alterSql))
		}
	}

	return err
}

func (l *ModifyAccountPoolColumnReqLogic) getPoolAndColumn(req *types.ModifyAccountPoolColumnReq) (
	*model.TPoolTable, *model.TPoolColumn, error,
) {
	var poolTable *model.TPoolTable
	var column *model.TPoolColumn

	column, err := l.svcCtx.TPoolColumnModel.FindColumnByPoolIdColumnId(l.ctx, req.PoolId, req.ColumnId)
	if err != nil || column == nil {
		return nil, nil, errors.New(
			fmt.Sprintf(
				"不存在pool_id为:%d, column_id为: %s的表字段，无法修改", req.PoolId, req.ColumnId,
			),
		)
	}
	poolTable, err = l.svcCtx.TPoolTableModel.FindPoolTableByPoolId(l.ctx, req.PoolId)
	if err != nil {
		return nil, nil, err
	}

	db, _ := l.svcCtx.DB.RawDB()
	poolEnvNames, err2 := getPoolEnvName(l, db, req.PoolId)

	if err2 != nil {
		return nil, nil, err2
	}

	if len(poolEnvNames) > 0 {
		poolNameStr := strings.Join(poolEnvNames, "、")
		return nil, nil, errors.New(
			fmt.Sprintf(
				"以下账户池环境: %s有未完成的流水，不能修改字段:%s", poolNameStr, req.ColumnId,
			),
		)
	}

	if column.ColumnType != common.ColumnTypeName[req.ColumnType] {
		return nil, nil, errors.New("字段类型不能修改")
	}

	if column.PrimaryKey.String == "YES" && req.ColumnAllowNull == common.YesOrNoValue["YES"] {
		return nil, nil, errors.New("主键字段不能修改为允许为空")
	}

	return poolTable, column, nil
}

func (l *ModifyAccountPoolColumnReqLogic) ModifyAccountPoolColumnReq(req *types.ModifyAccountPoolColumnReq) (
	*types.ModifyAccountPoolColumnResp, error,
) {
	poolId := req.PoolId
	columnId := req.ColumnId
	columnName := req.ColumnName
	newColumnId := req.NewColumnId
	columnLength := req.ColumnLength
	columnDefault := req.ColumnDefault
	columnAllowNull := req.ColumnAllowNull
	columnIsVisible := req.ColumnIsVisible
	columnComment := req.ColumnComment

	poolTable, column, err := l.getPoolAndColumn(req)
	if err != nil {
		return nil, err
	}

	var req_ types.CreateAccountPoolColumnReq
	_ = copier.Copy(&req_, req)
	err3 := utils.CheckColumnDdl(&req_)

	if err3 != nil {
		return nil, err3
	}

	err4 := utils.CheckColumnId(newColumnId)
	if err4 != nil {
		return nil, err4
	}

	// 再次判断字段是否合法
	allColumn, _ := l.svcCtx.TPoolColumnModel.FindAllColumnByPoolId(l.ctx, poolId)
	for _, c := range allColumn {
		if columnId != newColumnId && newColumnId == c.ColumnId {
			return nil, errors.New(fmt.Sprintf("字段id不能修改为'%s', 因为它已经存在", newColumnId))
		}
		if columnName != column.ColumnName && columnName == c.ColumnName {
			return nil, errors.New(fmt.Sprintf("字段别名不能修改为'%s', 因为它已经存在", columnName))
		}
	}

	// 所有需要修改定义的表，包括账户池模板表和账户池环境表
	var envNameList []string
	var tableNameList []string
	envTables, _ := l.svcCtx.TPoolTableModel.FindPoolEnvTablesByPoolId(l.ctx, poolId)
	for _, table := range envTables {
		envNameList = append(envNameList, table.PoolName)
		tableNameList = append(tableNameList, table.TableName)
	}

	db, err := l.svcCtx.DB.RawDB()
	if err != nil {
		return nil, err
	}

	// 如果字段有长度，需要特殊处理
	if columnLength != 0 {
		err6 := dealColumnLength(l, db, columnId, columnLength, tableNameList)
		if err6 != nil {
			return nil, err6
		}
	}

	// 字段由'允许为空'改为'不允许为空'
	if column.ColumnAllowNull == "YES" {
		if common.YesOrNoName[columnAllowNull] == "NO" {
			err7 := dealColumnAllowNull(l, db, columnId, envNameList, tableNameList)
			if err7 != nil {
				return nil, err7
			}
		}
	}

	// 生成字段描述信息
	columnTypeStr, _, nullStr, columnDefaultStr, commentStr := GenerateColumnDescribe(&req_)

	// 修改账户池模板表和环境表的结构
	err8 := modifyTableStruct(
		l, db, poolTable, tableNameList, columnId, newColumnId, columnTypeStr, columnDefaultStr, nullStr, commentStr,
	)
	if err8 != nil {
		return nil, err8
	}

	// 维护字段关系
	updateSql := fmt.Sprintf(
		"" +
			"update t_pool_column " +
			"set column_id=?, column_name=?, column_length=?, " +
			"column_default=?, column_allow_null=?, column_is_visible=?, column_comment=? " +
			"where column_id=? and pool_id=? and table_name=?",
	)
	var err10 error
	if columnLength == 0 {
		_, err10 = db.ExecContext(
			l.ctx, updateSql,
			newColumnId, columnName, "null", columnDefault,
			columnAllowNull, columnIsVisible, columnComment,
			columnId, poolId, poolTable.TableName,
		)
	} else {
		_, err10 = db.ExecContext(
			l.ctx, updateSql,
			newColumnId, columnName, columnLength, columnDefault,
			columnAllowNull, columnIsVisible, columnComment,
			columnId, poolId, poolTable.TableName,
		)
	}

	if err10 != nil {
		return nil, err10
	}

	poolColumn, _ := l.svcCtx.TPoolColumnModel.FindColumnByPoolIdColumnId(l.ctx, poolId, newColumnId)

	var createdBy *userinfo.UserInfo
	var userErr error
	if poolTable.CreatedBy == "empty" {
		createdBy = user.Admin
	} else {
		createdBy, userErr = user.GetUser(l.svcCtx.UserRpc, l.ctx, poolTable.CreatedBy)
		if userErr != nil {
			err = errorx.Err(errorx.Unknown, fmt.Sprintf("调用uerRpc服务发生错误 : %+v", userErr))
			return nil, err
		}
	}

	// 修改字段成功后需要尝试删除关联子环境redis数据
	poolTableName := poolTable.TableName
	prefixSlice := []string{"pool_column_name", "pool_fields", "pool_column_id_column_type"}
	for _, prefix := range prefixSlice {
		redisKey := fmt.Sprintf("%s_%s", prefix, poolTableName)
		_, redisErr := l.svcCtx.Redis.Del(redisKey)
		if redisErr != nil {
			logx.Error(fmt.Sprintf("删除redis发生错误:%s", err))
		}
	}

	loginUser := userinfo.FromContext(l.ctx)

	return &types.ModifyAccountPoolColumnResp{
		PoolId:          poolColumn.PoolId,
		ColumnId:        poolColumn.ColumnId,
		ColumnName:      poolColumn.ColumnName,
		ColumnType:      common.ColumnTypeValue[poolColumn.ColumnType],
		ColumnLength:    poolColumn.ColumnLength.Int64,
		ColumnDefault:   poolColumn.ColumnDefault.String,
		ColumnAllowNull: common.YesOrNoValue[poolColumn.ColumnAllowNull],
		ColumnIsVisible: common.YesOrNoValue[poolColumn.ColumnIsVisible],
		ColumnComment:   poolColumn.ColumnComment.String,
		PrimaryKey:      common.YesOrNoValue[poolColumn.PrimaryKey.String],
		ColumnIsUsing:   common.YesOrNoValue[poolColumn.ColumnIsUsing],
		CreatedBy:       createdBy,
		UpdatedBy:       loginUser,
		CreatedAt:       poolColumn.CreatedAt.UnixMilli(),
		UpdatedAt:       poolColumn.UpdatedAt.UnixMilli(),
	}, nil
}
