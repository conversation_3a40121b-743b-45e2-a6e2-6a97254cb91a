package conn

import (
	"fmt"
	"net"
	"net/http"
	"testing"
)

func TestName(t *testing.T) {
	// Serve HTTP
	http.HandleFunc(
		"/", func(w http.ResponseWriter, r *http.Request) {
			conn, _, err := w.(http.Hijacker).Hijack()
			if err != nil {
				http.Error(w, err.Error(), http.StatusInternalServerError)
				return
			}
			defer conn.Close()

			// Create a custom response writer
			writer := NewConnResponseWriter(conn, nil)

			// Send a simple response
			writer.WriteHeader(http.StatusOK)
			writer.Write([]byte("Hello, world!\n"))
		},
	)

	// Listen for incoming connections
	ln, err := net.Listen("tcp", ":8080")
	if err != nil {
		fmt.Println("Error:", err)
		return
	}
	defer ln.Close()

	// Serve HTTP requests
	if err := http.Serve(ln, nil); err != nil {
		fmt.Println("Error:", err)
		return
	}
}
