package blackboxcaseassistant

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type CreateBlackBoxCaseAssistantLogic struct {
	*logic.BaseLogic
}

// create black box case assistant
func NewCreateBlackBoxCaseAssistantLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *CreateBlackBoxCaseAssistantLogic {
	return &CreateBlackBoxCaseAssistantLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateBlackBoxCaseAssistantLogic) CreateBlackBoxCaseAssistant(req *types.CreateBlackBoxCaseAssistantReq) (
	resp *types.CreateBlackBoxCaseAssistantResp, err error,
) {
	in := &pb.CreateBlackBoxCaseAssistantReq{}
	if err = utils.Copy(in, req, l.Converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.SvcCtx.BlackBoxCaseAssistantServiceRpc.CreateBlackBoxCaseAssistant(l.Ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateBlackBoxCaseAssistantResp{ProjectId: req.ProjectId, AssistantId: out.GetAssistantId()}, nil
}
