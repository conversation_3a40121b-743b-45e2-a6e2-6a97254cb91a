package blackboxknowledgetag

import (
	"context"
	"errors"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
)

type UpdateBlackBoxKnowledgeTagLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// update blackbox knowledge tag
func NewUpdateBlackBoxKnowledgeTagLogic(ctx context.Context,
	svcCtx *svc.ServiceContext) *UpdateBlackBoxKnowledgeTagLogic {
	return &UpdateBlackBoxKnowledgeTagLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateBlackBoxKnowledgeTagLogic) UpdateBlackBoxKnowledgeTag(
	req *types.UpdateBlackBoxKnowledgeTagReq) (resp any, err error) {
	testGeniusReq := httpc.UpdateKnowledgeMetaTagReq{
		Id:  int64(req.Id),
		Tag: req.Tag,
	}

	tagRes, err := httpc.UpdateKnowledgeMetaTag(l.svcCtx.ExternalAiDomain, testGeniusReq)
	if err != nil {
		l.Logger.Errorf("failed to update blackbox knowledge tag, error: %+v", err)
		return nil, err
	}

	if tagRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + tagRes.Message)
	}

	return tagRes.Data, nil
}
