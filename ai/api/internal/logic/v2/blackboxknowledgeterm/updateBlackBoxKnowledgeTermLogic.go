package blackboxknowledgeterm

import (
	"context"
	"errors"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/convert"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
)

type UpdateBlackBoxKnowledgeTermLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// update blackbox knowledge term
func NewUpdateBlackBoxKnowledgeTermLogic(ctx context.Context,
	svcCtx *svc.ServiceContext) *UpdateBlackBoxKnowledgeTermLogic {
	return &UpdateBlackBoxKnowledgeTermLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateBlackBoxKnowledgeTermLogic) UpdateBlackBoxKnowledgeTerm(
	req *types.UpdateBlackBoxKnowledgeTermReq) (resp any, err error) {
	testGeniusReq := httpc.UpdateKnowledgeTermReq{
		Id:            req.Id,
		TermMeaning:   req.TermMeaning,
		TermInChinese: req.TermInChinese,
		Tags:          convert.Convert2KnowledgeTags(req.Tags),
	}

	tagRes, err := httpc.UpdateKnowledgeTerm(l.svcCtx.ExternalAiDomain, testGeniusReq)
	if err != nil {
		l.Logger.Errorf("failed to update blackbox knowledge term, error: %+v", err)
		return nil, err
	}

	if tagRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + tagRes.Message)
	}

	return tagRes.Data, nil
}
