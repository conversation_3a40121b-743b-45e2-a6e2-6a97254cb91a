package blackboxknowledgeexperiencecategory

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateBlackBoxKnowledgeExperienceCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// create blackbox knowledge experience category
func NewCreateBlackBoxKnowledgeExperienceCategoryLogic(ctx context.Context,
	svcCtx *svc.ServiceContext) *CreateBlackBoxKnowledgeExperienceCategoryLogic {
	return &CreateBlackBoxKnowledgeExperienceCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateBlackBoxKnowledgeExperienceCategoryLogic) CreateBlackBoxKnowledgeExperienceCategory(
	req *types.CreateBlackBoxKnowledgeExperienceCategoryReq) (resp any, err error) {
	categoryRes, err := httpc.CreateKnowledgeTestExperienceCategory(l.svcCtx.ExternalAiDomain,
		httpc.CreateKnowledgeTestExperienceCategoryReq{
			ProjectId: req.ProjectId,
			TypeName:  req.TypeName,
		})
	if err != nil {
		l.Logger.Errorf("failed to create blackbox knowledge experience category, error: %+v", err)
		return nil, err
	}

	if categoryRes.Code != 0 {
		return nil, errors.New("调用AI接口异常: " + categoryRes.Message)
	}

	return categoryRes.Data, nil
}
