package blackboxcasetwbetaservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type DeleteBlackBoxCaseTwBetaLogic struct {
	*logic.BaseLogic
}

func NewDeleteBlackBoxCaseTwBetaLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteBlackBoxCaseTwBetaLogic {
	return &DeleteBlackBoxCaseTwBetaLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// DeleteBlackBoxCaseTwBeta delete black box case of tw beta
func (l *DeleteBlackBoxCaseTwBetaLogic) DeleteBlackBoxCaseTwBeta(in *pb.DeleteBlackBoxCaseTwBetaReq) (*pb.DeleteBlackBoxCaseTwBetaResp, error) {
	err := l.SvcCtx.BlackBoxCaseTwBetaModel.LogicDeleteById(l.Ctx, in.BetaCaseId)
	if err != nil {
		return nil, err
	}
	return &pb.DeleteBlackBoxCaseTwBetaResp{}, nil
}
