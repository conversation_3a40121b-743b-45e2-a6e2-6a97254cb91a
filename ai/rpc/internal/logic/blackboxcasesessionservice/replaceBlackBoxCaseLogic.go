package blackboxcasesessionservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type ReplaceBlackBoxCaseLogic struct {
	*logic.BaseLogic
}

func NewReplaceBlackBoxCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReplaceBlackBoxCaseLogic {
	return &ReplaceBlackBoxCaseLogic{
		BaseLogic: logic.NewBaseLogic(ctx, svcCtx),
	}
}

// ReplaceBlackBoxCase replace black box case
func (l *ReplaceBlackBoxCaseLogic) ReplaceBlackBoxCase(in *pb.ReplaceBlackBoxCaseReq) (out *pb.ReplaceBlackBoxCaseResp, err error) {
	err = httpc.ReplaceBlackBoxCase(l.SvcCtx.ExternalAiDomain, in)
	if err != nil {
		return nil, err
	}
	return &pb.ReplaceBlackBoxCaseResp{}, nil
}
