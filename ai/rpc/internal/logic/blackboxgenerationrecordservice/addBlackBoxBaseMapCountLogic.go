package blackboxgenerationrecordservicelogic

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/model"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type AddBlackBoxBaseMapCountLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddBlackBoxBaseMapCountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddBlackBoxBaseMapCountLogic {
	return &AddBlackBoxBaseMapCountLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// AddBlackBoxBaseMapCount add blackbox base map count
func (l *AddBlackBoxBaseMapCountLogic) AddBlackBoxBaseMapCount(in *pb.AddBlackBoxBaseMapCountReq) (out *pb.AddBlackBoxBaseMapCountResp, err error) {
	err = l.svcCtx.BlackBoxGenerationRecordModel.Save(l.ctx, &model.BlackBoxGenerationRecord{
		BaseMapCaseCount: 1,
	})
	if err != nil {
		return nil, err
	}

	return &pb.AddBlackBoxBaseMapCountResp{}, nil
}
