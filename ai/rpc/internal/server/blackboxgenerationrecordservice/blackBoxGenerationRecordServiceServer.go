// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: ai.proto

package server

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	blackboxgenerationrecordservicelogic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/logic/blackboxgenerationrecordservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

type BlackBoxGenerationRecordServiceServer struct {
	svcCtx *svc.ServiceContext
	pb.UnimplementedBlackBoxGenerationRecordServiceServer
}

func NewBlackBoxGenerationRecordServiceServer(svcCtx *svc.ServiceContext) *BlackBoxGenerationRecordServiceServer {
	return &BlackBoxGenerationRecordServiceServer{
		svcCtx: svcCtx,
	}
}

// AddBlackBoxFuncCount add blackbox func count
func (s *BlackBoxGenerationRecordServiceServer) AddBlackBoxFuncCount(ctx context.Context, in *pb.AddBlackBoxFuncCountReq) (*pb.AddBlackBoxFuncCountResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxgenerationrecordservicelogic.NewAddBlackBoxFuncCountLogic(ctx, s.svcCtx)

	return l.AddBlackBoxFuncCount(in)
}

// AddBlackBoxTestSceneCount add blackbox test scene count
func (s *BlackBoxGenerationRecordServiceServer) AddBlackBoxTestSceneCount(ctx context.Context, in *pb.AddBlackBoxTestSceneCountReq) (*pb.AddBlackBoxTestSceneCountResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxgenerationrecordservicelogic.NewAddBlackBoxTestSceneCountLogic(ctx, s.svcCtx)

	return l.AddBlackBoxTestSceneCount(in)
}

// AddBlackBoxSupplementDocumentCount add blackbox supplement document count
func (s *BlackBoxGenerationRecordServiceServer) AddBlackBoxSupplementDocumentCount(ctx context.Context, in *pb.AddBlackBoxSupplementDocumentCountReq) (*pb.AddBlackBoxSupplementDocumentCountResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxgenerationrecordservicelogic.NewAddBlackBoxSupplementDocumentCountLogic(ctx, s.svcCtx)

	return l.AddBlackBoxSupplementDocumentCount(in)
}

// AddBlackBoxBaseMapCount add blackbox base map count
func (s *BlackBoxGenerationRecordServiceServer) AddBlackBoxBaseMapCount(ctx context.Context, in *pb.AddBlackBoxBaseMapCountReq) (*pb.AddBlackBoxBaseMapCountResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxgenerationrecordservicelogic.NewAddBlackBoxBaseMapCountLogic(ctx, s.svcCtx)

	return l.AddBlackBoxBaseMapCount(in)
}

// AddBlackBoxUpdatedAddCaseCount add blackbox updated add case count
func (s *BlackBoxGenerationRecordServiceServer) AddBlackBoxUpdatedAddCaseCount(ctx context.Context, in *pb.AddBlackBoxUpdatedAddCaseCountReq) (*pb.AddBlackBoxUpdatedAddCaseCountResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxgenerationrecordservicelogic.NewAddBlackBoxUpdatedAddCaseCountLogic(ctx, s.svcCtx)

	return l.AddBlackBoxUpdatedAddCaseCount(in)
}

// AddBlackBoxCaseNameUpdatedCount add blackbox case name updated count
func (s *BlackBoxGenerationRecordServiceServer) AddBlackBoxCaseNameUpdatedCount(ctx context.Context, in *pb.AddBlackBoxCaseNameUpdatedCountReq) (*pb.AddBlackBoxCaseNameUpdatedCountResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxgenerationrecordservicelogic.NewAddBlackBoxCaseNameUpdatedCountLogic(ctx, s.svcCtx)

	return l.AddBlackBoxCaseNameUpdatedCount(in)
}

// AddBlackBoxPreConditionUpdatedCount add blackbox precondition updated count
func (s *BlackBoxGenerationRecordServiceServer) AddBlackBoxPreConditionUpdatedCount(ctx context.Context, in *pb.AddBlackBoxPreConditionUpdatedCountReq) (*pb.AddBlackBoxPreConditionUpdatedCountResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxgenerationrecordservicelogic.NewAddBlackBoxPreConditionUpdatedCountLogic(ctx, s.svcCtx)

	return l.AddBlackBoxPreConditionUpdatedCount(in)
}

// AddBlackBoxCaseStepUpdatedCount add blackbox case step updated count
func (s *BlackBoxGenerationRecordServiceServer) AddBlackBoxCaseStepUpdatedCount(ctx context.Context, in *pb.AddBlackBoxCaseStepUpdatedCountReq) (*pb.AddBlackBoxCaseStepUpdatedCountResp, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxgenerationrecordservicelogic.NewAddBlackBoxCaseStepUpdatedCountLogic(ctx, s.svcCtx)

	return l.AddBlackBoxCaseStepUpdatedCount(in)
}

// AddBlackBoxExpectResultUpdatedCount add blackbox expect result updated count
func (s *BlackBoxGenerationRecordServiceServer) AddBlackBoxExpectResultUpdatedCount(ctx context.Context, in *pb.AddBlackBoxExpectResultUpdatedCountReq) (*pb.AddBlackBoxExpectResultUpdatedCountReq, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxgenerationrecordservicelogic.NewAddBlackBoxExpectResultUpdatedCountLogic(ctx, s.svcCtx)

	return l.AddBlackBoxExpectResultUpdatedCount(in)
}

// AddBlackBoxCaseLevelUpdatedCount add blackbox case level updated count
func (s *BlackBoxGenerationRecordServiceServer) AddBlackBoxCaseLevelUpdatedCount(ctx context.Context, in *pb.AddBlackBoxCaseLevelUpdatedCountReq) (*pb.AddBlackBoxCaseLevelUpdatedCountReq, error) {
	if m, ok := any(in).(protobuf.PGV); ok {
		if err := m.ValidateAll(); err != nil {
			return nil, err
		}
	}

	l := blackboxgenerationrecordservicelogic.NewAddBlackBoxCaseLevelUpdatedCountLogic(ctx, s.svcCtx)

	return l.AddBlackBoxCaseLevelUpdatedCount(in)
}
