// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

service IdentityService {
  // VerifyIdentity updates the waitlist.
  rpc VerifyIdentity(VerifyIdentityRequest) returns (VerifyIdentityResponse);
}

message VerifyIdentityRequest {
  // contact_info is the user's contact information
  oneof contact_info {
    // required ensures that exactly one field in oneof is set. Without this
    // option, at most one of email and phone_number is set.
    option (buf.validate.oneof).required = true;
    // email is the user's email
    string email = 1;
    // phone_number is the user's phone number.
    string phone_number = 2;
  }
  string password = 3;
}

message VerifyIdentityResponse {}
