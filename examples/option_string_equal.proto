// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";

service TermsService {
  rpc AcceptTerms(AcceptTermsRequest) returns (AcceptTermsResponse);
}

message AcceptTermsRequest {
  // `const` specifies that a string field must be the same as this string.
  // In this case, it validates that content must be "I agree to the terms and conditions.".
  string content = 1 [(buf.validate.field).string.const = "I agree to the terms and conditions."];
}

message AcceptTermsResponse {}
