// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

import "buf/validate/validate.proto";
import "google/protobuf/duration.proto";

service AlarmService {
  // SetSnoozeDuration sets snooze duration.
  rpc SetSnoozeDuration(SetSnoozeDurationRequest) returns (SetSnoozeDurationResponse);
}

message SetSnoozeDurationRequest {
  // duration is how long before the alarm goes off again.
  google.protobuf.Duration duration = 1 [(buf.validate.field).duration = {
    // `not_in` validates that a duration is not one the duration specified.
    // In this case, it validates that snooze duration must not be 600 seconds
    // or 300 seconds.
    not_in: [
      {seconds: 600},
      {seconds: 300}]
  }];
}

message SetSnoozeDurationResponse {}
