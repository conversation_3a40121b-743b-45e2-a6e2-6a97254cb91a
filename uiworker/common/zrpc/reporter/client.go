package reporter

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/uireporter"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type RPCClient struct {
	conf zrpc.RpcClientConf

	client uireporter.UIReporter
}

func NewRPCClient(c zrpc.RpcClientConf) *RPCClient {
	return &RPCClient{
		conf: c,

		client: uireporter.NewUIReporter(
			zrpc.MustNewClient(
				c, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}

func (c *RPCClient) CreateRecord(
	ctx context.Context, req *reporterpb.PutUICaseRecordRequest, opts ...grpc.CallOption,
) (*reporterpb.CreateUICaseRecordResponse, error) {
	return c.client.CreateUICaseRecord(ctx, req, opts...)
}

func (c *RPCClient) ModifyRecord(
	ctx context.Context, req *reporterpb.PutUICaseRecordRequest, opts ...grpc.CallOption,
) (*reporterpb.ModifyUICaseRecordResponse, error) {
	return c.client.ModifyUICaseRecord(ctx, req, opts...)
}

func (c *RPCClient) ViewPlanRecord(
	ctx context.Context, req *reporterpb.ViewUIPlanRecordRequest, opts ...grpc.CallOption,
) (*reporterpb.ViewUIPlanRecordResponse, error) {
	return c.client.ViewUIPlanRecord(ctx, req, opts...)
}

func (c *RPCClient) ModifyPlanRecord(
	ctx context.Context, req *reporterpb.PutUIPlanRecordRequest, opts ...grpc.CallOption,
) (*reporterpb.ModifyUIPlanRecordResponse, error) {
	return c.client.ModifyUIPlanRecord(ctx, req, opts...)
}
