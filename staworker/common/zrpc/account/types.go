package account

import (
	"context"

	"google.golang.org/grpc"

	accountpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/pb"
)

type IClient interface {
	AcquireAccount(
		ctx context.Context, in *accountpb.QueryAccountPoolEnvDataRequest, opts ...grpc.CallOption,
	) (*accountpb.QueryAccountPoolEnvDataResponse, error)
	ReleaseAccount(
		ctx context.Context, in *accountpb.ReleaseTestAccountRequest, opts ...grpc.CallOption,
	) (*accountpb.ReleaseTestAccountResponse, error)
}
