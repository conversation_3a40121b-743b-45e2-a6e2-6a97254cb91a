package account

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/pb"
)

var _ IClient = (*mockClient)(nil)

type mockClient struct{}

func NewMockClient() IClient {
	return &mockClient{}
}

func (c *mockClient) AcquireAccount(
	ctx context.Context, in *pb.QueryAccountPoolEnvDataRequest, opts ...grpc.CallOption,
) (*pb.QueryAccountPoolEnvDataResponse, error) {
	logx.WithContext(ctx).Infof("mockClient AcquireAccount: %s", protobuf.MarshalJSONIgnoreError(in))
	return &pb.QueryAccountPoolEnvDataResponse{
		ExpectedCount: 1,
		MatchCount:    1,
		MatchData: []*pb.QueryAccountPoolEnvDataResponse_Account{
			{
				Account: []*pb.QueryAccountPoolEnvDataResponse_Column{
					{
						Field:      "account",
						Value:      "*********",
						LockValue:  "*********",
						ColumnType: pb.ColumnType_VARCHAR,
					},
					{
						Field:      "password",
						Value:      "ttDevOps@2021",
						LockValue:  "ttDevOps@2021",
						ColumnType: pb.ColumnType_VARCHAR,
					},
				},
			},
		},
	}, nil
}

func (c *mockClient) ReleaseAccount(
	ctx context.Context, in *pb.ReleaseTestAccountRequest, opts ...grpc.CallOption,
) (*pb.ReleaseTestAccountResponse, error) {
	logx.WithContext(ctx).Infof("mockClient ReleaseAccount: %s", protobuf.MarshalJSONIgnoreError(in))
	return &pb.ReleaseTestAccountResponse{}, nil
}
