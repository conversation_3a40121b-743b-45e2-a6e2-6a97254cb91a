package reporter

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/reporter"
)

var _ IClient = (*mockClient)(nil)

type mockClient struct{}

func NewMockClient() IClient {
	return &mockClient{}
}

func (c *mockClient) CreateStabilityPlanRecord(
	ctx context.Context, req *reporter.PutStabilityPlanRecordReq,
) (*reporter.CreateStabilityPlanRecordResp, error) {
	logx.WithContext(ctx).Infof("mockClient CreateStabilityPlanRecord: %s", protobuf.MarshalJSONIgnoreError(req))
	return &reporter.CreateStabilityPlanRecordResp{}, nil
}

func (c *mockClient) ModifyStabilityPlanRecord(
	ctx context.Context, req *reporter.PutStabilityPlanRecordReq,
) (*reporter.ModifyStabilityPlanRecordResp, error) {
	logx.WithContext(ctx).Infof("mockClient ModifyStabilityPlanRecord: %s", protobuf.MarshalJSONIgnoreError(req))
	return &reporter.ModifyStabilityPlanRecordResp{}, nil
}

func (c *mockClient) CreateStabilityDeviceRecord(
	ctx context.Context, req *reporter.PutStabilityDeviceRecordReq,
) (*reporter.CreateStabilityDeviceRecordResp, error) {
	logx.WithContext(ctx).Infof("mockClient CreateStabilityDeviceRecord: %s", protobuf.MarshalJSONIgnoreError(req))
	return &reporter.CreateStabilityDeviceRecordResp{}, nil
}

func (c *mockClient) ModifyStabilityDeviceRecord(
	ctx context.Context, req *reporter.PutStabilityDeviceRecordReq,
) (*reporter.ModifyStabilityDeviceRecordResp, error) {
	logx.WithContext(ctx).Infof("mockClient ModifyStabilityDeviceRecord: %s", protobuf.MarshalJSONIgnoreError(req))
	return &reporter.ModifyStabilityDeviceRecordResp{}, nil
}
