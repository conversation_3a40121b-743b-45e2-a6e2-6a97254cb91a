package reporter

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/reporter"
)

type IClient interface {
	CreateStabilityPlanRecord(
		ctx context.Context, req *reporter.PutStabilityPlanRecordReq,
	) (*reporter.CreateStabilityPlanRecordResp, error)
	ModifyStabilityPlanRecord(
		ctx context.Context, req *reporter.PutStabilityPlanRecordReq,
	) (*reporter.ModifyStabilityPlanRecordResp, error)
	//SearchStabilityPlanRecord(
	//	ctx context.Context, req *reporter.SearchStabilityPlanRecordReq,
	//) (*reporter.SearchStabilityPlanRecordResp, error)
	//GetStabilityPlanRecord(
	//	ctx context.Context, req *reporter.GetStabilityPlanRecordReq,
	//) (*reporter.GetStabilityPlanRecordResp, error)
	CreateStabilityDeviceRecord(
		ctx context.Context, req *reporter.PutStabilityDeviceRecordReq,
	) (*reporter.CreateStabilityDeviceRecordResp, error)
	ModifyStabilityDeviceRecord(
		ctx context.Context, req *reporter.PutStabilityDeviceRecordReq,
	) (*reporter.ModifyStabilityDeviceRecordResp, error)
	//SearchStabilityDeviceRecord(
	//	ctx context.Context, req *reporter.SearchStabilityDeviceRecordReq,
	//) (*reporter.SearchStabilityDeviceRecordResp, error)
}
