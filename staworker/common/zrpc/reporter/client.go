package reporter

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/reporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/stabilityreporter"
)

var _ IClient = (*RPCClient)(nil)

type RPCClient struct {
	conf zrpc.RpcClientConf

	client stabilityreporter.StabilityReporter
}

func NewRPCClient(conf zrpc.RpcClientConf) IClient {
	c := &RPCClient{
		conf: conf,
		client: stabilityreporter.NewStabilityReporter(
			zrpc.MustNewClient(conf, clientinterceptors.UnaryUserInfoClientOption()),
		),
	}

	return c
}

func (c *RPCClient) CreateStabilityPlanRecord(
	ctx context.Context, req *reporter.PutStabilityPlanRecordReq,
) (*reporter.CreateStabilityPlanRecordResp, error) {
	return c.client.CreateStabilityPlanRecord(ctx, req)
}

func (c *RPCClient) ModifyStabilityPlanRecord(
	ctx context.Context, req *reporter.PutStabilityPlanRecordReq,
) (*reporter.ModifyStabilityPlanRecordResp, error) {
	return c.client.ModifyStabilityPlanRecord(ctx, req)
}

func (c *RPCClient) SearchStabilityPlanRecord(
	ctx context.Context, req *reporter.SearchStabilityPlanRecordReq,
) (*reporter.SearchStabilityPlanRecordResp, error) {
	return c.client.SearchStabilityPlanRecord(ctx, req)
}

func (c *RPCClient) GetStabilityPlanRecord(
	ctx context.Context, req *reporter.GetStabilityPlanRecordReq,
) (*reporter.GetStabilityPlanRecordResp, error) {
	return c.client.GetStabilityPlanRecord(ctx, req)
}

func (c *RPCClient) CreateStabilityDeviceRecord(
	ctx context.Context, req *reporter.PutStabilityDeviceRecordReq,
) (*reporter.CreateStabilityDeviceRecordResp, error) {
	return c.client.CreateStabilityDeviceRecord(ctx, req)
}

func (c *RPCClient) ModifyStabilityDeviceRecord(
	ctx context.Context, req *reporter.PutStabilityDeviceRecordReq,
) (*reporter.ModifyStabilityDeviceRecordResp, error) {
	return c.client.ModifyStabilityDeviceRecord(ctx, req)
}

func (c *RPCClient) SearchStabilityDeviceRecord(
	ctx context.Context, req *reporter.SearchStabilityDeviceRecordReq,
) (*reporter.SearchStabilityDeviceRecordResp, error) {
	return c.client.SearchStabilityDeviceRecord(ctx, req)
}
