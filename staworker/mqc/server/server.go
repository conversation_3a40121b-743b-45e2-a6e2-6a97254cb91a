package server

import (
	"context"
	"os"
	"sync"
	"syscall"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/server"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/svc"
)

type Server struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	process  *os.Process
	stopOnce sync.Once
}

// NewConsumeServer for single server startup
func NewConsumeServer(configFile string) (*server.Server, error) {
	return server.NewServer(Options(configFile)...)
}

// NewServer for combine server startup
func NewServer(c server.Config, w *log.ZapWriter) (service.Service, error) {
	p, err := os.FindProcess(os.Getpid())
	if err != nil {
		return nil, errors.Errorf("failed to find self process, error: %+v", err)
	}

	cc, ok := c.(config.Config)
	if !ok {
		return nil, errors.Errorf("failed to new mqc server, cause by the config[%T] isn't a mqc config", c)
	}

	if err = cc.ServiceConf.SetUp(); err != nil {
		return nil, errors.Errorf("failed to setup service config, error: %+v", err)
	}

	// 需要在 `ServiceConf.SetUp` 后再设置 `Writer`
	log.SetWriter(w)

	ctx := context.Background()
	s := &Server{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svc.NewServiceContext(cc),

		process: p,
	}

	if err = internal.HandleSetupOperations(s.svcCtx); err != nil {
		return nil, err
	}

	return s, nil
}

// NewConfig new a config of server
func NewConfig(configFile string) server.Config {
	var c config.Config
	conf.MustLoad(configFile, &c, conf.UseEnv())

	return c
}

// Options as a param of `server.NewServer`
func Options(configFile string) []server.Option {
	return []server.Option{
		server.WithNewConfigFunc(
			func() server.Config {
				return NewConfig(configFile)
			},
		),
		server.WithNewServiceFunc(NewServer),
	}
}

func (s *Server) Start() {
	s.Infof("Starting %q at %s", s.svcCtx.Config.Name, time.Now().Format(time.DateTime))

	threading.GoSafeCtx(s.ctx, s.monitor)
	s.svcCtx.StabilityWorkerConsumer.Start()
}

func (s *Server) Stop() {
	s.stopOnce.Do(
		func() {
			s.Infof("Stopping %q at %s", s.svcCtx.Config.Name, time.Now().Format(time.DateTime))

			// shutdown the consumer
			if err := s.process.Signal(syscall.SIGTERM); err != nil {
				s.Errorf("failed to send signal TERM to the consumer, pid: %d, error: %+v", s.process.Pid, err)
			}
			s.svcCtx.StabilityWorkerConsumer.Stop()

			// send an exit signal to other goroutines
			close(s.svcCtx.ExitChannel)
		},
	)
}

func (s *Server) monitor() {
	if s.svcCtx.Config.Name != common.NameOfStabilityTool {
		return
	}

	defer s.Stop()

	timer := timewheel.NewTimer(common.ConstTimeoutOfWaitTask)
	var stopOnce sync.Once
	stopFn := func() {
		stopOnce.Do(
			func() {
				timer.Stop()

				if err := s.process.Signal(syscall.SIGTSTP); err != nil {
					s.Errorf("failed to send signal TSTP to the consumer, pid: %d, error: %+v", s.process.Pid, err)
				}
			},
		)
	}
	defer stopFn()

	for {
		select {
		case <-timer.C:
			s.Warnf(
				"wait for a stability task timeout, task_id: %s, execute_id: %s, timeout: %s",
				s.svcCtx.Config.Task.TaskID, s.svcCtx.Config.Task.ExecuteID, common.ConstTimeoutOfWaitTask.String(),
			)
			return
		case <-s.svcCtx.TasksChannel:
			s.Infof(
				"got a stability task at %s, task_id: %s, execute_id: %s",
				time.Now().Format(time.DateTime), s.svcCtx.Config.Task.TaskID, s.svcCtx.Config.Task.ExecuteID,
			)
			stopFn()
		case <-s.svcCtx.FinishChannel:
			s.Infof(
				"the stability task completed at %s, task_id: %s, execute_id: %s",
				time.Now().Format(time.DateTime), s.svcCtx.Config.Task.TaskID, s.svcCtx.Config.Task.ExecuteID,
			)
			return
		case <-s.svcCtx.ExitChannel:
			s.Infof(
				"got an exit signal while waiting for a stability task at %s, task_id: %s, execute_id: %s",
				time.Now().Format(time.DateTime), s.svcCtx.Config.Task.TaskID, s.svcCtx.Config.Task.ExecuteID,
			)
			return
		}
	}
}
