package svc

import (
	"github.com/alicebob/miniredis/v2"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	qetredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/singleflight"

	constack "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/constack/v1alpha"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/common/zrpc/account"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/common/zrpc/manager"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/common/zrpc/reporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/config"
)

func NewMockServiceContext(s *miniredis.Miniredis) *ServiceContext {
	c := config.NewMockConfig(s)
	rdb := qetredis.NewClient(c.Redis.RedisConf)
	constackClient, err := constack.NewGRPCClient(c.Constack.Url, c.Constack.Token)
	if err != nil {
		return nil
	}

	return &ServiceContext{
		Config: c,

		Redis:           redis.MustNewRedis(c.Redis.RedisConf, redis.WithDB(c.Redis.RedisConf.DB)),
		RedisNode:       rdb,
		DispatcherRedis: qetredis.NewClient(c.DispatcherRedis),

		ADBClient:    mustNewADBClient(c.ADB),
		SingleFlight: singleflight.NewDistributedSingleFlight(rdb),

		AccountRPC:  account.NewMockClient(),
		ManagerRPC:  manager.NewMockClient(),
		ReporterRPC: reporter.NewMockClient(),

		StabilityWorkerConsumer: consumer.NewConsumer(c.StabilityWorkerConsumer),
		StabilityWorkerProducer: producer.NewProducer(c.StabilityWorkerProducer),
		DeviceHubProducer:       producer.NewProducer(c.DeviceHubProducer),
		ReporterProducer:        producer.NewProducer(c.ReporterProducer),
		DispatcherProducer:      producer.NewProducer(c.DispatcherProducer),

		ConstackGrpcClient: constackClient,

		TasksChannel:  make(chan lang.PlaceholderType, 1),
		FinishChannel: make(chan lang.PlaceholderType, 1),
		ExitChannel:   make(chan lang.PlaceholderType),
	}
}
