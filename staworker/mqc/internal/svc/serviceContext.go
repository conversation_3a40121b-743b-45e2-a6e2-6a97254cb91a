package svc

import (
	"github.com/electricbubble/gadb"
	red "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	qetredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/singleflight"

	constack "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/constack/v1alpha"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/common/zrpc/account"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/common/zrpc/manager"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/common/zrpc/reporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/config"
)

type ServiceContext struct {
	Config config.Config

	Redis           *redis.Redis
	RedisNode       red.UniversalClient
	DispatcherRedis red.UniversalClient

	ADBClient    *gadb.Client
	SingleFlight *singleflight.DistributedSingleFlight

	AccountRPC  account.IClient
	ManagerRPC  manager.IClient
	ReporterRPC reporter.IClient

	StabilityWorkerConsumer *consumer.Consumer
	StabilityWorkerProducer *producer.Producer
	DeviceHubProducer       *producer.Producer
	ReporterProducer        *producer.Producer
	DispatcherProducer      *producer.Producer

	ConstackGrpcClient constack.GRPCClient

	TasksChannel  chan lang.PlaceholderType // 接收消费者接收到任务的信号
	FinishChannel chan lang.PlaceholderType // 接收消费者完成任务的信号
	ExitChannel   chan lang.PlaceholderType // 接收退出信号
}

func NewServiceContext(c config.Config) *ServiceContext {
	rdb := qetredis.NewClient(c.Redis.RedisConf)
	constackClient, err := constack.NewGRPCClient(c.Constack.Url, c.Constack.Token)
	if err != nil {
		return nil
	}

	if c.Name == common.NameOfStabilityTool {
		c.StabilityWorkerConsumer.MaxWorker = 1
	}

	return &ServiceContext{
		Config: c,

		Redis:           redis.MustNewRedis(c.Redis.RedisConf, redis.WithDB(c.Redis.RedisConf.DB)),
		RedisNode:       rdb,
		DispatcherRedis: qetredis.NewClient(c.DispatcherRedis),

		ADBClient:    mustNewADBClient(c.ADB),
		SingleFlight: singleflight.NewDistributedSingleFlight(rdb),

		AccountRPC:  account.NewRPCClient(c.Account),
		ManagerRPC:  manager.NewRPCClient(c.Manager),
		ReporterRPC: reporter.NewRPCClient(c.Reporter),

		StabilityWorkerConsumer: consumer.NewConsumer(c.StabilityWorkerConsumer),
		StabilityWorkerProducer: producer.NewProducer(c.StabilityWorkerProducer),
		DeviceHubProducer:       producer.NewProducer(c.DeviceHubProducer),
		ReporterProducer:        producer.NewProducer(c.ReporterProducer),
		DispatcherProducer:      producer.NewProducer(c.DispatcherProducer),

		ConstackGrpcClient: constackClient,

		TasksChannel:  make(chan lang.PlaceholderType, 1),
		FinishChannel: make(chan lang.PlaceholderType, 1),
		ExitChannel:   make(chan lang.PlaceholderType),
	}
}

func mustNewADBClient(c gadb.Config) *gadb.Client {
	client, err := gadb.NewClientWithConfig(c)
	logx.Must(err)
	return client
}
