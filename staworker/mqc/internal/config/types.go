package config

type (
	ResourceRequirements struct {
		Limits   ResourceList
		Requests ResourceList
	}
	ResourceList struct {
		Cpu    string
		Memory string
	}

	TaskInfo struct {
		TypeName  string // 任务类型名称
		TaskID    string `json:",optional"` // 任务ID
		ExecuteID string `json:",optional"` // 压测用例执行ID
	}

	JobInfo struct {
		ClusterName string
		Namespace   string
		Image       string
		PvcName     string
		MountPath   string
		Resources   ResourceRequirements
	}
)
