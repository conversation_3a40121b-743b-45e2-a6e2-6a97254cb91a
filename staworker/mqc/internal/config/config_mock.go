package config

import (
	"github.com/alicebob/miniredis/v2"
	"github.com/electricbubble/gadb"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/constack"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/common"
)

func NewMockConfig(s *miniredis.Miniredis) Config {
	addr := s.Addr()

	return Config{
		ServiceConf: service.ServiceConf{
			Name: common.NameOfStabilityTool,
			Log: logx.LogConf{
				ServiceName: "mqc." + common.NameOfStabilityTool,
				Encoding:    "plain",
				Level:       "info",
				Stat:        false,
			},
		},

		Redis: redis.RedisKeyConf{
			RedisConf: redis.RedisConf{
				Host: addr,
				Type: redis.NodeType,
				DB:   23,
			},
			Key: "mqc." + common.NameOfStabilityTool,
		},
		DispatcherRedis: redis.RedisConf{
			Host: addr,
			Type: redis.NodeType,
			DB:   4,
		},

		Account: zrpc.RpcClientConf{
			Endpoints: []string{"127.0.0.1:20111"},
			NonBlock:  true,
			Timeout:   0,
		},
		Manager: zrpc.RpcClientConf{
			Endpoints: []string{"127.0.0.1:20211"},
			NonBlock:  true,
			Timeout:   0,
		},
		Reporter: zrpc.RpcClientConf{
			Endpoints: []string{"127.0.0.1:20511"},
			NonBlock:  true,
			Timeout:   0,
		},

		StabilityWorkerConsumer: consumer.Config{
			Broker:      addr,
			Backend:     addr,
			Queue:       "mqc:" + common.NameOfStabilityTool,
			ConsumerTag: "mqc:" + common.NameOfStabilityTool,
			Db:          20,
		},
		StabilityWorkerProducer: producer.Config{
			Broker:  addr,
			Backend: addr,
			Queue:   "mqc:" + common.NameOfStabilityTool,
			Db:      20,
		},
		DeviceHubProducer: producer.Config{
			Broker:  addr,
			Backend: addr,
			Queue:   "mqc:devicehub",
			Db:      20,
		},
		ReporterProducer: producer.Config{
			Broker:  addr,
			Backend: addr,
			Queue:   "mqc:reporter",
			Db:      20,
		},
		DispatcherProducer: producer.Config{
			Broker:  addr,
			Backend: addr,
			Queue:   "mqc:dispatcher",
			Db:      20,
		},

		ADB: gadb.Config{
			PathOfADB: "./assets/adb/adb-darwin/adb",
		},
		Constack: constack.Config{
			Url:   "cloud.ttyuyin.com:8100",
			Token: "Y2ljZA==.2565c2d93b2209fb0d52fe19cdc9557c1a4ba57b5a2b35cf10d4790b6e02e663",
		},

		LocalPath: "./reports/stability_test",

		Task: TaskInfo{
			TypeName:  constants.MQTaskTypeStabilityWorkerExecuteCaseTask,
			TaskID:    "task_id:1",
			ExecuteID: "execute_id:1111",
		},
		Job: JobInfo{
			ClusterName: "k8s-tc-bj-1-test",
			Namespace:   "probe-test",
			Image:       "cr.ttyuyin.com/devops/20/staworker:latest",
			PvcName:     "pvc-probe",
			MountPath:   "/app/data",
			Resources: ResourceRequirements{
				Limits: ResourceList{
					Cpu:    "1",
					Memory: "1Gi",
				},
				Requests: ResourceList{
					Cpu:    "500m",
					Memory: "512Mi",
				},
			},
		},
	}
}
