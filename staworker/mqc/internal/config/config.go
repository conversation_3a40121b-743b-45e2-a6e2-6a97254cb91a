package config

import (
	"github.com/electricbubble/gadb"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/constack"
)

type Config struct {
	service.ServiceConf

	Redis           redis.RedisKeyConf
	DispatcherRedis redis.RedisConf

	Account  zrpc.RpcClientConf
	Manager  zrpc.RpcClientConf
	Reporter zrpc.RpcClientConf

	StabilityWorkerConsumer consumer.Config
	StabilityWorkerProducer producer.Config
	DeviceHubProducer       producer.Config
	ReporterProducer        producer.Config
	DispatcherProducer      producer.Config

	ADB      gadb.Config `json:",optional"`
	Constack constack.Config

	LocalPath string

	Task TaskInfo `json:",optional"`
	Job  JobInfo  `json:",optional"`
}

func (c Config) LogConfig() logx.LogConf {
	return c.ServiceConf.Log
}

func (c Config) ListenOn() string {
	return constants.NoNeedToListen
}
