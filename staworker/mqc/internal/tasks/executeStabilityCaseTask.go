package tasks

import (
	"context"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/trace"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/stacase"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/svc"
)

var _ base.Handler = (*StabilityCaseTaskProcessor)(nil)

type StabilityCaseTaskProcessor struct {
	svcCtx *svc.ServiceContext
}

func NewStabilityCaseTaskProcessor(svcCtx *svc.ServiceContext) base.Handler {
	return &StabilityCaseTaskProcessor{
		svcCtx: svcCtx,
	}
}

func (p *StabilityCaseTaskProcessor) ProcessTask(ctx context.Context, task *base.Task) ([]byte, error) {
	logger := logx.WithContext(ctx)
	logger.Infof("got a stability task, type: %s, payload: %s", task.Typename, task.Payload)

	// notify the server that the consumer has received a stability task
	p.svcCtx.TasksChannel <- lang.Placeholder
	defer func() {
		// notify the server that the consumer has finished the stability task
		p.svcCtx.FinishChannel <- lang.Placeholder
	}()

	logger.Debugf(
		"processor trace_id: %s, span_id: %s, task_name: %s",
		trace.TraceIDFromContext(ctx), trace.SpanIDFromContext(ctx), task.Typename,
	)
	defer func() {
		if r := recover(); r != nil {
			logger.Errorf("processor recover result: %+v", r)
		}
	}()

	var info commonpb.StabilityCaseTaskInfo
	if err := protobuf.UnmarshalJSON(task.Payload, &info); err != nil {
		logger.Errorf(
			"failed to unmarshal the payload of stability case task, payload: %s, error: %+v", task.Payload, err,
		)
		return []byte(constants.FAILURE), nil
	}

	if err := info.ValidateAll(); err != nil {
		logger.Errorf(
			"failed to validate the payload of stability case task, payload: %s, error: %+v", task.Payload, err,
		)
		return []byte(constants.FAILURE), nil
	}

	ctx = updateContext(ctx, info.GetTaskId(), info.GetExecuteId())
	logger.Infof("the content of config: %s", jsonx.MarshalIgnoreError(p.svcCtx.Config))
	logger.Infof("stability case task info: %s", protobuf.MarshalJSONIgnoreError(&info))

	if err := stacase.NewExecuteStabilityCaseTaskLogic(ctx, p.svcCtx, &info).Execute(); err != nil {
		logger.Errorf("failed to execute the stability case task, payload: %s, error: %+v", task.Payload, err)
		return []byte(constants.FAILURE), nil
	}

	return []byte(constants.SUCCESS), nil
}
