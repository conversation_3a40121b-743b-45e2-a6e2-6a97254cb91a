package tasks

import (
	"context"
	"os"
	"path/filepath"
	"reflect"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/svc"
)

func TestStabilityCaseTaskProcessor_ProcessTask(t *testing.T) {
	if basePath, err := filepath.Abs("../../../.."); err == nil {
		_ = os.Chdir(basePath)
	}
	currentPath, _ := os.Getwd()
	t.Logf("PWD: %s", currentPath)

	logx.SetLevel(logx.DebugLevel)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	s := miniredis.RunT(t)
	svcCtx := svc.NewMockServiceContext(s)
	proc.AddShutdownListener(
		func() {
			close(svcCtx.ExitChannel)
		},
	)
	threading.GoSafe(
		func() {
			for {
				select {
				case <-svcCtx.TasksChannel:
					t.Log("got a stability task")
				case <-svcCtx.FinishChannel:
					t.Log("the stability task completed")
				case <-svcCtx.ExitChannel:
					t.Log("got an exit signal")
					return
				case <-ctx.Done():
					t.Logf("got a done signal, error: %+v", ctx.Err())
					return
				}
			}
		},
	)

	var (
		projectID   = "project_id:Kqllt5-9fA-I5UOdhjA5d"
		taskID      = "task_id:1"
		udid        = "LZYTYLZT9HFI6DLN"
		packageName = "com.yiyou.ga"
	)
	processor := NewStabilityCaseTaskProcessor(svcCtx)

	type args struct {
		task *base.Task
	}
	tests := []struct {
		name    string
		args    args
		want    []byte
		wantErr bool
	}{
		{
			name: "",
			args: args{
				task: &base.Task{
					Typename: constants.MQTaskTypeStabilityWorkerExecuteCaseTask,
					Payload: protobuf.MarshalJSONIgnoreError(
						&commonpb.StabilityCaseTaskInfo{
							ProjectId:     projectID,
							PlanId:        "stability_plan_id:1",
							TaskId:        taskID,
							ExecuteId:     "execute_id:1111",
							PlanExecuteId: "execute_id:11",
							TriggerMode:   commonpb.TriggerMode_MANUAL,
							AccountConfig: &commonpb.AccountConfig{
								ProjectId: projectID,
							},
							DeviceType:      commonpb.DeviceType_REAL_PHONE,
							PlatformType:    commonpb.PlatformType_ANDROID,
							Udid:            udid,
							PackageName:     packageName,
							AppDownloadLink: "",
							Activities:      nil,
							CustomScript:    nil,
							Duration:        2, // 2 mins
							ExecutedBy:      "T1704",
						},
					),
					Timeout: 3 * time.Minute,
				},
			},
			want:    []byte(constants.SUCCESS),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				if err := os.RemoveAll(filepath.Join(currentPath, "reports", "stability_test", "reports", taskID)); err != nil {
					t.Errorf("failed to remove report directory, error: %+v", err)
				}

				got, err := processor.ProcessTask(ctx, tt.args.task)
				if (err != nil) != tt.wantErr {
					t.Errorf("ProcessTask() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("ProcessTask() got = %v, want %v", got, tt.want)
				}
			},
		)
	}
}
