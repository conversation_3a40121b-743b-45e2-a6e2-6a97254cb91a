package internal

import (
	"os"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/tasks"
)

func HandleSetupOperations(svcCtx *svc.ServiceContext) error {
	// 启动`adb`服务
	if err := startADBServer(svcCtx); err != nil {
		return err
	}

	// 注册任务
	return registerTasks(svcCtx)
}

func startADBServer(svcCtx *svc.ServiceContext) error {
	logx.Infof("`PATH`: %s", os.Getenv("PATH"))

	if svcCtx.Config.Name == common.NameOfStabilityTool {
		if err := svcCtx.ADBClient.StartServer(); err != nil {
			return errors.Errorf(
				"failed to start adb server, task_id: %s, execute_id: %s, error: %+v",
				svcCtx.Config.Task.TaskID, svcCtx.Config.Task.ExecuteID, err,
			)
		}
		logx.Infof(
			"succeed to start adb server, task_id: %s, execute_id: %s",
			svcCtx.Config.Task.TaskID, svcCtx.Config.Task.ExecuteID,
		)

		proc.AddShutdownListener(
			func() {
				if svcCtx != nil && svcCtx.ADBClient != nil {
					_ = svcCtx.ADBClient.KillServer()
				}
			},
		)
	}

	return nil
}

func registerTasks(svcCtx *svc.ServiceContext) error {
	var (
		typename string
		handler  base.Handler
	)

	if svcCtx.Config.Name == common.NameOfStabilityTool {
		typename = constants.MQTaskTypeStabilityWorkerExecuteCaseTask
		if svcCtx.Config.Task.TypeName != "" {
			typename = svcCtx.Config.Task.TypeName
		}
		handler = tasks.NewStabilityCaseTaskProcessor(svcCtx)
	} else {
		typename = constants.MQTaskTypeStabilityWorkerExecutePlanTask
		handler = tasks.NewStabilityPlanTaskProcessor(svcCtx)
	}

	if err := svcCtx.StabilityWorkerConsumer.RegisterHandlers(
		consumer.NewTaskHandlerOjb(
			typename, handler,
		),
	); err != nil {
		return errors.Errorf(
			"failed to register stability test task, task_id: %s, execute_id: %s, error: %+v",
			svcCtx.Config.Task.TaskID, svcCtx.Config.Task.ExecuteID, err,
		)
	}

	logx.Infof(
		"succeed to register stability test task, task_id: %s, execute_id: %s, name: %s",
		svcCtx.Config.Task.TaskID, svcCtx.Config.Task.ExecuteID, typename,
	)
	return nil
}
