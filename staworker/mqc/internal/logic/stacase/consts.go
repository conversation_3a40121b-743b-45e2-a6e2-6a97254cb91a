package stacase

import "time"

const (
	intervalOfWatchStopSignal = 5 * time.Second

	keyOfDownloadAppPackage   = "download_app_package"
	keyOfAcquireAccounts      = "acquire_accounts"
	keyOfAcquireDevices       = "acquire_devices"
	keyOfInstallApp           = "install_app"
	keyOfExecuteBusinessLogic = "execute_business_logic"
	keyOfRunStabilityTest     = "run_stability_test"
	keyOfReleaseDevices       = "release_devices"
	keyOfReleaseAccounts      = "release_accounts"
)

type desc = string

const (
	descOfDownloadAppPackageEN desc = "download the app package"
	descOfDownloadAppPackageZH desc = "下载测试包"

	descOfAcquireAccountsEN desc = "acquire accounts"
	descOfAcquireAccountsZH desc = "获取账号"

	descOfAcquireDevicesEN desc = "acquire devices"
	descOfAcquireDevicesZH desc = "获取设备"

	descOfInstallAppEN desc = "install the app"
	descOfInstallAppZH desc = "安装测试包"

	descOfExecuteBusinessLogicEN desc = "execute business logic"
	descOfExecuteBusinessLogicZH desc = "执行前置的业务逻辑"

	descOfRunStabilityTestEN desc = "run stability test"
	descOfRunStabilityTestZH desc = "运行稳定性测试"

	descOfReleaseDevicesEN desc = "release devices"
	descOfReleaseDevicesZH desc = "释放设备"

	descOfReleaseAccountsEN desc = "release accounts"
	descOfReleaseAccountsZH desc = "释放账号"
)
