package stacase

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/electricbubble/gidevice/pkg/ipa"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/shogo82148/androidbinary/apk"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	accountcommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/common"
	accountpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/account/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/collector"
	commonconsts "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/logger"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pkgpuller"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	devicehubcommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
	devicehubpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/pb"
	dispatcherutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/utils"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managercommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/stacase/device"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/stacase/products/tt"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/svc"
)

type ExecuteStabilityCaseTaskLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	logger *logger.StepLogger
	stopCh chan lang.PlaceholderType

	taskInfo *commonpb.StabilityCaseTaskInfo
	state    dispatcherpb.ComponentState

	key        string
	appPath    string                // 待测试的App文件
	appName    string                // 待测试的App名称（Android: package_name, iOS: bundle_id）
	reportPath string                // 测试报告的存放路径
	devices    []*devicehubpb.Device // 占用的设备列表（注：正常情况下只会占用一个设备）
	accounts   []*Account            // 占用的账号列表（注：正常情况下只会占用一个账号）
	result     device.Result         // 稳定性测试结果

	initDeviceOnce sync.Once
	device         *device.AndroidDevice // 注：后续支持`iOS`时，这里需要改成接口
	businessLogic  *tt.AndroidLogic      // 注：后续支持其它产品时，这里需要改成接口
}

func NewExecuteStabilityCaseTaskLogic(
	ctx context.Context, svcCtx *svc.ServiceContext, taskInfo *commonpb.StabilityCaseTaskInfo,
) *ExecuteStabilityCaseTaskLogic {
	l := &ExecuteStabilityCaseTaskLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		logger: logger.NewStepLogger(ctx, svcCtx.Config.Log),
		stopCh: make(chan lang.PlaceholderType),

		taskInfo: taskInfo,
		state:    dispatcherpb.ComponentState_Pending,

		devices:  make([]*devicehubpb.Device, 0, 1),
		accounts: make([]*Account, 0, 1),
	}
	l.init()
	return l
}

func (l *ExecuteStabilityCaseTaskLogic) init() {
	taskID := l.taskInfo.GetTaskId()
	l.key = taskID
	if ss := strings.Split(taskID, ":"); len(ss) >= 2 {
		l.key = ss[1]
	}

	suffix := ""
	switch l.taskInfo.GetPlatformType() {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
		suffix = common.ConstSuffixOfApk
	case commonpb.PlatformType_IOS:
		suffix = common.ConstSuffixOfIpa
	default:
		l.Warnf("invalid platform type: %s", l.taskInfo.GetPlatformType().String())
	}

	l.appPath = filepath.Join(
		l.svcCtx.Config.LocalPath, common.ConstAppDownloadPath, fmt.Sprintf("%s%s", l.key, suffix),
	)
	l.reportPath = filepath.Join(l.svcCtx.Config.LocalPath, common.ConstStabilityTestReportPath, taskID)
}

func (l *ExecuteStabilityCaseTaskLogic) Execute() (err error) {
	defer func() {
		if r := recover(); r != nil {
			l.state = dispatcherpb.ComponentState_Panic
			l.Errorf(
				"got a panic while executing the stability case task, task_id: %s, error: %+v",
				l.taskInfo.GetTaskId(), r,
			)
		} else if err != nil {
			var target *errorOfInvalidStage
			if errors.Is(err, errorOfNullStep) || errors.As(err, &target) {
				l.state = dispatcherpb.ComponentState_Panic
			} else if errors.Is(err, errorOfStopSignal) {
				l.state = dispatcherpb.ComponentState_Stop
			} else {
				l.state = dispatcherpb.ComponentState_Failure
			}
		} else {
			l.state = dispatcherpb.ComponentState_Success
		}

		// 结束执行
		if e := l.sendTaskStatusToReporter(); e != nil {
			l.Error(e)
		}
	}()

	// 开始执行
	l.state = dispatcherpb.ComponentState_Started
	if err = l.sendTaskStatusToReporter(); err != nil {
		return err
	}

	threading.GoSafeCtx(l.ctx, l.watchStopSignal)

	defer func() {
		_ = l.runSteps(l.teardownSteps())
	}()

	if err = l.runSteps(l.setupSteps()); err != nil {
		return err
	}

	if err = l.runSteps(l.testSteps()); err != nil {
		return err
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) watchStopSignal() {
	ticker := timewheel.NewTicker(intervalOfWatchStopSignal)
	defer ticker.Stop()

	for {
		select {
		case <-l.ctx.Done():
			return
		case <-ticker.C:
			if err := l.isStopped(); err != nil {
				close(l.stopCh)
				return
			}
		}
	}
}

func (l *ExecuteStabilityCaseTaskLogic) isStopped() (err error) {
	taskID := l.taskInfo.GetTaskId()

	defer func() {
		if r := recover(); r != nil {
			l.state = dispatcherpb.ComponentState_Panic
			err = errors.Errorf("got a panic while checking the stop status, task_id: %s, error: %+v", taskID, r)
		}
	}()

	stop, err := dispatcherutils.GetStopStatus(l.ctx, l.svcCtx.DispatcherRedis, taskID)
	if err != nil {
		l.state = dispatcherpb.ComponentState_Panic
		return errors.Wrapf(err, "failed to get the stop status of task, task_id: %s", taskID)
	} else if stop {
		l.state = dispatcherpb.ComponentState_Stop
		return errors.Errorf("got a stop signal of task, task_id: %s", taskID)
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) downloadAppPackage() error {
	var (
		packageName = l.taskInfo.GetPackageName()
		link        = l.taskInfo.GetAppDownloadLink()

		version string
	)

	stat, err := os.Stat(l.appPath)
	if err == nil && !stat.IsDir() && stat.Size() > 0 {
		l.logger.Infof(
			"the app package has been downloaded, package_name: %s, link: %s, path: %s, size: %d",
			packageName, link, l.appPath, stat.Size(),
		)
	} else {
		ctx, cancel := commonutils.NewTimeoutContext(l.ctx, common.ConstTimeoutOfDownloadAppPackage)
		defer cancel()

		if link != "" {
			err = utils.DownloadFromUrl(ctx, link, l.appPath)
		} else {
			var puller pkgpuller.AppPkgPuller
			puller, err = pkgpuller.NewAppPkgPuller(ctx, pkgpuller.AppPkgNameType(packageName), l.appPath)
			if err != nil {
				return errors.Errorf(
					"failed to new app package puller, package_name: %s, path: %s, error: %+v",
					packageName, l.appPath, err,
				)
			}

			link, err = puller.Pull()
		}
		if err != nil {
			return errors.Errorf(
				"failed to download the app package, package_name: %s, link: %s, path: %s, error: %+v",
				packageName, link, l.appPath, err,
			)
		}

		stat, err = os.Stat(l.appPath)
		if err != nil {
			return errors.Errorf(
				"the app package was not found locally after download, package_name: %s, link: %s, path: %s, error: %+v",
				packageName, link, l.appPath, err,
			)
		}
	}

	switch l.taskInfo.GetPlatformType() {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
		info, err := apk.OpenFile(l.appPath)
		if err != nil {
			l.logger.Errorf(
				"failed to open the apk file, package_name: %s, link: %s, path: %s, size: %d, error: %+v",
				packageName, link, l.appPath, stat.Size(), err,
			)
		} else {
			l.appName = info.PackageName()
			version = info.Manifest().VersionName
		}
	case commonpb.PlatformType_IOS:
		info, err := ipa.Info(l.appPath)
		if err != nil {
			l.logger.Errorf(
				"failed to open the ipa file, package_name: %s, link: %s, path: %s, size: %d, error: %+v",
				packageName, link, l.appPath, stat.Size(), err,
			)
		} else {
			l.appName = info.CFBundleIdentifier
			version = info.CFBundleShortVersionString
			if len(info.CFBundleVersion) > 0 {
				version += "-" + info.CFBundleVersion
			}
		}
	}

	if version != "" {
		l.cacheAppInfo(
			&commonpb.AppInfo{
				DownloadLink: link,
				Version:      version,
				Name:         l.appName,
			},
		)
	}

	l.logger.Infof(
		"finish to download the app package, package_name: %s, link: %s, version: %s, path: %s, size: %d, app_name: %s",
		packageName, link, version, l.appPath, stat.Size(), l.appName,
	)
	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) cacheAppInfo(info *commonpb.AppInfo) {
	if info == nil || info.GetDownloadLink() == "" || info.GetVersion() == "" {
		return
	}

	key := fmt.Sprintf("%s:%s", commonconsts.ConstCachePrefixOfAppInfo, l.taskInfo.GetTaskId())
	value := protobuf.MarshalJSONToStringIgnoreError(info)
	result, err := l.svcCtx.Redis.SetnxExCtx(l.ctx, key, value, 24*60*60)
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			l.Errorf("failed to cache the app info, key: %s, value: %s, error: %+v", key, value, err)
		}

		return
	}

	l.Infof("cache the app info successfully, key: %s, value: %s, result: %t", key, value, result)
}

func (l *ExecuteStabilityCaseTaskLogic) acquireAccount() (err error) {
	accountConfig := l.taskInfo.GetAccountConfig()

	defer func() {
		if err != nil {
			_ = l.releaseAccount()
		}
	}()

	out, err := l.svcCtx.AccountRPC.AcquireAccount(
		l.ctx, &accountpb.QueryAccountPoolEnvDataRequest{
			PoolEnvTable:  accountConfig.GetPoolEnvTable(),
			ExpectedCount: 1,
		},
		zrpc.WithCallTimeout(accountcommon.ConstRPCAcquireAccountTimeout),
	)
	if err != nil {
		return err
	}

	if len(out.GetMatchData()) == 0 {
		return errors.Errorf("no accounts were occupied, pool_env_table %s", accountConfig.GetPoolEnvTable())
	}
	l.logger.Infof("acquired accounts: %s", protobuf.MarshalJSONIgnoreError(out))

	for _, account := range out.GetMatchData() {
		item := &Account{}
		for _, column := range account.GetAccount() {
			switch column.GetField() {
			case accountcommon.BuiltinTableFieldOfAccount:
				item.Username = column.GetValue()
				item.Token = column.GetLockValue()
			case accountcommon.BuiltinTableFieldOfPassword:
				item.Password = column.GetValue()
			default:
			}

			if item.Username != "" && item.Password != "" && item.Token != "" {
				break
			}
		}

		if item.Username != "" && item.Password != "" && item.Token != "" {
			l.accounts = append(l.accounts, item)
		}
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) releaseAccount() (err error) {
	if len(l.accounts) == 0 {
		return nil
	}

	ctx, cancel := commonutils.NewTimeoutContext(l.ctx, accountcommon.ConstRPCReleaseAccountTimeout)
	defer cancel()

	accountConfig := l.taskInfo.GetAccountConfig()
	in := &accountpb.ReleaseTestAccountRequest{
		ReleaseTasAccountArray: []*accountpb.ReleaseTestAccountRequest_PoolAccount{
			{
				PoolEnvTable: accountConfig.GetPoolEnvTable(),
				AccountArray: make([]*accountpb.ReleaseTestAccountRequest_Account, 0, len(l.accounts)),
			},
		},
	}
	for _, account := range l.accounts {
		in.ReleaseTasAccountArray[0].AccountArray = append(
			in.ReleaseTasAccountArray[0].AccountArray, &accountpb.ReleaseTestAccountRequest_Account{
				Account:   account.Username,
				LockValue: account.Token,
			},
		)
	}

	if _, err = l.svcCtx.AccountRPC.ReleaseAccount(ctx, in); err != nil {
		l.logger.Errorf(
			"failed to release the account, accounts: %s, error: %+v",
			jsonx.MarshalIgnoreError(l.accounts), err,
		)
	}

	return err
}

func (l *ExecuteStabilityCaseTaskLogic) acquireDevice() (err error) {
	var (
		projectID = l.taskInfo.GetProjectId()

		deviceType   = l.taskInfo.GetDeviceType()
		platformType = l.taskInfo.GetPlatformType()
		udid         = l.taskInfo.GetUdid()
	)

	defer func() {
		if err != nil {
			_ = l.releaseDevice()
		}
	}()

	condition := &rpc.Condition{
		Group: &rpc.GroupCondition{
			Relationship: constants.AND,
			Conditions: []*rpc.Condition{
				{
					Single: &rpc.SingleCondition{
						Field:   string(devicehubcommon.DeviceFieldOfType),
						Compare: constants.EQ,
						Other: &rpc.Other{
							Value: strconv.Itoa(int(deviceType.Number())),
						},
					},
				},
				{
					Single: &rpc.SingleCondition{
						Field:   string(devicehubcommon.DeviceFieldOfPlatform),
						Compare: constants.EQ,
						Other: &rpc.Other{
							Value: strconv.Itoa(int(platformType.Number())),
						},
					},
				},
			},
		},
	}
	if !strings.HasPrefix(udid, common.ConstPrefixOfRandomDevice) {
		condition.Group.Conditions = append(
			condition.Group.Conditions, &rpc.Condition{
				Single: &rpc.SingleCondition{
					Field:   string(devicehubcommon.DeviceFieldOfUDID),
					Compare: constants.EQ,
					Other: &rpc.Other{
						Value: udid,
					},
				},
			},
		)
	}

	out, err := l.svcCtx.ManagerRPC.AcquireProjectDevice(
		l.ctx, &managerpb.AcquireProjectDeviceReq{
			ProjectId: projectID,
			Usage:     commonpb.DeviceUsage_STABILITY_TESTING,
			Condition: condition,
			Count:     1,
		},
		zrpc.WithCallTimeout(devicehubcommon.ConstRPCAcquireDeviceTimeout),
	)
	if err != nil {
		return errors.Wrapf(
			err,
			"failed to acquire project device, device_type: %s, platform_type: %s",
			deviceType.String(), platformType.String(),
		)
	}

	if len(out.GetDevices()) == 0 {
		return errors.Errorf(
			"no project devices were occupied, device_type: %s, platform_type: %s",
			deviceType.String(), platformType.String(),
		)
	}
	l.logger.Infof("acquired project devices: %s", protobuf.MarshalJSONIgnoreError(out))

	for _, d := range out.GetDevices() {
		if d == nil || d.GetDevice() == nil {
			continue
		}

		l.devices = append(l.devices, d.GetDevice())
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) releaseDevice() (err error) {
	if err = mr.MapReduceVoid[*devicehubpb.Device, any](
		func(source chan<- *devicehubpb.Device) {
			for _, d := range l.devices {
				if d == nil || d.GetUdid() == "" || d.GetToken() == "" {
					continue
				}

				source <- d
			}
		},
		func(item *devicehubpb.Device, writer mr.Writer[any], cancel func(error)) {
			var (
				udid  = item.GetUdid()
				token = item.GetToken()
			)

			l.logger.Infof("ready to release the project device, udid: %s, token: %s", udid, token)

			ctx1, cancel1 := commonutils.NewTimeoutContext(l.ctx, managercommon.ConstRPCReleaseProjectDeviceTimeout)
			defer cancel1()

			if _, err := l.svcCtx.ManagerRPC.ReleaseProjectDevice(
				ctx1, &managerpb.ReleaseProjectDeviceReq{
					ProjectId: l.taskInfo.GetProjectId(),
					Udid:      udid,
					Token:     token,
				},
			); err != nil {
				l.logger.Errorf(
					"failed to release the project device by grpc, udid: %s, token: %s, error: %+v",
					udid, token, err,
				)

				l.logger.Infof("try to release the project device by mq, udid: %s, token: %s", udid, token)
				ctx2, cancel2 := commonutils.NewTimeoutContext(l.ctx, devicehubcommon.ConstRPCReleaseDeviceTimeout)
				defer cancel2()

				if _, err := l.svcCtx.DeviceHubProducer.Send(
					ctx2, base.NewTask(
						commonconsts.MQTaskTypeDeviceHubHandleReleaseDevice,
						protobuf.MarshalJSONIgnoreError(
							&devicehubpb.ReleaseDeviceReq{
								Udid:  udid,
								Token: token,
							},
						),
					), base.QueuePriorityDefault,
				); err != nil {
					l.logger.Errorf(
						"failed to release the project device by mq, udid: %s, token: %s, error: %+v",
						udid, token, err,
					)
					return
				}
			}

			l.logger.Infof("finish to release the project device, udid: %s, token: %s", udid, token)
		},
		func(pipe <-chan any, cancel func(error)) {
		},
	); err != nil {
		l.logger.Errorf(
			"got an error while releasing the project device, devices: %s, error: %+v",
			protobuf.MarshalJSONWithMessagesToStringIgnoreError(l.devices), err,
		)
	}

	return err
}

func (l *ExecuteStabilityCaseTaskLogic) initDevice() error {
	var (
		deviceType   = l.taskInfo.GetDeviceType()
		platformType = l.taskInfo.GetPlatformType()

		err error
	)

	if len(l.devices) == 0 {
		return errors.Errorf(
			"no devices were acquired, device_type: %s, platform_type: %s",
			deviceType.String(), platformType.String(),
		)
	}

	l.initDeviceOnce.Do(
		func() {
			device_ := l.devices[0] // 只取第一个
			udid := device_.GetUdid()
			addr := device_.GetRemoteAddress()
			if !strings.HasSuffix(l.reportPath, udid) {
				l.reportPath = filepath.Join(l.reportPath, udid)
			}

			switch platformType {
			case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
				l.device, err = device.NewAndroidDevice(
					l.ctx, deviceType, udid, addr,
					device.WithAPKPath(l.appPath),
					device.WithActivities(l.taskInfo.GetActivities()...),
				)
				if err != nil {
					return
				}

				l.businessLogic = tt.NewAndroidLogic(l.ctx, l.device)
			default:
				err = errors.Errorf("unsupported platform type: %s", platformType.String())
			}
		},
	)

	return err
}

func (l *ExecuteStabilityCaseTaskLogic) installApp() (err error) {
	if l.device == nil {
		return errors.Errorf("the device is not initialized")
	}

	ctx, cancel := commonutils.NewTimeoutContext(l.ctx, common.ConstTimeoutOfInstallApp)
	defer cancel()

	resultCh := make(chan error, 1)
	threading.GoSafeCtx(
		ctx, func() {
			e := l.device.AppInstall(l.appPath)
			if e != nil {
				l.logger.Errorf("failed to install app, udid: %s, path: %s, error: %+v", l.device.UDID(), l.appPath, e)
			} else {
				l.logger.Infof("install app successfully, udid: %s, path: %s", l.device.UDID(), l.appPath)
			}
			resultCh <- e
		},
	)

	select {
	case <-ctx.Done():
		return errors.Errorf(
			"installing app timed out, udid: %s, path: %s, timeout: %s",
			l.device.UDID(), l.appPath, common.ConstTimeoutOfInstallApp.String(),
		)
	case err = <-resultCh:
		return err
	}
}

func (l *ExecuteStabilityCaseTaskLogic) removeAppLogs() error {
	return l.businessLogic.ClearLogs()
}

func (l *ExecuteStabilityCaseTaskLogic) getAppLogs() error {
	appLogsPath := filepath.Join(l.reportPath, filenameOfAppLogs)
	if err := l.businessLogic.GetLogs(appLogsPath); err != nil {
		return err
	}

	l.result.Artifacts = append(
		l.result.Artifacts, device.Artifact{
			Type:        commonpb.ArtifactType_ArtifactType_LOG,
			FileName:    filenameOfAppLogs,
			FilePath:    appLogsPath,
			Description: descriptionOfAppLogs,
		},
	)
	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) executeBusinessLogic() error {
	if len(l.accounts) == 0 {
		return errors.Errorf(
			"no accounts were acquired, pool_env_table: %s", l.taskInfo.GetAccountConfig().GetPoolEnvTable(),
		)
	}

	var (
		udid = l.device.UDID()

		account_ = l.accounts[0] // 取第一个
		username = account_.Username
		password = account_.Password
	)

	//if err := l.businessLogic.Launch(); err != nil {
	//	return err
	//}
	//l.logger.Infof("finish to launch the app, udid: %s", udid)

	if err := l.businessLogic.Login(username, password); err != nil {
		return err
	}
	l.logger.Infof("finish to login the app, udid: %s", udid)

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) runStabilityTest() error {
	var (
		platformType = l.taskInfo.GetPlatformType()
		duration     = l.taskInfo.GetDuration()

		err error
	)

	// 增加2分钟作为兜底超时时间
	ctx, cancel := commonutils.NewTimeoutContext(l.ctx, time.Duration(duration+2)*time.Minute)
	defer cancel()

	threading.GoSafeCtx(
		l.ctx, func() {
			if err := l.device.CollectPerfData(
				ctx, l.appName, collector.WithCallback(l.sendPerfDataToReporter),
			); err != nil {
				l.logger.Errorf(
					"failed to collect the perf data, serial: %s, package_name: %s, error: %+v",
					l.device.UDID(), l.appName, err,
				)
			}
		},
	)

	switch platformType {
	case commonpb.PlatformType_ANDROID, commonpb.PlatformType_HarmonyOS:
		l.result, err = l.device.MonkeyTest(ctx, l.key, l.appName, int64(duration), l.reportPath)
		if err != nil {
			return err
		}

		l.logger.Infof("finish to run stability test, result: %s", jsonx.MarshalIgnoreError(l.result))
	default:
		return errors.Errorf("unsupported platform type: %s", platformType.String())
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) sendTaskStatusToReporter() error {
	ctx, cancel := commonutils.NewTimeoutContext(l.ctx, common.ConstTimeoutOfInvokeRPC)
	defer cancel()

	var (
		taskID        = l.taskInfo.GetTaskId()
		executeID     = l.taskInfo.GetExecuteId()
		projectID     = l.taskInfo.GetProjectId()
		planExecuteID = l.taskInfo.GetPlanExecuteId()
		udid          = l.taskInfo.GetUdid()
		executedBy    = l.taskInfo.GetExecutedBy()

		req        *reporterpb.PutStabilityDeviceRecordReq
		deviceInfo string
	)

	switch l.state {
	case dispatcherpb.ComponentState_Pending, dispatcherpb.ComponentState_Init, dispatcherpb.ComponentState_Started:
		req = &reporterpb.PutStabilityDeviceRecordReq{
			TaskId:        taskID,
			ExecuteId:     executeID,
			ProjectId:     projectID,
			PlanExecuteId: planExecuteID,
			Udid:          udid,
			Status:        l.state.String(),
			ExecutedBy:    executedBy,
			StartedAt:     timestamppb.New(time.Now()),
		}
	default:
		if len(l.devices) == 0 {
			if l.state != dispatcherpb.ComponentState_Stop && l.state != dispatcherpb.ComponentState_Panic {
				l.state = dispatcherpb.ComponentState_Skip
			}
		} else {
			udid = l.devices[0].GetUdid()
			deviceInfo = protobuf.MarshalJSONToStringIgnoreError(l.devices[0])
		}

		req = &reporterpb.PutStabilityDeviceRecordReq{
			TaskId:        taskID,
			ExecuteId:     executeID,
			ProjectId:     projectID,
			PlanExecuteId: planExecuteID,
			Udid:          udid,
			Device:        deviceInfo,
			Status:        l.state.String(),
			CrashCount:    uint32(l.result.CrashCount),
			AnrCount:      uint32(l.result.ANRCount),
			Result:        ConvertToStabilityResult(l.result),
			ExecutedBy:    executedBy,
			EndedAt:       timestamppb.New(time.Now()),
		}
	}

	if _, err := l.svcCtx.ReporterRPC.ModifyStabilityDeviceRecord(ctx, req); err != nil {
		return errors.Errorf(
			"failed to modify stability device record, task_id: %s, execute_id: %s, udid: %s, error: %+v",
			taskID, executeID, udid, err,
		)
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) sendPerfDataToReporter(
	dataType collector.DataType, interval time.Duration, points []collector.PointData,
) {
	var (
		taskID     = l.taskInfo.GetTaskId()
		executeID  = l.taskInfo.GetExecuteId()
		projectID  = l.taskInfo.GetProjectId()
		executedBy = l.taskInfo.GetExecutedBy()
	)

	fn := func(point collector.PointData) {
		ctx, cancel := context.WithTimeout(l.ctx, common.ConstTimeoutOfSendTask)
		defer cancel()

		payload := protobuf.MarshalJSONIgnoreError(
			&commonpb.SaveDevicePerfDataTaskInfo{
				TaskId:     taskID,
				ExecuteId:  executeID,
				ProjectId:  projectID,
				Udid:       l.device.UDID(),
				Usage:      commonpb.DeviceUsage_STABILITY_TESTING,
				DataType:   dataType.ConvertToPerfDataType(),
				Interval:   interval.Milliseconds(),
				Series:     string(point.Series),
				Unit:       string(point.Unit),
				X:          point.X,
				Y:          point.Y,
				ExecutedBy: executedBy,
			},
		)
		if _, err := l.svcCtx.ReporterProducer.Send(
			ctx, base.NewTask(
				commonconsts.MQTaskTypeReporterSaveDevicePerfDataTask,
				payload,
				base.WithRetentionOptions(time.Minute),
			), base.QueuePriorityDefault,
		); err != nil {
			l.logger.Errorf(
				"failed to send the perf data to reporter, task_id: %s, payload: %s, error: %+v",
				taskID, payload, err,
			)
		}
	}
	for _, point := range points {
		fn(point)
	}
}

func (l *ExecuteStabilityCaseTaskLogic) setupSteps() *StageSteps {
	return &StageSteps{
		Stage: commonpb.TestStage_SETUP,
		Steps: []*Step{
			{
				Key: keyOfAcquireDevices,
				Desc: StepDesc{
					EN: descOfAcquireDevicesEN,
					ZH: descOfAcquireDevicesZH,
				},
				Func: func() error {
					if err := l.acquireDevice(); err != nil {
						return err
					}

					return l.initDevice()
				},
			},
			{
				Key: keyOfDownloadAppPackage,
				Desc: StepDesc{
					EN: descOfDownloadAppPackageEN,
					ZH: descOfDownloadAppPackageZH,
				},
				Func: func() error {
					_, err := l.svcCtx.SingleFlight.Do(
						l.ctx, fmt.Sprintf("%s:%s", keyOfDownloadAppPackage, l.key), func() (any, error) {
							if err := l.downloadAppPackage(); err != nil {
								return commonconsts.FAILURE, err
							}
							return commonconsts.SUCCESS, nil
						},
					)
					return err
				},
			},
			{
				Key: keyOfAcquireAccounts,
				Desc: StepDesc{
					EN: descOfAcquireAccountsEN,
					ZH: descOfAcquireAccountsZH,
				},
				Func: l.acquireAccount,
			},
			{
				Key: keyOfInstallApp,
				Desc: StepDesc{
					EN: descOfInstallAppEN,
					ZH: descOfInstallAppZH,
				},
				Func:               l.installApp,
				ScreenshotInterval: 2 * time.Second,
			},
			{
				Key: keyOfClearAppLogs,
				Desc: StepDesc{
					EN: descOfClearAppLogsEN,
					ZH: descOfClearAppLogsZH,
				},
				Func: l.removeAppLogs,
			},
		},
	}
}

func (l *ExecuteStabilityCaseTaskLogic) testSteps() *StageSteps {
	return &StageSteps{
		Stage: commonpb.TestStage_TEST,
		Steps: []*Step{
			{
				Key: keyOfExecuteBusinessLogic,
				Desc: StepDesc{
					EN: descOfExecuteBusinessLogicEN,
					ZH: descOfExecuteBusinessLogicZH,
				},
				Func:               l.executeBusinessLogic,
				ScreenshotInterval: time.Second,
			},
			{
				Key: keyOfRunStabilityTest,
				Desc: StepDesc{
					EN: descOfRunStabilityTestEN,
					ZH: descOfRunStabilityTestZH,
				},
				Func: l.runStabilityTest,
			},
		},
	}
}

func (l *ExecuteStabilityCaseTaskLogic) teardownSteps() *StageSteps {
	return &StageSteps{
		Stage: commonpb.TestStage_TEARDOWN,
		Steps: []*Step{
			{
				Key: keyOfSaveAppLogs,
				Desc: StepDesc{
					EN: descOfSaveAppLogsEN,
					ZH: descOfSaveAppLogsZH,
				},
				Func: l.getAppLogs,
			},
			{
				Key: keyOfReleaseAccounts,
				Desc: StepDesc{
					EN: descOfReleaseAccountsEN,
					ZH: descOfReleaseAccountsZH,
				},
				Func: l.releaseAccount,
			},
			{
				Key: keyOfReleaseDevices,
				Desc: StepDesc{
					EN: descOfReleaseDevicesEN,
					ZH: descOfReleaseDevicesZH,
				},
				Func: l.releaseDevice,
			},
		},
	}
}

func (l *ExecuteStabilityCaseTaskLogic) runSteps(stageSteps *StageSteps) (err error) {
	var (
		taskID = l.taskInfo.GetTaskId()
		udid   = l.taskInfo.GetUdid()

		stage      = stageSteps.Stage
		strOfStage = protobuf.GetEnumStringOf(stage)
	)

	defer func() {
		if r := recover(); r != nil {
			if stage == commonpb.TestStage_TEARDOWN {
				l.Errorf(
					"got a panic while running the teardown steps, task_id: %s, udid: %s, error: %+v", taskID, udid, r,
				)
			} else {
				l.state = dispatcherpb.ComponentState_Panic
				err = errors.Errorf(
					"got a panic while running the %s steps, task_id: %s, udid: %s, error: %+v",
					strOfStage, taskID, udid, r,
				)
			}
		}
	}()

	if stage == commonpb.TestStage_TS_NULL {
		return errors.Errorf("invalid stage: %v", stage)
	} else if len(stageSteps.Steps) == 0 {
		return nil
	}

	if l.device != nil {
		udid = l.device.UDID()
	}

	l.Infof("start to run the %s steps, task_id: %s, udid: %s", strOfStage, taskID, udid)
	defer func() {
		l.Infof("finish running the %s steps, task_id: %s, udid: %s", strOfStage, taskID, udid)
	}()

	for i, step := range stageSteps.Steps {
		if step == nil || step.Func == nil {
			continue
		}

		if err = l.runStep(stage, i, step); err != nil {
			l.Errorf(
				"failed to run the %s step, task_id: %s, udid: %s, step: %q, error: %+v",
				strOfStage, taskID, udid, step.Desc.EN, err,
			)
			if stage != commonpb.TestStage_TEARDOWN {
				return err
			}
		}
	}

	return nil
}

func (l *ExecuteStabilityCaseTaskLogic) runStep(stage commonpb.TestStage, index int, step *Step) error {
	if stage == commonpb.TestStage_TS_NULL {
		return &errorOfInvalidStage{stage: stage}
	} else if step == nil || step.Func == nil {
		return errorOfNullStep
	}

	var (
		taskID     = l.taskInfo.GetTaskId()
		executeID  = l.taskInfo.GetExecuteId()
		projectID  = l.taskInfo.GetProjectId()
		udid       = l.taskInfo.GetUdid()
		strOfStage = protobuf.GetEnumStringOf(stage)

		startedAt = time.Now()
		status    = dispatcherpb.ComponentState_Failure

		doneCh   = make(chan lang.PlaceholderType)
		resultCh = make(chan []*commonpb.Artifact, 1)
	)

	if step.ScreenshotInterval > 0 {
		threading.GoSafeCtx(
			l.ctx, func() {
				l.logger.Infof("start to take screenshots, stage: %q, step: %q", strOfStage, step.Desc.EN)
				result, err := l.takeScreenshotByStep(stage, step, doneCh)
				if err != nil {
					l.logger.Errorf(
						"failed to take screenshots, stage: %q, step: %q, error: %+v", strOfStage, step.Desc.EN, err,
					)
				} else {
					l.logger.Infof(
						"finish to take screenshots, stage: %q, step: %q, count: %d",
						strOfStage, step.Desc.EN, len(result),
					)
				}

				resultCh <- result
			},
		)
	} else {
		close(resultCh)
	}

	var (
		artifacts []*commonpb.Artifact
		err       error
	)
	threading.GoSafeCtx(
		l.ctx, func() {
			err = step.Func()
			if err != nil {
				l.logger.Errorf(
					"failed to run the test step, stage: %q, step: %q, error: %+v", strOfStage, step.Desc.EN, err,
				)
			} else {
				status = dispatcherpb.ComponentState_Success
				l.logger.Infof("succeed to run the test step, stage: %q, step: %q", strOfStage, step.Desc.EN)
			}
			close(doneCh)
		},
	)

	select {
	case <-l.ctx.Done(): // 任务完成
		return l.ctx.Err()
	case <-l.stopCh: // 任务终止
		return errorOfStopSignal
	case <-doneCh: // 步骤完成
		if step.ScreenshotInterval > 0 {
			select {
			case <-l.ctx.Done(): // 任务完成
				return l.ctx.Err()
			case <-l.stopCh: // 任务终止
				return errorOfStopSignal
			case artifacts = <-resultCh: // 截图完成
				break
			}
		}
		break
	}

	if l.device != nil {
		udid = l.device.UDID()
	}

	task := base.NewTask(
		commonconsts.MQTaskTypeReporterSaveDeviceStepTask,
		protobuf.MarshalJSONIgnoreError(
			&commonpb.SaveDeviceStepTaskInfo{
				TaskId:     taskID,
				ExecuteId:  executeID,
				ProjectId:  projectID,
				Udid:       udid,
				Usage:      commonpb.DeviceUsage_STABILITY_TESTING,
				Stage:      stage,
				Index:      int64(index),
				Name:       step.Desc.ZH,
				Status:     status.String(),
				Content:    l.logger.Sync(),
				Artifacts:  artifacts,
				ExecutedBy: l.taskInfo.GetExecutedBy(),
				StartedAt:  timestamppb.New(startedAt),
				EndedAt:    timestamppb.New(time.Now()),
			},
		),
		base.WithRetentionOptions(5*time.Minute),
	)
	if _, e := l.svcCtx.ReporterProducer.Send(l.ctx, task, base.QueuePriorityDefault); e != nil {
		l.Errorf(
			"failed to send the task to mq, typename: %s, payload: %s, error: %+v", task.Typename, task.Payload, e,
		)
	}

	return err
}

func (l *ExecuteStabilityCaseTaskLogic) takeScreenshotByStep(
	stage commonpb.TestStage, step *Step, quitCh <-chan lang.PlaceholderType,
) ([]*commonpb.Artifact, error) {
	if stage == commonpb.TestStage_TS_NULL {
		return nil, &errorOfInvalidStage{stage: stage}
	} else if step == nil || step.Func == nil {
		return nil, errorOfNullStep
	}

	if step.ScreenshotInterval <= 0 {
		return []*commonpb.Artifact{}, nil
	}

	ctx, cancel := context.WithCancel(l.ctx)
	threading.GoSafeCtx(
		ctx, func() {
			select {
			case <-l.ctx.Done(): // 任务完成
			case <-l.stopCh: // 任务终止
			case <-quitCh: // 步骤完成
			}
			cancel()
		},
	)

	result, err := l.device.ScreenRecording(ctx, l.key, step.Desc.ZH, step.ScreenshotInterval, l.reportPath)
	if err != nil {
		return nil, err
	}

	artifacts := make([]*commonpb.Artifact, 0, len(result))
	for _, r := range result {
		artifacts = append(
			artifacts, &commonpb.Artifact{
				Type:        r.Type,
				FileName:    r.FileName,
				FilePath:    r.FilePath,
				Description: r.Description,
			},
		)
	}

	return artifacts, nil
}
