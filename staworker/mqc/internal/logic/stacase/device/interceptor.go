package device

import (
	"bytes"
	"context"
	"io"
	"sync"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/threading"
)

// LineCallback 行数据回调函数类型
type LineCallback func(line string)

// StreamInterceptor 流拦截器，用于拦截stdout和stderr
type StreamInterceptor struct {
	logx.Logger

	file         io.Writer      // 目标文件
	lineCallback LineCallback   // 行回调函数
	lineChan     chan string    // 行数据管道
	buffer       bytes.Buffer   // stdout缓冲区
	mutex        sync.Mutex     // 保护buffer的并发访问
	wg           sync.WaitGroup // 等待goroutine完成
	closed       bool           // 是否已关闭
}

// NewStreamInterceptor 创建新的流拦截器
func NewStreamInterceptor(file io.Writer, callback LineCallback) *StreamInterceptor {
	interceptor := &StreamInterceptor{
		Logger: logx.WithContext(context.Background()),

		file:         file,
		lineCallback: callback,
		lineChan:     make(chan string, 10000), // 增大缓冲区
	}

	// 启动行处理协程
	interceptor.wg.Add(1)
	threading.GoSafe(interceptor.processLines)

	return interceptor
}

// processLines 处理行数据的协程
func (si *StreamInterceptor) processLines() {
	defer si.wg.Done()

	for line := range si.lineChan {
		if si.lineCallback != nil {
			threading.RunSafe(
				func() {
					si.lineCallback(line)
				},
			)
		}
	}
}

// Write 实现io.Writer接口，用于stdout
func (si *StreamInterceptor) Write(p []byte) (n int, err error) {
	// 先写入文件，确保数据顺序
	n, err = si.file.Write(p)
	if err != nil {
		return n, err
	}

	// 处理行数据
	var linesToSend []string

	func() {
		si.mutex.Lock()
		defer si.mutex.Unlock()

		if si.closed {
			return
		}

		// 将数据添加到缓冲区
		si.buffer.Write(p)

		// 检查是否有完整的行
		linesToSend = si.processBufferedLines()
	}()

	// 在锁外发送行数据，保持顺序
	for _, line := range linesToSend {
		si.lineChan <- line
	}

	return n, nil
}

// processBufferedLines 处理缓冲区中的完整行，只保留不完整的行数据
// 返回需要发送的行列表
func (si *StreamInterceptor) processBufferedLines() []string {
	var (
		data = si.buffer.Bytes()

		lastIncompleteStart int
		linesToSend         []string
	)

	// 查找所有完整的行
	for i := 0; i < len(data); i++ {
		if data[i] == '\n' {
			// 找到完整行，提取行内容（不包括\n）
			lineBytes := data[lastIncompleteStart:i]
			lineStr := string(lineBytes)

			// 收集要发送的行
			linesToSend = append(linesToSend, lineStr)

			// 更新下一个不完整行的起始位置
			lastIncompleteStart = i + 1
		}
	}

	// 只保留不完整的行数据
	if lastIncompleteStart < len(data) {
		// 有未完成的行数据，保留它们
		remainingData := data[lastIncompleteStart:]
		si.buffer.Reset()
		si.buffer.Write(remainingData)
	} else {
		// 所有数据都是完整行，清空缓冲区
		si.buffer.Reset()
	}

	return linesToSend
}

// WriteStderr 用于stderr的写入方法
func (si *StreamInterceptor) WriteStderr(p []byte) (n int, err error) {
	// stderr直接写入文件，不进行行处理
	return si.file.Write(p)
}

// Close 关闭拦截器
func (si *StreamInterceptor) Close() error {
	si.mutex.Lock()
	defer si.mutex.Unlock()

	if si.closed {
		return nil
	}

	si.closed = true

	// 处理缓冲区中剩余的数据
	if si.buffer.Len() > 0 {
		remainingData := si.buffer.String()
		if len(remainingData) > 0 {
			select {
			case si.lineChan <- remainingData:
			default:
				// 管道满了，但这是最后的数据，可以考虑阻塞等待
				si.Warnf("line channel is full, drop the line data: %s", remainingData)
			}
		}
	}

	// 关闭管道
	close(si.lineChan)

	// 等待处理协程完成
	si.wg.Wait()

	return nil
}

// MultiWriter 组合写入器，用于同时写入多个目标
type MultiWriter struct {
	writers []io.Writer
}

// NewMultiWriter 创建新的多重写入器
func NewMultiWriter(writers ...io.Writer) *MultiWriter {
	return &MultiWriter{
		writers: writers,
	}
}

// Write 实现io.Writer接口
func (mw *MultiWriter) Write(p []byte) (n int, err error) {
	for _, w := range mw.writers {
		n, err = w.Write(p)
		if err != nil {
			return n, err
		}
	}
	return len(p), nil
}

// StderrWriter stderr专用写入器
type StderrWriter struct {
	interceptor *StreamInterceptor
}

// NewStderrWriter 创建stderr写入器
func NewStderrWriter(interceptor *StreamInterceptor) *StderrWriter {
	return &StderrWriter{
		interceptor: interceptor,
	}
}

// Write 实现io.Writer接口
func (sw *StderrWriter) Write(p []byte) (n int, err error) {
	return sw.interceptor.WriteStderr(p)
}
