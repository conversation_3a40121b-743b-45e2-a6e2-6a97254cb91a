package stacase

import (
	"time"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

type Account struct {
	Username string `json:"username"`
	Password string `json:"password"`
	Token    string `json:"token"`
}

type StageSteps struct {
	Stage commonpb.TestStage
	Steps []*Step
}

type Step struct {
	Key                string        // 步骤唯一标识
	Desc               StepDesc      // 步骤描述
	Func               func() error  // 执行函数
	ScreenshotInterval time.Duration // 截图间隔（大于0表示需要截图）
}

type StepDesc struct {
	EN desc // 英文描述
	ZH desc // 中文描述
}
