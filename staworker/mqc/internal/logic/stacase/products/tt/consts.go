package tt

const (
	packageName = "com.yiyou.ga"

	activityOfComposeLogin = "com.tt.auth.biz.login.composelogin.ComposeLoginActivity"
	activityOfLogin        = "com.tt.auth.biz.login.LoginActivity"
	activityOfHome         = ".client.home.HomeActivity"

	textOfAllow         = "允许"
	textOfSkip          = "跳过"
	textOfAgree         = "同意"
	textOfUserAgreement = "用户协议更新"
	textOfJuvenileMode  = "未成年人模式"
	textOfIKnow         = "我知道了"

	resourceIDOfImgClose = "com.yiyou.ga:id/img_close" // 弹窗关闭按钮

	resourceIDOfHasAccount    = "com.yiyou.ga:id/tv_has_account"        // 已有账号？
	resourceIDOfLoginPhone    = "com.yiyou.ga:id/login_phone"           // 请输入手机号/TT语音号
	resourceIDOfLoginPassword = "com.yiyou.ga:id/login_password"        //nolint: gosec
	resourceIDOfLoginProxy    = "com.yiyou.ga:id/iv_login_proxy_submit" // 我已阅读并同意
	resourceIDOfLoginButton   = "com.yiyou.ga:id/user_login"            // 登录按钮

	resourceIDOfLoginLotteryCloseButton = "com.yiyou.ga:id/loginLotteryClose" // 签到领好礼关闭按钮
	resourceIDOfHomeChannel             = "com.yiyou.ga:id/home_channel"      // 首页

	pathOf
)
