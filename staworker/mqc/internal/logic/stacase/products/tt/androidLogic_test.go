package tt

import (
	"context"
	"testing"

	"github.com/electricbubble/gadb"

	gu2 "gitlab.ttyuyin.com/TestDevelopment/go-uiautomator2"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/stacase/device"
)

func TestGetElementInfo(t *testing.T) {
	client, err := gadb.NewClient()
	if err != nil {
		t.Fatalf("failed to new client: %v", err)
	}

	devices, err := client.ListDevices()
	if err != nil {
		t.Fatalf("failed to list devices: %v", err)
	} else if len(devices) == 0 {
		t.Fatal("no device found")
	}

	d := devices[0]
	driver, err := gu2.NewDriver(d)
	if err != nil {
		t.Fatalf("failed to new driver: %v", err)
	}

	info, err := driver.FindElementBySelectorOptions(selectorOfLoginProxy).Info()
	if err != nil {
		t.Fatalf("failed to get element info: %v", err)
	}

	t.Logf("selected: %t", info.Selected)

	err = driver.FindElementBySelectorOptions(selectorOfLoginPhone).SetText("***********")
	if err != nil {
		t.Fatalf("failed to set text: %v", err)
	}
}

func TestAppLaunchAndLogin(t *testing.T) {
	var (
		ctx      = context.Background()
		account  = "*********"
		password = "ttDevOps@2021"
	)

	d, err := device.NewAndroidDevice(ctx, commonpb.DeviceType_REAL_PHONE, "", "")
	if err != nil {
		t.Fatalf("failed to new android device: %v", err)
	}

	l := NewAndroidLogic(ctx, d)
	if err = l.Launch(); err != nil {
		t.Fatalf("failed to launch: %v", err)
	} else {
		t.Logf("succeed to launch")
	}

	if err = l.Login(account, password); err != nil {
		t.Fatalf("failed to login: %v", err)
	} else {
		t.Logf("succeed to login")
	}
}
