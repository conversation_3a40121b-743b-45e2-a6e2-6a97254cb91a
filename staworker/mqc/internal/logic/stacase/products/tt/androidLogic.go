package tt

import (
	"archive/zip"
	"context"
	"io"
	"os"
	"path/filepath"
	"slices"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	gu2 "gitlab.ttyuyin.com/TestDevelopment/go-uiautomator2"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/stacase/device"
)

type AndroidLogic struct {
	logx.Logger
	ctx context.Context

	device *device.AndroidDevice
}

func NewAndroidLogic(ctx context.Context, device *device.AndroidDevice) *AndroidLogic {
	return &AndroidLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,

		device: device,
	}
}

func (l *AndroidLogic) Launch() error {
	var (
		adb  = l.device.ADB()
		udid = l.device.UDID()
	)

	driver, err := gu2.NewDriver(adb)
	if err != nil {
		return errors.Wrapf(err, "failed to new uiautomator2 driver, udid: %s", udid)
	}
	defer func(driver *gu2.Driver) {
		if driver != nil {
			_ = driver.Close()
		}
	}(driver)

	if err = l.device.AppLaunch(packageName); err != nil {
		return err
	}

	timer := timewheel.NewTimer(10 * time.Second)
	defer timer.Stop()

	var (
		closeE = driver.FindElementBySelectorOptions(selectorOfImgClose) // 弹窗关闭按钮（如果存在则点击关闭弹窗）
		allowE = driver.FindElementBySelectorOptions(selectorOfAllow)    // 允许xxx向你发送通知的「允许」按钮
		skipE  = driver.FindElementBySelectorOptions(selectorOfSkip)     // 广告的「跳过」按钮
		agreeE = driver.FindElementBySelectorOptions(selectorOfAgree)    // 「温馨提示」的「同意」按钮
	)

	for {
		select {
		case <-l.ctx.Done():
			return errors.Errorf(
				"got a done signal while launching the app, udid: %s, error: %+v",
				l.device.UDID(), l.ctx.Err(),
			)
		case <-timer.C:
			return nil
		default:
			if activity, err := l.device.TopActivity(); err == nil && activity != nil {
				if slices.Contains(launchedActivities, activity.ActivityName) {
					return nil
				}
			}

			for _, element := range []*gu2.Element{
				closeE,
				allowE,
				skipE,
				agreeE,
			} {
				if ok, _ := element.Exist(); ok {
					_ = element.Click()
				}
			}

			time.Sleep(time.Second)
		}
	}
}

func (l *AndroidLogic) Login(account, password string) error {
	var (
		adb  = l.device.ADB()
		udid = l.device.UDID()
	)

	driver, err := gu2.NewDriver(adb)
	if err != nil {
		return errors.Wrapf(err, "failed to new uiautomator2 driver, udid: %s", udid)
	}
	defer func(driver *gu2.Driver) {
		if driver != nil {
			_ = driver.Close()
		}
	}(driver)

	// 当前窗口不是目标应用，则启动目标应用
	time.Sleep(2 * time.Second)
	activity, err := l.device.TopActivity()
	if err != nil || activity.PackageName != packageName {
		_ = l.Launch()
		time.Sleep(2 * time.Second)
	}

	// 当前窗口是主页，则直接返回
	if activity, err = l.device.TopActivity(); err == nil && activity != nil {
		if slices.Contains(loginActivities, activity.ActivityName) {
			return nil
		}
	}

	// 「已有账号？」
	_ = driver.FindElementBySelectorOptions(selectorOfHasAccount).Click(gu2.WithTimeout(time.Second))

	// 页面切换到「欢迎登录TT语音」
	time.Sleep(2 * time.Second)

	// 「请输入手机号/TT语音号」
	_ = driver.FindElementBySelectorOptions(selectorOfLoginPhone).SetText(account, gu2.WithTimeout(5*time.Second))

	// 「请输入密码」
	passwordE := driver.FindElementBySelectorOptions(selectorOfLoginPassword)
	_ = passwordE.ClearText()
	_ = passwordE.SetText(password, gu2.WithTimeout(5*time.Second))

	// 「我已阅读并同意」
	loginProxyE := driver.FindElementBySelectorOptions(selectorOfLoginProxy)
	info, err := loginProxyE.Info()
	if err != nil || !info.Selected {
		_ = loginProxyE.Click()
	}

	// 「登录」按钮
	_ = driver.FindElementBySelectorOptions(selectorOfLoginButton).Click(gu2.WithTimeout(5 * time.Second))

	timeout := time.Minute
	timer := timewheel.NewTimer(timeout)
	defer timer.Stop()

	var (
		homeE          = driver.FindElementBySelectorOptions(selectorOfHomeChannel)             // 首页
		closeE         = driver.FindElementBySelectorOptions(selectorOfImgClose)                // 弹窗关闭按钮（如果存在则点击关闭弹窗）
		userAgreementE = driver.FindElementBySelectorOptions(selectorOfUserAgreement)           // 「用户协议更新」
		agreeE         = driver.FindElementBySelectorOptions(selectorOfAgree)                   // 「同意」按钮
		loginLotteryE  = driver.FindElementBySelectorOptions(selectorOfLoginLotteryCloseButton) // 「签到领好礼」
		juvenileModeE  = driver.FindElementBySelectorOptions(selectorOfJuvenileMode)            // 「未成年人模式」
		iKnowE         = driver.FindElementBySelectorOptions(selectorOfIKnow)                   // 「我知道了」按钮
	)

	for {
		select {
		case <-l.ctx.Done():
			return errors.Errorf(
				"got a done signal while processing the login logic, udid: %s, error: %+v", udid, l.ctx.Err(),
			)
		case <-timer.C:
			return errors.Errorf(
				"timeout while processing the login logic, udid: %s, timeout: %s", udid, timeout.String(),
			)
		default:
			if exist, err := homeE.Exist(); err == nil && exist {
				return nil
			}

			for _, item := range []struct {
				locator  *gu2.Element
				operator *gu2.Element
			}{
				{locator: closeE, operator: closeE},               // 弹窗关闭按钮
				{locator: userAgreementE, operator: agreeE},       // 「用户协议更新」
				{locator: loginLotteryE, operator: loginLotteryE}, // 「签到领好礼」
				{locator: juvenileModeE, operator: iKnowE},        // 「未成年人模式」
			} {
				if ok, _ := item.locator.Exist(); ok {
					_ = item.operator.Click()
				}
			}

			time.Sleep(time.Second)
		}
	}
}

func (l *AndroidLogic) ClearLogs() error {
	udid := l.device.UDID()

	output, err := l.device.RunShellCommand("rm", "-rf", pathOfCacheLogs)
	if err != nil {
		return errors.Wrapf(err, "failed to clear the app logs, udid: %s, path: %s", udid, pathOfCacheLogs)
	}

	l.Infof("finish to clear the app logs, udid: %s, path: %s, output: %s", udid, pathOfCacheLogs, output)
	return nil
}

func (l *AndroidLogic) GetLogs(localPath string) error {
	udid := l.device.UDID()

	// Ensure the local directory exists
	localDir := filepath.Dir(localPath)
	if err := os.MkdirAll(localDir, 0o755); err != nil {
		return errors.Wrapf(err, "failed to create local directory, udid: %s, path: %s", udid, localDir)
	}

	// Check if the logs directory exists on device
	if !l.device.ADB().Exists(pathOfCacheLogs) {
		return errors.Errorf("logs directory does not exist on device, udid: %s, path: %s", udid, pathOfCacheLogs)
	}

	// Create zip file directly from device files using List and ReadFile
	if err := l.createZipFromDeviceFiles(localPath); err != nil {
		return errors.Wrapf(err, "failed to create zip from device, udid: %s, path: %s", udid, localPath)
	}

	l.Infof("successfully created zip file from device logs, udid: %s, path: %s", udid, localPath)
	return nil
}

// createZipFromDeviceFiles creates a zip file by directly reading files from device using List and ReadFile
func (l *AndroidLogic) createZipFromDeviceFiles(localPath string) error {
	udid := l.device.UDID()

	// Create the zip file
	zipFile, err := os.Create(localPath)
	if err != nil {
		return errors.Wrapf(err, "failed to create zip file, udid: %s, path: %s", udid, localPath)
	}
	defer func(zipFile *os.File) {
		if zipFile != nil {
			_ = zipFile.Close()
		}
	}(zipFile)

	// Create a zip writer
	zipWriter := zip.NewWriter(zipFile)
	defer func(zipWriter *zip.Writer) {
		if zipWriter != nil {
			_ = zipWriter.Close()
		}
	}(zipWriter)

	// Recursively traverse and add files to zip
	if err := l.traverseAndAddToZip(zipWriter, pathOfCacheLogs, ""); err != nil {
		return errors.Wrapf(err, "failed to traverse and add files to zip, udid: %s, path: %s", udid, pathOfCacheLogs)
	}

	l.Infof("successfully created zip from device files, udid: %s", udid)
	return nil
}

// traverseAndAddToZip recursively traverses a remote directory and adds files to zip
func (l *AndroidLogic) traverseAndAddToZip(zipWriter *zip.Writer, remotePath, zipBasePath string) error {
	var (
		udid = l.device.UDID()
		adb  = l.device.ADB()
	)

	// List files and directories in the remote path
	fileInfos, err := adb.List(remotePath)
	if err != nil {
		return errors.Wrapf(err, "failed to list directory, udid: %s, path: %s", udid, remotePath)
	}

	for _, fileInfo := range fileInfos {
		// Skip current and parent directory entries
		if fileInfo.Name() == "." || fileInfo.Name() == ".." {
			continue
		}

		// Construct full remote path
		fullRemotePath := filepath.Join(remotePath, fileInfo.Name())
		// Construct zip entry path (use forward slashes for zip)
		zipEntryPath := zipBasePath
		if zipBasePath != "" {
			zipEntryPath += "/"
		}
		zipEntryPath += fileInfo.Name()

		if fileInfo.IsDir() {
			// Create directory entry in zip
			dirHeader := &zip.FileHeader{
				Name:   zipEntryPath + "/",
				Method: zip.Store,
			}
			dirHeader.Modified = fileInfo.ModTime()

			if _, err := zipWriter.CreateHeader(dirHeader); err != nil {
				return errors.Wrapf(
					err, "failed to create directory entry in zip, udid: %s, path: %s", udid, zipEntryPath,
				)
			}

			// Recursively process subdirectory
			if err := l.traverseAndAddToZip(zipWriter, fullRemotePath, zipEntryPath); err != nil {
				return err
			}
		} else if fileInfo.IsFile() {
			// Create file entry in zip
			fileHeader := &zip.FileHeader{
				Name:   zipEntryPath,
				Method: zip.Deflate,
			}
			fileHeader.SetModTime(fileInfo.ModTime())

			writer, err := zipWriter.CreateHeader(fileHeader)
			if err != nil {
				return errors.Wrapf(err, "failed to create file entry in zip, udid: %s, path: %s", udid, zipEntryPath)
			}

			// Read file content from device and write to zip
			if err := l.device.ADB().ReadFile(fullRemotePath, writer); err != nil {
				return errors.Wrapf(err, "failed to read file from device, udid: %s, path: %s", udid, fullRemotePath)
			}

			l.Infof(
				"added file to zip, udid: %s, remote: %s, zip: %s, size: %d", udid, fullRemotePath, zipEntryPath,
				fileInfo.Size(),
			)
		}
	}

	return nil
}

// createZipFromDirectory creates a zip file from a directory
func (l *AndroidLogic) createZipFromDirectory(sourceDir, zipPath string) error {
	// Create the zip file
	zipFile, err := os.Create(zipPath)
	if err != nil {
		return errors.Wrapf(err, "failed to create zip file, path: %s", zipPath)
	}
	defer func(zipFile *os.File) {
		if zipFile != nil {
			_ = zipFile.Close()
		}
	}(zipFile)

	// Create a zip writer
	zipWriter := zip.NewWriter(zipFile)
	defer func(zipWriter *zip.Writer) {
		if zipWriter != nil {
			_ = zipWriter.Close()
		}
	}(zipWriter)

	// Walk through the source directory and add files to zip
	return filepath.Walk(
		sourceDir, func(filePath string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			// Get relative path for zip entry
			relPath, err := filepath.Rel(sourceDir, filePath)
			if err != nil {
				return err
			}

			// Skip the root directory itself
			if relPath == "." {
				return nil
			}

			// Create zip entry header
			header, err := zip.FileInfoHeader(info)
			if err != nil {
				return err
			}

			// Set the name to the relative path
			header.Name = strings.ReplaceAll(relPath, string(os.PathSeparator), "/")

			// Handle directories
			if info.IsDir() {
				header.Name += "/"
				_, err := zipWriter.CreateHeader(header)
				return err
			}

			// Handle files
			writer, err := zipWriter.CreateHeader(header)
			if err != nil {
				return err
			}

			// Open and copy file content
			file, err := os.Open(filePath)
			if err != nil {
				return err
			}
			defer func(file *os.File) {
				if file != nil {
					_ = file.Close()
				}
			}(file)

			_, err = io.Copy(writer, file)
			return err
		},
	)
}
