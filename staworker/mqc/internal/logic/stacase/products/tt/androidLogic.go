package tt

import (
	"archive/zip"
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"slices"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	gu2 "gitlab.ttyuyin.com/TestDevelopment/go-uiautomator2"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/logic/stacase/device"
)

type AndroidLogic struct {
	logx.Logger
	ctx context.Context

	device *device.AndroidDevice
}

func NewAndroidLogic(ctx context.Context, device *device.AndroidDevice) *AndroidLogic {
	return &AndroidLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,

		device: device,
	}
}

func (l *AndroidLogic) Launch() error {
	var (
		adb  = l.device.ADB()
		udid = l.device.UDID()
	)

	driver, err := gu2.NewDriver(adb)
	if err != nil {
		return errors.Wrapf(err, "failed to new uiautomator2 driver, udid: %s", udid)
	}
	defer func(driver *gu2.Driver) {
		if driver != nil {
			_ = driver.Close()
		}
	}(driver)

	if err = l.device.AppLaunch(packageName); err != nil {
		return err
	}

	timer := timewheel.NewTimer(10 * time.Second)
	defer timer.Stop()

	var (
		closeE = driver.FindElementBySelectorOptions(selectorOfImgClose) // 弹窗关闭按钮（如果存在则点击关闭弹窗）
		allowE = driver.FindElementBySelectorOptions(selectorOfAllow)    // 允许xxx向你发送通知的「允许」按钮
		skipE  = driver.FindElementBySelectorOptions(selectorOfSkip)     // 广告的「跳过」按钮
		agreeE = driver.FindElementBySelectorOptions(selectorOfAgree)    // 「温馨提示」的「同意」按钮
	)

	for {
		select {
		case <-l.ctx.Done():
			return errors.Errorf(
				"got a done signal while launching the app, udid: %s, error: %+v",
				l.device.UDID(), l.ctx.Err(),
			)
		case <-timer.C:
			return nil
		default:
			if activity, err := l.device.TopActivity(); err == nil && activity != nil {
				if slices.Contains(launchedActivities, activity.ActivityName) {
					return nil
				}
			}

			for _, element := range []*gu2.Element{
				closeE,
				allowE,
				skipE,
				agreeE,
			} {
				if ok, _ := element.Exist(); ok {
					_ = element.Click()
				}
			}

			time.Sleep(time.Second)
		}
	}
}

func (l *AndroidLogic) Login(account, password string) error {
	var (
		adb  = l.device.ADB()
		udid = l.device.UDID()
	)

	driver, err := gu2.NewDriver(adb)
	if err != nil {
		return errors.Wrapf(err, "failed to new uiautomator2 driver, udid: %s", udid)
	}
	defer func(driver *gu2.Driver) {
		if driver != nil {
			_ = driver.Close()
		}
	}(driver)

	// 当前窗口不是目标应用，则启动目标应用
	time.Sleep(2 * time.Second)
	activity, err := l.device.TopActivity()
	if err != nil || activity.PackageName != packageName {
		_ = l.Launch()
		time.Sleep(2 * time.Second)
	}

	// 当前窗口是主页，则直接返回
	if activity, err = l.device.TopActivity(); err == nil && activity != nil {
		if slices.Contains(loginActivities, activity.ActivityName) {
			return nil
		}
	}

	// 「已有账号？」
	_ = driver.FindElementBySelectorOptions(selectorOfHasAccount).Click(gu2.WithTimeout(time.Second))

	// 页面切换到「欢迎登录TT语音」
	time.Sleep(2 * time.Second)

	// 「请输入手机号/TT语音号」
	_ = driver.FindElementBySelectorOptions(selectorOfLoginPhone).SetText(account, gu2.WithTimeout(5*time.Second))

	// 「请输入密码」
	passwordE := driver.FindElementBySelectorOptions(selectorOfLoginPassword)
	_ = passwordE.ClearText()
	_ = passwordE.SetText(password, gu2.WithTimeout(5*time.Second))

	// 「我已阅读并同意」
	loginProxyE := driver.FindElementBySelectorOptions(selectorOfLoginProxy)
	info, err := loginProxyE.Info()
	if err != nil || !info.Selected {
		_ = loginProxyE.Click()
	}

	// 「登录」按钮
	_ = driver.FindElementBySelectorOptions(selectorOfLoginButton).Click(gu2.WithTimeout(5 * time.Second))

	timeout := time.Minute
	timer := timewheel.NewTimer(timeout)
	defer timer.Stop()

	var (
		homeE          = driver.FindElementBySelectorOptions(selectorOfHomeChannel)             // 首页
		closeE         = driver.FindElementBySelectorOptions(selectorOfImgClose)                // 弹窗关闭按钮（如果存在则点击关闭弹窗）
		userAgreementE = driver.FindElementBySelectorOptions(selectorOfUserAgreement)           // 「用户协议更新」
		agreeE         = driver.FindElementBySelectorOptions(selectorOfAgree)                   // 「同意」按钮
		loginLotteryE  = driver.FindElementBySelectorOptions(selectorOfLoginLotteryCloseButton) // 「签到领好礼」
		juvenileModeE  = driver.FindElementBySelectorOptions(selectorOfJuvenileMode)            // 「未成年人模式」
		iKnowE         = driver.FindElementBySelectorOptions(selectorOfIKnow)                   // 「我知道了」按钮
	)

	for {
		select {
		case <-l.ctx.Done():
			return errors.Errorf(
				"got a done signal while processing the login logic, udid: %s, error: %+v", udid, l.ctx.Err(),
			)
		case <-timer.C:
			return errors.Errorf(
				"timeout while processing the login logic, udid: %s, timeout: %s", udid, timeout.String(),
			)
		default:
			if exist, err := homeE.Exist(); err == nil && exist {
				return nil
			}

			for _, item := range []struct {
				locator  *gu2.Element
				operator *gu2.Element
			}{
				{locator: closeE, operator: closeE},               // 弹窗关闭按钮
				{locator: userAgreementE, operator: agreeE},       // 「用户协议更新」
				{locator: loginLotteryE, operator: loginLotteryE}, // 「签到领好礼」
				{locator: juvenileModeE, operator: iKnowE},        // 「未成年人模式」
			} {
				if ok, _ := item.locator.Exist(); ok {
					_ = item.operator.Click()
				}
			}

			time.Sleep(time.Second)
		}
	}
}

func (l *AndroidLogic) ClearLogs() error {
	udid := l.device.UDID()

	output, err := l.device.RunShellCommand("rm", "-rf", pathOfCacheLogs)
	if err != nil {
		return errors.Wrapf(err, "failed to clear the app logs, udid: %s, path: %s", udid, pathOfCacheLogs)
	}

	l.Infof("finish to clear the app logs, udid: %s, path: %s, output: %s", udid, pathOfCacheLogs, output)
	return nil
}

func (l *AndroidLogic) GetLogs(localPath string) error {
	udid := l.device.UDID()

	l
	// Ensure the local directory exists
	localDir := filepath.Dir(localPath)
	if err := os.MkdirAll(localDir, 0o755); err != nil {
		return errors.Wrapf(err, "failed to create local directory, udid: %s, path: %s", udid, localDir)
	}

	// Try to create zip directly on device first (preferred approach)
	if err := l.createZipOnDevice(localPath); err != nil {
		l.Infof("failed to create zip on device, falling back to local compression, udid: %s, error: %+v", udid, err)

		// Fallback: pull directory locally then compress
		return l.pullAndCompressLocally(localPath)
	}

	l.Infof("successfully created zip file from device logs, udid: %s, path: %s", udid, localPath)
	return nil
}

// createZipOnDevice creates a zip file on the Android device and pulls it to local path
func (l *AndroidLogic) createZipOnDevice(localPath string) error {
	udid := l.device.UDID()

	// Check if the logs directory exists on device
	if !l.device.ADB().Exists(pathOfCacheLogs) {
		return errors.Errorf("logs directory does not exist on device, udid: %s, path: %s", udid, pathOfCacheLogs)
	}

	// Create a temporary zip file path on device
	remoteZipPath := "/data/local/tmp/cache_logs.zip"

	// Remove any existing zip file
	if _, err := l.device.RunShellCommand("rm", "-f", remoteZipPath); err != nil {
		l.Errorf("failed to remove existing zip file, udid: %s, path: %s, error: %+v", udid, remoteZipPath, err)
	}

	// Create zip file on device using shell command
	// Use cd to change to parent directory and zip the logs directory
	parentDir := filepath.Dir(pathOfCacheLogs)
	logsDir := filepath.Base(pathOfCacheLogs)
	zipCommand := fmt.Sprintf("cd %s && zip -r %s %s", parentDir, remoteZipPath, logsDir)

	output, err := l.device.RunShellCommand("sh", "-c", zipCommand)
	if err != nil {
		return errors.Wrapf(
			err, "failed to create zip file on device, udid: %s, command: %s, output: %s", udid, zipCommand, output,
		)
	}

	// Check if zip file was created successfully
	if !l.device.ADB().Exists(remoteZipPath) {
		return errors.Errorf("zip file was not created on device, udid: %s, path: %s", udid, remoteZipPath)
	}
	defer func() {
		// Clean up remote zip file
		if _, err = l.device.RunShellCommand("rm", "-f", remoteZipPath); err != nil {
			l.Errorf("failed to clean up remote zip file, udid: %s, path: %s, error: %+v", udid, remoteZipPath, err)
		}
	}()

	// Pull the zip file to local path
	if err = l.device.ADB().Pull(remoteZipPath, localPath); err != nil {
		return errors.Wrapf(
			err, "failed to pull zip file from device, udid: %s, remote: %s, local: %s", udid, remoteZipPath, localPath,
		)
	}

	return nil
}

// pullAndCompressLocally pulls the logs directory to local temp and compresses it
func (l *AndroidLogic) pullAndCompressLocally(localPath string) error {
	udid := l.device.UDID()

	// Check if the logs directory exists on device
	if !l.device.ADB().Exists(pathOfCacheLogs) {
		return errors.Errorf("logs directory does not exist on device, udid: %s, path: %s", udid, pathOfCacheLogs)
	}

	// Create a temporary directory for pulling logs
	tempDir, err := os.MkdirTemp("", "app_logs_*")
	if err != nil {
		return errors.Wrapf(err, "failed to create temp directory, udid: %s", udid)
	}
	defer func() {
		if err := os.RemoveAll(tempDir); err != nil {
			l.Errorf("failed to clean up temp directory, udid: %s, path: %s, error: %+v", udid, tempDir, err)
		}
	}()

	// Pull the logs directory to temp location
	tempLogsPath := filepath.Join(tempDir, "logs")
	if err = l.device.ADB().Pull(pathOfCacheLogs, tempLogsPath); err != nil {
		return errors.Wrapf(
			err, "failed to pull logs directory, udid: %s, remote: %s, local: %s", udid, pathOfCacheLogs, tempLogsPath,
		)
	}

	// Create zip file from the pulled directory
	if err = l.createZipFromDirectory(tempLogsPath, localPath); err != nil {
		return errors.Wrapf(
			err, "failed to create zip file on local, udid: %s, source: %s, target: %s", udid, tempLogsPath, localPath,
		)
	}

	l.Infof("successfully pulled and compressed logs directory, udid: %s, path: %s", udid, localPath)
	return nil
}

// createZipFromDirectory creates a zip file from a directory
func (l *AndroidLogic) createZipFromDirectory(sourceDir, zipPath string) error {
	// Create the zip file
	zipFile, err := os.Create(zipPath)
	if err != nil {
		return errors.Wrapf(err, "failed to create zip file, path: %s", zipPath)
	}
	defer func(zipFile *os.File) {
		if zipFile != nil {
			_ = zipFile.Close()
		}
	}(zipFile)

	// Create a zip writer
	zipWriter := zip.NewWriter(zipFile)
	defer func(zipWriter *zip.Writer) {
		if zipWriter != nil {
			_ = zipWriter.Close()
		}
	}(zipWriter)

	// Walk through the source directory and add files to zip
	return filepath.Walk(
		sourceDir, func(filePath string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			// Get relative path for zip entry
			relPath, err := filepath.Rel(sourceDir, filePath)
			if err != nil {
				return err
			}

			// Skip the root directory itself
			if relPath == "." {
				return nil
			}

			// Create zip entry header
			header, err := zip.FileInfoHeader(info)
			if err != nil {
				return err
			}

			// Set the name to the relative path
			header.Name = strings.ReplaceAll(relPath, string(os.PathSeparator), "/")

			// Handle directories
			if info.IsDir() {
				header.Name += "/"
				_, err := zipWriter.CreateHeader(header)
				return err
			}

			// Handle files
			writer, err := zipWriter.CreateHeader(header)
			if err != nil {
				return err
			}

			// Open and copy file content
			file, err := os.Open(filePath)
			if err != nil {
				return err
			}
			defer func(file *os.File) {
				if file != nil {
					_ = file.Close()
				}
			}(file)

			_, err = io.Copy(writer, file)
			return err
		},
	)
}
