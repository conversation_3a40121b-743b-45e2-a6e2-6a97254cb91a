package tt

import (
	"archive/zip"
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/zeromicro/go-zero/core/logx"
)

// MockDeviceNew simulates Android device behavior for testing the new implementation
type MockDeviceNew struct {
	serial           string
	existsFunc       func(path string) bool
	listFunc         func(remotePath string) ([]*MockFileInfoNew, error)
	readFileFunc     func(remoteFile string, writer io.Writer) error
	shouldFailOnList bool
	shouldFailOnRead bool
}

// MockFileInfoNew simulates gadb.FileInfo for testing
type MockFileInfoNew struct {
	name    string
	size    int64
	mode    os.FileMode
	modTime time.Time
	isDir   bool
}

func (m *MockFileInfoNew) Name() string       { return m.name }
func (m *MockFileInfoNew) Size() int64        { return m.size }
func (m *MockFileInfoNew) Mode() os.FileMode  { return m.mode }
func (m *MockFileInfoNew) ModTime() time.Time { return m.modTime }
func (m *MockFileInfoNew) IsDir() bool        { return m.isDir }
func (m *MockFileInfoNew) IsFile() bool       { return !m.isDir }
func (m *MockFileInfoNew) Path() string       { return m.name }

func (m *MockDeviceNew) UDID() string {
	return m.serial
}

func (m *MockDeviceNew) Exists(path string) bool {
	if m.existsFunc != nil {
		return m.existsFunc(path)
	}
	return true // Default: path exists
}

func (m *MockDeviceNew) List(remotePath string) ([]*MockFileInfoNew, error) {
	if m.listFunc != nil {
		return m.listFunc(remotePath)
	}
	if m.shouldFailOnList {
		return nil, fmt.Errorf("mock list error")
	}
	// Default: return empty list
	return []*MockFileInfoNew{}, nil
}

func (m *MockDeviceNew) ReadFile(remoteFile string, writer io.Writer) error {
	if m.readFileFunc != nil {
		return m.readFileFunc(remoteFile, writer)
	}
	if m.shouldFailOnRead {
		return fmt.Errorf("mock read file error")
	}
	// Default: write some test content
	_, err := writer.Write([]byte("mock file content"))
	return err
}

// TestAndroidLogicNew is a test version that uses the new List/ReadFile approach
type TestAndroidLogicNew struct {
	*AndroidLogic
	mockDevice *MockDeviceNew
}

// createZipFromDeviceFiles creates a zip file by directly reading files from device using List and ReadFile
func (t *TestAndroidLogicNew) createZipFromDeviceFiles(localPath string) error {
	udid := t.mockDevice.UDID()

	// Create the zip file
	zipFile, err := os.Create(localPath)
	if err != nil {
		return fmt.Errorf("failed to create zip file, udid: %s, path: %s, error: %v", udid, localPath, err)
	}
	defer zipFile.Close()

	// Create a zip writer
	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	// Recursively traverse and add files to zip
	if err := t.traverseAndAddToZip(zipWriter, pathOfCacheLogs, ""); err != nil {
		return fmt.Errorf("failed to traverse and add files to zip, udid: %s, path: %s, error: %v", udid, pathOfCacheLogs, err)
	}

	t.Infof("successfully created zip from device files, udid: %s", udid)
	return nil
}

// traverseAndAddToZip recursively traverses a remote directory and adds files to zip
func (t *TestAndroidLogicNew) traverseAndAddToZip(zipWriter *zip.Writer, remotePath, zipBasePath string) error {
	udid := t.mockDevice.UDID()

	// List files and directories in the remote path
	fileInfos, err := t.mockDevice.List(remotePath)
	if err != nil {
		return fmt.Errorf("failed to list directory, udid: %s, path: %s, error: %v", udid, remotePath, err)
	}

	for _, fileInfo := range fileInfos {
		// Skip current and parent directory entries
		if fileInfo.Name() == "." || fileInfo.Name() == ".." {
			continue
		}

		// Construct full remote path
		fullRemotePath := filepath.Join(remotePath, fileInfo.Name())
		// Construct zip entry path (use forward slashes for zip)
		zipEntryPath := zipBasePath
		if zipBasePath != "" {
			zipEntryPath += "/"
		}
		zipEntryPath += fileInfo.Name()

		if fileInfo.IsDir() {
			// Create directory entry in zip
			dirHeader := &zip.FileHeader{
				Name:   zipEntryPath + "/",
				Method: zip.Store,
			}
			dirHeader.SetModTime(fileInfo.ModTime())
			
			if _, err := zipWriter.CreateHeader(dirHeader); err != nil {
				return fmt.Errorf("failed to create directory entry in zip, udid: %s, path: %s, error: %v", udid, zipEntryPath, err)
			}

			// Recursively process subdirectory
			if err := t.traverseAndAddToZip(zipWriter, fullRemotePath, zipEntryPath); err != nil {
				return err
			}
		} else if fileInfo.IsFile() {
			// Create file entry in zip
			fileHeader := &zip.FileHeader{
				Name:   zipEntryPath,
				Method: zip.Deflate,
			}
			fileHeader.SetModTime(fileInfo.ModTime())

			writer, err := zipWriter.CreateHeader(fileHeader)
			if err != nil {
				return fmt.Errorf("failed to create file entry in zip, udid: %s, path: %s, error: %v", udid, zipEntryPath, err)
			}

			// Read file content from device and write to zip
			if err := t.mockDevice.ReadFile(fullRemotePath, writer); err != nil {
				return fmt.Errorf("failed to read file from device, udid: %s, path: %s, error: %v", udid, fullRemotePath, err)
			}

			t.Infof("added file to zip, udid: %s, remote: %s, zip: %s, size: %d", udid, fullRemotePath, zipEntryPath, fileInfo.Size())
		}
	}

	return nil
}

func (t *TestAndroidLogicNew) GetLogs(localPath string) error {
	udid := t.mockDevice.UDID()
	
	// Ensure the local directory exists
	localDir := filepath.Dir(localPath)
	if err := os.MkdirAll(localDir, 0755); err != nil {
		return fmt.Errorf("failed to create local directory, udid: %s, path: %s, error: %v", udid, localDir, err)
	}

	// Check if the logs directory exists on device
	if !t.mockDevice.Exists(pathOfCacheLogs) {
		return fmt.Errorf("logs directory does not exist on device, udid: %s, path: %s", udid, pathOfCacheLogs)
	}

	// Create zip file directly from device files using List and ReadFile
	if err := t.createZipFromDeviceFiles(localPath); err != nil {
		return fmt.Errorf("failed to create zip from device, udid: %s, path: %s, error: %v", udid, localPath, err)
	}

	t.Infof("successfully created zip file from device logs, udid: %s, path: %s", udid, localPath)
	return nil
}

// Helper function to create a test AndroidLogic with mock device
func createTestAndroidLogicNew(mockDevice *MockDeviceNew) *TestAndroidLogicNew {
	return &TestAndroidLogicNew{
		AndroidLogic: &AndroidLogic{
			Logger: logx.WithContext(context.Background()),
			ctx:    context.Background(),
		},
		mockDevice: mockDevice,
	}
}

func TestAndroidLogic_GetLogs_NewImplementation(t *testing.T) {
	tests := []struct {
		name            string
		setupMock       func() *MockDeviceNew
		localPath       string
		expectedError   string
		shouldCreateZip bool
		verifyZip       bool
	}{
		{
			name: "Success - Simple files",
			setupMock: func() *MockDeviceNew {
				return &MockDeviceNew{
					serial: "test-device-001",
					existsFunc: func(path string) bool {
						return path == pathOfCacheLogs
					},
					listFunc: func(remotePath string) ([]*MockFileInfoNew, error) {
						if remotePath == pathOfCacheLogs {
							return []*MockFileInfoNew{
								{name: "app.log", size: 100, isDir: false, modTime: time.Now()},
								{name: "error.log", size: 50, isDir: false, modTime: time.Now()},
							}, nil
						}
						return []*MockFileInfoNew{}, nil
					},
					readFileFunc: func(remoteFile string, writer io.Writer) error {
						content := "Log content for " + filepath.Base(remoteFile)
						_, err := writer.Write([]byte(content))
						return err
					},
				}
			},
			localPath:       "/tmp/test_logs_simple_new.zip",
			shouldCreateZip: true,
			verifyZip:       true,
		},
		{
			name: "Success - Nested directories",
			setupMock: func() *MockDeviceNew {
				return &MockDeviceNew{
					serial: "test-device-002",
					existsFunc: func(path string) bool {
						return path == pathOfCacheLogs
					},
					listFunc: func(remotePath string) ([]*MockFileInfoNew, error) {
						if remotePath == pathOfCacheLogs {
							return []*MockFileInfoNew{
								{name: "app.log", size: 100, isDir: false, modTime: time.Now()},
								{name: "debug", size: 0, isDir: true, modTime: time.Now()},
							}, nil
						} else if strings.HasSuffix(remotePath, "debug") {
							return []*MockFileInfoNew{
								{name: "verbose.log", size: 200, isDir: false, modTime: time.Now()},
								{name: "network.log", size: 150, isDir: false, modTime: time.Now()},
							}, nil
						}
						return []*MockFileInfoNew{}, nil
					},
					readFileFunc: func(remoteFile string, writer io.Writer) error {
						content := "Log content for " + filepath.Base(remoteFile)
						_, err := writer.Write([]byte(content))
						return err
					},
				}
			},
			localPath:       "/tmp/test_logs_nested_new.zip",
			shouldCreateZip: true,
			verifyZip:       true,
		},
		{
			name: "Error - Logs directory does not exist",
			setupMock: func() *MockDeviceNew {
				return &MockDeviceNew{
					serial: "test-device-003",
					existsFunc: func(path string) bool {
						return false // No paths exist
					},
				}
			},
			localPath:     "/tmp/test_logs_no_dir_new.zip",
			expectedError: "logs directory does not exist on device",
		},
		{
			name: "Error - List operation fails",
			setupMock: func() *MockDeviceNew {
				return &MockDeviceNew{
					serial:           "test-device-004",
					shouldFailOnList: true,
					existsFunc: func(path string) bool {
						return path == pathOfCacheLogs
					},
				}
			},
			localPath:     "/tmp/test_logs_list_fail_new.zip",
			expectedError: "mock list error",
		},
		{
			name: "Error - ReadFile operation fails",
			setupMock: func() *MockDeviceNew {
				return &MockDeviceNew{
					serial:           "test-device-005",
					shouldFailOnRead: true,
					existsFunc: func(path string) bool {
						return path == pathOfCacheLogs
					},
					listFunc: func(remotePath string) ([]*MockFileInfoNew, error) {
						if remotePath == pathOfCacheLogs {
							return []*MockFileInfoNew{
								{name: "app.log", size: 100, isDir: false, modTime: time.Now()},
							}, nil
						}
						return []*MockFileInfoNew{}, nil
					},
				}
			},
			localPath:     "/tmp/test_logs_read_fail_new.zip",
			expectedError: "mock read file error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockDevice := tt.setupMock()
			logic := createTestAndroidLogicNew(mockDevice)

			// Ensure test directory exists
			testDir := filepath.Dir(tt.localPath)
			require.NoError(t, os.MkdirAll(testDir, 0755))

			// Cleanup
			defer func() {
				os.Remove(tt.localPath)
			}()

			// Execute
			err := logic.GetLogs(tt.localPath)

			// Verify
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
				
				if tt.shouldCreateZip {
					assert.FileExists(t, tt.localPath)
					
					if tt.verifyZip {
						// Verify it's a valid zip file
						_, err := zip.OpenReader(tt.localPath)
						assert.NoError(t, err, "Created file should be a valid zip")
					}
				}
			}
		})
	}
}
