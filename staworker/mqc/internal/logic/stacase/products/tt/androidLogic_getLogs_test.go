package tt

import (
	"archive/zip"
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/zeromicro/go-zero/core/logx"
)

// TestAndroidLogic is a test version of AndroidLogic that allows dependency injection
type TestAndroidLogic struct {
	*AndroidLogic
	mockDevice *MockDevice
}

// MockDevice simulates Android device behavior for testing
type MockDevice struct {
	serial           string
	existsFunc       func(path string) bool
	pullFunc         func(remotePath, localPath string) error
	runShellCmdFunc  func(args ...string) (string, error)
	shouldFailOnZip  bool
	shouldFailOnPull bool
}

func (m *MockDevice) UDID() string {
	return m.serial
}

func (m *MockDevice) RunShellCommand(args ...string) (string, error) {
	if m.runShellCmdFunc != nil {
		return m.runShellCmdFunc(args...)
	}

	// Handle zip command
	if len(args) >= 3 && args[0] == "sh" && args[1] == "-c" {
		if strings.Contains(args[2], "zip") && m.shouldFailOnZip {
			return "", fmt.Errorf("mock zip command failed")
		}
	}

	return "mock command output", nil
}

func (m *MockDevice) Exists(path string) bool {
	if m.existsFunc != nil {
		return m.existsFunc(path)
	}
	return true // Default: path exists
}

func (m *MockDevice) Pull(remotePath, localPath string) error {
	if m.pullFunc != nil {
		return m.pullFunc(remotePath, localPath)
	}
	if m.shouldFailOnPull {
		return fmt.Errorf("mock pull error")
	}
	return nil // Default: success
}

// Override methods to use mock device
func (t *TestAndroidLogic) createZipOnDevice(localPath string) error {
	udid := t.mockDevice.UDID()

	// Check if the logs directory exists on device
	if !t.mockDevice.Exists(pathOfCacheLogs) {
		return fmt.Errorf("logs directory does not exist on device, udid: %s, path: %s", udid, pathOfCacheLogs)
	}

	// Create a temporary zip file path on device
	remoteZipPath := "/data/local/tmp/cache_logs.zip"

	// Remove any existing zip file
	if _, err := t.mockDevice.RunShellCommand("rm", "-f", remoteZipPath); err != nil {
		t.Errorf("failed to remove existing zip file, udid: %s, path: %s, error: %+v", udid, remoteZipPath, err)
	}

	// Create zip file on device using shell command
	parentDir := filepath.Dir(pathOfCacheLogs)
	logsDir := filepath.Base(pathOfCacheLogs)
	zipCommand := fmt.Sprintf("cd %s && zip -r %s %s", parentDir, remoteZipPath, logsDir)

	output, err := t.mockDevice.RunShellCommand("sh", "-c", zipCommand)
	if err != nil {
		return fmt.Errorf("failed to create zip on device, udid: %s, command: %s, output: %s, error: %v", udid, zipCommand, output, err)
	}

	// Check if zip file was created successfully
	if !t.mockDevice.Exists(remoteZipPath) {
		return fmt.Errorf("zip file was not created on device, udid: %s, path: %s", udid, remoteZipPath)
	}

	// Pull the zip file to local path
	if err := t.mockDevice.Pull(remoteZipPath, localPath); err != nil {
		// Clean up remote zip file
		t.mockDevice.RunShellCommand("rm", "-f", remoteZipPath)
		return fmt.Errorf("failed to pull zip file from device, udid: %s, remote: %s, local: %s, error: %v", udid, remoteZipPath, localPath, err)
	}

	// Clean up remote zip file
	if _, err := t.mockDevice.RunShellCommand("rm", "-f", remoteZipPath); err != nil {
		t.Errorf("failed to clean up remote zip file, udid: %s, path: %s, error: %+v", udid, remoteZipPath, err)
	}

	return nil
}

func (t *TestAndroidLogic) pullAndCompressLocally(localPath string) error {
	udid := t.mockDevice.UDID()

	// Check if the logs directory exists on device
	if !t.mockDevice.Exists(pathOfCacheLogs) {
		return fmt.Errorf("logs directory does not exist on device, udid: %s, path: %s", udid, pathOfCacheLogs)
	}

	// Create a temporary directory for pulling logs
	tempDir, err := os.MkdirTemp("", "android_logs_*")
	if err != nil {
		return fmt.Errorf("failed to create temp directory, udid: %s, error: %v", udid, err)
	}
	defer func() {
		if err := os.RemoveAll(tempDir); err != nil {
			t.Errorf("failed to clean up temp directory, udid: %s, path: %s, error: %+v", udid, tempDir, err)
		}
	}()

	// Pull the logs directory to temp location
	tempLogsPath := filepath.Join(tempDir, "logs")
	if err := t.mockDevice.Pull(pathOfCacheLogs, tempLogsPath); err != nil {
		return fmt.Errorf("failed to pull logs directory, udid: %s, remote: %s, local: %s, error: %v", udid, pathOfCacheLogs, tempLogsPath, err)
	}

	// Create zip file from the pulled directory
	if err := t.createZipFromDirectory(tempLogsPath, localPath); err != nil {
		return fmt.Errorf("failed to create zip from directory, udid: %s, source: %s, target: %s, error: %v", udid, tempLogsPath, localPath, err)
	}

	t.Infof("successfully pulled and compressed logs directory, udid: %s, path: %s", udid, localPath)
	return nil
}

func (t *TestAndroidLogic) GetLogs(localPath string) error {
	udid := t.mockDevice.UDID()

	// Ensure the local directory exists
	localDir := filepath.Dir(localPath)
	if err := os.MkdirAll(localDir, 0o755); err != nil {
		return fmt.Errorf("failed to create local directory, udid: %s, path: %s, error: %v", udid, localDir, err)
	}

	// Try to create zip directly on device first (preferred approach)
	if err := t.createZipOnDevice(localPath); err != nil {
		t.Infof("failed to create zip on device, falling back to local compression, udid: %s, error: %+v", udid, err)

		// Fallback: pull directory locally then compress
		return t.pullAndCompressLocally(localPath)
	}

	t.Infof("successfully created zip file from device logs, udid: %s, path: %s", udid, localPath)
	return nil
}

// Helper function to create a test AndroidLogic with mock device
func createTestAndroidLogic(mockDevice *MockDevice) *TestAndroidLogic {
	return &TestAndroidLogic{
		AndroidLogic: &AndroidLogic{
			Logger: logx.WithContext(context.Background()),
			ctx:    context.Background(),
		},
		mockDevice: mockDevice,
	}
}

// Helper function to create test files in a directory
func createTestFiles(dir string, files map[string]string) error {
	for filePath, content := range files {
		fullPath := filepath.Join(dir, filePath)
		if err := os.MkdirAll(filepath.Dir(fullPath), 0o755); err != nil {
			return err
		}
		if err := os.WriteFile(fullPath, []byte(content), 0o644); err != nil {
			return err
		}
	}
	return nil
}

// Helper function to verify zip file contents
func verifyZipContents(zipPath string, expectedFiles map[string]string) error {
	reader, err := zip.OpenReader(zipPath)
	if err != nil {
		return err
	}
	defer reader.Close()

	foundFiles := make(map[string]bool)
	for _, file := range reader.File {
		foundFiles[file.Name] = true

		if expectedContent, exists := expectedFiles[file.Name]; exists {
			rc, err := file.Open()
			if err != nil {
				return err
			}

			content, err := io.ReadAll(rc)
			rc.Close()
			if err != nil {
				return err
			}

			if string(content) != expectedContent {
				return fmt.Errorf("file %s content mismatch: expected %q, got %q",
					file.Name, expectedContent, string(content))
			}
		}
	}

	// Check if all expected files are present
	for expectedFile := range expectedFiles {
		if !foundFiles[expectedFile] {
			return fmt.Errorf("expected file %s not found in zip", expectedFile)
		}
	}

	return nil
}

func TestAndroidLogic_GetLogs(t *testing.T) {
	tests := []struct {
		name            string
		setupMock       func() *MockDevice
		localPath       string
		expectedError   string
		shouldCreateZip bool
		verifyZip       bool
	}{
		{
			name: "Success - Create zip on device",
			setupMock: func() *MockDevice {
				return &MockDevice{
					existsFunc: func(path string) bool {
						return path == pathOfCacheLogs || path == "/data/local/tmp/cache_logs.zip"
					},
					pullFunc: func(remotePath, localPath string) error {
						if remotePath == "/data/local/tmp/cache_logs.zip" {
							// Create a dummy zip file for testing
							return createDummyZipFile(localPath)
						}
						return nil
					},
				}
			},
			localPath:       "/tmp/test_logs_success.zip",
			shouldCreateZip: true,
			verifyZip:       true,
		},
		{
			name: "Success - Fallback to local compression",
			setupMock: func() *MockDevice {
				return &MockDevice{
					shouldFailOnZip: true, // Force fallback
					existsFunc: func(path string) bool {
						return path == pathOfCacheLogs
					},
					pullFunc: func(remotePath, localPath string) error {
						if remotePath == pathOfCacheLogs {
							// Create test files in the local path
							testFiles := map[string]string{
								"app.log":        "Application log content",
								"error.log":      "Error log content",
								"debug/test.log": "Debug log content",
							}
							return createTestFiles(localPath, testFiles)
						}
						return nil
					},
				}
			},
			localPath:       "/tmp/test_logs_fallback.zip",
			shouldCreateZip: true,
			verifyZip:       true,
		},
		{
			name: "Error - Logs directory does not exist",
			setupMock: func() *MockDevice {
				return &MockDevice{
					existsFunc: func(path string) bool {
						return false // No paths exist
					},
				}
			},
			localPath:     "/tmp/test_logs_no_dir.zip",
			expectedError: "logs directory does not exist on device",
		},
		{
			name: "Error - Zip creation fails and pull fails",
			setupMock: func() *MockDevice {
				return &MockDevice{
					shouldFailOnZip:  true,
					shouldFailOnPull: true,
					existsFunc: func(path string) bool {
						return path == pathOfCacheLogs
					},
				}
			},
			localPath:     "/tmp/test_logs_both_fail.zip",
			expectedError: "mock pull error",
		},

		{
			name: "Success - Empty logs directory",
			setupMock: func() *MockDevice {
				return &MockDevice{
					existsFunc: func(path string) bool {
						return path == pathOfCacheLogs || path == "/data/local/tmp/cache_logs.zip"
					},
					pullFunc: func(remotePath, localPath string) error {
						if remotePath == "/data/local/tmp/cache_logs.zip" {
							// Create an empty zip file
							return createEmptyZipFile(localPath)
						} else if remotePath == pathOfCacheLogs {
							// Create empty directory
							return os.MkdirAll(localPath, 0o755)
						}
						return nil
					},
				}
			},
			localPath:       "/tmp/test_logs_empty.zip",
			shouldCreateZip: true,
			verifyZip:       true,
		},
		{
			name: "Success - Large directory structure",
			setupMock: func() *MockDevice {
				return &MockDevice{
					shouldFailOnZip: true, // Force fallback to test local compression
					existsFunc: func(path string) bool {
						return path == pathOfCacheLogs
					},
					pullFunc: func(remotePath, localPath string) error {
						if remotePath == pathOfCacheLogs {
							// Create a complex directory structure
							testFiles := map[string]string{
								"app.log":                   "Main application log",
								"error.log":                 "Error messages",
								"debug/verbose.log":         "Verbose debug info",
								"debug/network.log":         "Network debug info",
								"crash/crash_2023.log":      "Crash report 2023",
								"crash/crash_2024.log":      "Crash report 2024",
								"performance/cpu.log":       "CPU performance data",
								"performance/memory.log":    "Memory usage data",
								"nested/deep/very/deep.log": "Deeply nested log file",
							}
							return createTestFiles(localPath, testFiles)
						}
						return nil
					},
				}
			},
			localPath:       "/tmp/test_logs_large.zip",
			shouldCreateZip: true,
			verifyZip:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			mockDevice := tt.setupMock()
			mockDevice.serial = "test-device-" + tt.name
			logic := createTestAndroidLogic(mockDevice)

			// Ensure test directory exists
			testDir := filepath.Dir(tt.localPath)
			require.NoError(t, os.MkdirAll(testDir, 0o755))

			// Cleanup
			defer func() {
				os.Remove(tt.localPath)
			}()

			// Execute
			err := logic.GetLogs(tt.localPath)

			// Verify
			if tt.expectedError != "" {
				assert.Error(t, err)
				if !assert.Contains(t, err.Error(), tt.expectedError) {
					t.Logf("Expected error to contain: %s, but got: %s", tt.expectedError, err.Error())
				}
			} else {
				assert.NoError(t, err)

				if tt.shouldCreateZip {
					assert.FileExists(t, tt.localPath)

					if tt.verifyZip {
						// Verify it's a valid zip file
						_, err := zip.OpenReader(tt.localPath)
						assert.NoError(t, err, "Created file should be a valid zip")
					}
				}
			}
		})
	}
}

// Helper function to create a dummy zip file for testing
func createDummyZipFile(path string) error {
	file, err := os.Create(path)
	if err != nil {
		return err
	}
	defer file.Close()

	zipWriter := zip.NewWriter(file)
	defer zipWriter.Close()

	// Add a test file to the zip
	writer, err := zipWriter.Create("test.log")
	if err != nil {
		return err
	}

	_, err = writer.Write([]byte("test log content"))
	return err
}

// Helper function to create an empty zip file
func createEmptyZipFile(path string) error {
	file, err := os.Create(path)
	if err != nil {
		return err
	}
	defer file.Close()

	zipWriter := zip.NewWriter(file)
	return zipWriter.Close()
}

// Test the createZipFromDirectory method which doesn't depend on device mocking

func TestAndroidLogic_createZipFromDirectory(t *testing.T) {
	tests := []struct {
		name          string
		setupFiles    map[string]string
		expectedError string
		verifyContent bool
	}{
		{
			name: "Success - Simple files",
			setupFiles: map[string]string{
				"file1.txt": "content1",
				"file2.txt": "content2",
			},
			verifyContent: true,
		},
		{
			name: "Success - Nested directories",
			setupFiles: map[string]string{
				"root.txt":            "root content",
				"dir1/file1.txt":      "dir1 content",
				"dir1/dir2/file2.txt": "nested content",
			},
			verifyContent: true,
		},
		{
			name:          "Success - Empty directory",
			setupFiles:    map[string]string{},
			verifyContent: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create temporary source directory
			sourceDir, err := os.MkdirTemp("", "test_source_*")
			require.NoError(t, err)
			defer os.RemoveAll(sourceDir)

			// Create test files
			require.NoError(t, createTestFiles(sourceDir, tt.setupFiles))

			// Create temporary zip file
			zipPath := filepath.Join(os.TempDir(), "test_create_zip_from_dir.zip")
			defer os.Remove(zipPath)

			logic := &AndroidLogic{
				Logger: logx.WithContext(context.Background()),
				ctx:    context.Background(),
			}

			err = logic.createZipFromDirectory(sourceDir, zipPath)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
				assert.FileExists(t, zipPath)

				if tt.verifyContent {
					assert.NoError(t, verifyZipContents(zipPath, tt.setupFiles))
				}
			}
		})
	}
}

// Benchmark tests for performance evaluation
func BenchmarkAndroidLogic_createZipFromDirectory_SmallFiles(b *testing.B) {
	// Create test files
	testFiles := map[string]string{
		"app.log":   strings.Repeat("log line\n", 100),
		"error.log": strings.Repeat("error line\n", 50),
	}

	logic := &AndroidLogic{
		Logger: logx.WithContext(context.Background()),
		ctx:    context.Background(),
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		b.StopTimer()
		// Setup
		sourceDir, _ := os.MkdirTemp("", "bench_source_*")
		createTestFiles(sourceDir, testFiles)
		zipPath := fmt.Sprintf("/tmp/bench_small_%d.zip", i)

		b.StartTimer()
		logic.createZipFromDirectory(sourceDir, zipPath)
		b.StopTimer()

		// Cleanup
		os.RemoveAll(sourceDir)
		os.Remove(zipPath)
	}
}

func BenchmarkAndroidLogic_createZipFromDirectory_LargeFiles(b *testing.B) {
	// Create larger test files
	testFiles := map[string]string{
		"app.log":     strings.Repeat("application log line with more content\n", 10000),
		"debug.log":   strings.Repeat("debug information line\n", 5000),
		"network.log": strings.Repeat("network request/response data\n", 3000),
	}

	logic := &AndroidLogic{
		Logger: logx.WithContext(context.Background()),
		ctx:    context.Background(),
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		b.StopTimer()
		// Setup
		sourceDir, _ := os.MkdirTemp("", "bench_source_*")
		createTestFiles(sourceDir, testFiles)
		zipPath := fmt.Sprintf("/tmp/bench_large_%d.zip", i)

		b.StartTimer()
		logic.createZipFromDirectory(sourceDir, zipPath)
		b.StopTimer()

		// Cleanup
		os.RemoveAll(sourceDir)
		os.Remove(zipPath)
	}
}
