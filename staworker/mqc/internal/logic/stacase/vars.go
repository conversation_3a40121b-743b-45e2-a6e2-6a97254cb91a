package stacase

import (
	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

var (
	errorOfStopSignal = errors.New("got a stop signal")
	errorOfNullStep   = errors.New("the test step is null")
)

var _ error = (*errorOfInvalidStage)(nil)

type errorOfInvalidStage struct {
	stage commonpb.TestStage
}

func (e *errorOfInvalidStage) Error() string {
	return "invalid stage: " + protobuf.GetEnumStringOf(e.stage)
}
