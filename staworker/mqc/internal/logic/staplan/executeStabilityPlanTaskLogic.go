package staplan

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/timewheel"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/utils"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/staworker/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/uiworker/common"
)

type ExecuteStabilityPlanTaskLogic struct {
	*BaseLogic
}

func NewExecuteStabilityPlanTaskLogic(
	ctx context.Context, svcCtx *svc.ServiceContext, req *dispatcherpb.WorkerReq,
) *ExecuteStabilityPlanTaskLogic {
	return &ExecuteStabilityPlanTaskLogic{
		BaseLogic: NewBaseLogic(ctx, svcCtx, req),
	}
}

func (l *ExecuteStabilityPlanTaskLogic) Execute() (err error) {
	l.ctx, l.cancel = context.WithCancel(l.ctx)
	defer l.cancel()

	threading.GoSafeCtx(l.ctx, l.watchStopSignal)
	defer func() {
		_ = l.teardown()
	}()
	_ = l.execute()
	return nil
}

func (l *ExecuteStabilityPlanTaskLogic) watchStopSignal() {
	ticker := timewheel.NewTicker(common.DefaultPeriodOfWatchStopSignal)
	defer ticker.Stop()

	for {
		select {
		case <-l.ctx.Done():
			return
		case <-ticker.C:
			if err := l.isStopped(); err != nil {
				close(l.stopCh)
				l.cancel()
				l.Errorf("broadcast the stop signal and cancel context, error: %+v", err)
				return
			}
		}
	}
}

func (l *ExecuteStabilityPlanTaskLogic) isStopped() (err error) {
	taskID := l.taskReq.GetTaskId()

	defer func() {
		if r := recover(); r != nil {
			l.state.CompareAndSwap(l.state.String(), dispatcherpb.ComponentState_Panic.String())
			err = errors.Errorf("got a panic while checking the stop status, task_id: %s, error: %+v", taskID, r)
		}
	}()

	stop, err := utils.GetStopStatus(l.ctx, l.svcCtx.DispatcherRedis, taskID)
	if err != nil {
		l.state.CompareAndSwap(l.state.String(), dispatcherpb.ComponentState_Panic.String())
		return errors.Wrapf(err, "failed to get the stop status of task, task_id: %s", taskID)
	} else if stop {
		l.state.CompareAndSwap(l.state.String(), dispatcherpb.ComponentState_Stop.String())
		return errors.Errorf("got a stop signal of task, task_id: %s", taskID)
	}

	return nil
}
