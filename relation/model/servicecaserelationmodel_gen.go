// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.7.4

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	serviceCaseRelationTableName           = "`service_case_relation`"
	serviceCaseRelationFieldNames          = builder.RawFieldNames(&ServiceCaseRelation{})
	serviceCaseRelationRows                = strings.Join(serviceCaseRelationFieldNames, ",")
	serviceCaseRelationRowsExpectAutoSet   = strings.Join(stringx.Remove(serviceCaseRelationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	serviceCaseRelationRowsWithPlaceHolder = strings.Join(stringx.Remove(serviceCaseRelationFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheRelationServiceCaseRelationIdPrefix = "cache:relation:serviceCaseRelation:id:"
)

type (
	serviceCaseRelationModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ServiceCaseRelation) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ServiceCaseRelation, error)
		Update(ctx context.Context, session sqlx.Session, data *ServiceCaseRelation) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultServiceCaseRelationModel struct {
		sqlc.CachedConn
		table string
	}

	ServiceCaseRelation struct {
		Id              int64          `db:"id"`                // 自增id
		ProjectId       string         `db:"project_id"`        // 项目ID
		GeneralConfigId string         `db:"general_config_id"` // 入口通配环境id
		Service         string         `db:"service"`           // 入口服务名称
		InterfacePath   string         `db:"interface_path"`    // 入口服务接口路径
		Cmd             sql.NullString `db:"cmd"`               // 入口md命令号
		RelationService string         `db:"relation_service"`  // 关联的服务名称（包含入口服务）
		CaseId          string         `db:"case_id"`           // 测试用例id
		CreatedAt       time.Time      `db:"created_at"`        // 创建时间
		UpdatedAt       time.Time      `db:"updated_at"`        // 更新时间
	}
)

func newServiceCaseRelationModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultServiceCaseRelationModel {
	return &defaultServiceCaseRelationModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`service_case_relation`",
	}
}

func (m *defaultServiceCaseRelationModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	relationServiceCaseRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceCaseRelationIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, relationServiceCaseRelationIdKey)
	return err
}

func (m *defaultServiceCaseRelationModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	relationServiceCaseRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceCaseRelationIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, relationServiceCaseRelationIdKey)
	return err
}

func (m *defaultServiceCaseRelationModel) FindOne(ctx context.Context, id int64) (*ServiceCaseRelation, error) {
	relationServiceCaseRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceCaseRelationIdPrefix, id)
	var resp ServiceCaseRelation
	err := m.QueryRowCtx(ctx, &resp, relationServiceCaseRelationIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", serviceCaseRelationRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultServiceCaseRelationModel) Insert(ctx context.Context, session sqlx.Session, data *ServiceCaseRelation) (sql.Result, error) {
	relationServiceCaseRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceCaseRelationIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?)", m.table, serviceCaseRelationRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.GeneralConfigId, data.Service, data.InterfacePath, data.Cmd, data.RelationService, data.CaseId)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.GeneralConfigId, data.Service, data.InterfacePath, data.Cmd, data.RelationService, data.CaseId)
	}, relationServiceCaseRelationIdKey)
}

func (m *defaultServiceCaseRelationModel) Update(ctx context.Context, session sqlx.Session, data *ServiceCaseRelation) (sql.Result, error) {

	relationServiceCaseRelationIdKey := fmt.Sprintf("%s%v", cacheRelationServiceCaseRelationIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, serviceCaseRelationRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.GeneralConfigId, data.Service, data.InterfacePath, data.Cmd, data.RelationService, data.CaseId, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.GeneralConfigId, data.Service, data.InterfacePath, data.Cmd, data.RelationService, data.CaseId, data.Id)
	}, relationServiceCaseRelationIdKey)
}

func (m *defaultServiceCaseRelationModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheRelationServiceCaseRelationIdPrefix, primary)
}

func (m *defaultServiceCaseRelationModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", serviceCaseRelationRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultServiceCaseRelationModel) tableName() string {
	return m.table
}
