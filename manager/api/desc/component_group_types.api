syntax = "v1"

import "types.api"

type Import {
    Name        string             `json:"name"`
    Source      int64              `json:"source"`
    Manual      *VariableValue     `json:"manual"`
    Export      *VariableNodeValue `json:"export"`
    Environment *VariableValue     `json:"environment"`
    Function    *VariableFuncValue `json:"function,omitempty,optional"`
    Description string             `json:"description,omitempty,optional"`
}

type Export {
    Name        string             `json:"name"`
    Export      *VariableNodeValue `json:"export"`
    Description string             `json:"description,omitempty,optional"`
}

type ComponentGroup {
    ProjectId          string         `json:"project_id"`
    CategoryId         string         `json:"category_id"`
    ComponentGroupId   string         `json:"component_group_id"`
    ComponentGroupType string         `json:"component_group_type"`
    Name               string         `json:"name"`
    Description        string         `json:"description"`
    Priority           int64          `json:"priority"`
    Tags               []string       `json:"tags"`
    Imports            []*Import      `json:"imports"`
    Exports            []*Export      `json:"exports"`
    AccountConfig      *AccountConfig `json:"account_config"`
    Version            string         `json:"version"`
    MaintainedBy       *FullUserInfo  `json:"maintained_by"`
    CreatedBy          *FullUserInfo  `json:"created_by"`
    UpdatedBy          *FullUserInfo  `json:"updated_by"`
    CreatedAt          int64          `json:"created_at"`
    UpdatedAt          int64          `json:"updated_at"`
    Nodes              []*Node        `json:"nodes"`
    Edges              []*Edge        `json:"edges"`
    Combos             []*Combo       `json:"combos"`
}

type Referenced {
    ProjectId        string `json:"project_id"`
    ReferenceId      string `json:"reference_id"`
    ReferenceType    string `json:"reference_type"`
    ReferenceVersion string `json:"reference_version"`
}

type SearchComponentGroupReferenceItem {
    ProjectId                   string        `json:"project_id"`                     // 项目ID
    ComponentGroupId            string        `json:"component_group_id"`             // 组件组ID
    ReferenceType               string        `json:"reference_type"`                 // 引用对象类型
    ReferenceId                 string        `json:"reference_id"`                   // 引用对象ID
    ReferenceVersion            string        `json:"reference_version"`              // 引用对象版本
    ReferenceParentId           string        `json:"reference_parent_id"`            // 引用对象的父ID
    ReferenceComponentGroupType string        `json:"reference_component_group_type"` // 引用对象的组件组类型
    Name                        string        `json:"name"`                           // 引用对象名称
    Description                 string        `json:"description"`                    // 引用对象描述
    Priority                    int64         `json:"priority"`                       // 优先级
    Tags                        []string      `json:"tags"`                           // 标签
    State                       int8          `json:"state"`                          // 状态
    MaintainedBy                *FullUserInfo `json:"maintained_by"`                  // 维护者
    CreatedBy                   *FullUserInfo `json:"created_by"`                     // 创建者
    UpdatedBy                   *FullUserInfo `json:"updated_by"`                     // 更新者
    CreatedAt                   int64         `json:"created_at"`                     // 创建时间
    UpdatedAt                   int64         `json:"updated_at"`                     // 更新时间
}

// 创建组件组
type (
    CreateComponentGroupReq {
        ProjectId          string         `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId         string         `json:"category_id" validate:"required" zh:"分类ID"`
        ComponentGroupType string         `json:"component_group_type" validate:"required,oneof=SINGLE GROUP SETUP TEARDOWN" zh:"组件组类型"`
        Name               string         `json:"name" validate:"gte=1,lte=64" zh:"组件组名称"`
        Description        string         `json:"description" validate:"lte=255" zh:"组件组描述"`
        Priority           int64          `json:"priority" validate:"gte=0" zh:"优先级"`
        Tags               []string       `json:"tags,optional" validate:"omitempty,gte=0" zh:"组件组标签"`
        Imports            []*Import      `json:"imports" validate:"gte=0" zh:"组件组的入参列表"`
        Exports            []*Export      `json:"exports" validate:"gte=0" zh:"组件组的出参列表"`
        AccountConfig      *AccountConfig `json:"account_config"`
        Nodes              []*Node        `json:"nodes" validate:"gt=2" zh:"组件组画布中的节点列表"`
        Edges              []*Edge        `json:"edges" validate:"gt=1" zh:"组件组画布中的线段列表"`
        Combos             []*Combo       `json:"combos" validate:"gte=0" zh:"组件组画布中的组合列表"`
        Relations          []*Relation    `json:"relations" validate:"gte=2" zh:"组件组画布中的节点与组合的关系列表"`
        ReferenceRelations *Relation      `json:"reference_relations" validate:"required" zh:"组件组画布中引用时涉及的节点与组合的关系"`
        MaintainedBy       *FullUserInfo  `json:"maintained_by" validate:"lte=64" zh:"API用例维护者"`
    }
    CreateComponentGroupResp {
        ComponentGroupId string `json:"component_group_id"`
    }
)

// 删除组件组
type (
    RemoveComponentGroupReq {
        ProjectId         string   `json:"project_id" validate:"required" zh:"项目ID"`
        ComponentGroupIds []string `json:"component_group_ids" validate:"gt=0" zh:"组件组ID列表"`
    }
    RemoveComponentGroupResp {}
)

// 编辑组件组
type (
    ModifyComponentGroupReq {
        ProjectId          string         `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId         string         `json:"category_id" validate:"required" zh:"分类ID"`
        ComponentGroupId   string         `json:"component_group_id" validate:"required" zh:"组件组ID"`
        ComponentGroupType string         `json:"component_group_type" validate:"required,oneof=SINGLE GROUP SETUP TEARDOWN" zh:"组件组类型"`
        Name               string         `json:"name" validate:"gte=1,lte=64" zh:"组件组名称"`
        Description        string         `json:"description" validate:"lte=255" zh:"组件组描述"`
        Priority           int64          `json:"priority" validate:"gte=0" zh:"优先级"`
        Tags               []string       `json:"tags,optional" validate:"omitempty,gte=0" zh:"组件组标签"`
        Imports            []*Import      `json:"imports" validate:"gte=0" zh:"组件组的入参列表"`
        Exports            []*Export      `json:"exports" validate:"gte=0" zh:"组件组的出参列表"`
        AccountConfig      *AccountConfig `json:"account_config"`
        Nodes              []*Node        `json:"nodes" validate:"gt=2" zh:"组件组画布中的节点列表"`
        Edges              []*Edge        `json:"edges" validate:"gt=1" zh:"组件组画布中的线段列表"`
        Combos             []*Combo       `json:"combos" validate:"gte=0" zh:"组件组画布中的组合列表"`
        Relations          []*Relation    `json:"relations" validate:"gte=2" zh:"组件组画布中的节点与组合的关系列表"`
        ReferenceRelations *Relation      `json:"reference_relations" validate:"required" zh:"组件组画布中引用时涉及的节点与组合的关系列表"`
        MaintainedBy       string         `json:"maintained_by" validate:"lte=64" zh:"组件组维护者"`
    }
    ModifyComponentGroupResp {}
)

// 查询组件组
type (
    SearchComponentGroupReq {
        ProjectId  string       `json:"project_id" validate:"required" zh:"项目ID"`
        CategoryId string       `json:"category_id" validate:"required" zh:"分类ID"`
        Condition  *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort       []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchComponentGroupResp {
        CurrentPage uint64            `json:"current_page"`
        PageSize    uint64            `json:"page_size"`
        TotalCount  uint64            `json:"total_count"`
        TotalPage   uint64            `json:"total_page"`
        Items       []*ComponentGroup `json:"items"`
    }
)

// 查看组件组
type (
    ViewComponentGroupReq {
        ProjectId        string `form:"project_id" validate:"required" zh:"项目ID"`
        ComponentGroupId string `form:"component_group_id" validate:"required" zh:"组件组ID"`
        Version          string `form:"version,omitempty,optional"`
        OnlyInfo         bool   `form:"only_info"`
    }
    ViewComponentGroupResp {
        *ComponentGroup

        ReferencedRelations []*Referenced `json:"referenced_relations"`
    }
)

// 搜索组件组引用详情
type (
    SearchComponentGroupReferenceReq {
        ProjectId        string       `json:"project_id" validate:"required" zh:"项目ID"`
        ComponentGroupId string       `json:"component_group_id" validate:"required" zh:"组件组ID"`
        Condition        *Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination       *Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort             []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchComponentGroupReferenceResp {
        CurrentPage uint64            `json:"current_page"`
        PageSize    uint64            `json:"page_size"`
        TotalCount  uint64            `json:"total_count"`
        TotalPage   uint64            `json:"total_page"`
        Items       []*SearchComponentGroupReferenceItem `json:"items"`
    }
)
