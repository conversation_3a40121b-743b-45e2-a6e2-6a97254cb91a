syntax = "v1"

import "apicase_types.api"

@server (
	prefix: manager/v1
	group: apicase
)
service manager {
	@doc "create api test case"
	@handler createApiCase
	post /api_case/create (CreateApiCaseReq) returns (CreateApiCaseResp)

	@doc "delete api test case"
	@handler removeApiCase
	put /api_case/remove (RemoveApiCaseReq) returns (RemoveApiCaseResp)

	@doc "modify api test case"
	@handler modifyApiCase
	put /api_case/modify (ModifyApiCaseReq) returns (ModifyApiCaseResp)
	
	@doc "search api test cases"
	@handler searchApiCase
	post /api_case/search (SearchApiCaseReq) returns (SearchApiCaseResp)
	
	@doc "view api test case"
	@handler viewApiCase
	get /api_case/view (ViewApiCaseReq) returns (ViewApiCaseResp)

	@doc "search reference data of api case"
	@handler searchApiCaseReference
	post /api_case/reference/search (SearchApiCaseReferenceReq) returns (SearchApiCaseReferenceResp)

	@doc "maintain api test case"
	@handler maintainApiCase
	put /api_case/maintain (MaintainApiCaseReq) returns (MaintainApiCaseResp)

	@doc "publish api test case"
	@handler publishApiCase
	put /api_case/publish (PublishApiCaseReq) returns (PublishApiCaseResp)
}
