package perfLarkChat

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyPerfLarkChatLogic struct {
	*BaseLogic
}

func NewModifyPerfLarkChatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyPerfLarkChatLogic {
	return &ModifyPerfLarkChatLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyPerfLarkChatLogic) ModifyPerfLarkChat(req *types.ModifyPerfLarkChatReq) (
	resp *types.ModifyPerfLarkChatResp, err error,
) {
	in := &pb.ModifyPerfLarkChatReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	_, err = l.svcCtx.ManagerPerfLarkChatRPC.ModifyPerfLarkChat(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyPerfLarkChatResp{}, nil
}
