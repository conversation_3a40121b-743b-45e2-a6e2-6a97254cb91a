package interfaceDefinition

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchInterfaceDocumentReferenceLogic struct {
	*BaseLogic
}

func NewSearchInterfaceDocumentReferenceLogic(
	ctx context.Context, svcCtx *svc.ServiceContext,
) *SearchInterfaceDocumentReferenceLogic {
	return &SearchInterfaceDocumentReferenceLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchInterfaceDocumentReferenceLogic) SearchInterfaceDocumentReference(req *types.SearchInterfaceDocumentReferenceReq) (
	resp *types.SearchInterfaceDocumentReferenceResp, err error,
) {
	in := &pb.SearchInterfaceDocumentReferenceReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to request, error: %+v",
			req, err,
		)
	}

	out, err := l.svcCtx.ManagerInterfaceDefinitionRpc.SearchInterfaceDocumentReference(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchInterfaceDocumentReferenceResp{Items: []*types.SearchInterfaceDocumentReferenceItem{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data[%+v] to response, error: %+v",
			out, err,
		)
	}

	return resp, nil
}
