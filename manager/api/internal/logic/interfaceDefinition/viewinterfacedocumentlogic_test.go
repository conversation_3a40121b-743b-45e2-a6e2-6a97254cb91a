package interfaceDefinition

import (
	"context"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/rest/httpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/response"
)

var (
	ctx     context.Context
	service httpc.Service
)

func TestMain(m *testing.M) {
	ctx = context.Background()
	service = httpc.NewService("ViewInterfaceDocument")
	m.Run()
}

func TestViewInterfaceDocumentLogic_ViewInterfaceDocument(t *testing.T) {
	type args struct {
		req *types.ViewInterfaceDocumentReq
	}
	tests := []struct {
		name string
		args args
		resp *response.CommonResponse
	}{
		{
			name: "",
			args: args{
				req: &types.ViewInterfaceDocumentReq{
					ProjectId:  "project_id:YWb3GBvLl1AWSKCf5SL-c",
					DocumentId: "interface_document_id:eAi71MTkSVx-NH22204TC",
				},
			},
			resp: &response.CommonResponse{},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				resp, err := service.Do(
					ctx, http.MethodGet, "http://0.0.0.0:11001/manager/v1/interface_definition/document/view",
					*tt.args.req,
				)
				if err != nil {
					t.Errorf("ViewInterfaceDocument() error = %+v", err)
					return
				}

				assert.Equal(t, http.StatusOK, resp.StatusCode)

				err = httpc.ParseJsonBody(resp, tt.resp)
				if err != nil {
					t.Errorf("ViewInterfaceDocument() error = %+v", err)
					return
				}

				assert.Equal(t, uint32(errorx.OK), tt.resp.Code)

				t.Logf("ViewInterfaceDocument() resp = \n%s", jsonx.MarshalToIndentStringIgnoreError(tt.resp, "", "  "))
			},
		)
	}
}
