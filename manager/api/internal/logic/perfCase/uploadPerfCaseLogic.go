package perfCase

import (
	"context"
	"database/sql"
	"encoding/hex"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
)

type UploadPerfCaseLogic struct {
	*BaseLogic
}

func NewUploadPerfCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UploadPerfCaseLogic {
	return &UploadPerfCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *UploadPerfCaseLogic) UploadPerfCase(
	req *types.UploadPerfCaseReq, r *http.Request,
) (resp *types.UploadPerfCaseResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, req.ProjectId); err != nil {
		return nil, err
	}

	// get the specified number of virtual users from the request
	vus := int64(req.NumberOfVu)
	if req.PerfDataId != "" {
		if perfData, err := model.CheckPerfDataByDataID(
			l.ctx, l.svcCtx.PerfDataModel, req.ProjectId, req.PerfDataId,
		); err != nil {
			return nil, err
		} else {
			// get the number of virtual users from the perf data
			vus = perfData.NumberOfVu
		}
	}

	// parse the multipart form in req
	if err = r.ParseMultipartForm(maxFileSize); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.ParseParamError, err.Error()),
			"failed to parse multipart form, error: %+v",
			err,
		)
	}

	// get the file from the multipart form
	file, fileHeader, err := r.FormFile(formFileKey)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.ParseParamError, err.Error()),
			"failed to get the %q parameter, error: %+v",
			formFileKey, err,
		)
	}
	defer func(file multipart.File) {
		_ = file.Close()
	}(file)

	// generate the perf case id
	caseID, err := l.generatePerfCaseID(req.ProjectId)
	if err != nil {
		return nil, err
	}

	// save the file to the local file system
	localFilePath := filepath.Join(l.svcCtx.Config.PVCPath, subDir, caseID)
	// save the file to the other file system, e.g. HS NFS
	otherFilePath := filepath.Join(l.svcCtx.Config.PVCNFSPath, subDir, caseID)
	defer func() {
		if err != nil {
			_ = os.Remove(localFilePath)
			_ = os.Remove(otherFilePath)
		}
	}()
	md5Hash, size, err := logic.SaveFile(file, localFilePath)
	if err != nil {
		return nil, err
	} else if size != fileHeader.Size {
		return nil, errorx.Errorf(
			errorx.FileOperationFailure,
			"the size of file is mismatch, filename: %s, expected: %d, actual: %d",
			fileHeader.Filename, fileHeader.Size, size,
		)
	}

	// 双写
	err = commonutils.CopyFile(localFilePath, otherFilePath)
	if err != nil {
		// ignore error
		l.Errorf(
			"failed to save the perf case file to path, filename: %s, path: %s, error: %+v",
			fileHeader.Filename, otherFilePath, err,
		)
	}

	// get the content from the perf case file
	data, err := commonutils.GetPerfCaseFromReader(file)
	if err != nil {
		return nil, err
	}

	// calculate the maximum RPS by serial steps and parallel steps
	rl, err := commonutils.GetMaxRPSFromSerialStepsAndParallelSteps(data.GetSerialSteps(), data.GetParallelSteps())
	if err != nil {
		return nil, err
	}

	// automatically calculate the number of virtual users
	if req.PerfDataId == "" && vus == 0 {
		vus = calculateNumberOfVirtualUsers(data.GetSerialSteps(), data.GetParallelSteps())
	}

	// calculate the load generator resource
	lg := calculateLoadGeneratorResource(
		vus, commontypes.LoadGenerator{
			NumberOfLg:       req.NumberOfLg,
			RequestsOfCpu:    req.RequestsOfCpu,
			RequestsOfMemory: req.RequestsOfMemory,
			LimitsOfCpu:      req.LimitsOfCpu,
			LimitsOfMemory:   req.LimitsOfMemory,
		},
	)

	fileExt := filepath.Ext(fileHeader.Filename)
	now := time.Now()
	perfCase := &model.PerfCase{
		ProjectId: req.ProjectId,
		CaseId:    caseID,
		Name:      strings.TrimSuffix(fileHeader.Filename, fileExt),
		Description: sql.NullString{
			String: fileHeader.Filename,
			Valid:  true,
		},
		Extension:        fileExt,
		Hash:             hex.EncodeToString(md5Hash.Sum(nil)),
		Size:             size,
		Path:             localFilePath,
		TargetRps:        rl.GetTargetRps(),
		InitialRps:       rl.GetInitialRps(),
		StepHeight:       rl.GetStepHeight(),
		StepDuration:     rl.GetStepDuration(),
		PerfDataId:       req.PerfDataId,
		NumberOfVu:       vus,
		NumberOfLg:       int64(lg.GetNumberOfLg()),
		RequestsOfCpu:    lg.GetRequestsOfCpu(),
		RequestsOfMemory: lg.GetRequestsOfMemory(),
		LimitsOfCpu:      lg.GetLimitsOfCpu(),
		LimitsOfMemory:   lg.GetLimitsOfMemory(),
		State:            int64(constants.EnableStatus),
		MaintainedBy: sql.NullString{
			String: l.currentUser.Account,
			Valid:  true,
		},
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: now,
		UpdatedAt: now,
	}
	if err = l.create(perfCase, data); err != nil {
		return nil, err
	}

	resp = &types.UploadPerfCaseResp{PerfCase: &types.PerfCase{}}
	if err = utils.Copy(resp.PerfCase, perfCase, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf case to response, perf case: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfCase), err,
		)
	}

	return resp, nil
}

func (l *UploadPerfCaseLogic) create(perfCase *model.PerfCase, content *commonpb.PerfCaseContent) error {
	return l.svcCtx.PerfCaseModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			// create perf case steps
			if err := l.createPerfCaseSteps(context, session, perfCase, content); err != nil {
				return err
			}

			// create perf case
			if _, err := l.svcCtx.PerfCaseModel.Insert(context, session, perfCase); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.PerfCaseModel.Table(), jsonx.MarshalIgnoreError(perfCase), err,
				)
			}

			return nil
		},
	)
}
