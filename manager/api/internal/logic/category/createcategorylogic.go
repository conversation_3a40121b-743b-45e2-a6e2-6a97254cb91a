package category

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type CreateCategoryLogic struct {
	*BaseLogic
}

func NewCreateCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateCategoryLogic {
	return &CreateCategoryLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *CreateCategoryLogic) CreateCategory(req *types.CreateCategoryReq) (resp *types.CreateCategoryResp, err error) {
	in := &pb.CreateCategoryReq{}
	if err = commonutils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req,
			err,
		)
	}

	out, err := l.svcCtx.ManagerCategoryRpc.CreateCategory(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.CreateCategoryResp{CategoryId: out.GetCategory().GetCategoryId()}, nil
}

//func (l *CreateCategoryLogic) CreateCategoryForInternal(
//	ctx context.Context, session sqlx.Session, req CreateCategoryInternalReq,
//) (string, error) {
//	categoryId, err := l.generateCategoryId(req.ProjectId, req.Type)
//	if err != nil {
//		return "", err
//	}
//
//	// do the check if not an internal call and parent_id isn't an empty string
//	if !req.IsInternalCall && req.ParentId != "" {
//		var (
//			c     *model.Category
//			index int64
//		)
//
//		// validate the parent category by parent_id
//		c, err = model.CheckCategoryByCategoryId(l.ctx, l.svcCtx.CategoryModel, req.ProjectId, req.Type, req.ParentId)
//		if err != nil {
//			return "", err
//		} else if c.CategoryType != common.ConstCategoryTypeDirectory {
//			return "", errors.WithStack(
//				errorx.Err(
//					errorx.ProhibitedBehavior, fmt.Sprintf(
//						"the type of parent category[%s] does not support creation of sub category", c.CategoryType,
//					),
//				),
//			)
//		} else if req.Type == common.ConstCategoryTreeTypeComponentGroup && c.Name == common.ConstCategoryRootAllComponentGroup {
//			return "", errors.WithStack(
//				errorx.Err(
//					errorx.ProhibitedBehavior, fmt.Sprintf(
//						"cannot create category under the root of component group category tree[%s]",
//						common.ConstCategoryRootAllComponentGroup,
//					),
//				),
//			)
//		} else if req.Type == common.ConstCategoryTreeTypeInterfaceSchema && c.Name == common.ConstCategorySubRootTDSService {
//			return "", errors.WithStack(
//				errorx.Err(
//					errorx.ProhibitedBehavior, fmt.Sprintf(
//						"cannot create category under the builtin tds service category tree[%s]",
//						common.ConstCategorySubRootTDSService,
//					),
//				),
//			)
//		}
//
//		req.RootType = c.RootType.String
//
//		if req.Index == 0 {
//			// get the max index under the parent category
//			index, err = l.svcCtx.CategoryTreeModel.FindMaxIndexInSameLevel(l.ctx, req.ProjectId, req.ParentId)
//			if err != nil && err != model.ErrNotFound {
//				return "", errors.Wrapf(
//					errorx.Err(errorx.DBError, err.Error()),
//					"failed to find the max index of category[%s]'s children categories, error: %+v", c.Name, err,
//				)
//			}
//			req.Index = index + 1
//		}
//	} else if req.Index == 0 {
//		req.Index = 1
//	}
//
//	fn := func(context context.Context, session sqlx.Session) error {
//		var b int64
//		if req.Builtin {
//			b = 1
//		}
//
//		category := &model.Category{
//			ProjectId:    req.ProjectId,
//			Type:         req.Type,
//			CategoryId:   categoryId,
//			CategoryType: req.CategoryType,
//			RootType: sql.NullString{
//				String: req.RootType,
//				Valid:  req.RootType != "",
//			},
//			NodeType: sql.NullString{
//				String: req.NodeType,
//				Valid:  req.NodeType != "",
//			},
//			NodeId: sql.NullString{
//				String: req.NodeId,
//				Valid:  req.NodeId != "",
//			},
//			Name: req.Name,
//			Description: sql.NullString{
//				String: req.Description,
//				Valid:  req.Description != "",
//			},
//			Builtin:   b,
//			CreatedBy: "1", // 暂时hard code
//			UpdatedBy: "1", // 暂时hard code
//			CreatedAt: time.Now(),
//			UpdatedAt: time.Now(),
//		}
//		categoryTree := &model.CategoryTree{
//			ProjectId:  req.ProjectId,
//			Ancestor:   categoryId,
//			Descendant: categoryId,
//			Depth:      0,
//			Index:      req.Index,
//			CreatedBy:  "1", // 暂时hard code
//			UpdatedBy:  "1", // 暂时hard code
//			CreatedAt:  time.Now(),
//			UpdatedAt:  time.Now(),
//		}
//
//		if _, err = l.svcCtx.CategoryModel.Insert(context, session, category); err != nil {
//			return errors.Wrapf(
//				errorx.Err(errorx.DBError, err.Error()), "failed to insert table[%s] with values[%+v], error: %+v",
//				l.svcCtx.CategoryModel.Table(), category, err,
//			)
//		}
//
//		if _, err = l.svcCtx.CategoryTreeModel.InsertTree(context, session, req.ParentId, categoryTree); err != nil {
//			return errors.Wrapf(
//				errorx.Err(errorx.DBError, err.Error()),
//				"failed to insert table[%s] with values[parentId: %s, %+v], error: %+v",
//				l.svcCtx.CategoryTreeModel.Table(), req.ParentId, categoryTree, err,
//			)
//		}
//
//		return nil
//	}
//
//	if session != nil {
//		if ctx == nil {
//			ctx = l.ctx
//		}
//		if err = fn(ctx, session); err != nil {
//			return "", err
//		}
//	} else if err = l.svcCtx.CategoryModel.Trans(l.ctx, fn); err != nil {
//		return "", err
//	}
//
//	return categoryId, nil
//}
//
//func (l *CreateCategoryLogic) generateCategoryId(projectId, tp string) (string, error) {
//	g := commonutils.NewUniqueIdGenerator(
//		commonutils.WithGenerateFunc(utils.GenCategoryId), commonutils.WithIsUniqueFunc(
//			func(id string) bool {
//				r, err := l.svcCtx.CategoryModel.FindOneByProjectIdTypeCategoryId(l.ctx, projectId, tp, id)
//				if err == model.ErrNotFound || r == nil {
//					return true
//				}
//				return false
//			},
//		),
//	)
//	categoryId := g.Next()
//	if categoryId == "" {
//		return "", errors.WithStack(
//			errorx.Err(
//				errorx.GenerateUniqueIdFailure, "failed to generate category id, please try it later",
//			),
//		)
//	}
//
//	return categoryId, nil
//}
