package componentgroup

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyComponentGroupLogic struct {
	*BaseLogic
}

func NewModifyComponentGroupLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyComponentGroupLogic {
	return &ModifyComponentGroupLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyComponentGroupLogic) ModifyComponentGroup(req *types.ModifyComponentGroupReq) (resp *types.ModifyComponentGroupResp, err error) {
	in := &pb.ModifyComponentGroupReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	_, err = l.svcCtx.ManagerComponentGroupRpc.ModifyComponentGroup(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyComponentGroupResp{}, nil
}
