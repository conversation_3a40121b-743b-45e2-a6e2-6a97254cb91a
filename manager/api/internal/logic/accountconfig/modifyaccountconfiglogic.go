package accountconfig

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyAccountConfigLogic struct {
	*BaseLogic
}

func NewModifyAccountConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyAccountConfigLogic {
	return &ModifyAccountConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *ModifyAccountConfigLogic) ModifyAccountConfig(req *types.ModifyAccountConfigReq) (resp *types.ModifyAccountConfigResp, err error) {
	in := &pb.ModifyAccountConfigurationReq{}
	if err = utils.Copy(in, req); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy data[%+v] to request, error: %+v", req, err)
	}

	_, err = l.svcCtx.ManagerAccountConfigurationRpc.ModifyAccountConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	return &types.ModifyAccountConfigResp{}, nil
}
