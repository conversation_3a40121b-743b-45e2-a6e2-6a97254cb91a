// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3

package handler

import (
	"net/http"
	"time"

	accountconfig "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/accountconfig"
	advancedsearch "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/advancedsearch"
	apicase "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/apicase"
	apiplan "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/apiplan"
	apiservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/apiservice"
	apisuite "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/apisuite"
	casepublic "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/casepublic"
	category "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/category"
	componentgroup "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/componentgroup"
	dataprocessingfunction "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/dataprocessingfunction"
	generalconfig "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/generalconfig"
	gitconfig "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/gitconfig"
	interfaceDefinition "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/interfaceDefinition"
	larkApp "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/larkApp"
	like "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/like"
	notify "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/notify"
	perfCase "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/perfCase"
	perfCaseV2 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/perfCaseV2"
	perfData "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/perfData"
	perfLarkChat "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/perfLarkChat"
	perfLarkMember "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/perfLarkMember"
	perfPlan "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/perfPlan"
	perfPlanV2 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/perfPlanV2"
	perfStopRule "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/perfStopRule"
	project "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/project"
	projectDevice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/projectDevice"
	protobufConfig "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/protobufConfig"
	review "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/review"
	stabilityPlan "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/stabilityPlan"
	tag "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/tag"
	uiplan "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/handler/uiplan"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/project/create",
				Handler: project.CreateProjectHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/project/delete",
				Handler: project.RemoveProjectHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/project/modify",
				Handler: project.ModifyProjectHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/project/search",
				Handler: project.SearchProjectHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/project/view",
				Handler: project.ViewProjectHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/project/user/search",
				Handler: project.SearchProjectUserHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/project/review/modify",
				Handler: project.ModifyProjectReviewFunctionHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/project/coverage/modify",
				Handler: project.ModifyProjectCoverageFunctionHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create category
				Method:  http.MethodPost,
				Path:    "/category/create",
				Handler: category.CreateCategoryHandler(serverCtx),
			},
			{
				// delete category
				Method:  http.MethodPut,
				Path:    "/category/remove",
				Handler: category.RemoveCategoryHandler(serverCtx),
			},
			{
				// modify category
				Method:  http.MethodPut,
				Path:    "/category/modify",
				Handler: category.ModifyCategoryHandler(serverCtx),
			},
			{
				// search categories
				Method:  http.MethodPost,
				Path:    "/category/search",
				Handler: category.SearchCategoryHandler(serverCtx),
			},
			{
				// move category tree
				Method:  http.MethodPost,
				Path:    "/category/tree/move",
				Handler: category.MoveCategoryTreeHandler(serverCtx),
			},
			{
				// get category tree
				Method:  http.MethodGet,
				Path:    "/category/tree/get",
				Handler: category.GetCategoryTreeHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create tag
				Method:  http.MethodPost,
				Path:    "/tag/create",
				Handler: tag.CreateTagHandler(serverCtx),
			},
			{
				// delete tag
				Method:  http.MethodPut,
				Path:    "/tag/remove",
				Handler: tag.RemoveTagHandler(serverCtx),
			},
			{
				// modify tag
				Method:  http.MethodPut,
				Path:    "/tag/modify",
				Handler: tag.ModifyTagHandler(serverCtx),
			},
			{
				// search tag
				Method:  http.MethodPost,
				Path:    "/tag/search",
				Handler: tag.SearchTagHandler(serverCtx),
			},
			{
				// view tag
				Method:  http.MethodGet,
				Path:    "/tag/view",
				Handler: tag.ViewTagHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create custom function
				Method:  http.MethodPost,
				Path:    "/function/create",
				Handler: dataprocessingfunction.CreateDataProcessingFunctionHandler(serverCtx),
			},
			{
				// delete custom function
				Method:  http.MethodPut,
				Path:    "/function/remove",
				Handler: dataprocessingfunction.RemoveDataProcessingFunctionHandler(serverCtx),
			},
			{
				// modify custom function
				Method:  http.MethodPut,
				Path:    "/function/modify",
				Handler: dataprocessingfunction.ModifyDataProcessingFunctionHandler(serverCtx),
			},
			{
				// search data processing function
				Method:  http.MethodPost,
				Path:    "/function/search",
				Handler: dataprocessingfunction.SearchDataProcessingFunctionHandler(serverCtx),
			},
			{
				// view data processing function
				Method:  http.MethodGet,
				Path:    "/function/view",
				Handler: dataprocessingfunction.ViewDataProcessingFunctionHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create general config
				Method:  http.MethodPost,
				Path:    "/general_config/create",
				Handler: generalconfig.CreateGeneralConfigHandler(serverCtx),
			},
			{
				// delete general config
				Method:  http.MethodPut,
				Path:    "/general_config/remove",
				Handler: generalconfig.RemoveGeneralConfigHandler(serverCtx),
			},
			{
				// modify general config
				Method:  http.MethodPut,
				Path:    "/general_config/modify",
				Handler: generalconfig.ModifyGeneralConfigHandler(serverCtx),
			},
			{
				// search general configs
				Method:  http.MethodPost,
				Path:    "/general_config/search",
				Handler: generalconfig.SearchGeneralConfigHandler(serverCtx),
			},
			{
				// view general config
				Method:  http.MethodGet,
				Path:    "/general_config/view",
				Handler: generalconfig.ViewGeneralConfigHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create account config
				Method:  http.MethodPost,
				Path:    "/account_config/create",
				Handler: accountconfig.CreateAccountConfigHandler(serverCtx),
			},
			{
				// delete account config
				Method:  http.MethodPut,
				Path:    "/account_config/remove",
				Handler: accountconfig.RemoveAccountConfigHandler(serverCtx),
			},
			{
				// modify account config
				Method:  http.MethodPut,
				Path:    "/account_config/modify",
				Handler: accountconfig.ModifyAccountConfigHandler(serverCtx),
			},
			{
				// search account configs
				Method:  http.MethodPost,
				Path:    "/account_config/search",
				Handler: accountconfig.SearchAccountConfigHandler(serverCtx),
			},
			{
				// view account config
				Method:  http.MethodGet,
				Path:    "/account_config/view",
				Handler: accountconfig.ViewAccountConfigHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// import interface definition by local
				Method:  http.MethodPost,
				Path:    "/interface_definition/local_import",
				Handler: interfaceDefinition.LocalImportInterfaceDefinitionHandler(serverCtx),
			},
			{
				// import interface definition by remote
				Method:  http.MethodPost,
				Path:    "/interface_definition/remote_import",
				Handler: interfaceDefinition.RemoteImportInterfaceDefinitionHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
		rest.WithTimeout(600000*time.Millisecond),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create interface document
				Method:  http.MethodPost,
				Path:    "/interface_definition/document/create",
				Handler: interfaceDefinition.CreateInterfaceDocumentHandler(serverCtx),
			},
			{
				// delete interface document
				Method:  http.MethodPut,
				Path:    "/interface_definition/document/remove",
				Handler: interfaceDefinition.RemoveInterfaceDocumentHandler(serverCtx),
			},
			{
				// modify interface document
				Method:  http.MethodPut,
				Path:    "/interface_definition/document/modify",
				Handler: interfaceDefinition.ModifyInterfaceDocumentHandler(serverCtx),
			},
			{
				// search interface document
				Method:  http.MethodPost,
				Path:    "/interface_definition/document/search",
				Handler: interfaceDefinition.SearchInterfaceDocumentHandler(serverCtx),
			},
			{
				// view interface document
				Method:  http.MethodGet,
				Path:    "/interface_definition/document/view",
				Handler: interfaceDefinition.ViewInterfaceDocumentHandler(serverCtx),
			},
			{
				// generate data of interface case from interface document
				Method:  http.MethodGet,
				Path:    "/interface_definition/document/mock",
				Handler: interfaceDefinition.MockInterfaceDocumentHandler(serverCtx),
			},
			{
				// search reference data of interface document
				Method:  http.MethodPost,
				Path:    "/interface_definition/document/reference/search",
				Handler: interfaceDefinition.SearchInterfaceDocumentReferenceHandler(serverCtx),
			},
			{
				// modify reference state of interface document which is in the api plans
				Method:  http.MethodPut,
				Path:    "/interface_definition/document/reference_state/modify",
				Handler: interfaceDefinition.ModifyInterfaceDocumentReferenceStateHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create interface schema
				Method:  http.MethodPost,
				Path:    "/interface_definition/schema/create",
				Handler: interfaceDefinition.CreateInterfaceSchemaHandler(serverCtx),
			},
			{
				// delete interface schema
				Method:  http.MethodPut,
				Path:    "/interface_definition/schema/remove",
				Handler: interfaceDefinition.RemoveInterfaceSchemaHandler(serverCtx),
			},
			{
				// modify interface schema
				Method:  http.MethodPut,
				Path:    "/interface_definition/schema/modify",
				Handler: interfaceDefinition.ModifyInterfaceSchemaHandler(serverCtx),
			},
			{
				// search interface schema
				Method:  http.MethodPost,
				Path:    "/interface_definition/schema/search",
				Handler: interfaceDefinition.SearchInterfaceSchemaHandler(serverCtx),
			},
			{
				// view interface schema
				Method:  http.MethodGet,
				Path:    "/interface_definition/schema/view",
				Handler: interfaceDefinition.ViewInterfaceSchemaHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create interface config
				Method:  http.MethodPost,
				Path:    "/interface_definition/config/create",
				Handler: interfaceDefinition.CreateInterfaceConfigHandler(serverCtx),
			},
			{
				// delete interface config
				Method:  http.MethodPut,
				Path:    "/interface_definition/config/remove",
				Handler: interfaceDefinition.RemoveInterfaceConfigHandler(serverCtx),
			},
			{
				// modify interface config
				Method:  http.MethodPut,
				Path:    "/interface_definition/config/modify",
				Handler: interfaceDefinition.ModifyInterfaceConfigHandler(serverCtx),
			},
			{
				// search interface config
				Method:  http.MethodPost,
				Path:    "/interface_definition/config/search",
				Handler: interfaceDefinition.SearchInterfaceConfigHandler(serverCtx),
			},
			{
				// view interface config
				Method:  http.MethodGet,
				Path:    "/interface_definition/config/view",
				Handler: interfaceDefinition.ViewInterfaceConfigHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create interface test case
				Method:  http.MethodPost,
				Path:    "/interface_definition/case/create",
				Handler: interfaceDefinition.CreateInterfaceCaseHandler(serverCtx),
			},
			{
				// delete interface test case
				Method:  http.MethodPut,
				Path:    "/interface_definition/case/remove",
				Handler: interfaceDefinition.RemoveInterfaceCaseHandler(serverCtx),
			},
			{
				// modify interface test case
				Method:  http.MethodPut,
				Path:    "/interface_definition/case/modify",
				Handler: interfaceDefinition.ModifyInterfaceCaseHandler(serverCtx),
			},
			{
				// search interface test cases
				Method:  http.MethodPost,
				Path:    "/interface_definition/case/search",
				Handler: interfaceDefinition.SearchInterfaceCaseHandler(serverCtx),
			},
			{
				// view interface test case
				Method:  http.MethodGet,
				Path:    "/interface_definition/case/view",
				Handler: interfaceDefinition.ViewInterfaceCaseHandler(serverCtx),
			},
			{
				// maintain interface test case
				Method:  http.MethodPut,
				Path:    "/interface_definition/case/maintain",
				Handler: interfaceDefinition.MaintainInterfaceCaseHandler(serverCtx),
			},
			{
				// publish interface test case
				Method:  http.MethodPut,
				Path:    "/interface_definition/case/publish",
				Handler: interfaceDefinition.PublishInterfaceCaseHandler(serverCtx),
			},
			{
				// search reference data of interface test case
				Method:  http.MethodPost,
				Path:    "/interface_definition/reference/search",
				Handler: interfaceDefinition.SearchInterfaceCaseReferenceHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/interface_definition/coverage/team/get",
				Handler: interfaceDefinition.GetInterfaceCoverageTeamsHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/interface_definition/coverage/data/get",
				Handler: interfaceDefinition.GetInterfaceCoverageDataHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create component group
				Method:  http.MethodPost,
				Path:    "/component_group/create",
				Handler: componentgroup.CreateComponentGroupHandler(serverCtx),
			},
			{
				// delete component group
				Method:  http.MethodPut,
				Path:    "/component_group/remove",
				Handler: componentgroup.RemoveComponentGroupHandler(serverCtx),
			},
			{
				// modify component group
				Method:  http.MethodPut,
				Path:    "/component_group/modify",
				Handler: componentgroup.ModifyComponentGroupHandler(serverCtx),
			},
			{
				// search component groups
				Method:  http.MethodPost,
				Path:    "/component_group/search",
				Handler: componentgroup.SearchComponentGroupHandler(serverCtx),
			},
			{
				// view component group
				Method:  http.MethodGet,
				Path:    "/component_group/view",
				Handler: componentgroup.ViewComponentGroupHandler(serverCtx),
			},
			{
				// search reference data of component group
				Method:  http.MethodPost,
				Path:    "/component_group/reference/search",
				Handler: componentgroup.SearchComponentGroupReferenceHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create api test case
				Method:  http.MethodPost,
				Path:    "/api_case/create",
				Handler: apicase.CreateApiCaseHandler(serverCtx),
			},
			{
				// delete api test case
				Method:  http.MethodPut,
				Path:    "/api_case/remove",
				Handler: apicase.RemoveApiCaseHandler(serverCtx),
			},
			{
				// modify api test case
				Method:  http.MethodPut,
				Path:    "/api_case/modify",
				Handler: apicase.ModifyApiCaseHandler(serverCtx),
			},
			{
				// search api test cases
				Method:  http.MethodPost,
				Path:    "/api_case/search",
				Handler: apicase.SearchApiCaseHandler(serverCtx),
			},
			{
				// view api test case
				Method:  http.MethodGet,
				Path:    "/api_case/view",
				Handler: apicase.ViewApiCaseHandler(serverCtx),
			},
			{
				// search reference data of api case
				Method:  http.MethodPost,
				Path:    "/api_case/reference/search",
				Handler: apicase.SearchApiCaseReferenceHandler(serverCtx),
			},
			{
				// maintain api test case
				Method:  http.MethodPut,
				Path:    "/api_case/maintain",
				Handler: apicase.MaintainApiCaseHandler(serverCtx),
			},
			{
				// publish api test case
				Method:  http.MethodPut,
				Path:    "/api_case/publish",
				Handler: apicase.PublishApiCaseHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create api test suite
				Method:  http.MethodPost,
				Path:    "/api_suite/create",
				Handler: apisuite.CreateApiSuiteHandler(serverCtx),
			},
			{
				// delete api test suite
				Method:  http.MethodPut,
				Path:    "/api_suite/remove",
				Handler: apisuite.RemoveApiSuiteHandler(serverCtx),
			},
			{
				// modify api test suite
				Method:  http.MethodPut,
				Path:    "/api_suite/modify",
				Handler: apisuite.ModifyApiSuiteHandler(serverCtx),
			},
			{
				// search api test suites
				Method:  http.MethodPost,
				Path:    "/api_suite/search",
				Handler: apisuite.SearchApiSuiteHandler(serverCtx),
			},
			{
				// view api test suite
				Method:  http.MethodGet,
				Path:    "/api_suite/view",
				Handler: apisuite.ViewApiSuiteHandler(serverCtx),
			},
			{
				// search api cases in api suite
				Method:  http.MethodPost,
				Path:    "/api_suite/api_case/search",
				Handler: apisuite.SearchApiCaseInApiSuiteHandler(serverCtx),
			},
			{
				// search api cases not in api suite
				Method:  http.MethodPost,
				Path:    "/api_suite/api_case/remaining_search",
				Handler: apisuite.SearchApiCaseNotInApiSuiteHandler(serverCtx),
			},
			{
				// add api cases to api suite or remove api cases from api suite
				Method:  http.MethodPut,
				Path:    "/api_suite/api_case/modify",
				Handler: apisuite.ModifyApiCaseListOfApiSuiteHandler(serverCtx),
			},
			{
				// search reference data of api suite
				Method:  http.MethodPost,
				Path:    "/api_suite/reference/search",
				Handler: apisuite.SearchApiSuiteReferenceHandler(serverCtx),
			},
			{
				// modify reference state of api suite which is in the api plans
				Method:  http.MethodPut,
				Path:    "/api_suite/reference_state/modify",
				Handler: apisuite.ModifyApiSuiteReferenceStateHandler(serverCtx),
			},
			{
				// search cases in api suite
				Method:  http.MethodPost,
				Path:    "/api_suite/case/search",
				Handler: apisuite.SearchCaseInApiSuiteHandler(serverCtx),
			},
			{
				// add cases to api suite or remove cases from api suite
				Method:  http.MethodPut,
				Path:    "/api_suite/case/modify",
				Handler: apisuite.ModifyCaseListOfApiSuiteHandler(serverCtx),
			},
			{
				// search service cases not in api suite
				Method:  http.MethodPost,
				Path:    "/api_suite/service_case/remaining_search",
				Handler: apisuite.SearchServiceCaseNotInApiSuiteHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create api test plan
				Method:  http.MethodPost,
				Path:    "/api_plan/create",
				Handler: apiplan.CreateApiPlanHandler(serverCtx),
			},
			{
				// delete api test plan
				Method:  http.MethodPut,
				Path:    "/api_plan/remove",
				Handler: apiplan.RemoveApiPlanHandler(serverCtx),
			},
			{
				// modify api test plan
				Method:  http.MethodPut,
				Path:    "/api_plan/modify",
				Handler: apiplan.ModifyApiPlanHandler(serverCtx),
			},
			{
				// search api test plans
				Method:  http.MethodPost,
				Path:    "/api_plan/search",
				Handler: apiplan.SearchApiPlanHandler(serverCtx),
			},
			{
				// view api test plan
				Method:  http.MethodGet,
				Path:    "/api_plan/view",
				Handler: apiplan.ViewApiPlanHandler(serverCtx),
			},
			{
				// search suites in api test plan
				Method:  http.MethodPost,
				Path:    "/api_plan/suite/search",
				Handler: apiplan.SearchSuiteInApiPlanHandler(serverCtx),
			},
			{
				// search suites not in api test plan
				Method:  http.MethodPost,
				Path:    "/api_plan/suite/remaining_search",
				Handler: apiplan.SearchSuiteNotInApiPlanHandler(serverCtx),
			},
			{
				// advanced search suites not in api test plan
				Method:  http.MethodPost,
				Path:    "/api_plan/suite/advanced_remaining_search",
				Handler: apiplan.AdvancedSearchSuiteNotInApiPlanHandler(serverCtx),
			},
			{
				// add suites to api test plan or remove suites from api test plan
				Method:  http.MethodPut,
				Path:    "/api_plan/suite/modify",
				Handler: apiplan.ModifySuiteListOfApiPlanHandler(serverCtx),
			},
			{
				// search cases in api test plan
				Method:  http.MethodPost,
				Path:    "/api_plan/suite/case/search",
				Handler: apiplan.SearchCaseInApiPlanHandler(serverCtx),
			},
			{
				// modify reference state of suites or cases whose in api test plan
				Method:  http.MethodPut,
				Path:    "/api_plan/reference_state/modify",
				Handler: apiplan.ModifyApiPlanReferenceStateHandler(serverCtx),
			},
			{
				// search like api test plans
				Method:  http.MethodPost,
				Path:    "/api_plan/like/search",
				Handler: apiplan.SearchLikeApiPlanHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// search advanced search field
				Method:  http.MethodPost,
				Path:    "/advanced_search/field/search",
				Handler: advancedsearch.SearchFieldHandler(serverCtx),
			},
			{
				// search advanced search condition
				Method:  http.MethodPost,
				Path:    "/advanced_search/condition/search",
				Handler: advancedsearch.SearchConditionHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create notify
				Method:  http.MethodPost,
				Path:    "/notify/create",
				Handler: notify.CreateNotifyHandler(serverCtx),
			},
			{
				// delete notify
				Method:  http.MethodPut,
				Path:    "/notify/remove",
				Handler: notify.RemoveNotifyHandler(serverCtx),
			},
			{
				// modify notify
				Method:  http.MethodPut,
				Path:    "/notify/modify",
				Handler: notify.ModifyNotifyHandler(serverCtx),
			},
			{
				// search notify
				Method:  http.MethodPost,
				Path:    "/notify/search",
				Handler: notify.SearchNotifyHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create git config
				Method:  http.MethodPost,
				Path:    "/git_config/create",
				Handler: gitconfig.CreateGitConfigHandler(serverCtx),
			},
			{
				// delete git config
				Method:  http.MethodPut,
				Path:    "/git_config/remove",
				Handler: gitconfig.RemoveGitConfigHandler(serverCtx),
			},
			{
				// modify git config
				Method:  http.MethodPut,
				Path:    "/git_config/modify",
				Handler: gitconfig.ModifyGitConfigHandler(serverCtx),
			},
			{
				// search git configs
				Method:  http.MethodPost,
				Path:    "/git_config/search",
				Handler: gitconfig.SearchGitConfigHandler(serverCtx),
			},
			{
				// view git config
				Method:  http.MethodGet,
				Path:    "/git_config/view",
				Handler: gitconfig.ViewGitConfigHandler(serverCtx),
			},
			{
				// test git config
				Method:  http.MethodPost,
				Path:    "/git_config/test",
				Handler: gitconfig.TestGitConfigHandler(serverCtx),
			},
			{
				// sync git config
				Method:  http.MethodPost,
				Path:    "/git_config/sync",
				Handler: gitconfig.SyncGitConfigHandler(serverCtx),
			},
			{
				// sync git config by webhook
				Method:  http.MethodPost,
				Path:    "/git_config/sync/webhook",
				Handler: gitconfig.SyncGitConfigByWebhookHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create ui test plan
				Method:  http.MethodPost,
				Path:    "/ui_plan/create",
				Handler: uiplan.CreateUiPlanHandler(serverCtx),
			},
			{
				// delete ui test plan
				Method:  http.MethodPut,
				Path:    "/ui_plan/remove",
				Handler: uiplan.RemoveUiPlanHandler(serverCtx),
			},
			{
				// modify ui test plan
				Method:  http.MethodPut,
				Path:    "/ui_plan/modify",
				Handler: uiplan.ModifyUiPlanHandler(serverCtx),
			},
			{
				// search ui test plans
				Method:  http.MethodPost,
				Path:    "/ui_plan/search",
				Handler: uiplan.SearchUiPlanHandler(serverCtx),
			},
			{
				// view ui test plan
				Method:  http.MethodGet,
				Path:    "/ui_plan/view",
				Handler: uiplan.ViewUiPlanHandler(serverCtx),
			},
			{
				// get ui test case tree of ui test plan
				Method:  http.MethodGet,
				Path:    "/ui_plan/case_tree/get",
				Handler: uiplan.GetCaseTreeOfUIPlanHandler(serverCtx),
			},
			{
				// get ui test case tree of not added to ui test plan
				Method:  http.MethodGet,
				Path:    "/ui_plan/case_tree/remaining_get",
				Handler: uiplan.GetCaseTreeOfNotAddedToUIPlanHandler(serverCtx),
			},
			{
				// search cases in ui test plan
				Method:  http.MethodPost,
				Path:    "/ui_plan/case/search",
				Handler: uiplan.SearchCaseInUIPlanHandler(serverCtx),
			},
			{
				// list disable cases in ui test plan
				Method:  http.MethodPost,
				Path:    "/ui_plan/disable_case/list",
				Handler: uiplan.ListDisableCaseInUIPlanHandler(serverCtx),
			},
			{
				// search cases not in ui test plan
				Method:  http.MethodPost,
				Path:    "/ui_plan/case/remaining_search",
				Handler: uiplan.SearchCaseNotInUIPlanHandler(serverCtx),
			},
			{
				// add cases to ui test plan or remove cases from ui test plan
				Method:  http.MethodPut,
				Path:    "/ui_plan/case/modify",
				Handler: uiplan.ModifyCaseListOfUIPlanHandler(serverCtx),
			},
			{
				// search like ui test plans
				Method:  http.MethodPost,
				Path:    "/ui_plan/like/search",
				Handler: uiplan.SearchLikeUiPlanHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// view service test case
				Method:  http.MethodGet,
				Path:    "/apiservice/case/view",
				Handler: apiservice.ViewApiServiceCaseHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// like test plan
				Method:  http.MethodPut,
				Path:    "/plan/handle/like",
				Handler: like.HandlePlanHandler(serverCtx),
			},
			{
				// check like test plan
				Method:  http.MethodGet,
				Path:    "/plan/check/like",
				Handler: like.CheckPlanHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create review record
				Method:  http.MethodPost,
				Path:    "/review/apply",
				Handler: review.CreateReviewRecordHandler(serverCtx),
			},
			{
				// modify review record
				Method:  http.MethodPut,
				Path:    "/review/modify",
				Handler: review.ModifyReviewRecordHandler(serverCtx),
			},
			{
				// revoke review record
				Method:  http.MethodPut,
				Path:    "/review/revoke",
				Handler: review.RevokeReviewRecordHandler(serverCtx),
			},
			{
				// approve review record
				Method:  http.MethodPost,
				Path:    "/review/approve",
				Handler: review.ApproveReviewRecordHandler(serverCtx),
			},
			{
				// search review record
				Method:  http.MethodPost,
				Path:    "/review/search",
				Handler: review.SearchReviewRecordHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// search not released cases
				Method:  http.MethodPost,
				Path:    "/case/not_released/search",
				Handler: casepublic.SearchNotReleasedCaseHandler(serverCtx),
			},
			{
				// search fail log cases
				Method:  http.MethodPost,
				Path:    "/case/fail_log/search",
				Handler: casepublic.SearchFailLogCaseHandler(serverCtx),
			},
			{
				// delete fail log case
				Method:  http.MethodDelete,
				Path:    "/case/fail_log/del",
				Handler: casepublic.DeleteFailLogCaseHandler(serverCtx),
			},
			{
				// batch delete fail log case
				Method:  http.MethodDelete,
				Path:    "/case/fail_log/batch/del",
				Handler: casepublic.BatchDeleteFailLogCaseHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/lark_app/chats",
				Handler: larkApp.GetLarkAppChatsHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/protobuf_config/create",
				Handler: protobufConfig.CreateProtobufConfigHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/protobuf_config/remove",
				Handler: protobufConfig.RemoveProtobufConfigHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/protobuf_config/modify",
				Handler: protobufConfig.ModifyProtobufConfigHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/protobuf_config/search",
				Handler: protobufConfig.SearchProtobufConfigHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/protobuf_config/view",
				Handler: protobufConfig.ViewProtobufConfigHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/perf_data/upload",
				Handler: perfData.UploadPerfDataHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/perf_data/download",
				Handler: perfData.DownloadPerfDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/perf_data/remove",
				Handler: perfData.RemovePerfDataHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/perf_data/search",
				Handler: perfData.SearchPerfDataHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/perf_case/upload",
				Handler: perfCase.UploadPerfCaseHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/perf_case/download",
				Handler: perfCase.DownloadPerfCaseHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/perf_case/remove",
				Handler: perfCase.RemovePerfCaseHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/perf_case/modify",
				Handler: perfCase.ModifyPerfCaseHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/perf_case/view",
				Handler: perfCase.ViewPerfCaseHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/perf_case/calculate",
				Handler: perfCase.CalculatePerfCaseHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/perf_plan/create",
				Handler: perfPlan.CreatePerfPlanHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/perf_plan/remove",
				Handler: perfPlan.RemovePerfPlanHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/perf_plan/modify",
				Handler: perfPlan.ModifyPerfPlanHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/perf_plan/search",
				Handler: perfPlan.SearchPerfPlanHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/perf_plan/view",
				Handler: perfPlan.ViewPerfPlanHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/perf_plan/case/search",
				Handler: perfPlan.SearchCaseInPerfPlanHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/perf_case_v2/create",
				Handler: perfCaseV2.CreatePerfCaseV2Handler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/perf_case_v2/remove",
				Handler: perfCaseV2.RemovePerfCaseV2Handler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/perf_case_v2/modify",
				Handler: perfCaseV2.ModifyPerfCaseV2Handler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/perf_case_v2/search",
				Handler: perfCaseV2.SearchPerfCaseV2Handler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/perf_case_v2/view",
				Handler: perfCaseV2.ViewPerfCaseV2Handler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/perf_plan_v2/create",
				Handler: perfPlanV2.CreatePerfPlanV2Handler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/perf_plan_v2/remove",
				Handler: perfPlanV2.RemovePerfPlanV2Handler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/perf_plan_v2/modify",
				Handler: perfPlanV2.ModifyPerfPlanV2Handler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/perf_plan_v2/search",
				Handler: perfPlanV2.SearchPerfPlanV2Handler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/perf_plan_v2/view",
				Handler: perfPlanV2.ViewPerfPlanV2Handler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/perf_plan_v2/case/search",
				Handler: perfPlanV2.SearchCaseInPerfPlanV2Handler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/perf_plan_v2/protobuf/search",
				Handler: perfPlanV2.SearchProtobufInPerfPlanV2Handler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/perf_plan_v2/rule/search",
				Handler: perfPlanV2.SearchRuleInPerfPlanV2Handler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/perf_stop_rule/create",
				Handler: perfStopRule.CreatePerfStopRuleHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/perf_stop_rule/remove",
				Handler: perfStopRule.RemovePerfStopRuleHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/perf_stop_rule/modify",
				Handler: perfStopRule.ModifyPerfStopRuleHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/perf_stop_rule/search",
				Handler: perfStopRule.SearchPerfStopRuleHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/perf_stop_rule/view",
				Handler: perfStopRule.ViewPerfStopRuleHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPut,
				Path:    "/perf_lark_chat/modify",
				Handler: perfLarkChat.ModifyPerfLarkChatHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/perf_lark_chat/search",
				Handler: perfLarkChat.SearchPerfLarkChatHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/perf_lark_member/create",
				Handler: perfLarkMember.CreatePerfLarkMemberHandler(serverCtx),
			},
			{
				Method:  http.MethodPut,
				Path:    "/perf_lark_member/remove",
				Handler: perfLarkMember.RemovePerfLarkMemberHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/perf_lark_member/search",
				Handler: perfLarkMember.SearchPerfLarkMemberHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPut,
				Path:    "/project_device/modify",
				Handler: projectDevice.ModifyProjectDeviceHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/project_device/search",
				Handler: projectDevice.SearchProjectDeviceHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/project_device/remaining_search",
				Handler: projectDevice.SearchUnassignedProjectDeviceHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/project_device/reference/search",
				Handler: projectDevice.SearchProjectDeviceReferenceHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// create stability test plan
				Method:  http.MethodPost,
				Path:    "/stability_plan/create",
				Handler: stabilityPlan.CreateStabilityPlanHandler(serverCtx),
			},
			{
				// delete stability test plan
				Method:  http.MethodPut,
				Path:    "/stability_plan/remove",
				Handler: stabilityPlan.RemoveStabilityPlanHandler(serverCtx),
			},
			{
				// modify stability test plan
				Method:  http.MethodPut,
				Path:    "/stability_plan/modify",
				Handler: stabilityPlan.ModifyStabilityPlanHandler(serverCtx),
			},
			{
				// search stability test plans
				Method:  http.MethodPost,
				Path:    "/stability_plan/search",
				Handler: stabilityPlan.SearchStabilityPlanHandler(serverCtx),
			},
			{
				// view stability test plan
				Method:  http.MethodGet,
				Path:    "/stability_plan/view",
				Handler: stabilityPlan.ViewStabilityPlanHandler(serverCtx),
			},
		},
		rest.WithPrefix("/manager/v1"),
	)
}
