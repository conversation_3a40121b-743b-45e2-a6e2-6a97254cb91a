package gitconfig

import (
	"net/http"

	"github.com/go-playground/webhooks/v6/gitlab"
	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/response"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/logic/gitconfig"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
)

func SyncGitConfigByWebhookHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SyncGitConfigByWebhookReq
		hook, _ := gitlab.New()
		payload, err := hook.Parse(r, gitlab.MergeRequestEvents)
		if err != nil {
			response.MakeHttpResponse(
				r, w, nil, errors.Wrapf(
					errorx.Err(errorx.ValidateParamError, err.Error()),
					"failed to parse webhook event, error: %+v",
					err,
				),
			)
			return
		}

		switch v := payload.(type) {
		case gitlab.MergeRequestEventPayload:
			req.GitUrl = v.Repository.Homepage
			req.TargetBranch = v.ObjectAttributes.TargetBranch
			req.Email = v.ObjectAttributes.LastCommit.Author.Email
		default:
			response.MakeHttpResponse(
				r, w, nil, errorx.Errorf(
					errorx.TypeError,
					"invalid event type, expected gitlab.MergeRequestEventPayload, but got %T",
					payload,
				),
			)
			return
		}

		if err := svcCtx.Validator.Validate.StructCtx(r.Context(), req); err != nil {
			response.MakeHttpResponse(
				r, w, nil, errors.Wrapf(
					errorx.Err(errorx.ValidateParamError, svcCtx.Validator.Translate(err)),
					"failed to validate parameters, error: %+v", err,
				),
			)
			return
		}

		l := gitconfig.NewSyncGitConfigByWebhookLogic(r.Context(), svcCtx)
		resp, err := l.SyncGitConfigByWebhook(&req)
		response.MakeHttpResponse(r, w, resp, err)
	}
}
