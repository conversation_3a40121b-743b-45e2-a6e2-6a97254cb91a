package svc

import (
	"time"

	red "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/keepalive"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"
	qetredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/permission/permissioncontrol"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/client/userservice"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/permission/domainservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/permission/functionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/permission/policyservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common/zrpc/permission/roleservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/accountconfigurationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/advancedsearchservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/apicaseservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/apiexecutionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/apiplanservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/apisuiteservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/casecommonservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/categoryservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/componentgroupservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/dataprocessingfunctionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/generalconfigurationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/gitconfigurationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/interfacedefinitionservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/notifyservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/perfcaseservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/perfcasev2service"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/perfdataservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/perflarkchatservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/perflarkmemberservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/perfplanservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/perfplanv2service"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/perfstopruleservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/plancommonservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/projectdeviceservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/projectservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/protobufconfigurationservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/reviewservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/stabilityplanservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/tagservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/uiplanservice"
)

type ServiceContext struct {
	Config config.Config

	DB        sqlx.SqlConn
	Redis     *redis.Redis
	RedisNode red.UniversalClient

	Validator *utils.Validator

	ProjectModel      model.ProjectModel
	FunctionModel     model.FunctionModel
	PerfDataModel     model.PerfDataModel
	PerfCaseModel     model.PerfCaseModel
	PerfCaseStepModel model.PerfCaseStepModel
	// ProjectModel                         model.ProjectModel
	// CategoryModel                        model.CategoryModel
	// CategoryTreeModel                    model.CategoryTreeModel
	// TagModel                             model.TagModel
	// TagReferenceRelationshipModel        model.TagReferenceRelationshipModel
	// FunctionReferenceModel               model.FunctionReferenceRelationshipModel
	// GeneralConfigModel                   model.GeneralConfigurationModel
	// AccountConfigModel                   model.AccountConfigurationModel
	// InterfaceDocumentModel               model.InterfaceDocumentModel
	// InterfaceSchemaModel                 model.InterfaceSchemaModel
	// InterfaceSchemaReferenceModel        model.InterfaceSchemaReferenceRelationshipModel
	// InterfaceConfigurationModel          model.InterfaceConfigurationModel
	// InterfaceCaseModel                   model.InterfaceCaseModel
	// InterfaceCaseElementModel            model.InterfaceCaseElementModel
	// ComponentGroupModel                  model.ComponentGroupModel
	// ApiCaseModel                         model.ApiCaseModel
	// ComponentModel                       model.ComponentModel
	// ComponentGroupElementModel           model.ComponentGroupElementModel
	// ApiCaseElementModel                  model.ApiCaseElementModel
	// ComponentGroupReferenceModel         model.ComponentGroupReferenceRelationshipModel
	// ApiSuiteModel                        model.ApiSuiteModel
	// ApiPlanModel                         model.ApiPlanModel
	// AdvancedSearchFieldModel             model.AdvancedSearchFieldModel
	// AdvancedSearchConditionModel         model.AdvancedSearchConditionModel
	// AdvancedSearchFieldRelationshipModel model.AdvancedSearchFieldRelationshipModel
	// NotifyModel                          model.NotifyModel
	// UiPlanModel                          model.UiPlanModel
	// GitConfigurationModel                model.GitConfigurationModel

	ManagerAccountConfigurationRpc   accountconfigurationservice.AccountConfigurationService
	ManagerApiCaseRpc                apicaseservice.ApiCaseService
	ManagerApiExecutionRpc           apiexecutionservice.ApiExecutionService
	ManagerApiPlanRpc                apiplanservice.ApiPlanService
	ManagerApiSuiteRpc               apisuiteservice.ApiSuiteService
	ManagerCategoryRpc               categoryservice.CategoryService
	ManagerComponentGroupRpc         componentgroupservice.ComponentGroupService
	ManagerDataProcessingFunctionRpc dataprocessingfunctionservice.DataProcessingFunctionService
	ManagerGeneralConfigurationRpc   generalconfigurationservice.GeneralConfigurationService
	ManagerInterfaceDefinitionRpc    interfacedefinitionservice.InterfaceDefinitionService
	ManagerProjectRpc                projectservice.ProjectService
	ManagerTagRpc                    tagservice.TagService
	ManagerAdvancedSearchRpc         advancedsearchservice.AdvancedSearchService
	ManagerNotifyRpc                 notifyservice.NotifyService
	ManagerGitConfigurationRpc       gitconfigurationservice.GitConfigurationService
	ManagerUiPlanRpc                 uiplanservice.UiPlanService
	ManagerPlanCommonServiceRpc      plancommonservice.PlanCommonService
	ManagerReviewRpc                 reviewservice.ReviewService
	ManagerCaseCommonServiceRpc      casecommonservice.CaseCommonService
	ManagerProtobufConfigurationRPC  protobufconfigurationservice.ProtobufConfigurationService
	ManagerPerfDataRPC               perfdataservice.PerfDataService
	ManagerPerfCaseRPC               perfcaseservice.PerfCaseService
	ManagerPerfPlanRPC               perfplanservice.PerfPlanService
	ManagerPerfCaseV2RPC             perfcasev2service.PerfCaseV2Service
	ManagerPerfPlanV2RPC             perfplanv2service.PerfPlanV2Service
	ManagerPerfStopRuleRPC           perfstopruleservice.PerfStopRuleService
	ManagerPerfLarkChatRPC           perflarkchatservice.PerfLarkChatService
	ManagerPerfLarkMemberRPC         perflarkmemberservice.PerfLarkMemberService
	ManagerProjectDeviceRPC          projectdeviceservice.ProjectDeviceService
	ManagerStabilityPlanPRC          stabilityplanservice.StabilityPlanService

	PermissionDomainRpc   *domainservice.PermissionDomainRpc
	PermissionFunctionRpc *functionservice.PermissionFunctionRpc
	PermissionPolicyRpc   *policyservice.PermissionPolicyRpc
	PermissionRoleRpc     *roleservice.PermissionRoleRpc

	UserRpc userservice.UserService
}

func NewServiceContext(c config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(c.DB.DataSource)

	return &ServiceContext{
		Config: c,

		// Middleware
		DB:        sqlConn,
		Redis:     redis.MustNewRedis(c.Redis, redis.WithDB(c.Redis.DB)),
		RedisNode: qetredis.NewClient(c.Redis),
		Validator: utils.NewValidator(c.Validator.Locale),

		// Model
		ProjectModel:      model.NewProjectModel(sqlConn, c.Cache),
		FunctionModel:     model.NewFunctionModel(sqlConn, c.Cache),
		PerfDataModel:     model.NewPerfDataModel(sqlConn, c.Cache),
		PerfCaseModel:     model.NewPerfCaseModel(sqlConn, c.Cache),
		PerfCaseStepModel: model.NewPerfCaseStepModel(sqlConn, c.Cache),
		// ProjectModel:                         model.NewProjectModel(sqlConn, c.Cache),
		// CategoryModel:                        model.NewCategoryModel(sqlConn, c.Cache),
		// CategoryTreeModel:                    model.NewCategoryTreeModel(sqlConn, c.Cache),
		// TagModel:                             model.NewTagModel(sqlConn, c.Cache),
		// TagReferenceRelationshipModel:        model.NewTagReferenceRelationshipModel(sqlConn, c.Cache),
		// FunctionReferenceModel:               model.NewFunctionReferenceRelationshipModel(sqlConn, c.Cache),
		// GeneralConfigModel:                   model.NewGeneralConfigurationModel(sqlConn, c.Cache),
		// AccountConfigModel:                   model.NewAccountConfigurationModel(sqlConn, c.Cache),
		// InterfaceDocumentModel:               model.NewInterfaceDocumentModel(sqlConn, c.Cache),
		// InterfaceSchemaModel:                 model.NewInterfaceSchemaModel(sqlConn, c.Cache),
		// InterfaceSchemaReferenceModel:        model.NewInterfaceSchemaReferenceRelationshipModel(sqlConn, c.Cache),
		// InterfaceConfigurationModel:          model.NewInterfaceConfigurationModel(sqlConn, c.Cache),
		// InterfaceCaseModel:                   model.NewInterfaceCaseModel(sqlConn, c.Cache),
		// InterfaceCaseElementModel:            model.NewInterfaceCaseElementModel(sqlConn, c.Cache),
		// ComponentGroupModel:                  model.NewComponentGroupModel(sqlConn, c.Cache),
		// ApiCaseModel: model.NewApiCaseModel(sqlConn, c.Cache),
		// ComponentModel:                       model.NewComponentModel(sqlConn, c.Cache),
		// ComponentGroupElementModel:           model.NewComponentGroupElementModel(sqlConn, c.Cache),
		// ApiCaseElementModel:                  model.NewApiCaseElementModel(sqlConn, c.Cache),
		// ComponentGroupReferenceModel:         model.NewComponentGroupReferenceRelationshipModel(sqlConn, c.Cache),
		// ApiSuiteModel:                        model.NewApiSuiteModel(sqlConn, c.Cache),
		// ApiPlanModel:                         model.NewApiPlanModel(sqlConn, c.Cache),
		// AdvancedSearchFieldModel:             model.NewAdvancedSearchFieldModel(sqlConn, c.Cache),
		// AdvancedSearchConditionModel:         model.NewAdvancedSearchConditionModel(sqlConn, c.Cache),
		// AdvancedSearchFieldRelationshipModel: model.NewAdvancedSearchFieldRelationshipModel(sqlConn, c.Cache),
		// NotifyModel:                          model.NewNotifyModel(sqlConn, c.Cache),
		// UiPlanModel:                          model.NewUiPlanModel(sqlConn, c.Cache),
		// GitConfigurationModel:                model.NewGitConfigurationModel(sqlConn, c.Cache),

		// RPC
		ManagerAccountConfigurationRpc: accountconfigurationservice.NewAccountConfigurationService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerApiCaseRpc: apicaseservice.NewApiCaseService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerApiExecutionRpc: apiexecutionservice.NewApiExecutionService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerApiPlanRpc: apiplanservice.NewApiPlanService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerApiSuiteRpc: apisuiteservice.NewApiSuiteService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerCategoryRpc: categoryservice.NewCategoryService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerComponentGroupRpc: componentgroupservice.NewComponentGroupService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerDataProcessingFunctionRpc: dataprocessingfunctionservice.NewDataProcessingFunctionService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerGeneralConfigurationRpc: generalconfigurationservice.NewGeneralConfigurationService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerInterfaceDefinitionRpc: interfacedefinitionservice.NewInterfaceDefinitionService(
			zrpc.MustNewClient(
				c.Manager,
				clientinterceptors.UnaryUserInfoClientOption(),
				zrpc.WithDialOption(grpc.WithKeepaliveParams(keepalive.ClientParameters{Time: 10 * time.Minute})),
			),
		),
		ManagerProjectRpc: projectservice.NewProjectService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
				permissioncontrol.UnaryPermissionInfoClientOption(),
			),
		),
		ManagerTagRpc: tagservice.NewTagService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerAdvancedSearchRpc: advancedsearchservice.NewAdvancedSearchService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerNotifyRpc: notifyservice.NewNotifyService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerGitConfigurationRpc: gitconfigurationservice.NewGitConfigurationService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerUiPlanRpc: uiplanservice.NewUiPlanService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerPlanCommonServiceRpc: plancommonservice.NewPlanCommonService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerReviewRpc: reviewservice.NewReviewService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerCaseCommonServiceRpc: casecommonservice.NewCaseCommonService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerProtobufConfigurationRPC: protobufconfigurationservice.NewProtobufConfigurationService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerPerfDataRPC: perfdataservice.NewPerfDataService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerPerfCaseRPC: perfcaseservice.NewPerfCaseService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerPerfPlanRPC: perfplanservice.NewPerfPlanService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerPerfCaseV2RPC: perfcasev2service.NewPerfCaseV2Service(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerPerfPlanV2RPC: perfplanv2service.NewPerfPlanV2Service(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerPerfStopRuleRPC: perfstopruleservice.NewPerfStopRuleService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerPerfLarkChatRPC: perflarkchatservice.NewPerfLarkChatService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerPerfLarkMemberRPC: perflarkmemberservice.NewPerfLarkMemberService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerProjectDeviceRPC: projectdeviceservice.NewProjectDeviceService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerStabilityPlanPRC: stabilityplanservice.NewStabilityPlanService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),

		PermissionDomainRpc:   domainservice.NewPermissionDomainRpc(c.Permission),
		PermissionFunctionRpc: functionservice.NewPermissionPolicyRpc(c.Permission),
		PermissionPolicyRpc:   policyservice.NewPermissionPolicyRpc(c.Permission),
		PermissionRoleRpc:     roleservice.NewPermissionRoleRpc(c.Permission),

		UserRpc: userservice.NewUserService(
			zrpc.MustNewClient(
				c.User, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
	}
}
