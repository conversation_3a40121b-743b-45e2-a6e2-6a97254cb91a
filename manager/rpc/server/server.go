package server

import (
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/server"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/permission/permissioncontrol"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/config"
	accountconfigurationservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/accountconfigurationservice"
	advancedsearchservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/advancedsearchservice"
	apicaseservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/apicaseservice"
	apiexecutionservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/apiexecutionservice"
	apiplanservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/apiplanservice"
	apisuiteservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/apisuiteservice"
	casecommonservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/casecommonservice"
	categoryservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/categoryservice"
	componentgroupservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/componentgroupservice"
	dataprocessingfunctionservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/dataprocessingfunctionservice"
	generalconfigurationservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/generalconfigurationservice"
	gitconfigurationservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/gitconfigurationservice"
	interfacedefinitionservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/interfacedefinitionservice"
	notifyservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/notifyservice"
	perfcaseservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/perfcaseservice"
	perfcasev2service "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/perfcasev2service"
	perfdataservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/perfdataservice"
	perflarkchatservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/perflarkchatservice"
	perflarkmemberservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/perflarkmemberservice"
	perfplanservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/perfplanservice"
	perfplanv2service "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/perfplanv2service"
	perfstopruleservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/perfstopruleservice"
	plancommonservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/plancommonservice"
	projectdeviceservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/projectdeviceservice"
	projectservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/projectservice"
	protobufconfigurationservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/protobufconfigurationservice"
	reviewservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/reviewservice"
	stabilityplanservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/stabilityplanservice"
	tagservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/tagservice"
	uiplanservice "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/server/uiplanservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

// NewRpcServer for single server startup
func NewRpcServer(configFile string) (*server.Server, error) {
	return server.NewServer(Options(configFile)...)
}

// NewServer for combine server startup
func NewServer(c server.Config, w *log.ZapWriter) (service.Service, error) {
	cc, ok := c.(config.Config)
	if !ok {
		return nil, errors.Errorf("failed to new rpc server, cause by the config[%T] isn't a rpc config", c)
	}

	ctx := svc.NewServiceContext(cc)
	s := zrpc.MustNewServer(
		cc.RpcServerConf, func(grpcServer *grpc.Server) {
			// account configuration
			pb.RegisterAccountConfigurationServiceServer(
				grpcServer, accountconfigurationservice.NewAccountConfigurationServiceServer(ctx),
			)
			// advanced search
			pb.RegisterAdvancedSearchServiceServer(
				grpcServer, advancedsearchservice.NewAdvancedSearchServiceServer(ctx),
			)
			// api case
			pb.RegisterApiCaseServiceServer(grpcServer, apicaseservice.NewApiCaseServiceServer(ctx))
			// api execution
			pb.RegisterApiExecutionServiceServer(grpcServer, apiexecutionservice.NewApiExecutionServiceServer(ctx))
			// api plan
			pb.RegisterApiPlanServiceServer(grpcServer, apiplanservice.NewApiPlanServiceServer(ctx))
			// api suite
			pb.RegisterApiSuiteServiceServer(grpcServer, apisuiteservice.NewApiSuiteServiceServer(ctx))
			// category
			pb.RegisterCategoryServiceServer(grpcServer, categoryservice.NewCategoryServiceServer(ctx))
			// case common
			pb.RegisterCaseCommonServiceServer(
				grpcServer, casecommonservice.NewCaseCommonServiceServer(ctx),
			)
			// component group
			pb.RegisterComponentGroupServiceServer(
				grpcServer, componentgroupservice.NewComponentGroupServiceServer(ctx),
			)
			// data processing function
			pb.RegisterDataProcessingFunctionServiceServer(
				grpcServer, dataprocessingfunctionservice.NewDataProcessingFunctionServiceServer(ctx),
			)
			// general configuration
			pb.RegisterGeneralConfigurationServiceServer(
				grpcServer, generalconfigurationservice.NewGeneralConfigurationServiceServer(ctx),
			)
			// git configuration
			pb.RegisterGitConfigurationServiceServer(
				grpcServer, gitconfigurationservice.NewGitConfigurationServiceServer(ctx),
			)
			// interface definition
			pb.RegisterInterfaceDefinitionServiceServer(
				grpcServer, interfacedefinitionservice.NewInterfaceDefinitionServiceServer(ctx),
			)
			// notify
			pb.RegisterNotifyServiceServer(grpcServer, notifyservice.NewNotifyServiceServer(ctx))
			// perf data
			pb.RegisterPerfDataServiceServer(
				grpcServer, perfdataservice.NewPerfDataServiceServer(ctx),
			)
			// perf case
			pb.RegisterPerfCaseServiceServer(
				grpcServer, perfcaseservice.NewPerfCaseServiceServer(ctx),
			)
			// perf case v2
			pb.RegisterPerfCaseV2ServiceServer(
				grpcServer, perfcasev2service.NewPerfCaseV2ServiceServer(ctx),
			)
			// perf lark chat
			pb.RegisterPerfLarkChatServiceServer(
				grpcServer, perflarkchatservice.NewPerfLarkChatServiceServer(ctx),
			)
			// perf plan
			pb.RegisterPerfPlanServiceServer(
				grpcServer, perfplanservice.NewPerfPlanServiceServer(ctx),
			)
			// perf plan v2
			pb.RegisterPerfPlanV2ServiceServer(
				grpcServer, perfplanv2service.NewPerfPlanV2ServiceServer(ctx),
			)
			// perf stop rule
			pb.RegisterPerfStopRuleServiceServer(
				grpcServer, perfstopruleservice.NewPerfStopRuleServiceServer(ctx),
			)
			// perf lark member
			pb.RegisterPerfLarkMemberServiceServer(
				grpcServer, perflarkmemberservice.NewPerfLarkMemberServiceServer(ctx),
			)
			// plan common
			pb.RegisterPlanCommonServiceServer(
				grpcServer, plancommonservice.NewPlanCommonServiceServer(ctx),
			)
			// project device
			pb.RegisterProjectDeviceServiceServer(grpcServer, projectdeviceservice.NewProjectDeviceServiceServer(ctx))
			// project
			pb.RegisterProjectServiceServer(grpcServer, projectservice.NewProjectServiceServer(ctx))
			// protobuf configuration
			pb.RegisterProtobufConfigurationServiceServer(
				grpcServer, protobufconfigurationservice.NewProtobufConfigurationServiceServer(ctx),
			)
			// review
			pb.RegisterReviewServiceServer(grpcServer, reviewservice.NewReviewServiceServer(ctx))
			// tag
			pb.RegisterTagServiceServer(grpcServer, tagservice.NewTagServiceServer(ctx))
			// ui plan
			pb.RegisterUiPlanServiceServer(grpcServer, uiplanservice.NewUiPlanServiceServer(ctx))
			// stability plan
			pb.RegisterStabilityPlanServiceServer(
				grpcServer, stabilityplanservice.NewStabilityPlanServiceServer(ctx),
			)

			if cc.Mode == service.DevMode || cc.Mode == service.TestMode {
				reflection.Register(grpcServer)
			}
		},
	)

	if err := internal.InitOperation(ctx, true); err != nil {
		return nil, errors.Errorf("failed to init operation, error: %+v", err)
	}

	// 需要在 `ServiceConf.SetUp` 后再设置 `Writer`
	log.SetWriter(w)

	return s, nil
}

// NewConfig new a config of server
func NewConfig(configFile string) server.Config {
	var c config.Config
	conf.MustLoad(configFile, &c)

	return c
}

// Options as a param of `server.NewServer`
func Options(configFile string) []server.Option {
	return []server.Option{
		server.WithNewConfigFunc(
			func() server.Config {
				return NewConfig(configFile)
			},
		),
		server.WithNewServiceFunc(NewServer),
		server.WithCustomServerOptions(grpc.MaxRecvMsgSize(common.ConstMaxRecvSize)),
		server.WithCustomUnaryInterceptors(permissioncontrol.PermissionInfoUnaryServerInterceptor),
		server.WithCustomStreamInterceptors(permissioncontrol.PermissionInfoStreamServerInterceptor),
	}
}
