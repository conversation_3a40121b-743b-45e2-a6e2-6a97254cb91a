// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: manager/base.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/descriptorpb"
	structpb "google.golang.org/protobuf/types/known/structpb"

	_ "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ApiExecutionDataType API执行数据类型
type ApiExecutionDataType int32

const (
	ApiExecutionDataType_API_UNKNOWN ApiExecutionDataType = 0
	// 外层的执行数据类型
	ApiExecutionDataType_API_COMPONENT_GROUP ApiExecutionDataType = 1  // API组件组
	ApiExecutionDataType_API_CASE            ApiExecutionDataType = 2  // API测试用例
	ApiExecutionDataType_API_SUITE           ApiExecutionDataType = 3  // API测试集合
	ApiExecutionDataType_API_PLAN            ApiExecutionDataType = 4  // API测试计划
	ApiExecutionDataType_INTERFACE_CASE      ApiExecutionDataType = 5  // 接口用例
	ApiExecutionDataType_INTERFACE_DOCUMENT  ApiExecutionDataType = 6  // 接口文档（即接口集合）
	ApiExecutionDataType_UI_CASE             ApiExecutionDataType = 7  // UI测试用例
	ApiExecutionDataType_UI_SUITE            ApiExecutionDataType = 8  // UI测试集合
	ApiExecutionDataType_UI_PLAN             ApiExecutionDataType = 9  // UI测试计划
	ApiExecutionDataType_API_SERVICE         ApiExecutionDataType = 10 // API测试服务（精准测试服务）
	ApiExecutionDataType_PERF_CASE           ApiExecutionDataType = 91 // 压力测试用例
	ApiExecutionDataType_PERF_SUITE          ApiExecutionDataType = 92 // 压力测试集合
	ApiExecutionDataType_PERF_PLAN           ApiExecutionDataType = 93 // 压力测试计划
	ApiExecutionDataType_STABILITY_PLAN      ApiExecutionDataType = 81 // 稳定性测试计划
	// 内层的执行数据类型，即组件类型
	// 21 ~ 40: 框的组件（预留）
	// 41 ~ 60: 点的组件（预留）
	ApiExecutionDataType_START                  ApiExecutionDataType = 11 // 开始
	ApiExecutionDataType_END                    ApiExecutionDataType = 12 // 结束
	ApiExecutionDataType_SETUP                  ApiExecutionDataType = 21 // 前置 - 框
	ApiExecutionDataType_TEARDOWN               ApiExecutionDataType = 22 // 后置 - 框
	ApiExecutionDataType_BUSINESS_SINGLE        ApiExecutionDataType = 23 // 业务单请求 - 框
	ApiExecutionDataType_BUSINESS_GROUP         ApiExecutionDataType = 24 // 业务行为组 - 框
	ApiExecutionDataType_LOOP                   ApiExecutionDataType = 25 // 循环 - 框
	ApiExecutionDataType_PARALLEL               ApiExecutionDataType = 26 // 并行 - 框
	ApiExecutionDataType_HTTP                   ApiExecutionDataType = 41 // HTTP请求
	ApiExecutionDataType_COMPONENT_GROUP        ApiExecutionDataType = 42 // 引用组件组
	ApiExecutionDataType_CONDITION              ApiExecutionDataType = 43 // 条件
	ApiExecutionDataType_WAIT                   ApiExecutionDataType = 44 // 等待
	ApiExecutionDataType_ASSERT                 ApiExecutionDataType = 45 // 断言
	ApiExecutionDataType_POOL_ACCOUNT           ApiExecutionDataType = 46 // 池账号
	ApiExecutionDataType_DATA_PROCESSING        ApiExecutionDataType = 47 // 数据处理
	ApiExecutionDataType_DATA_DRIVEN            ApiExecutionDataType = 48 // 数据驱动
	ApiExecutionDataType_SQL_EXECUTION          ApiExecutionDataType = 49 // SQL执行
	ApiExecutionDataType_PRECISION_TESTING_HTTP ApiExecutionDataType = 50 // 精准测试HTTP请求
)

// Enum value maps for ApiExecutionDataType.
var (
	ApiExecutionDataType_name = map[int32]string{
		0:  "API_UNKNOWN",
		1:  "API_COMPONENT_GROUP",
		2:  "API_CASE",
		3:  "API_SUITE",
		4:  "API_PLAN",
		5:  "INTERFACE_CASE",
		6:  "INTERFACE_DOCUMENT",
		7:  "UI_CASE",
		8:  "UI_SUITE",
		9:  "UI_PLAN",
		10: "API_SERVICE",
		91: "PERF_CASE",
		92: "PERF_SUITE",
		93: "PERF_PLAN",
		81: "STABILITY_PLAN",
		11: "START",
		12: "END",
		21: "SETUP",
		22: "TEARDOWN",
		23: "BUSINESS_SINGLE",
		24: "BUSINESS_GROUP",
		25: "LOOP",
		26: "PARALLEL",
		41: "HTTP",
		42: "COMPONENT_GROUP",
		43: "CONDITION",
		44: "WAIT",
		45: "ASSERT",
		46: "POOL_ACCOUNT",
		47: "DATA_PROCESSING",
		48: "DATA_DRIVEN",
		49: "SQL_EXECUTION",
		50: "PRECISION_TESTING_HTTP",
	}
	ApiExecutionDataType_value = map[string]int32{
		"API_UNKNOWN":            0,
		"API_COMPONENT_GROUP":    1,
		"API_CASE":               2,
		"API_SUITE":              3,
		"API_PLAN":               4,
		"INTERFACE_CASE":         5,
		"INTERFACE_DOCUMENT":     6,
		"UI_CASE":                7,
		"UI_SUITE":               8,
		"UI_PLAN":                9,
		"API_SERVICE":            10,
		"PERF_CASE":              91,
		"PERF_SUITE":             92,
		"PERF_PLAN":              93,
		"STABILITY_PLAN":         81,
		"START":                  11,
		"END":                    12,
		"SETUP":                  21,
		"TEARDOWN":               22,
		"BUSINESS_SINGLE":        23,
		"BUSINESS_GROUP":         24,
		"LOOP":                   25,
		"PARALLEL":               26,
		"HTTP":                   41,
		"COMPONENT_GROUP":        42,
		"CONDITION":              43,
		"WAIT":                   44,
		"ASSERT":                 45,
		"POOL_ACCOUNT":           46,
		"DATA_PROCESSING":        47,
		"DATA_DRIVEN":            48,
		"SQL_EXECUTION":          49,
		"PRECISION_TESTING_HTTP": 50,
	}
)

func (x ApiExecutionDataType) Enum() *ApiExecutionDataType {
	p := new(ApiExecutionDataType)
	*p = x
	return p
}

func (x ApiExecutionDataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ApiExecutionDataType) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_base_proto_enumTypes[0].Descriptor()
}

func (ApiExecutionDataType) Type() protoreflect.EnumType {
	return &file_manager_base_proto_enumTypes[0]
}

func (x ApiExecutionDataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ApiExecutionDataType.Descriptor instead.
func (ApiExecutionDataType) EnumDescriptor() ([]byte, []int) {
	return file_manager_base_proto_rawDescGZIP(), []int{0}
}

// CommonState 通用状态
type CommonState int32

const (
	CommonState_CS_NULL    CommonState = 0 // NULL
	CommonState_CS_ENABLE  CommonState = 1 // 生效
	CommonState_CS_DISABLE CommonState = 2 // 失效
)

// Enum value maps for CommonState.
var (
	CommonState_name = map[int32]string{
		0: "CS_NULL",
		1: "CS_ENABLE",
		2: "CS_DISABLE",
	}
	CommonState_value = map[string]int32{
		"CS_NULL":    0,
		"CS_ENABLE":  1,
		"CS_DISABLE": 2,
	}
)

func (x CommonState) Enum() *CommonState {
	p := new(CommonState)
	*p = x
	return p
}

func (x CommonState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommonState) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_base_proto_enumTypes[1].Descriptor()
}

func (CommonState) Type() protoreflect.EnumType {
	return &file_manager_base_proto_enumTypes[1]
}

func (x CommonState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommonState.Descriptor instead.
func (CommonState) EnumDescriptor() ([]byte, []int) {
	return file_manager_base_proto_rawDescGZIP(), []int{1}
}

// ResourceState 资源状态
type ResourceState int32

const (
	ResourceState_RS_NULL              ResourceState = 0  // NULL
	ResourceState_RS_ENABLED           ResourceState = 1  // 生效
	ResourceState_RS_DISABLED          ResourceState = 2  // 失效
	ResourceState_RS_NEW               ResourceState = 11 // 新
	ResourceState_RS_TO_BE_IMPLEMENTED ResourceState = 12 // 待实现
	ResourceState_RS_TO_BE_MAINTAINED  ResourceState = 13 // 待维护
	ResourceState_RS_PENDING_REVIEW    ResourceState = 14 // 待审核
	ResourceState_RS_PUBLISHED         ResourceState = 15 // 已上线
)

// Enum value maps for ResourceState.
var (
	ResourceState_name = map[int32]string{
		0:  "RS_NULL",
		1:  "RS_ENABLED",
		2:  "RS_DISABLED",
		11: "RS_NEW",
		12: "RS_TO_BE_IMPLEMENTED",
		13: "RS_TO_BE_MAINTAINED",
		14: "RS_PENDING_REVIEW",
		15: "RS_PUBLISHED",
	}
	ResourceState_value = map[string]int32{
		"RS_NULL":              0,
		"RS_ENABLED":           1,
		"RS_DISABLED":          2,
		"RS_NEW":               11,
		"RS_TO_BE_IMPLEMENTED": 12,
		"RS_TO_BE_MAINTAINED":  13,
		"RS_PENDING_REVIEW":    14,
		"RS_PUBLISHED":         15,
	}
)

func (x ResourceState) Enum() *ResourceState {
	p := new(ResourceState)
	*p = x
	return p
}

func (x ResourceState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResourceState) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_base_proto_enumTypes[2].Descriptor()
}

func (ResourceState) Type() protoreflect.EnumType {
	return &file_manager_base_proto_enumTypes[2]
}

func (x ResourceState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResourceState.Descriptor instead.
func (ResourceState) EnumDescriptor() ([]byte, []int) {
	return file_manager_base_proto_rawDescGZIP(), []int{2}
}

// ExecutionMode 执行模式
type ExecutionMode int32

const (
	ExecutionMode_EM_NULL     ExecutionMode = 0 // NULL
	ExecutionMode_EM_PARALLEL ExecutionMode = 1 // 并行
	ExecutionMode_EM_SERIAL   ExecutionMode = 2 // 串行
)

// Enum value maps for ExecutionMode.
var (
	ExecutionMode_name = map[int32]string{
		0: "EM_NULL",
		1: "EM_PARALLEL",
		2: "EM_SERIAL",
	}
	ExecutionMode_value = map[string]int32{
		"EM_NULL":     0,
		"EM_PARALLEL": 1,
		"EM_SERIAL":   2,
	}
)

func (x ExecutionMode) Enum() *ExecutionMode {
	p := new(ExecutionMode)
	*p = x
	return p
}

func (x ExecutionMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExecutionMode) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_base_proto_enumTypes[3].Descriptor()
}

func (ExecutionMode) Type() protoreflect.EnumType {
	return &file_manager_base_proto_enumTypes[3]
}

func (x ExecutionMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExecutionMode.Descriptor instead.
func (ExecutionMode) EnumDescriptor() ([]byte, []int) {
	return file_manager_base_proto_rawDescGZIP(), []int{3}
}

// UIPlatformType UI平台类型
type UIPlatformType int32

const (
	UIPlatformType_PT_NULL    UIPlatformType = 0 // NULL
	UIPlatformType_PT_ANDROID UIPlatformType = 1 // Android
	UIPlatformType_PT_IOS     UIPlatformType = 2 // iOS
)

// Enum value maps for UIPlatformType.
var (
	UIPlatformType_name = map[int32]string{
		0: "PT_NULL",
		1: "PT_ANDROID",
		2: "PT_IOS",
	}
	UIPlatformType_value = map[string]int32{
		"PT_NULL":    0,
		"PT_ANDROID": 1,
		"PT_IOS":     2,
	}
)

func (x UIPlatformType) Enum() *UIPlatformType {
	p := new(UIPlatformType)
	*p = x
	return p
}

func (x UIPlatformType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UIPlatformType) Descriptor() protoreflect.EnumDescriptor {
	return file_manager_base_proto_enumTypes[4].Descriptor()
}

func (UIPlatformType) Type() protoreflect.EnumType {
	return &file_manager_base_proto_enumTypes[4]
}

func (x UIPlatformType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UIPlatformType.Descriptor instead.
func (UIPlatformType) EnumDescriptor() ([]byte, []int) {
	return file_manager_base_proto_rawDescGZIP(), []int{4}
}

// AccountConfig 池账号配置
type AccountConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Whole         map[string]string      `protobuf:"bytes,1,rep,name=whole,proto3" json:"whole,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Combo         map[string]string      `protobuf:"bytes,2,rep,name=combo,proto3" json:"combo,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountConfig) Reset() {
	*x = AccountConfig{}
	mi := &file_manager_base_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountConfig) ProtoMessage() {}

func (x *AccountConfig) ProtoReflect() protoreflect.Message {
	mi := &file_manager_base_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountConfig.ProtoReflect.Descriptor instead.
func (*AccountConfig) Descriptor() ([]byte, []int) {
	return file_manager_base_proto_rawDescGZIP(), []int{0}
}

func (x *AccountConfig) GetWhole() map[string]string {
	if x != nil {
		return x.Whole
	}
	return nil
}

func (x *AccountConfig) GetCombo() map[string]string {
	if x != nil {
		return x.Combo
	}
	return nil
}

type Relation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Children      []*structpb.ListValue  `protobuf:"bytes,3,rep,name=children,proto3" json:"children,omitempty"`
	ReferenceId   string                 `protobuf:"bytes,4,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Relation) Reset() {
	*x = Relation{}
	mi := &file_manager_base_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Relation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Relation) ProtoMessage() {}

func (x *Relation) ProtoReflect() protoreflect.Message {
	mi := &file_manager_base_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Relation.ProtoReflect.Descriptor instead.
func (*Relation) Descriptor() ([]byte, []int) {
	return file_manager_base_proto_rawDescGZIP(), []int{1}
}

func (x *Relation) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Relation) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Relation) GetChildren() []*structpb.ListValue {
	if x != nil {
		return x.Children
	}
	return nil
}

func (x *Relation) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

type Statistic struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Increased     int64                  `protobuf:"varint,2,opt,name=increased,proto3" json:"increased,omitempty"`
	Modified      int64                  `protobuf:"varint,3,opt,name=modified,proto3" json:"modified,omitempty"`
	Unchanged     int64                  `protobuf:"varint,4,opt,name=unchanged,proto3" json:"unchanged,omitempty"`
	Skipped       int64                  `protobuf:"varint,5,opt,name=skipped,proto3" json:"skipped,omitempty"`
	Failure       int64                  `protobuf:"varint,6,opt,name=failure,proto3" json:"failure,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Statistic) Reset() {
	*x = Statistic{}
	mi := &file_manager_base_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Statistic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Statistic) ProtoMessage() {}

func (x *Statistic) ProtoReflect() protoreflect.Message {
	mi := &file_manager_base_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Statistic.ProtoReflect.Descriptor instead.
func (*Statistic) Descriptor() ([]byte, []int) {
	return file_manager_base_proto_rawDescGZIP(), []int{2}
}

func (x *Statistic) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *Statistic) GetIncreased() int64 {
	if x != nil {
		return x.Increased
	}
	return 0
}

func (x *Statistic) GetModified() int64 {
	if x != nil {
		return x.Modified
	}
	return 0
}

func (x *Statistic) GetUnchanged() int64 {
	if x != nil {
		return x.Unchanged
	}
	return 0
}

func (x *Statistic) GetSkipped() int64 {
	if x != nil {
		return x.Skipped
	}
	return 0
}

func (x *Statistic) GetFailure() int64 {
	if x != nil {
		return x.Failure
	}
	return 0
}

var File_manager_base_proto protoreflect.FileDescriptor

var file_manager_base_proto_rawDesc = []byte{
	0x0a, 0x12, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x1a, 0x20, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf5, 0x01, 0x0a, 0x0d, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x37, 0x0a, 0x05, 0x77, 0x68, 0x6f, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x57,
	0x68, 0x6f, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x77, 0x68, 0x6f, 0x6c, 0x65,
	0x12, 0x37, 0x0a, 0x05, 0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x05, 0x63, 0x6f, 0x6d, 0x62, 0x6f, 0x1a, 0x38, 0x0a, 0x0a, 0x57, 0x68, 0x6f,
	0x6c, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x1a, 0x38, 0x0a, 0x0a, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x89, 0x01,
	0x0a, 0x08, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x36,
	0x0a, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x08, 0x63, 0x68,
	0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0xad, 0x01, 0x0a, 0x09, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1c, 0x0a,
	0x09, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x69, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6d,
	0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d,
	0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x75, 0x6e, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x6e, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x65, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x6b, 0x69, 0x70, 0x70, 0x65, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x2a, 0xab, 0x04, 0x0a, 0x14, 0x41, 0x70,
	0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x50, 0x49, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x50, 0x49, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f,
	0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08,
	0x41, 0x50, 0x49, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x50,
	0x49, 0x5f, 0x53, 0x55, 0x49, 0x54, 0x45, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x50, 0x49,
	0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x10, 0x04, 0x12, 0x12, 0x0a, 0x0e, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x46, 0x41, 0x43, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x05, 0x12, 0x16, 0x0a, 0x12, 0x49,
	0x4e, 0x54, 0x45, 0x52, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x06, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x49, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x07,
	0x12, 0x0c, 0x0a, 0x08, 0x55, 0x49, 0x5f, 0x53, 0x55, 0x49, 0x54, 0x45, 0x10, 0x08, 0x12, 0x0b,
	0x0a, 0x07, 0x55, 0x49, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x10, 0x09, 0x12, 0x0f, 0x0a, 0x0b, 0x41,
	0x50, 0x49, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x0a, 0x12, 0x0d, 0x0a, 0x09,
	0x50, 0x45, 0x52, 0x46, 0x5f, 0x43, 0x41, 0x53, 0x45, 0x10, 0x5b, 0x12, 0x0e, 0x0a, 0x0a, 0x50,
	0x45, 0x52, 0x46, 0x5f, 0x53, 0x55, 0x49, 0x54, 0x45, 0x10, 0x5c, 0x12, 0x0d, 0x0a, 0x09, 0x50,
	0x45, 0x52, 0x46, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x10, 0x5d, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54,
	0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x10, 0x51, 0x12, 0x09,
	0x0a, 0x05, 0x53, 0x54, 0x41, 0x52, 0x54, 0x10, 0x0b, 0x12, 0x07, 0x0a, 0x03, 0x45, 0x4e, 0x44,
	0x10, 0x0c, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x45, 0x54, 0x55, 0x50, 0x10, 0x15, 0x12, 0x0c, 0x0a,
	0x08, 0x54, 0x45, 0x41, 0x52, 0x44, 0x4f, 0x57, 0x4e, 0x10, 0x16, 0x12, 0x13, 0x0a, 0x0f, 0x42,
	0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x49, 0x4e, 0x47, 0x4c, 0x45, 0x10, 0x17,
	0x12, 0x12, 0x0a, 0x0e, 0x42, 0x55, 0x53, 0x49, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x47, 0x52, 0x4f,
	0x55, 0x50, 0x10, 0x18, 0x12, 0x08, 0x0a, 0x04, 0x4c, 0x4f, 0x4f, 0x50, 0x10, 0x19, 0x12, 0x0c,
	0x0a, 0x08, 0x50, 0x41, 0x52, 0x41, 0x4c, 0x4c, 0x45, 0x4c, 0x10, 0x1a, 0x12, 0x08, 0x0a, 0x04,
	0x48, 0x54, 0x54, 0x50, 0x10, 0x29, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e,
	0x45, 0x4e, 0x54, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x10, 0x2a, 0x12, 0x0d, 0x0a, 0x09, 0x43,
	0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x2b, 0x12, 0x08, 0x0a, 0x04, 0x57, 0x41,
	0x49, 0x54, 0x10, 0x2c, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x53, 0x53, 0x45, 0x52, 0x54, 0x10, 0x2d,
	0x12, 0x10, 0x0a, 0x0c, 0x50, 0x4f, 0x4f, 0x4c, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x10, 0x2e, 0x12, 0x13, 0x0a, 0x0f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45,
	0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x2f, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x41, 0x54, 0x41, 0x5f,
	0x44, 0x52, 0x49, 0x56, 0x45, 0x4e, 0x10, 0x30, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x51, 0x4c, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x31, 0x12, 0x1a, 0x0a, 0x16, 0x50,
	0x52, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x45, 0x53, 0x54, 0x49, 0x4e, 0x47,
	0x5f, 0x48, 0x54, 0x54, 0x50, 0x10, 0x32, 0x2a, 0x39, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x53, 0x5f, 0x4e, 0x55, 0x4c,
	0x4c, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x53, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45,
	0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45,
	0x10, 0x02, 0x2a, 0x99, 0x02, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x53, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10,
	0x00, 0x12, 0x1b, 0x0a, 0x0a, 0x52, 0x53, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10,
	0x01, 0x1a, 0x0b, 0x82, 0xb5, 0x18, 0x07, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x12, 0x1d,
	0x0a, 0x0b, 0x52, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x1a,
	0x0c, 0x82, 0xb5, 0x18, 0x08, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x12, 0x13, 0x0a,
	0x06, 0x52, 0x53, 0x5f, 0x4e, 0x45, 0x57, 0x10, 0x0b, 0x1a, 0x07, 0x82, 0xb5, 0x18, 0x03, 0x4e,
	0x45, 0x57, 0x12, 0x2f, 0x0a, 0x14, 0x52, 0x53, 0x5f, 0x54, 0x4f, 0x5f, 0x42, 0x45, 0x5f, 0x49,
	0x4d, 0x50, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x45, 0x44, 0x10, 0x0c, 0x1a, 0x15, 0x82, 0xb5,
	0x18, 0x11, 0x54, 0x4f, 0x5f, 0x42, 0x45, 0x5f, 0x49, 0x4d, 0x50, 0x4c, 0x45, 0x4d, 0x45, 0x4e,
	0x54, 0x45, 0x44, 0x12, 0x2d, 0x0a, 0x13, 0x52, 0x53, 0x5f, 0x54, 0x4f, 0x5f, 0x42, 0x45, 0x5f,
	0x4d, 0x41, 0x49, 0x4e, 0x54, 0x41, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x0d, 0x1a, 0x14, 0x82, 0xb5,
	0x18, 0x10, 0x54, 0x4f, 0x5f, 0x42, 0x45, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x54, 0x41, 0x49, 0x4e,
	0x45, 0x44, 0x12, 0x29, 0x0a, 0x11, 0x52, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x10, 0x0e, 0x1a, 0x12, 0x82, 0xb5, 0x18, 0x0e, 0x50,
	0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x12, 0x1f, 0x0a,
	0x0c, 0x52, 0x53, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x53, 0x48, 0x45, 0x44, 0x10, 0x0f, 0x1a,
	0x0d, 0x82, 0xb5, 0x18, 0x09, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x53, 0x48, 0x45, 0x44, 0x2a, 0x3c,
	0x0a, 0x0d, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12,
	0x0b, 0x0a, 0x07, 0x45, 0x4d, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b,
	0x45, 0x4d, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4c, 0x4c, 0x45, 0x4c, 0x10, 0x01, 0x12, 0x0d, 0x0a,
	0x09, 0x45, 0x4d, 0x5f, 0x53, 0x45, 0x52, 0x49, 0x41, 0x4c, 0x10, 0x02, 0x2a, 0x39, 0x0a, 0x0e,
	0x55, 0x49, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b,
	0x0a, 0x07, 0x50, 0x54, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x50,
	0x54, 0x5f, 0x41, 0x4e, 0x44, 0x52, 0x4f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x50,
	0x54, 0x5f, 0x49, 0x4f, 0x53, 0x10, 0x02, 0x42, 0x41, 0x5a, 0x3f, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65,
	0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72,
	0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_manager_base_proto_rawDescOnce sync.Once
	file_manager_base_proto_rawDescData = file_manager_base_proto_rawDesc
)

func file_manager_base_proto_rawDescGZIP() []byte {
	file_manager_base_proto_rawDescOnce.Do(func() {
		file_manager_base_proto_rawDescData = protoimpl.X.CompressGZIP(file_manager_base_proto_rawDescData)
	})
	return file_manager_base_proto_rawDescData
}

var file_manager_base_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_manager_base_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_manager_base_proto_goTypes = []any{
	(ApiExecutionDataType)(0),  // 0: manager.ApiExecutionDataType
	(CommonState)(0),           // 1: manager.CommonState
	(ResourceState)(0),         // 2: manager.ResourceState
	(ExecutionMode)(0),         // 3: manager.ExecutionMode
	(UIPlatformType)(0),        // 4: manager.UIPlatformType
	(*AccountConfig)(nil),      // 5: manager.AccountConfig
	(*Relation)(nil),           // 6: manager.Relation
	(*Statistic)(nil),          // 7: manager.Statistic
	nil,                        // 8: manager.AccountConfig.WholeEntry
	nil,                        // 9: manager.AccountConfig.ComboEntry
	(*structpb.ListValue)(nil), // 10: google.protobuf.ListValue
}
var file_manager_base_proto_depIdxs = []int32{
	8,  // 0: manager.AccountConfig.whole:type_name -> manager.AccountConfig.WholeEntry
	9,  // 1: manager.AccountConfig.combo:type_name -> manager.AccountConfig.ComboEntry
	10, // 2: manager.Relation.children:type_name -> google.protobuf.ListValue
	3,  // [3:3] is the sub-list for method output_type
	3,  // [3:3] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_manager_base_proto_init() }
func file_manager_base_proto_init() {
	if File_manager_base_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_manager_base_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_manager_base_proto_goTypes,
		DependencyIndexes: file_manager_base_proto_depIdxs,
		EnumInfos:         file_manager_base_proto_enumTypes,
		MessageInfos:      file_manager_base_proto_msgTypes,
	}.Build()
	File_manager_base_proto = out.File
	file_manager_base_proto_rawDesc = nil
	file_manager_base_proto_goTypes = nil
	file_manager_base_proto_depIdxs = nil
}
