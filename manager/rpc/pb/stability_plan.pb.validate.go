// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: manager/stability_plan.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.TriggerMode(0)
)

// Validate checks the field values on StabilityPlan with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StabilityPlan) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StabilityPlan with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StabilityPlanMultiError, or
// nil if none found.
func (m *StabilityPlan) ValidateAll() error {
	return m.validate(true)
}

func (m *StabilityPlan) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for CategoryId

	// no validation rules for PlanId

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for State

	// no validation rules for Type

	// no validation rules for PriorityType

	// no validation rules for CronExpression

	// no validation rules for AccountConfigId

	// no validation rules for DeviceType

	// no validation rules for PlatformType

	if all {
		switch v := interface{}(m.GetDevices()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StabilityPlanValidationError{
					field:  "Devices",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StabilityPlanValidationError{
					field:  "Devices",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevices()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StabilityPlanValidationError{
				field:  "Devices",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PackageName

	// no validation rules for AppDownloadLink

	// no validation rules for Duration

	if all {
		switch v := interface{}(m.GetCustomScript()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StabilityPlanValidationError{
					field:  "CustomScript",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StabilityPlanValidationError{
					field:  "CustomScript",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomScript()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StabilityPlanValidationError{
				field:  "CustomScript",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaintainedBy

	// no validation rules for CreatedBy

	// no validation rules for UpdatedBy

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	if len(errors) > 0 {
		return StabilityPlanMultiError(errors)
	}

	return nil
}

// StabilityPlanMultiError is an error wrapping multiple validation errors
// returned by StabilityPlan.ValidateAll() if the designated constraints
// aren't met.
type StabilityPlanMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StabilityPlanMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StabilityPlanMultiError) AllErrors() []error { return m }

// StabilityPlanValidationError is the validation error returned by
// StabilityPlan.Validate if the designated constraints aren't met.
type StabilityPlanValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StabilityPlanValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StabilityPlanValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StabilityPlanValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StabilityPlanValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StabilityPlanValidationError) ErrorName() string { return "StabilityPlanValidationError" }

// Error satisfies the builtin error interface
func (e StabilityPlanValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStabilityPlan.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StabilityPlanValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StabilityPlanValidationError{}
