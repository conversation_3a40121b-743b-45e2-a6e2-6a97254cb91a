SerialSteps:
  # @陈剑平
  - Name: 40 - 拉取公会信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 15s
    Method: ga.api.guild_go.GuildGoLogic.GuildGetInfo
    Body: '
{
    "base_req": {},
    "guild_id": 2556504
}
'
    #	topic-channel-logic	@黄立尧@马少鹏
  - Name: 3100 - 通过channel id获取主题房信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_play.ChannelPlayLogic.GetTopicChannelCfgInfo
    Body: '
{
    "base_req": {},
    "channel_id": 185563011,
    "channel_type": 1
}
'

    #	channel-minigame-go-logic	@林贤
  - Name: 30001 - 获得房间送礼统计是否开
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_minigame_go.ChannelMiniGameGoLogic.ChannelPresentCountInit
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'

    #	hunt-monster-logic	@王群盛
  - Name: 5601 - 取当前房间的boss信息列表
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.hunt_monster.HuntMonsterLogic.GetMonsterList
    Body: '
{
    "base_req": {},
    "channel_id": 185563011
}
'

    #	channelgamelogic	@李金安
  - Name: 5016 - 画笔配置信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channelgame.ChannelGameLogic.ChannelDrawGameGetLineParaList
    Body: '
{
    "base_req": {}
}
'

