SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": *********
}
'
ParallelSteps:
  #	channel-live-logic	@李金安
  - Name: 3595 - 获取粉丝信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.channel_live.ChannelLiveLogic.GetFansInfo
    Body: '{
      "base_req":   {}, 
      "anchor_uid": 608024,
      "uid": 608024,
      "cid": *********
    }'
  #	activitylogic	@李金安
  - Name: 2708 - 获取主播实时榜单配置
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.activity.ActivityLogic.GetAnchorRankingListConfig
    Body: '{
      "base_req":   {}
    }'
  #	account-go-logic	@田文杰@韩杰
  - Name: 27 - 获取用户详细信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.account_go.AccountGoLogic.GetUserDetail
    Body: '{
    "base_req": {},
    "target_account": "*********"
    }'
#	"channel-live-logic"	@李金安
  - Name: 3572 - 获取我已经获得到道具
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.channel_live.ChannelLiveLogic.GetMyToolList
    Body: '{
      "base_req":   {}, 
      "uid": 608024,
      "channel_id": *********
    }'
#	knight-privilege-logic	@鲜强@吴淑杰
  - Name: 3774 - 骑士团入口-个人资料卡/主播卡片
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.knight_privilege.KnightPrivilegeLogic.GetKnightGroupEntry
    Body: '{
      "base_req":   {},
      "anchor_uid": 608024,
      "channel_id": *********
    }'
#	knight-group-logic	@鲜强
  - Name: 3779 - 取骑士团营地入口信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.knight_group.KnightGroupLogic.GetKnightGroupCampInfo
    Body: '{
      "base_req":   {},
      "anchor_uid": 608024,
      "channel_id": *********
    }'
#	wish-list-logic	@李昊哲
  - Name: 50700 - 获取主播心愿单
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.wish_list.WishListLogic.GetAnchorWishList
    Body: '{
      "base_req":   {},
      "anchor_uid": 608024,
      "channel_id": *********
    }'
#	fellow-logic	@吴世衍@曾幸华
  - Name: 30681 - 获取自己在房间内送出/收到的所有挚友邀请
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.fellow.FellowLogic.GetAllChannelFellowInvite
    Body: '{
      "base_req":   {},
      "channel_id": *********
    }'
#	channel-play-logic	@黄立尧@马少鹏
  - Name: 3100 - 获取房间玩法、配置相关信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.channel_play.ChannelPlayLogic.GetTopicChannelCfgInfo
    Body: '{
      "base_req":   {},
      "channel_id": *********,
      "channel_type": 3
    }'
#	channel-minigame-go-logic	@林贤
  - Name: 32104 - 获取直播房投票PK的信息
    RateLimit:
      TargetRPS: 1500
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 2s
    Method: ga.api.channel_minigame_go.ChannelMiniGameGoLogic.ChannelLiveVotePKGetInfo
    Body: '{
      "base_req":   {},
      "channel_id": *********
    }'
TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": *********
}
'
