# 压测cmd有：2020、30350、1157、33100、2064共5个
SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": {{int .my_channel_id}}
}
'

SerialSteps:

  # 权限不足
  - Name:           获取房间召集信息-1157
    RateLimit:
      TargetRPS:    2000
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method:         ga.api.channel_convene.ChannelConveneLogic.ChannelGetConveneInfo
    Body:           '
{
    "base_req":     {},
    "channel_id":   {{int .my_channel_id}}
}
'

TeardownSteps:
  - Name:           离开房间-424
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body:           '
{
    "channel_id": {{int .my_channel_id}}
}
'
