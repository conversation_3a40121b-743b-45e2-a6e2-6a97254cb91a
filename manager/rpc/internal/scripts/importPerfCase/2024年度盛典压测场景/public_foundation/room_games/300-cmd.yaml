SetupSteps:
  - Name:           进入房间-50858
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method:         ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2
    Body:           '
{
    "channel_id": {{int .my_channel_id}}
}
'

  - Name: 切换房间兴趣玩法-31501
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method: ga.api.hobby_channel.HobbyChannelLogic.SwitchHobbyChannelSubject
    Body: '
      {
          "base_req":     {},
          "channel_id":   {{int .my_channel_id}},
          "tab_id": 423
      }
    '

  - Name: 获取当前在玩小游戏信息|获取房间游戏加载情况-30072
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method: ga.api.channel_open_game.ChannelOpenGameLogic.GetChannelLoadingGame
    Body: '{
      "base_req": {}, 
      "channel_id" : {{int .my_channel_id}}
    }'
    Exports:
      - Name: load_seq
        Expression: $.game_info.load_seq

  - Name: 加入小游戏|加入游戏-30112
    RateLimit:
      TargetRPS:    100
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method: ga.api.channel_open_game_controller.ChannelOpenGameControllerLogic.JoinChannelGame
    Body: '{
      "base_req":   {}, 
      "channel_id": {{int .my_channel_id}}, 
      "load_seq":   {{int .load_seq}}
    }'

SerialSteps:
  - Name: 获取房间小游戏流程状态数据|获取游戏数据-30116
    RateLimit:
      TargetRPS:    300
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method: ga.api.channel_open_game_controller.ChannelOpenGameControllerLogic.GetChannelGameStatusInfo
    Body: '{
      "base_req":   {}, 
      "channel_id": {{int .my_channel_id}}, 
      "load_seq":   {{int .load_seq}}
    }'

  - Name: 获取用户在小游戏平台的openid|游戏加载完，登录获取openid和code-30111
    RateLimit:
      TargetRPS:    300
      InitialRPS:   0
      StepHeight:   10
      StepDuration: 1s
    Method: ga.api.channel_open_game_controller.ChannelOpenGameControllerLogic.GetChannelGamePlayerOpenid
    Body: '{
      "base_req":   {}, 
      "channel_id": {{int .my_channel_id}}, 
      "load_seq":   {{int .load_seq}}
    }'


TeardownSteps:
  - Name: 切换房间兴趣玩法-31501
    RateLimit:
      TargetRPS:    200
      InitialRPS:   0
      StepHeight:   50
      StepDuration: 1s
    Method: ga.api.hobby_channel.HobbyChannelLogic.SwitchHobbyChannelSubject
    Body: '{
      "base_req":   {},
      "channel_id": {{int .my_channel_id}},
      "tab_id":     1
    }'

  - Name:        离开房间-424
    RateLimit:
      TargetRPS: 200
      InitialRPS: 0
      StepHeight: 50
      StepDuration: 1s
    Method: ga.api.channel_core.ChannelCoreLogic.ChannelQuit
    Body: '
{
    "channel_id": {{int .my_channel_id}}
}
'
