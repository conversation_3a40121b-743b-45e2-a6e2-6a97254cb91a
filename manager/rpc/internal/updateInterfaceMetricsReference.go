package internal

import (
	"context"
	"database/sql"
	"errors"
	"math"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/appInsight"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	relationpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

var systemUser = userinfo.System()

type UpdateInterfaceMetricsReferenceHandler struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func newUpdateInterfaceMetricsReferenceHandler(svcCtx *svc.ServiceContext) *UpdateInterfaceMetricsReferenceHandler {
	ctx := context.Background()
	return &UpdateInterfaceMetricsReferenceHandler{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (h *UpdateInterfaceMetricsReferenceHandler) Update() error {
	var (
		now      = time.Now()
		protocol = string(common.ConstInterfaceMetricsReferenceProtocolTT)
		key      = common.ConstLockUpdateInterfaceMetricsReference
		cancel   context.CancelFunc
	)

	h.ctx, cancel = context.WithTimeout(context.Background(), common.ConstExpireOfUpdateInterfaceMetricsReferenceTask)
	defer cancel()

	fn := func() error {
		configs, err := h.svcCtx.InterfaceUpdateConfigModel.FindAll(h.ctx)
		if err != nil {
			return err
		}

		var projectIds []string
		for _, config := range configs {
			if config.Type == protocol {
				projectIds = append(projectIds, config.ProjectId)
			}
		}
		if len(projectIds) == 0 {
			return nil
		}

		out, err := h.svcCtx.RelationRPC.GetAllServiceMethods(h.ctx, &relationpb.GetAllServiceMethodsReq{})
		if err != nil {
			return err
		}

		timeRanges := getBeforeNWeekendsTimeRanges(
			common.ConstGetMetricsNWeekends,
			common.ConstGetMetricsDateHour,
			common.ConstGetMetricsDuration,
			now,
		)

		return mr.MapReduceVoid[*relationpb.ServiceMethodRelation, any](
			func(source chan<- *relationpb.ServiceMethodRelation) {
				for _, relation := range out.Relations {
					source <- relation
				}
			},
			func(relation *relationpb.ServiceMethodRelation, writer mr.Writer[any], cancel func(error)) {
				if relation == nil {
					return
				}

				reqPath := relation.GetQueryPath()
				if len(reqPath) == 0 {
					reqPath = relation.GetReqPath()
				}

				referenceQps, err := h.calculateReferenceQPS2(
					relation.GetService(), relation.GetNamespace(), reqPath, timeRanges,
				)
				if err != nil {
					h.Errorf(
						"failed to calculate the reference qps, service: %s, namespace: %s, reqpath: %s, error: %+v",
						relation.GetService(), relation.GetNamespace(), reqPath, err,
					)
				}

				for _, projectId := range projectIds {
					r, err := h.svcCtx.InterfaceMetricsReferenceModel.FindOneByProjectIdProtocolMethod(
						h.ctx, projectId, protocol, relation.GetMethod(),
					)
					if err != nil && !errors.Is(err, model.ErrNotFound) {
						h.Errorf(
							"failed to find the interface metrics reference, project_id: %s, protocol: %s, method: %s, error: %+v",
							projectId, protocol, relation.GetMethod(), err,
						)
						continue
					} else if errors.Is(err, model.ErrNotFound) {
						_, err = h.svcCtx.InterfaceMetricsReferenceModel.InsertTX(
							h.ctx, nil, &model.InterfaceMetricsReference{
								ProjectId:    projectId,
								Protocol:     protocol,
								Method:       relation.GetMethod(),
								ReferenceQps: referenceQps,
								CreatedBy:    systemUser.Account,
								UpdatedBy:    systemUser.Account,
								CreatedAt:    now,
								UpdatedAt:    now,
							},
						)
					} else if r != nil {
						r.ReferenceQps = referenceQps
						r.UpdatedBy = systemUser.Account
						r.UpdatedAt = now

						_, err = h.svcCtx.InterfaceMetricsReferenceModel.UpdateTX(h.ctx, nil, r)
					}

					if err != nil {
						h.Errorf(
							"failed to save the interface metrics reference, project_id: %s, protocol: %s, method: %s, error: %+v",
							projectId, protocol, relation.GetMethod(), err,
						)
					}
				}
			},
			func(pipe <-chan any, cancel func(error)) {
				for range pipe {
				}

				if err := h.svcCtx.InterfaceMetricsReferenceModel.DeleteBeforeDateRecords(
					h.ctx, nil, now.Add(-8*time.Hour),
				); err != nil {
					h.Errorf(
						"failed to delete the interface metrics reference records before the date: %s, error: %+v",
						now, err,
					)
				}
			},
			mr.WithContext(h.ctx), mr.WithWorkers(common.ConstMRMaxWorkers),
		)
	}

	if err := caller.LockWithOptionDo(
		h.svcCtx.Redis, key, fn, redislock.WithExpire(common.ConstExpireOfUpdateInterfaceMetricsReferenceTask),
	); err != nil {
		if re, ok := errorx.RootError(err); !ok || re.Code() != errorx.AcquireRedisLockFailure {
			h.Errorf("failed to acquire the redis lock, key: %s, error: %+v", key, err)
			return err
		}

		h.Infof("another service is executing the task of update interface metrics reference, key: %s", key)
	} else {
		h.Infof("finished to update interface metrics reference, key: %s", key)
	}

	return nil
}

func getBeforeNWeekendsTimeRanges(weeks, hour, duration int, now time.Time) (timeRanges []appInsight.TimeRange) {
	// Monday 00:00:00
	mon := now.Truncate(7 * 24 * time.Hour).Add(-8 * time.Hour)

	var sun, sat time.Time
	for n := 0; n < weeks; n++ {
		sun = mon.Add(time.Duration(hour-24)*time.Hour - time.Duration(n)*7*24*time.Hour)
		sat = sun.Add(-24 * time.Hour)

		timeRanges = append(
			timeRanges, appInsight.TimeRange{
				// Last N Sunday 20:00:00
				StartedAt: sun,
				// Last N Sunday 23:00:00
				EndedAt: sun.Add(time.Duration(duration) * time.Hour),
			},
		)
		timeRanges = append(
			timeRanges, appInsight.TimeRange{
				// Last N Saturday 20:00:00
				StartedAt: sat,
				// Last N Saturday 23:00:00
				EndedAt: sat.Add(time.Duration(duration) * time.Hour),
			},
		)
	}

	return timeRanges
}

func (h *UpdateInterfaceMetricsReferenceHandler) calculateReferenceQPS(
	service, namespace, reqpath string, timeRanges []appInsight.TimeRange,
) (sql.NullInt64, error) {
	var (
		referenceQps sql.NullInt64
		points       []*appInsight.SampleValue
		entries      = len(timeRanges) / appInsight.MaxQueryResultsCount
	)

	for i := 0; i <= entries; i++ {
		var tr []appInsight.TimeRange
		if i == entries {
			if len(timeRanges) == appInsight.MaxQueryResultsCount*i {
				break
			} else {
				tr = timeRanges[appInsight.MaxQueryResultsCount*i:]
			}
		} else {
			tr = timeRanges[appInsight.MaxQueryResultsCount*i : appInsight.MaxQueryResultsCount*i+appInsight.MaxQueryResultsCount]
		}

		rsp, err := h.svcCtx.AppInsightClient.QueryResults2Metrics(
			&appInsight.QueryResults2MetricsReq{
				Metric:     appInsight.MetricTypeOfSUM,
				Workload:   service,
				Namespace:  namespace,
				Method:     reqpath,
				TimeRanges: tr,
			},
		)
		if err != nil {
			return referenceQps, err
		}

		points = append(points, rsp.Points...)
	}

	results := removeMaxMin(points)

	if len(results) > 0 {
		var sumPoints float64
		for _, result := range results {
			sumPoints += result
		}
		referenceQps = sql.NullInt64{
			// 总数 / 天数 / 时长（秒）* 两倍
			Int64: int64(sumPoints / float64(len(results)) / (common.ConstGetMetricsDuration * time.Hour).Seconds() * 2),
			Valid: true,
		}
	}

	return referenceQps, nil
}

func removeMaxMin(points []*appInsight.SampleValue) []float64 {
	var (
		maxPoint, minPoint float64
		stages, results    []float64
		count              int
	)

	for i, point := range points {
		if point == nil {
			continue
		}

		numPoint := float64(*point)
		if i == 0 {
			maxPoint = numPoint
			minPoint = numPoint
		}
		if numPoint > maxPoint {
			maxPoint = numPoint
		}
		if numPoint < minPoint {
			minPoint = numPoint
		}
		if numPoint > 0 {
			count++
		}

		stages = append(stages, numPoint)
	}

	for _, stage := range stages {
		if count > 0 && stage == 0 {
			continue
		}

		if count > common.ConstGetMetricsNWeekends {
			if maxPoint > 0 && stage == maxPoint {
				maxPoint = 0
				continue
			}
			if minPoint > 0 && stage == minPoint {
				minPoint = 0
				continue
			}
		}

		results = append(results, stage)
	}

	return results
}

func (h *UpdateInterfaceMetricsReferenceHandler) calculateReferenceQPS2(
	service, namespace, reqPath string, timeRanges []appInsight.TimeRange,
) (sql.NullInt64, error) {
	var (
		referenceQps sql.NullInt64
		points       []*appInsight.SampleValue
	)

	for _, timeRange := range timeRanges {
		rsp, err := h.svcCtx.AppInsightClient.QueryRangeMetrics(
			&appInsight.QueryRangeMetricsReq{
				Metric:    appInsight.MetricTypeOfQPS,
				Workload:  service,
				Namespace: namespace,
				Method:    reqPath,
				StartedAt: timeRange.StartedAt,
				EndedAt:   timeRange.EndedAt,
			},
		)
		if err != nil {
			return referenceQps, err
		}

		if len(rsp.Points) == 0 {
			continue
		}

		var maxPoint appInsight.SampleValue
		for _, point := range rsp.Points {
			if point.Value > maxPoint {
				maxPoint = point.Value
			}
		}
		points = append(points, &maxPoint)
	}

	results := removeMaxMin(points)

	if len(results) > 0 {
		var sumPoints float64
		for _, result := range results {
			sumPoints += result
		}
		referenceQps = sql.NullInt64{
			// 两倍平均值，小数点四舍五入取整
			Int64: int64(math.Round(sumPoints / float64(len(results)) * 2)),
			Valid: true,
		}
	}

	return referenceQps, nil
}
