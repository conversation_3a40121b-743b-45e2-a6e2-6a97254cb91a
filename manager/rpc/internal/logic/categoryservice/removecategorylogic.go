package categoryservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type RemoveCategoryLogic struct {
	*BaseLogic
}

func NewRemoveCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveCategoryLogic {
	return &RemoveCategoryLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveCategory 删除分类
func (l *RemoveCategoryLogic) RemoveCategory(in *pb.RemoveCategoryReq) (resp *pb.RemoveCategoryResp, err error) {
	// validate the project_id in req
	if _, err := model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the category_id in req
	origin, err := model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), in.GetType(), in.GetCategoryId(),
	)
	if err != nil {
		return nil, err
	} else if origin.Builtin != 0 {
		return nil, errors.WithStack(
			errorx.Errorf(
				errorx.ProhibitedBehavior, "cannot remove the builtin category[%s]", origin.Name,
			),
		)
	} else if origin.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errors.WithStack(
			errorx.Errorf(
				errorx.ProhibitedBehavior, "cannot remove the non-directory category[%s]", origin.Name,
			),
		)
	}

	countFn, err := logic.GetCountFunctionByCategoryTreeType(l.ctx, l.svcCtx, in.GetType())
	if err != nil {
		return nil, err
	}

	count, err := countFn(in.GetProjectId(), in.GetCategoryId(), true)
	if err != nil {
		return nil, err
	} else if count > 0 {
		return nil, errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior, "cannot remove a category that is not empty",
			),
		)
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockCategoryTreeProjectIdTypePrefix, in.GetProjectId(), in.GetType())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	if err = l.svcCtx.CategoryModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			// remove category
			if _, err := l.svcCtx.CategoryModel.RemoveDescendantCategories(
				context, session, in.GetProjectId(), in.GetType(), in.GetCategoryId(),
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove the category[%s] and it's descendant categories, error: %+v",
					origin.Name, err,
				)
			}

			// remove category tree
			if _, err := l.svcCtx.CategoryTreeModel.RemoveTree(
				context, session, in.GetProjectId(), in.GetCategoryId(),
			); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to remove the category[%s] and it's descendant categories, error: %+v",
					origin.Name, err,
				)
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return &pb.RemoveCategoryResp{}, nil
}
