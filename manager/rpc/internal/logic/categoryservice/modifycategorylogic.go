package categoryservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyCategoryLogic struct {
	*BaseLogic
}

func NewModifyCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyCategoryLogic {
	return &ModifyCategoryLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyCategory 编辑分类
func (l *ModifyCategoryLogic) ModifyCategory(in *pb.ModifyCategoryReq) (resp *pb.ModifyCategoryResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	category, err := l.ModifyCategoryForInternal(l.ctx, nil, in)
	if err != nil {
		return nil, err
	}

	resp = &pb.ModifyCategoryResp{Category: &pb.Category{}}
	if err = utils.Copy(resp.Category, category); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy category[%+v] to response, error: %+v",
			category, err,
		)
	}

	return resp, nil
}

func (l *ModifyCategoryLogic) ModifyCategoryForInternal(
	ctx context.Context, session sqlx.Session, req *pb.ModifyCategoryReq,
) (*model.Category, error) {
	// validate the category_id in req
	origin, err := model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, req.GetProjectId(), req.GetType(), req.GetCategoryId(),
	)
	if err != nil {
		return nil, err
	} else if origin.Builtin != 0 {
		return nil, errors.WithStack(
			errorx.Errorf(
				errorx.ProhibitedBehavior, "cannot modify the builtin category[%s]", origin.Name,
			),
		)
	}

	if origin.Name == req.GetName() && (origin.Description.String == req.GetDescription()) {
		// nothing change, no need to update
		return origin, nil
	}

	// validate the name in req
	if err = common.CheckCategoryNameByType(req.GetName(), req.GetType()); err != nil {
		return nil, err
	}

	// acquire redis lock
	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockCategoryProjectIdTypeCategoryIdPrefix, req.GetProjectId(), req.GetType(),
		req.GetCategoryId(),
	)
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	category := &model.Category{
		Id:           origin.Id,
		ProjectId:    origin.ProjectId,
		Type:         origin.Type,
		CategoryId:   origin.CategoryId,
		CategoryType: origin.CategoryType,
		RootType:     origin.RootType,
		NodeType:     origin.NodeType,
		NodeId:       origin.NodeId,
		Name:         req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		UpdatedAt: time.Now(),
	}

	fn := func(context context.Context, session sqlx.Session) error {
		if _, err = l.svcCtx.CategoryModel.Update(context, session, category); err != nil {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to update table[%s] with values[%+v], error: %+v",
				l.svcCtx.CategoryModel.Table(), origin, err,
			)
		}

		return nil
	}

	if session != nil {
		if ctx == nil {
			ctx = l.ctx
		}
		if err = fn(ctx, session); err != nil {
			return nil, err
		}
	} else if err = l.svcCtx.CategoryModel.Trans(l.ctx, fn); err != nil {
		return nil, err
	}

	return category, nil
}
