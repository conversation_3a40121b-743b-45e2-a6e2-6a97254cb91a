package perfplanv2servicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/gorhill/cronexpr"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyPerfPlanV2Logic struct {
	*BaseLogic
}

func NewModifyPerfPlanV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyPerfPlanV2Logic {
	return &ModifyPerfPlanV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyPerfPlanV2 编辑压测计划
func (l *ModifyPerfPlanV2Logic) ModifyPerfPlanV2(in *pb.ModifyPerfPlanV2Req) (out *pb.ModifyPerfPlanV2Resp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	var c *model.Category
	// validate the category_id in req
	if c, err = model.CheckCategoryByCategoryId(
		l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypePerfPlan, in.GetCategoryId(),
	); err != nil {
		return nil, err
	} else if c.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errors.WithStack(
			errorx.Errorf(
				errorx.ProhibitedBehavior,
				"the type of parent category[%s] does not support creation of sub category",
				c.CategoryType,
			),
		)
	}

	// validate the plan_id in req
	origin, err := model.CheckPerfPlanV2ByPlanID(l.ctx, l.svcCtx.PerfPlanV2Model, in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}

	// validate the protobuf_configs in req
	if len(in.GetProtobufConfigs()) > 0 {
		if err = l.validateProtobufConfigs(in.GetProjectId(), in.GetProtobufConfigs()); err != nil {
			return nil, err
		}
	}

	// validate the general_config_id in req
	if in.GetGeneralConfigId() != "" {
		gc, err := model.CheckGeneralConfigByConfigId(
			l.ctx, l.svcCtx.GeneralConfigModel, in.GetProjectId(), in.GetGeneralConfigId(),
		)
		if err != nil {
			return nil, err
		}
		if gc.Type != string(common.ConstTestTypePerf) {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot to choose a general configuration whose type is not %s for perf plan, name: %s, type: %s",
				common.ConstTestTypePerf, gc.Name, gc.Type,
			)
		}
	}

	// validate the account_config_id in req
	if in.GetAccountConfigId() != "" {
		if _, err = model.CheckAccountConfigByConfigId(
			l.ctx, l.svcCtx.AccountConfigModel, in.GetProjectId(), in.GetAccountConfigId(),
		); err != nil {
			return nil, err
		}
	}

	if len(in.GetRules()) > 0 {
		if err = l.validateStopRules(in.GetProjectId(), in.GetRules()); err != nil {
			return nil, err
		}
	}

	// validate the protocol and cases in req
	if in.GetProtocol() == commonpb.Protocol_PROTOCOL_TT_AUTH && len(in.GetCases()) > 1 {
		return nil, errorx.Errorf(
			errorx.ProhibitedBehavior,
			"the perf plan cannot include more than one perf case when the protocol is %q",
			protobuf.GetEnumStringOf(in.GetProtocol()),
		)
	}

	var perfPlan *model.PerfPlanV2
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockPerfPlanProjectIDPlanIDPrefix, in.GetProjectId(), in.GetPlanId())
	fn := func() error {
		perfPlan, err = l.modify(in, origin)
		return err
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	out = &pb.ModifyPerfPlanV2Resp{Plan: &pb.PerfPlanV2{}}
	if err = utils.Copy(out.Plan, perfPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf plan to response, perf plan: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfPlan), err,
		)
	}

	return out, nil
}

func (l *ModifyPerfPlanV2Logic) modify(req *pb.ModifyPerfPlanV2Req, origin *model.PerfPlanV2) (
	*model.PerfPlanV2, error,
) {
	var (
		projectID       = req.GetProjectId()
		planID          = req.GetPlanId()
		description     = req.GetDescription()
		cronExpression  = req.GetCronExpression()
		generalConfigID = req.GetGeneralConfigId()
		accountConfigID = req.GetAccountConfigId()
		maintainedBy    = req.GetMaintainedBy()

		tags, authRateLimits sql.NullString
	)

	if len(req.GetTags()) > 0 {
		// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
		tags.String = jsonx.MarshalToStringIgnoreError(req.GetTags())
		tags.Valid = true
	}

	if len(req.GetAuthRateLimits()) > 0 {
		authRateLimits.String = protobuf.MarshalJSONWithMessagesToStringIgnoreError(req.GetAuthRateLimits())
		authRateLimits.Valid = true
	}

	if maintainedBy != "" {
		if user, err := l.getUserInfoByAccount(maintainedBy); err != nil {
			return nil, err
		} else if !user.GetEnabled() {
			return nil, errorx.Errorf(
				errorx.ProhibitedBehavior,
				"cannot assign a disabled user as a maintainer of perf plan, project_id: %s, plan_id: %s, maintained_by: %s",
				projectID, planID, maintainedBy,
			)
		}
	} else {
		maintainedBy = origin.MaintainedBy.String
	}

	perfPlanCases, maxDuration, err := l.handlePerfPlanCases(
		projectID, req.GetProtocol(), req.GetAuthRateLimits(), req.GetCases(),
	)
	if err != nil {
		return nil, err
	}

	duration := int64(maxDuration.Seconds())
	if req.GetCustomDuration() {
		if req.GetDuration() != 0 {
			if int64(req.GetDuration()) < duration {
				l.Warnf(
					"the customized duration cannot be less than the estimated duration calculated by the backend, customized: %d, estimated: %d",
					req.GetDuration(), duration,
				)
			}
			duration = int64(req.GetDuration())
		} else {
			req.CustomDuration = false
		}
	}

	// validate the cron_expression in req if the plan type is SCHEDULE
	if req.GetType() == commonpb.TriggerMode_SCHEDULE {
		schedule, err := cronexpr.Parse(req.GetCronExpression())
		if err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CronExpressionParseError, err.Error()),
				"failed to parse the cron expression, cron: %s, error: %+v",
				req.GetCronExpression(), err,
			)
		}

		if err = checkScheduleWithDuration(schedule, maxDuration, 0); err != nil {
			return nil, err
		}
	}

	now := time.Now()
	perfPlan := &model.PerfPlanV2{
		Id:         origin.Id,
		ProjectId:  origin.ProjectId,
		CategoryId: req.GetCategoryId(),
		PlanId:     origin.PlanId,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: description,
			Valid:  description != "",
		},
		Type: protobuf.GetEnumStringOf(req.GetType()),
		CronExpression: sql.NullString{
			String: cronExpression,
			Valid:  cronExpression != "",
		},
		Tags:      tags,
		Protocol:  protobuf.GetEnumStringOf(req.GetProtocol()),
		TargetEnv: protobuf.GetEnumStringOf(req.GetTargetEnv()),
		GeneralConfigId: sql.NullString{
			String: generalConfigID,
			Valid:  generalConfigID != "",
		},
		AccountConfigId: sql.NullString{
			String: accountConfigID,
			Valid:  accountConfigID != "",
		},
		AuthRateLimits:       authRateLimits,
		CustomDuration:       cast.ToInt64(req.GetCustomDuration()),
		Duration:             duration,
		CreateLarkChat:       cast.ToInt64(req.GetCreateLarkChat()),
		LarkChatId:           origin.LarkChatId,
		AdvancedNotification: cast.ToInt64(req.GetAdvancedNotification()),
		State:                int64(req.GetState()),
		MaintainedBy: sql.NullString{
			String: maintainedBy,
			Valid:  maintainedBy != "",
		},
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
		UpdatedAt: now,
	}

	if err := l.svcCtx.PerfPlanV2Model.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			// update the perf plan and protobuf configurations relationship
			if err := l.updateProtobufConfigRelationship(
				context, session, perfPlan, req.GetProtobufConfigs(),
			); err != nil {
				return err
			}

			// update the perf plan and perf stop rules relationship
			if err := l.updatePerfStopRuleRelationship(context, session, perfPlan, req.GetRules()); err != nil {
				return err
			}

			// update the perf plan and perf case relationship
			if err := l.updatePerfPlanCaseRelationship(context, session, perfPlan, perfPlanCases); err != nil {
				return err
			}

			// update the perf plan
			if _, err := l.svcCtx.PerfPlanV2Model.Update(context, session, perfPlan); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.PerfPlanV2Model.Table(), jsonx.MarshalIgnoreError(perfPlan), err,
				)
			}

			// update the new tag and tag reference of perf plan
			if err := l.createTagLogic.CreateTagAndReferenceForInternal(
				context, session, types.CreateOrUpdateTagReference{
					ProjectId:     perfPlan.ProjectId,
					ReferenceType: common.ConstReferenceTypePerfPlan,
					ReferenceId:   perfPlan.PlanId,
					Tags:          req.GetTags(),
				},
			); err != nil {
				return err
			}

			// update notify item of perf plan
			if !req.GetCreateLarkChat() {
				// 1. create lark chat => specified lark chats
				// 2. specified lark chats => specified lark chats
				if err := l.updateNotifyItems(context, session, perfPlan, req.GetLarkChats()); err != nil {
					return err
				}
			} else if origin.CreateLarkChat == 0 {
				// specified lark chats => create lark chat
				if err := l.updateNotifyItems(context, session, perfPlan, nil); err != nil {
					return err
				}
			}

			return l.updateScheduleTask(req, origin)
		},
	); err != nil {
		return nil, err
	}

	return perfPlan, nil
}
