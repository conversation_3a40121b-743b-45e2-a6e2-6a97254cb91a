package apisuiteservicelogic

import (
	"context"
	"fmt"

	"github.com/emirpasic/gods/sets/hashset"
	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/mr"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
)

type RemoveCaseFromApiSuiteLogic struct {
	*BaseLogic
}

func NewRemoveCaseFromApiSuiteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveCaseFromApiSuiteLogic {
	return &RemoveCaseFromApiSuiteLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// RemoveCaseFromApiSuite 从API集合中移除用例
func (l *RemoveCaseFromApiSuiteLogic) RemoveCaseFromApiSuite(in *pb.RemoveCaseFromApiSuiteReq) (
	resp *pb.RemoveCaseFromApiSuiteResp, err error,
) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the suite_id in req
	apiSuite, err := model.CheckApiSuiteBySuiteId(l.ctx, l.svcCtx.ApiSuiteModel, in.GetProjectId(), in.GetSuiteId())
	if err != nil {
		return nil, err
	}

	rrs, err := l.svcCtx.ApiSuiteReferenceModel.FindReferenceBySuiteId(
		l.ctx, in.GetProjectId(), in.GetSuiteId(),
	)
	if err != nil {
		if err != model.ErrNotFound {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find case in api suite with project_id[%s] and suite_id[%s], error: %+v",
				in.GetProjectId(), in.GetSuiteId(), err,
			)
		}
		return nil, errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior,
				fmt.Sprintf("cannot remove case from an empty api suite[%s]", apiSuite.Name),
			),
		)
	}

	recorder := make(map[string]*model.ApiSuiteReferenceRelationship)
	allSet := hashset.New()
	for _, rr := range rrs {
		rr := rr
		allSet.Add(rr.ReferenceId)
		recorder[rr.ReferenceType+":"+rr.ReferenceId] = rr
	}

	removeSet := hashset.New()
	cases := make(map[string]*pb.CaseTypeId)
	for _, item := range in.GetCaseIds() {
		removeSet.Add(item.GetCaseId())
		if _, ok := cases[item.GetCaseType()+":"+item.GetCaseId()]; ok {
			continue
		} else {
			cases[item.GetCaseType()+":"+item.GetCaseId()] = item
		}
	}

	diffSet := removeSet.Difference(allSet)
	if !diffSet.Empty() {
		return nil, errors.WithStack(
			errorx.Err(
				errorx.ProhibitedBehavior, fmt.Sprintf(
					"cannot remove the cases %s which are not in the api suite[%s]",
					logic.ContainerToString(diffSet), apiSuite.Name,
				),
			),
		)
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockApiSuiteProjectIdSuiteIdPrefix, in.GetProjectId(), in.GetSuiteId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			// l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	workers := len(cases)
	if workers > common.ConstMRMaxWorkers {
		workers = common.ConstMRMaxWorkers
	}

	_ = mr.MapReduceVoid[*pb.CaseTypeId, *pb.CaseTypeId](
		func(source chan<- *pb.CaseTypeId) {
			for _, item := range cases {
				source <- item
			}
		}, func(item *pb.CaseTypeId, writer mr.Writer[*pb.CaseTypeId], cancel func(error)) {
			if reference, ok := recorder[item.GetCaseType()+":"+item.GetCaseId()]; !ok {
				err = multierror.Append(
					err, errors.Wrapf(errorx.ErrGrpcInternal, "cannot find the reference of api suite[%s]", item.GetCaseId()),
				)
			} else {
				// remove api suite reference of case
				if e := l.removeApiSuiteReference(l.ctx, nil, reference); e != nil {
					err = multierror.Append(err, e)
				} else {
					writer.Write(item)
				}
			}
		}, func(pipe <-chan *pb.CaseTypeId, cancel func(error)) {
			req := createApiSuiteReferenceInternalReq{
				ProjectId: in.GetProjectId(),
				SuiteId:   in.GetSuiteId(),
				Items: make(
					[]*pb.CaseTypeId, 0, constants.ConstDefaultMakeSliceSize,
				), // 预置切片容量为`constants.ConstDefaultMakeSliceSize`，减少切片扩容的情况
			}

			for item := range pipe {
				req.Items = append(req.Items, item)
			}

			// remove api plan reference of case which is removed from api suite
			if e := l.removeApiPlanReference2(
				l.ctx, nil, createOrRemoveApiPlanReference2InternalReq{
					createApiSuiteReferenceInternalReq: req,
				},
			); e != nil {
				err = multierror.Append(err, e)
			}
		}, mr.WithContext(l.ctx), mr.WithWorkers(workers),
	)

	return &pb.RemoveCaseFromApiSuiteResp{}, err
}
