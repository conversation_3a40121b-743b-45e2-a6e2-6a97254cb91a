package perfcaseservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewPerfCaseLogic struct {
	*BaseLogic
}

func NewViewPerfCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewPerfCaseLogic {
	return &ViewPerfCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewPerfCase 查看压测用例
func (l *ViewPerfCaseLogic) ViewPerfCase(in *pb.ViewPerfCaseReq) (out *pb.ViewPerfCaseResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	perfCase, err := model.CheckPerfCaseByCaseID(l.ctx, l.svcCtx.PerfCaseModel, in.GetProjectId(), in.GetCaseId())
	if err != nil {
		return nil, err
	}

	out = &pb.ViewPerfCaseResp{Case: &pb.PerfCase{}}
	if err = utils.Copy(out.Case, perfCase, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf case to response, perf case: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfCase), err,
		)
	}

	return out, nil
}
