package perfplanservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zyedidia/generic"
	"github.com/zyedidia/generic/hashmap"
	"github.com/zyedidia/generic/set"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyPerfPlanLogic struct {
	*BaseLogic
}

func NewModifyPerfPlanLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyPerfPlanLogic {
	return &ModifyPerfPlanLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyPerfPlan 编辑压测计划
func (l *ModifyPerfPlanLogic) ModifyPerfPlan(in *pb.ModifyPerfPlanReq) (out *pb.ModifyPerfPlanResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the plan_id in req
	origin, err := model.CheckPerfPlanByPlanID(l.ctx, l.svcCtx.PerfPlanModel, in.GetProjectId(), in.GetPlanId())
	if err != nil {
		return nil, err
	}

	// validate the protobuf_config_id in req
	if in.GetProtobufConfigId() != "" {
		if _, err = model.CheckProtobufConfigByConfigID(
			l.ctx, l.svcCtx.ProtobufConfigModel, in.GetProjectId(), in.GetProtobufConfigId(),
		); err != nil {
			return nil, err
		}
	}

	// validate the general_config_id in req
	if in.GetGeneralConfigId() != "" {
		if _, err = model.CheckGeneralConfigByConfigId(
			l.ctx, l.svcCtx.GeneralConfigModel, in.GetProjectId(), in.GetGeneralConfigId(),
		); err != nil {
			return nil, err
		}
	}

	// validate the account_config_id in req
	if in.GetAccountConfigId() != "" {
		if _, err = model.CheckAccountConfigByConfigId(
			l.ctx, l.svcCtx.AccountConfigModel, in.GetProjectId(), in.GetAccountConfigId(),
		); err != nil {
			return nil, err
		}
	}

	var perfPlan *model.PerfPlan
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockPerfPlanProjectIDPlanIDPrefix, in.GetProjectId(), in.GetPlanId())
	fn := func() error {
		perfPlan, err = l.modify(in, origin)
		return err
	}
	if err = caller.LockDo(l.svcCtx.Redis, key, fn); err != nil {
		return nil, err
	}

	out = &pb.ModifyPerfPlanResp{Plan: &pb.PerfPlan{}}
	if err = utils.Copy(out.Plan, perfPlan, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy perf plan to response, perf plan: %s, error: %+v",
			jsonx.MarshalIgnoreError(perfPlan), err,
		)
	}

	return out, nil
}

func (l *ModifyPerfPlanLogic) modify(req *pb.ModifyPerfPlanReq, origin *model.PerfPlan) (*model.PerfPlan, error) {
	// 由于`protojson`只能序列化`[]proto.Message`，而`tags`字段类型为`[]string`，Go Struct跟PB没有大的区别，因此直接使用`jsonx`暂代`protojson`
	tags := jsonx.MarshalToStringIgnoreError(req.GetTags())

	now := time.Now()
	perfPlan := &model.PerfPlan{
		Id:        origin.Id,
		ProjectId: origin.ProjectId,
		PlanId:    origin.PlanId,
		Name:      req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Type: req.GetType().String(),
		Tags: sql.NullString{
			String: tags,
			Valid:  tags != "",
		},
		Protocol: protobuf.GetEnumStringOf(req.GetProtocol()),
		ProtobufConfigId: sql.NullString{
			String: req.GetProtobufConfigId(),
			Valid:  req.GetProtobufConfigId() != "",
		},
		GeneralConfigId: sql.NullString{
			String: req.GetGeneralConfigId(),
			Valid:  req.GetGeneralConfigId() != "",
		},
		AccountConfigId: sql.NullString{
			String: req.GetAccountConfigId(),
			Valid:  req.GetAccountConfigId() != "",
		},
		Duration:  int64(req.GetDuration()),
		TargetEnv: protobuf.GetEnumStringOf(req.GetTargetEnv()),
		Keepalive: protobuf.MarshalJSONToStringIgnoreError(req.GetKeepalive()),
		Delay:     int64(req.GetDelay()),
		State:     int64(pb.CommonState_CS_ENABLE),
		MaintainedBy: sql.NullString{
			String: req.GetMaintainedBy(),
			Valid:  false,
		},
		CreatedBy: origin.CreatedBy,
		UpdatedBy: l.currentUser.Account,
		CreatedAt: origin.CreatedAt,
		UpdatedAt: now,
	}

	if err := l.svcCtx.PerfPlanModel.Trans(
		l.ctx, func(context context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.PerfPlanModel.Update(context, session, perfPlan); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to update values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.PerfPlanModel.Table(), jsonx.MarshalIgnoreError(perfPlan), err,
				)
			}

			// update the case reference of perf plan
			if err := l.updatePerfCase(context, session, perfPlan, req.GetCases()); err != nil {
				return err
			}

			// update the new tag and tag reference of perf plan
			if err := l.createTagLogic.CreateTagAndReferenceForInternal(
				context, session, types.CreateOrUpdateTagReference{
					ProjectId:     perfPlan.ProjectId,
					ReferenceType: common.ConstReferenceTypePerfPlan,
					ReferenceId:   perfPlan.PlanId,
					Tags:          req.GetTags(),
				},
			); err != nil {
				return err
			}

			// update notify item of perf plan
			if err := l.updateNotify(context, session, perfPlan, req.GetLarkChats()); err != nil {
				return err
			}

			return nil
		},
	); err != nil {
		return nil, err
	}

	return perfPlan, nil
}

func (l *ModifyPerfPlanLogic) updatePerfCase(
	ctx context.Context, session sqlx.Session, perfPlan *model.PerfPlan, caseIDs []string,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	rrs, err := l.svcCtx.PerfPlanReferenceModel.FindReferenceByPlanID(ctx, perfPlan.ProjectId, perfPlan.PlanId)
	if err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find reference of perf plan, project_id: %s, plan_id: %s, error: %+v",
			perfPlan.ProjectId, perfPlan.PlanId, err,
		)
	}

	fromMap := hashmap.New[string, *model.PerfPlanReferenceRelationship](
		uint64(len(rrs)), generic.Equals[string], generic.HashString,
	)
	fromSet := set.NewHashset[string](uint64(len(rrs)), generic.Equals[string], generic.HashString)
	for _, rr := range rrs {
		if rr.ReferenceType != common.ConstReferenceTypePerfCase {
			continue
		}

		fromMap.Put(rr.ReferenceId, rr)
		fromSet.Put(rr.ReferenceId)
	}

	toSet := set.NewHashset[string](uint64(len(caseIDs)), generic.Equals[string], generic.HashString, caseIDs...)

	removes := fromSet.Difference(toSet)
	increases := toSet.Difference(fromSet)

	references := make([]*model.PerfPlanReferenceRelationship, 0, increases.Size())
	increases.Each(
		func(key string) {
			references = append(
				references, &model.PerfPlanReferenceRelationship{
					ProjectId:     perfPlan.ProjectId,
					ReferenceType: common.ConstReferenceTypePerfCase,
					ReferenceId:   key,
					PlanId:        perfPlan.PlanId,
					State:         int64(pb.CommonState_CS_ENABLE),
					CreatedBy:     perfPlan.CreatedBy,
					UpdatedBy:     perfPlan.UpdatedBy,
					CreatedAt:     perfPlan.CreatedAt,
					UpdatedAt:     perfPlan.UpdatedAt,
				},
			)
		},
	)

	if err = mr.MapReduceVoid[*model.PerfPlanReferenceRelationship, any](
		func(source chan<- *model.PerfPlanReferenceRelationship) {
			for _, key := range removes.Keys() {
				val, ok := fromMap.Get(key)
				if !ok || val.Id == 0 {
					continue
				}

				source <- val
			}
		}, func(item *model.PerfPlanReferenceRelationship, writer mr.Writer[any], cancel func(error)) {
			var err error
			defer func() {
				if err != nil {
					cancel(err)
				}
			}()

			if err = l.svcCtx.PerfPlanReferenceModel.LogicDelete(ctx, session, item.Id); err != nil {
				err = errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to delete item from table, table: %s, item: %s, error: %+v",
					l.svcCtx.PerfPlanReferenceModel.Table(), jsonx.MarshalIgnoreError(item), err,
				)
				return
			}
		}, func(pipe <-chan any, cancel func(error)) {
		},
	); err != nil {
		return err
	}

	if _, err = l.svcCtx.PerfPlanReferenceModel.BatchInsert(ctx, session, references); err != nil {
		return errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to batch insert values to table, table: %s, values: %s, error: %+v",
			l.svcCtx.PerfPlanReferenceModel.Table(), jsonx.MarshalIgnoreError(references), err,
		)
	}

	return nil
}

func (l *ModifyPerfPlanLogic) updateNotify(
	ctx context.Context, session sqlx.Session, perfPlan *model.PerfPlan, chats []*commonpb.LarkChat,
) error {
	if ctx == nil {
		ctx = l.ctx
	}

	notifyItems := make([]*pb.CreateNotifyItem, 0, len(chats))
	for _, chat := range chats {
		notifyItems = append(
			notifyItems, &pb.CreateNotifyItem{
				ReceiverName: chat.GetName(),
				Receiver:     chat.GetChatId(),
			},
		)
	}

	return l.createNotifyLogic.CreatePlanNotifyForInternal(
		ctx, session, types.CreateOrUpdateNotifyReference{
			ProjectID:   perfPlan.ProjectId,
			PlanID:      perfPlan.PlanId,
			NotifyMode:  pb.NotifyMode_ALWAYS_NOTIFY,
			NotifyType:  pb.NotifyType_LARK_CHAT,
			NotifyItems: notifyItems,
		},
	)
}
