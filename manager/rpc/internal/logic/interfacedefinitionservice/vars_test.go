package interfacedefinitionservicelogic

import (
	"testing"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func TestMarshalSchema(t *testing.T) {
	t.Logf("CommonCallReqSchema: \n%s", jsonx.MarshalToStringIgnoreError(tdsCommonCallReqSchema))
	t.Logf("SearchClientSchema: \n%s", jsonx.MarshalToStringIgnoreError(tdsSearchClientSchema))
	t.Logf("FixDictSchema: \n%s", jsonx.MarshalToStringIgnoreError(tdsFixDictSchema))
	t.Logf("CommonRespSchema: \n%s", jsonx.MarshalToStringIgnoreError(tdsCommonRespSchema))
	t.Logf("ErrorRespSchema: \n%s", jsonx.MarshalToStringIgnoreError(tdsErrorRespSchema))

	var tmpSchema pb.Schema
	err := utils.Copy(&tmpSchema, tdsCommonCallReqSchema)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("from: %p, %p", tdsCommonCallReqSchema, tdsCommonCallReqSchema.Properties)
	t.Logf("to: %p, %p", &tmpSchema, tmpSchema.Properties)

	err = utils.CleanUp(&tmpSchema)
	if err != nil {
		t.Fatal(err)
	}

	err = utils.Copy(&tmpSchema, tdsSearchClientSchema)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("from: %p, %p", tdsSearchClientSchema, tdsSearchClientSchema.Properties)
	t.Logf("to: %p, %p", &tmpSchema, tmpSchema.Properties)
}
