package interfacedefinitionservicelogic

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/emirpasic/gods/sets/hashset"
	"github.com/pkg/errors"
	"github.com/r3labs/diff/v3"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/codes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ModifyInterfaceSchemaLogic struct {
	*BaseLogic
}

func NewModifyInterfaceSchemaLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyInterfaceSchemaLogic {
	return &ModifyInterfaceSchemaLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyInterfaceSchema 编辑接口数据模型
func (l *ModifyInterfaceSchemaLogic) ModifyInterfaceSchema(in *pb.ModifyInterfaceSchemaReq) (resp *pb.ModifyInterfaceSchemaResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	var c *model.Category
	// validate the category_id in req
	if c, err = model.CheckCategoryByCategoryId(l.ctx, l.svcCtx.CategoryModel, in.GetProjectId(), common.ConstCategoryTreeTypeInterfaceSchema, in.GetCategoryId()); err != nil {
		return nil, err
	} else if c.CategoryType != common.ConstCategoryTypeDirectory {
		return nil, errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("the type of parent category[%s] does not support creation of sub category", c.CategoryType)))
	}

	// validate the schema_id in req
	origin, err := model.CheckInterfaceSchemaBySchemaId(l.ctx, l.svcCtx.InterfaceSchemaModel, in.GetProjectId(), in.GetSchemaId())
	if err != nil {
		return nil, err
	}

	// cannot modify builtin schema
	if origin.Mode == common.ConstInterfaceDefinitionCreateModeBuiltin {
		return nil, errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("cannot modify the builtin interface schema[%s]", origin.Name)))
	}

	var change bool
	if change, err = l.hasChange(in, *origin); err != nil {
		return nil, err
	} else if !change {
		l.Logger.Infof("there is no change with interface schema, project_id: %s, schema_id: %s", in.GetProjectId(), in.GetSchemaId())

		resp = &pb.ModifyInterfaceSchemaResp{Schema: &pb.InterfaceSchema{}}
		if err = utils.Copy(resp.Schema, origin, l.converters...); err != nil {
			return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy interface schema[%+v] to response, error: %+v", origin, err)
		}
		return resp, nil
	}

	// cannot modify schema name to the builtin names
	if origin.Name != in.GetName() && stringx.Contains(common.InterfaceSchemaBuiltinCategoryNames, in.GetName()) {
		return nil, errors.WithStack(errorx.Err(errorx.ProhibitedBehavior, fmt.Sprintf("the name[%s] conflicts with the builtin name of interface schema category tree and is not allowed to be used", in.GetName())))
	}

	// acquire redis lock
	key := fmt.Sprintf("%s:%s:%s", common.ConstLockInterfaceSchemaProjectIdSchemaIdPrefix, in.GetProjectId(), in.GetSchemaId())
	lock, err := redislock.NewRedisLockAndAcquire(l.svcCtx.Redis, key, redislock.WithExpire(common.ConstLockExpireTime))
	if err != nil {
		return nil, err
	}
	defer func() {
		// release redis lock
		e := lock.Release()
		if e != nil {
			l.Logger.Error(e)
			if re, ok := errorx.RootError(e); ok && re.Code() != errorx.ReleaseRedisLockFailure && err == nil {
				err = e
			}
		}
	}()

	interfaceSchema, err := l.modify(in, origin)
	if err != nil {
		return nil, err
	}

	resp = &pb.ModifyInterfaceSchemaResp{Schema: &pb.InterfaceSchema{}}
	if err = utils.Copy(resp.Schema, interfaceSchema, l.converters...); err != nil {
		return nil, errors.Wrapf(errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy interface schema[%+v] to response, error: %+v", interfaceSchema, err)
	}

	return resp, nil
}

func (l *ModifyInterfaceSchemaLogic) hasChange(req *pb.ModifyInterfaceSchemaReq, is model.InterfaceSchema) (bool, error) {
	var data pb.Schema
	if err := protobuf.UnmarshalJSONFromString(is.Data, &data); err != nil {
		return false, errors.Wrapf(errorx.Err(errorx.SerializationError, err.Error()), "failed to unmarshal interface schema data[%s], error: %+v", is.Data, err)
	}

	origin := &pb.ModifyInterfaceSchemaReq{
		ProjectId:   is.ProjectId,
		CategoryId:  is.CategoryId,
		SchemaId:    is.SchemaId,
		Name:        is.Name,
		Description: is.Description.String,
		Data:        &data,
	}

	differ, err := diff.NewDiffer()
	if err != nil {
		return false, errors.Wrapf(errorx.Err(errorx.NewObjectFailure, err.Error()), "failed to new a differ object, error: %+v", err)
	}

	if cl, err := differ.Diff(origin, req); err != nil {
		return false, errors.Wrapf(errorx.Err(codes.StructDiffFailure, err.Error()), "failed to diff interface schema[%s], error: %+v", is.Name, err)
	} else if len(cl) != 0 {
		return true, nil
	}

	return false, nil
}

// in order to reduce cyclomatic complexity of ModifyInterfaceSchemaLogic.ModifyInterfaceSchema
func (l *ModifyInterfaceSchemaLogic) modify(req *pb.ModifyInterfaceSchemaReq, origin *model.InterfaceSchema) (*model.InterfaceSchema, error) {
	schema := &model.InterfaceSchema{
		Id:         origin.Id,
		ProjectId:  origin.ProjectId,
		CategoryId: req.GetCategoryId(),
		SchemaId:   origin.SchemaId,
		Name:       req.GetName(),
		Description: sql.NullString{
			String: req.GetDescription(),
			Valid:  req.GetDescription() != "",
		},
		Mode:       origin.Mode,
		ImportType: origin.ImportType,
		Data:       protobuf.MarshalJSONToStringIgnoreError(req.GetData()),
		CreatedBy:  origin.CreatedBy,
		UpdatedBy:  l.currentUser.Account,
		CreatedAt:  origin.CreatedAt,
	}

	// update interface schema in a transaction
	if err := l.svcCtx.InterfaceSchemaModel.Trans(l.ctx, func(context context.Context, session sqlx.Session) error {
		if _, err := l.svcCtx.InterfaceSchemaModel.UpdateTX(context, session, schema); err != nil {
			return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to update table[%s] with values[%+v], error: %+v", l.svcCtx.InterfaceSchemaModel.Table(), schema, err)
		}

		schemaCategory, err := l.svcCtx.CategoryModel.FindOneByNodeId(context, origin.ProjectId, common.ConstCategoryTreeTypeInterfaceSchema, origin.SchemaId)
		if err != nil {
			return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find category with project_id[%s], type[%s] and schema_id[%s], error: %+v", origin.ProjectId, common.ConstCategoryTreeTypeInterfaceSchema, origin.SchemaId, err)
		}

		if schema.Name != origin.Name || schema.Description != origin.Description {
			// modify the interface schema category
			if _, err = l.modifyCategoryLogic.ModifyCategoryForInternal(context, session, &pb.ModifyCategoryReq{
				ProjectId:   req.GetProjectId(),
				Type:        common.ConstCategoryTreeTypeInterfaceSchema,
				CategoryId:  schemaCategory.CategoryId,
				Name:        schema.Name,
				Description: schema.Description.String,
			}); err != nil {
				return err
			}
		}

		if origin.CategoryId != req.GetCategoryId() {
			// move the component group category from c.CategoryId to req.CategoryId
			if err = l.moveCategoryLogic.MoveCategoryTreeForInternal(context, session, &pb.MoveCategoryTreeReq{
				ProjectId:     req.GetProjectId(),
				Type:          common.ConstCategoryTreeTypeInterfaceSchema,
				SourceId:      schemaCategory.CategoryId,
				TargetId:      req.GetCategoryId(),
				BeingModified: true,
			}); err != nil {
				return err
			}
		}

		rrs, err := l.svcCtx.InterfaceSchemaReferenceModel.FindReferenceByReference(context, req.GetProjectId(), common.ConstInterfaceDefinitionTypeSchema, req.GetSchemaId())
		if err != nil && err != model.ErrNotFound {
			return errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to find the reference relationship with project_id[%s] and schema_id[%s], error: %+v", req.GetProjectId(), req.GetSchemaId(), err)
		}

		return l.updateReference(context, session, req, rrs)
	}); err != nil {
		return nil, err
	}

	return schema, nil
}

func (l *ModifyInterfaceSchemaLogic) updateReference(context context.Context, session sqlx.Session, req *pb.ModifyInterfaceSchemaReq, rrs []*model.InterfaceSchemaReferenceRelationship) error {
	if context == nil {
		context = l.ctx
	}

	m := make(map[string]*model.InterfaceSchemaReferenceRelationship, len(rrs))

	oSet := hashset.New()
	for _, rr := range rrs {
		oSet.Add(rr.SchemaId)
		m[rr.SchemaId] = rr
	}

	cSet := hashset.New()
	for _, s := range findReferenceInSchema(req.GetData()) {
		cSet.Add(s)
	}

	// 待删除：在`oSet`中而不在`cSet`中的元素
	dSet := oSet.Difference(cSet)
	// 待新增：在`cSet`中而不在`oSet`中的元素
	iSet := cSet.Difference(oSet)

	// 删除引用关系
	if err := mr.MapReduceVoid[*model.InterfaceSchemaReferenceRelationship, any](func(source chan<- *model.InterfaceSchemaReferenceRelationship) {
		for _, v := range dSet.Values() {
			if s, ok := v.(string); !ok {
				continue
			} else if rs, ok := m[s]; ok {
				source <- rs
			}
		}
	}, func(item *model.InterfaceSchemaReferenceRelationship, writer mr.Writer[any], cancel func(error)) {
		var err error
		defer func() {
			if err != nil {
				cancel(err)
			}
		}()

		item.Deleted = int64(constants.HasDeleted)
		item.DeletedBy = sql.NullString{
			String: l.currentUser.Account,
			Valid:  true,
		}
		item.DeletedAt = sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		}
		if _, err = l.svcCtx.InterfaceSchemaReferenceModel.Update(context, session, item); err != nil {
			err = errors.Wrapf(errorx.Err(errorx.DBError, err.Error()), "failed to update table[%s] with values[%+v], error: %+v", l.svcCtx.InterfaceSchemaReferenceModel.Table(), item, err)
		}
	}, func(pipe <-chan any, cancel func(error)) {
	}, mr.WithContext(l.ctx)); err != nil {
		return err
	}

	// 新增引用关系
	return mr.MapReduceVoid[string, any](func(source chan<- string) {
		for _, v := range iSet.Values() {
			source <- v.(string)
		}
	}, func(item string, writer mr.Writer[any], cancel func(error)) {
		var err error
		defer func() {
			if err != nil {
				cancel(err)
			}
		}()

		err = l.createSchemaReference(context, session, &createSchemaReferenceInternalReq{
			ProjectId:     req.GetProjectId(),
			ReferenceType: common.ConstInterfaceDefinitionTypeSchema,
			ReferenceId:   req.GetSchemaId(),
			SchemaId:      item,
		})
	}, func(pipe <-chan any, cancel func(error)) {
	}, mr.WithContext(l.ctx))
}
