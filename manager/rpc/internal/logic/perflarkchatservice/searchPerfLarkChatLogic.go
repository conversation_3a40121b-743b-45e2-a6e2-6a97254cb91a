package perflarkchatservicelogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchPerfLarkChatLogic struct {
	*BaseLogic
}

func NewSearchPerfLarkChatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchPerfLarkChatLogic {
	return &SearchPerfLarkChatLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchPerfLarkChat 搜索压测通知飞书群组
func (l *SearchPerfLarkChatLogic) SearchPerfLarkChat(in *pb.SearchPerfLarkChatReq) (
	out *pb.SearchPerfLarkChatResp, err error,
) {
	var (
		projectID  = in.GetProjectId()
		condition  = in.GetCondition()
		pagination = in.GetPagination()
		sort       = in.GetSort()
	)

	out = &pb.SearchPerfLarkChatResp{}

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectID); err != nil {
		return nil, err
	}

	req := model.SearchPerfLarkChatReq{
		BaseSearchReq: model.BaseSearchReq{
			ProjectID:  projectID,
			Condition:  condition,
			Pagination: pagination,
			Sort:       rpc.ConvertSortFields(sort),
		},
	}
	count, err := l.svcCtx.PerfLarkChatModel.FindCountBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count pref lark chat, project_id: %s, error: %+v",
			projectID, err,
		)
	}
	out.TotalCount = uint64(count)
	out.TotalPage = 1

	chats, err := l.svcCtx.PerfLarkChatModel.FindAllBySearchReq(l.ctx, req)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find perf lark chat, project_id: %s, error: %+v",
			projectID, err,
		)
	}

	out.Items = make([]*pb.PerfLarkChat, 0, len(chats))
	for _, chat := range chats {
		item := &pb.PerfLarkChat{}
		if err = utils.Copy(item, chat, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy perf lark chat to response, chat: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(chat), err,
			)
		}

		out.Items = append(out.Items, item)
	}

	if pagination != nil {
		out.CurrentPage = pagination.GetCurrentPage()
		out.PageSize = pagination.GetPageSize()
		out.TotalPage = (out.TotalCount + out.PageSize - 1) / out.PageSize // round up
	}

	return out, nil
}
