package notifyservicelogic

import (
	"context"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchPlanNotifyLogic struct {
	*BaseLogic
}

func NewSearchPlanNotifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchPlanNotifyLogic {
	return &SearchPlanNotifyLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchPlanNotifyLogic) SearchPlanNotify(in *pb.SearchPlanNotifyReq) (
	resp *pb.SearchPlanNotifyResp, err error,
) {
	resp = &pb.SearchPlanNotifyResp{}

	selectBuilder, countBuilder := l.svcCtx.NotifyModel.SearchNotifySqlBuilder(
		model.SearchNotifyReq{
			ProjectId:  in.GetProjectId(),
			PlanId:     in.GetPlanId(),
			Condition:  in.GetCondition(),
			Pagination: in.GetPagination(),
			Sort:       rpc.ConvertSortFields(in.GetSort()),
		},
	)
	count, err := l.svcCtx.NotifyModel.FindCountNotify(l.ctx, countBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to count notify in search with project_id[%s] and plan_id[%s], error: %+v",
			in.GetProjectId(), in.GetPlanId(), err,
		)
	}

	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	notifies, err := l.svcCtx.NotifyModel.FindNotify(l.ctx, selectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find notify in search with project_id[%s], error: %+v",
			in.GetProjectId(), err,
		)
	}

	resp.Items = make([]*pb.Notify, 0, len(notifies))
	for _, notify := range notifies {
		item := &pb.Notify{}
		if err = utils.Copy(item, notify, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy notify [%+v] to response, error: %+v",
				notify, err,
			)
		}

		resp.Items = append(resp.Items, item)
	}

	pagination := in.GetPagination()
	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
