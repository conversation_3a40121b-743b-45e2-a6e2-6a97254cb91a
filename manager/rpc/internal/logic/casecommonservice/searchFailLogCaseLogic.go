package casecommonservicelogic

import (
	"context"
	"fmt"
	"math"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/db"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchFailLogCaseLogic struct {
	*BaseLogic
}

func NewSearchFailLogCaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchFailLogCaseLogic {
	return &SearchFailLogCaseLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// SearchFailLogCaseV2 搜索用例失败记录
func (l *SearchFailLogCaseLogic) SearchFailLogCaseV2(in *pb.SearchCaseFailLogReq) (
	resp *pb.SearchCaseFailLogResp, err error,
) {
	resp = &pb.SearchCaseFailLogResp{}

	OrderByStrings := make([]string, 0, len(in.GetSort()))
	for _, field := range in.GetSort() {
		OrderByStrings = append(OrderByStrings, fmt.Sprintf("%s %s", field.Field, field.Order))
	}
	query := db.PageQuery{
		Pagination: in.GetPagination(),
		Condition:  in.GetCondition(),
		OrderBy:    OrderByStrings,
	}
	queries := make([]db.DiyQuery, 0, 1)
	queries = append(
		queries, db.DiyQuery{
			Field: "project_id",
			Value: in.GetProjectId(),
		},
	)
	pageQueryList, err := l.svcCtx.CaseFailStatModel.FindByPageQuery(l.ctx, query, queries)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "FindByPageQuery case with project_id[%s], error: %+v",
			in.GetProjectId(), err,
		)
	}
	pagination := in.GetPagination()
	resp.TotalCount = pageQueryList.Total
	resp.TotalPage = 1

	resp.Items = make([]*pb.FailCase, 0, len(pageQueryList.DataList))
	for i, _Case := range pageQueryList.DataList {
		item := &pb.FailCase{}
		// 查询 api用例数据
		// 查询 接口用例数据
		switch _Case.CaseType {
		case constants.CaseTypeApiCase:
			aCase, err := l.svcCtx.ApiCaseModel.FindLatestOneNoCache(l.ctx, in.GetProjectId(), _Case.CaseId)
			if err != nil {
				return nil, errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()), "FindByPageQuery case with project_id[%s], error: %+v",
					in.GetProjectId(), err,
				)
			}
			if err = utils.Copy(item, aCase, l.converters...); err != nil {
				return nil, errors.Wrapf(
					errorx.Err(errorx.CopyToStructFailure, err.Error()),
					"failed to copy case[%+v] to response, error: %+v",
					_Case, err,
				)
			}
		case constants.CaseTypeInterfaceCase:
			iCase, err := l.svcCtx.InterfaceCaseModel.FindLatestOneNoCache(
				l.ctx, in.GetProjectId(), _Case.BranchId, _Case.CaseId,
			)
			if err != nil {
				return nil, errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()), "FindByPageQuery case with project_id[%s], error: %+v",
					in.GetProjectId(), err,
				)
			}
			if err = utils.Copy(item, iCase, l.converters...); err != nil {
				return nil, errors.Wrapf(
					errorx.Err(errorx.CopyToStructFailure, err.Error()),
					"failed to copy case[%+v] to response, error: %+v",
					_Case, err,
				)
			}
		}
		item.FailCount = uint32(_Case.FailCount)
		item.CaseType = _Case.CaseType

		if pagination != nil {
			item.Rank = uint32(pageQueryList.CurrentPage-1)*uint32(pageQueryList.PageSize) + uint32(i) + 1
		}
		resp.Items = append(resp.Items, item)
	}

	if pagination != nil {
		resp.TotalPage = pageQueryList.Pages
		resp.CurrentPage = pageQueryList.CurrentPage
		resp.PageSize = pageQueryList.PageSize
	}

	return resp, nil
}

// SearchFailLogCase 搜索用例失败记录
func (l *SearchFailLogCaseLogic) SearchFailLogCase(in *pb.SearchCaseFailLogReq) (
	resp *pb.SearchCaseFailLogResp, err error,
) {
	resp = &pb.SearchCaseFailLogResp{}

	req := model.SearchFailLogCaseReq{
		ProjectId:  in.GetProjectId(),
		Condition:  in.GetCondition(),
		Pagination: in.GetPagination(),
		Sort:       rpc.ConvertSortFields(in.GetSort()),
	}

	selectBuilder, countBuilder := l.svcCtx.CaseFailStatModel.GenerateSearchFailLogCaseQuery(req)

	count, err := l.svcCtx.CaseFailStatModel.FindCount(l.ctx, countBuilder.SelectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to count case with project_id[%s], error: %+v",
			in.GetProjectId(), err,
		)
	}
	resp.TotalCount = uint64(count)
	resp.TotalPage = 1

	cases, err := l.svcCtx.CaseFailStatModel.FindNoCacheByQueryV2(l.ctx, selectBuilder.SelectBuilder)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()), "failed to find case with project_id[%s], error: %+v",
			in.GetProjectId(), err,
		)
	}
	pagination := in.GetPagination()

	resp.Items = make([]*pb.FailCase, 0, len(cases))
	for i, _case := range cases {
		item := &pb.FailCase{}
		if err = utils.Copy(item, _case, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()), "failed to copy case[%+v] to response, error: %+v",
				_case, err,
			)
		}
		if pagination != nil {
			item.Rank = uint32(pagination.CurrentPage-1)*uint32(pagination.PageSize) + uint32(i) + 1
		}

		resp.Items = append(resp.Items, item)
	}

	if pagination != nil {
		resp.CurrentPage = pagination.GetCurrentPage()
		resp.PageSize = pagination.GetPageSize()
		resp.TotalPage = uint64(math.Ceil(float64(resp.TotalCount) / float64(resp.PageSize)))
	}

	return resp, nil
}
