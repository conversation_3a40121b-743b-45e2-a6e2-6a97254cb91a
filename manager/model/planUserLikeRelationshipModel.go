package model

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"strings"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/db"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
)

var (
	_ PlanUserLikeRelationshipModel = (*customPlanUserLikeRelationshipModel)(nil)

	planUserLikeRelationshipInsertFields = stringx.Remove(
		planUserLikeRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`",
		"`updated_at`", "`deleted_at`",
	)

	cacheManagerPlanUserLikeRelationshipAccountPlanTypeProjectIdPrefix       = "cache:manager:planUserLikeRelationship:AccountPlanTypeProjectId"
	cacheManagerPlanUserLikeRelationshipAccountProjectIdPlanIdPrefix         = "cache:manager:planUserLikeRelationship:AccountProjectIdPlanId"
	cacheManagerPlanUserLikeRelationshipAccountPlanTypeProjectIdPlanIdPrefix = "cache:manager:planUserLikeRelationship:AccountPlanTypeProjectIdPlanId"
	cacheManagerPlanUserLikeRelationshipPlanIdPrefix                         = "cache:manager:planUserLikeRelationship:PlanId"
)

type SearchPlanLikeSelectBuilder struct {
	squirrel.SelectBuilder
}

type SearchPlanLikeCountBuilder struct {
	squirrel.SelectBuilder
}

type SearchJoinApiPlanItem struct {
	ApiPlan
}

type SearchJoinUiPlanItem struct {
	UiPlan
}

type (
	// PlanUserLikeRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customPlanUserLikeRelationshipModel.
	PlanUserLikeRelationshipModel interface {
		planUserLikeRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *PlanUserLikeRelationship) squirrel.InsertBuilder
		InsertTX(ctx context.Context, session sqlx.Session, data *PlanUserLikeRelationship) (sql.Result, error)
		UpdateBuilder(data *PlanUserLikeRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
			[]*PlanUserLikeRelationship, error,
		)
		FindNoCacheJoinApiPlanByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
			[]*SearchJoinApiPlanItem, error,
		)
		FindNoCacheJoinUiPlanByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
			[]*SearchJoinUiPlanItem, error,
		)
		FindByPageQuery(ctx context.Context, pageQuery db.PageQuery) (db.PageInfo[*PlanUserLikeRelationship], error)
		FindByAccountAndPlanTypeAndProjectIdAndPlanId(
			ctx context.Context, account string, planType pb.PlanType, projectId, planId string,
		) (*PlanUserLikeRelationship, error)
		FindByAccountAndProjectIdAndPlanId(
			ctx context.Context, account, projectId, planId string,
		) (*PlanUserLikeRelationship, error)
		FindByAccountPlanTypeProjectId(
			ctx context.Context, account, projectId string, planType pb.PlanType,
		) ([]*PlanUserLikeRelationship, error)
		FindByPlanId(ctx context.Context, planId string) ([]*PlanUserLikeRelationship, error)
		DeleteByData(ctx context.Context, session sqlx.Session, data *PlanUserLikeRelationship) error
		DeleteByProjectIdAndPlanIdAndPlanType(
			ctx context.Context, session sqlx.Session, projectId, planId string, planType pb.PlanType,
		) error
		GenerateApiPlanJoinPlanLikeSqlBuilderForProjectIdAndQuery(
			projectId, account string, req SearchApiPlanReq,
		) (SearchPlanLikeSelectBuilder, SearchPlanLikeCountBuilder)
		GenerateUiPlanJoinPlanLikeSqlBuilderForProjectIdAndQuery(
			projectId, account string, req SearchUiPlanReq,
		) (SearchPlanLikeSelectBuilder, SearchPlanLikeCountBuilder)
	}

	customPlanUserLikeRelationshipModel struct {
		*defaultPlanUserLikeRelationshipModel
	}
)

// NewPlanUserLikeRelationshipModel returns a model for the database table.
func NewPlanUserLikeRelationshipModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) PlanUserLikeRelationshipModel {
	return &customPlanUserLikeRelationshipModel{
		defaultPlanUserLikeRelationshipModel: newPlanUserLikeRelationshipModel(conn, c, opts...),
	}
}

func (m *customPlanUserLikeRelationshipModel) Table() string {
	return m.table
}

func (m *customPlanUserLikeRelationshipModel) Fields() []string {
	return planUserLikeRelationshipFieldNames
}

func (m *customPlanUserLikeRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customPlanUserLikeRelationshipModel) InsertBuilder(data *PlanUserLikeRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(planUserLikeRelationshipInsertFields...).Values(
		data.ProjectId, data.PlanId, data.Account, data.PlanType, data.Deleted, data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customPlanUserLikeRelationshipModel) UpdateBuilder(data *PlanUserLikeRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customPlanUserLikeRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(planUserLikeRelationshipFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customPlanUserLikeRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customPlanUserLikeRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customPlanUserLikeRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*PlanUserLikeRelationship, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*PlanUserLikeRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customPlanUserLikeRelationshipModel) FindNoCacheJoinApiPlanByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*SearchJoinApiPlanItem, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*SearchJoinApiPlanItem
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customPlanUserLikeRelationshipModel) FindNoCacheJoinUiPlanByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) (
	[]*SearchJoinUiPlanItem, error,
) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*SearchJoinUiPlanItem
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customPlanUserLikeRelationshipModel) InsertTX(
	ctx context.Context, session sqlx.Session, data *PlanUserLikeRelationship,
) (
	sql.Result, error,
) {
	planUserLikeRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPlanUserLikeRelationshipIdPrefix, data.Id)
	planUserLikeRelationshipAccountProjectIdPlanIdKey := fmt.Sprintf(
		"%s:%s:%s:%s", cacheManagerPlanUserLikeRelationshipAccountProjectIdPlanIdPrefix, data.Account, data.ProjectId,
		data.PlanId,
	)
	planUserLikeRelationshipAccountPlanTypeProjectIdPlanIdKey := fmt.Sprintf(
		"%s:%s:%d:%s:%s", cacheManagerPlanUserLikeRelationshipAccountPlanTypeProjectIdPlanIdPrefix, data.Account,
		int(data.PlanType), data.ProjectId, data.PlanId,
	)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query, values, err := m.InsertBuilder(data).ToSql()
			if err != nil {
				return nil, err
			}
			if session != nil {
				return conn.ExecCtx(ctx, query, values...)
			}
			return conn.ExecCtx(ctx, query, values...)
		}, planUserLikeRelationshipIdKey, planUserLikeRelationshipAccountProjectIdPlanIdKey,
		planUserLikeRelationshipAccountPlanTypeProjectIdPlanIdKey,
	)
}

// FindByPageQuery 分页查询列配置列表
func (l *customPlanUserLikeRelationshipModel) FindByPageQuery(
	ctx context.Context, pageQuery db.PageQuery,
) (db.PageInfo[*PlanUserLikeRelationship], error) {
	newPageInfo := db.NewPageInfo[*PlanUserLikeRelationship]()
	err := newPageInfo.FindByPageQuery(ctx, pageQuery, l, l.CachedConn)
	if err != nil {
		return db.PageInfo[*PlanUserLikeRelationship]{}, err
	}
	return *newPageInfo, nil
}

func (m *customPlanUserLikeRelationshipModel) FindByAccountAndPlanTypeAndProjectIdAndPlanId(
	ctx context.Context, account string, planType pb.PlanType, projectId, planId string,
) (*PlanUserLikeRelationship, error) {
	planUserLikeRelationshipAccountPlanTypeProjectIdPlanIdKey := fmt.Sprintf(
		"%s:%s:%d:%s:%s", cacheManagerPlanUserLikeRelationshipAccountPlanTypeProjectIdPlanIdPrefix, account,
		int(planType), projectId, planId,
	)
	var resp PlanUserLikeRelationship
	err := m.QueryRowCtx(
		ctx, &resp, planUserLikeRelationshipAccountPlanTypeProjectIdPlanIdKey,
		func(ctx context.Context, conn sqlx.SqlConn, v any) error {
			query := fmt.Sprintf(
				"select %s from %s where `account` = ? and `plan_type` = ? and `project_id` = ? and `plan_id` = ? and `deleted` = ?  limit 1",
				planUserLikeRelationshipRows,
				m.table,
			)
			return conn.QueryRowCtx(ctx, v, query, account, int(planType), projectId, planId, constants.NotDeleted)
		},
	)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *customPlanUserLikeRelationshipModel) FindByAccountAndProjectIdAndPlanId(
	ctx context.Context, account, projectId, planId string,
) (*PlanUserLikeRelationship, error) {
	planUserLikeRelationshipAccountProjectIdPlanIdKey := fmt.Sprintf(
		"%s:%s:%s:%s", cacheManagerPlanUserLikeRelationshipAccountProjectIdPlanIdPrefix, account, projectId,
		planId,
	)
	var resp PlanUserLikeRelationship
	err := m.QueryRowCtx(
		ctx, &resp, planUserLikeRelationshipAccountProjectIdPlanIdKey,
		func(ctx context.Context, conn sqlx.SqlConn, v any) error {
			query := fmt.Sprintf(
				"select %s from %s where `account` = ? and `project_id` = ? and `plan_id` = ? and `deleted` = ?  limit 1",
				planUserLikeRelationshipRows,
				m.table,
			)
			return conn.QueryRowCtx(ctx, v, query, account, projectId, planId, constants.NotDeleted)
		},
	)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *customPlanUserLikeRelationshipModel) FindByProjectIdAndPlanIdAndPlanType(
	ctx context.Context, projectId, planId string, planType pb.PlanType,
) ([]*PlanUserLikeRelationship, error) {
	resp := make([]*PlanUserLikeRelationship, 0)
	err := m.QueryRowsNoCacheCtx(
		ctx, &resp, fmt.Sprintf(
			"select %s from %s where `project_id` = ? and `plan_id` = ? and `deleted` = ?  and `plan_type` = ?",
			planUserLikeRelationshipRows, m.table,
		), projectId, planId, constants.NotDeleted, int(planType),
	)
	switch err {
	case nil:
		return resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *customPlanUserLikeRelationshipModel) FindByPlanId(
	ctx context.Context, planId string,
) ([]*PlanUserLikeRelationship, error) {
	planUserLikeRelationshipPlanIdKey := fmt.Sprintf(
		"%s:%s", cacheManagerPlanUserLikeRelationshipPlanIdPrefix, planId,
	)
	planUserLikeRelationshipList := make([]*PlanUserLikeRelationship, 0)
	err := m.QueryRowCtx(
		ctx, &planUserLikeRelationshipList, planUserLikeRelationshipPlanIdKey,
		func(ctx context.Context, conn sqlx.SqlConn, v any) error {
			query := fmt.Sprintf(
				"select %s from %s where  `plan_id` = ? and `deleted` = ? ",
				planUserLikeRelationshipRows,
				m.table,
			)
			return conn.QueryRowCtx(ctx, v, query, planId, constants.NotDeleted)
		},
	)
	switch err {
	case nil:
		return planUserLikeRelationshipList, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *customPlanUserLikeRelationshipModel) FindByAccountPlanTypeProjectId(
	ctx context.Context, account, projectId string, planType pb.PlanType,
) ([]*PlanUserLikeRelationship, error) {
	planUserLikeRelationshipPlanIdKey := fmt.Sprintf(
		"%s:%s:%d:%s", cacheManagerPlanUserLikeRelationshipAccountPlanTypeProjectIdPrefix, account, int(planType),
		projectId,
	)
	planUserLikeRelationshipList := make([]*PlanUserLikeRelationship, 0)
	err := m.QueryRowCtx(
		ctx, &planUserLikeRelationshipList, planUserLikeRelationshipPlanIdKey,
		func(ctx context.Context, conn sqlx.SqlConn, v any) error {
			query := fmt.Sprintf(
				"select %s from %s where  `account` = ? and `plan_type` = ?  and `project_id` = ?  and `deleted` = ? ",
				planUserLikeRelationshipRows,
				m.table,
			)
			return conn.QueryRowCtx(ctx, v, query, account, int(planType), projectId, constants.NotDeleted)
		},
	)
	switch err {
	case nil:
		return planUserLikeRelationshipList, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *customPlanUserLikeRelationshipModel) DeleteByData(
	ctx context.Context, session sqlx.Session, data *PlanUserLikeRelationship,
) error {
	planUserLikeRelationshipAccountPlanTypeProjectIdPlanIdKey := fmt.Sprintf(
		"%s:%s:%d:%s:%s", cacheManagerPlanUserLikeRelationshipAccountPlanTypeProjectIdPlanIdPrefix, data.Account,
		int(data.PlanType), data.ProjectId, data.PlanId,
	)
	planUserLikeRelationshipAccountProjectIdPlanIdKey := fmt.Sprintf(
		"%s:%s:%s:%s", cacheManagerPlanUserLikeRelationshipAccountProjectIdPlanIdPrefix, data.Account, data.ProjectId,
		data.PlanId,
	)
	managerplanUserLikeRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerPlanUserLikeRelationshipIdPrefix, data.Id)
	_, err := m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
			if session != nil {
				return session.ExecCtx(ctx, query, data.Id)
			}
			return conn.ExecCtx(ctx, query, data.Id)
		}, managerplanUserLikeRelationshipIdKey, planUserLikeRelationshipAccountProjectIdPlanIdKey,
		planUserLikeRelationshipAccountPlanTypeProjectIdPlanIdKey,
	)
	return err
}

func (m *customPlanUserLikeRelationshipModel) DeleteByProjectIdAndPlanIdAndPlanType(
	ctx context.Context, session sqlx.Session, projectId, planId string, planType pb.PlanType,
) error {
	list, err := m.FindByProjectIdAndPlanIdAndPlanType(ctx, projectId, planId, planType)
	if err != nil {
		return err
	}
	keyStings := make([]string, 0, len(list))
	ids := make([]string, 0, len(list))
	for _, relationship := range list {
		keyStings = append(
			keyStings, fmt.Sprintf(
				"%s:%s:%d:%s:%s", cacheManagerPlanUserLikeRelationshipAccountPlanTypeProjectIdPlanIdPrefix,
				relationship.Account,
				int(relationship.PlanType), relationship.ProjectId, relationship.PlanId,
			), fmt.Sprintf(
				"%s:%s:%s:%s", cacheManagerPlanUserLikeRelationshipAccountProjectIdPlanIdPrefix, relationship.Account,
				relationship.ProjectId,
				relationship.PlanId,
			), fmt.Sprintf("%s%v", cacheManagerPlanUserLikeRelationshipIdPrefix, relationship.Id),
		)
		ids = append(ids, strconv.FormatInt(relationship.Id, 10))
	}
	if len(list) <= 0 {
		return nil
	}
	_, err = m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
			query := fmt.Sprintf("delete from %s where `id` in (%s)", m.table, strings.Join(ids, ","))
			if session != nil {
				return session.ExecCtx(ctx, query)
			}
			return conn.ExecCtx(ctx, query)
		}, keyStings...,
	)
	return err
}

func (m *customPlanUserLikeRelationshipModel) GenerateApiPlanJoinPlanLikeSqlBuilderForProjectIdAndQuery(
	projectId, account string, req SearchApiPlanReq,
) (SearchPlanLikeSelectBuilder, SearchPlanLikeCountBuilder) {
	/*
		SELECT ap.`id`,
		       ap.`project_id`,
		       ap.`plan_id`,
		       ap.`name`,
		       ap.`description`,
		       ap.`priority`,
		       ap.`tags`,
		       ap.`state`,
		       ap.`type`,
		       ap.`purpose`,
		       ap.`cron_expression`,
		       ap.`general_config_id`,
		       ap.`suite_execution_mode`,
		       ap.`case_execution_mode`,
		       ap.`deleted`,
		       ap.`maintained_by`,
		       ap.`created_by`,
		       ap.`updated_by`,
		       ap.`deleted_by`,
		       ap.`created_at`,
		       ap.`updated_at`,
		       ap.`deleted_at`
		FROM `plan_user_like_relationship` AS pulr
		         LEFT JOIN `api_plan` AS ap ON pulr.`plan_id` = ap.`plan_id`
		WHERE pulr.`project_id` = ?
		  AND pulr.`account` = ?
		  AND pulr.`plan_type` = ?
		  AND pulr.`deleted` = ?
		  AND (ap.`name` LIKE ? AND ap.`maintained_by` IN (?))
		ORDER BY pulr.`updated_at` DESC
		LIMIT 20 OFFSET 0;

		SELECT COUNT(*)
		FROM (SELECT ap.`id`,
		             ap.`project_id`,
		             ap.`plan_id`,
		             ap.`name`,
		             ap.`description`,
		             ap.`priority`,
		             ap.`tags`,
		             ap.`state`,
		             ap.`type`,
		             ap.`purpose`,
		             ap.`cron_expression`,
		             ap.`general_config_id`,
		             ap.`suite_execution_mode`,
		             ap.`case_execution_mode`,
		             ap.`deleted`,
		             ap.`maintained_by`,
		             ap.`created_by`,
		             ap.`updated_by`,
		             ap.`deleted_by`,
		             ap.`created_at`,
		             ap.`updated_at`,
		             ap.`deleted_at`
		      FROM `plan_user_like_relationship` AS pulr
		               LEFT JOIN `api_plan` AS ap ON pulr.`plan_id` = ap.`plan_id`
		      WHERE pulr.`project_id` = ?
		        AND pulr.`account` = ?
		        AND pulr.`plan_type` = ?
		        AND pulr.`deleted` = ?) AS t
		WHERE (t.`name` LIKE ? AND t.`maintained_by` IN (?)) ;
	*/
	var sb, scb squirrel.SelectBuilder

	leftJoinOnStr := "pulr.`plan_id` = ap.`plan_id`"
	dbApiPlanModel := new(customApiPlanModel)
	tmp := squirrel.Select(
		utils.AddTableNameToFields(
			"ap", apiPlanFieldNames,
		)...,
	).From(m.table+" AS pulr").InnerJoin(
		fmt.Sprintf("%s AS ap ON %s", apiPlanTableName, leftJoinOnStr),
	).Where(
		"pulr.`project_id` = ? AND pulr.`account` = ?  AND pulr.`plan_type` = ? AND pulr.`deleted` = ?", projectId,
		account, 0, constants.NotDeleted,
	)

	sb = tmp
	scb = squirrel.Select("COUNT(*)").FromSelect(tmp, "t")

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, "ap", sqlbuilder.WithCondition(dbApiPlanModel, req.Condition),
		sqlbuilder.WithPagination(m, req.Pagination),
	)
	sb = sqlbuilder.SearchOptionsWithAlias(sb, "pulr", sqlbuilder.WithSort(m, req.Sort))
	scb = sqlbuilder.SearchOptionsWithAlias(scb, "t", sqlbuilder.WithCondition(dbApiPlanModel, req.Condition))

	return SearchPlanLikeSelectBuilder{SelectBuilder: sb}, SearchPlanLikeCountBuilder{SelectBuilder: scb}
}

func (m *customPlanUserLikeRelationshipModel) GenerateUiPlanJoinPlanLikeSqlBuilderForProjectIdAndQuery(
	projectId, account string, req SearchUiPlanReq,
) (SearchPlanLikeSelectBuilder, SearchPlanLikeCountBuilder) {
	/*

	 */
	var sb, scb squirrel.SelectBuilder
	leftJoinOnStr := "pulr.`plan_id` = up.`plan_id`"
	dbUiPlanModel := new(customUiPlanModel)
	tmp := squirrel.Select(
		utils.AddTableNameToFields(
			"up", uiPlanFieldNames,
		)...,
	).From(m.table+" AS pulr").InnerJoin(
		fmt.Sprintf("%s AS up ON %s", uiPlanTableName, leftJoinOnStr),
	).Where(
		"pulr.`project_id` = ? AND pulr.`account` = ?  AND pulr.`plan_type` = ? AND pulr.`deleted` = ?", projectId,
		account, 1, constants.NotDeleted,
	)

	sb = tmp
	scb = squirrel.Select("COUNT(*)").FromSelect(tmp, "t")

	sb = sqlbuilder.SearchOptionsWithAlias(
		sb, "up", sqlbuilder.WithCondition(dbUiPlanModel, req.Condition),
		sqlbuilder.WithPagination(m, req.Pagination),
	)
	sb = sqlbuilder.SearchOptionsWithAlias(sb, "pulr", sqlbuilder.WithSort(m, req.Sort))
	scb = sqlbuilder.SearchOptionsWithAlias(scb, "t", sqlbuilder.WithCondition(dbUiPlanModel, req.Condition))

	return SearchPlanLikeSelectBuilder{SelectBuilder: sb}, SearchPlanLikeCountBuilder{SelectBuilder: scb}
}
