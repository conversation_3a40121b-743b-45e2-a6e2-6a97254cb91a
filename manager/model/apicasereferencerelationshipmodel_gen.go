// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	apiCaseReferenceRelationshipTableName           = "`api_case_reference_relationship`"
	apiCaseReferenceRelationshipFieldNames          = builder.RawFieldNames(&ApiCaseReferenceRelationship{})
	apiCaseReferenceRelationshipRows                = strings.Join(apiCaseReferenceRelationshipFieldNames, ",")
	apiCaseReferenceRelationshipRowsExpectAutoSet   = strings.Join(stringx.Remove(apiCaseReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	apiCaseReferenceRelationshipRowsWithPlaceHolder = strings.Join(stringx.Remove(apiCaseReferenceRelationshipFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheManagerApiCaseReferenceRelationshipIdPrefix = "cache:manager:apiCaseReferenceRelationship:id:"
)

type (
	apiCaseReferenceRelationshipModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ApiCaseReferenceRelationship) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ApiCaseReferenceRelationship, error)
		Update(ctx context.Context, session sqlx.Session, data *ApiCaseReferenceRelationship) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultApiCaseReferenceRelationshipModel struct {
		sqlc.CachedConn
		table string
	}

	ApiCaseReferenceRelationship struct {
		Id            int64          `db:"id"`
		ProjectId     string         `db:"project_id"`     // 项目ID
		ReferenceType string         `db:"reference_type"` // 引用类型（API集合）
		ReferenceId   string         `db:"reference_id"`   // 引用ID（API集合ID）
		CaseId        string         `db:"case_id"`        // API用例ID
		Deleted       int64          `db:"deleted"`        // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`     // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`     // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`     // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`     // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`     // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`     // 删除时间
	}
)

func newApiCaseReferenceRelationshipModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultApiCaseReferenceRelationshipModel {
	return &defaultApiCaseReferenceRelationshipModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`api_case_reference_relationship`",
	}
}

func (m *defaultApiCaseReferenceRelationshipModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	managerApiCaseReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, managerApiCaseReferenceRelationshipIdKey)
	return err
}

func (m *defaultApiCaseReferenceRelationshipModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	managerApiCaseReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseReferenceRelationshipIdPrefix, id)
	_, err := m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, managerApiCaseReferenceRelationshipIdKey)
	return err
}

func (m *defaultApiCaseReferenceRelationshipModel) FindOne(ctx context.Context, id int64) (*ApiCaseReferenceRelationship, error) {
	managerApiCaseReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseReferenceRelationshipIdPrefix, id)
	var resp ApiCaseReferenceRelationship
	err := m.QueryRowCtx(ctx, &resp, managerApiCaseReferenceRelationshipIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", apiCaseReferenceRelationshipRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultApiCaseReferenceRelationshipModel) Insert(ctx context.Context, session sqlx.Session, data *ApiCaseReferenceRelationship) (sql.Result, error) {
	managerApiCaseReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, apiCaseReferenceRelationshipRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.CaseId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.CaseId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, managerApiCaseReferenceRelationshipIdKey)
}

func (m *defaultApiCaseReferenceRelationshipModel) Update(ctx context.Context, session sqlx.Session, data *ApiCaseReferenceRelationship) (sql.Result, error) {

	managerApiCaseReferenceRelationshipIdKey := fmt.Sprintf("%s%v", cacheManagerApiCaseReferenceRelationshipIdPrefix, data.Id)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, apiCaseReferenceRelationshipRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.CaseId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
		}
		return conn.ExecCtx(ctx, query, data.ProjectId, data.ReferenceType, data.ReferenceId, data.CaseId, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}, managerApiCaseReferenceRelationshipIdKey)
}

func (m *defaultApiCaseReferenceRelationshipModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheManagerApiCaseReferenceRelationshipIdPrefix, primary)
}

func (m *defaultApiCaseReferenceRelationshipModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", apiCaseReferenceRelationshipRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultApiCaseReferenceRelationshipModel) tableName() string {
	return m.table
}
