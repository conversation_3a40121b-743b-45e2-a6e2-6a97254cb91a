package model

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ ProtobufConfigurationReferenceRelationshipModel = (*customProtobufConfigurationReferenceRelationshipModel)(nil)

	protobufConfigurationReferenceRelationshipInsertFields = stringx.Remove(
		protobufConfigurationReferenceRelationshipFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`",
		"`created_at`", "`updated_at`", "`deleted_at`",
	)
)

type (
	// ProtobufConfigurationReferenceRelationshipModel is an interface to be customized, add more methods here,
	// and implement the added methods in customProtobufConfigurationReferenceRelationshipModel.
	ProtobufConfigurationReferenceRelationshipModel interface {
		protobufConfigurationReferenceRelationshipModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *ProtobufConfigurationReferenceRelationship) squirrel.InsertBuilder
		UpdateBuilder(data *ProtobufConfigurationReferenceRelationship) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(
			ctx context.Context, selectBuilder squirrel.SelectBuilder,
		) ([]*ProtobufConfigurationReferenceRelationship, error)

		FindByReference(
			ctx context.Context, projectID, referenceType, referenceID string,
		) ([]*ProtobufConfigurationReferenceRelationship, error)
		FindByConfigID(ctx context.Context, projectID, configID string) (
			[]*ProtobufConfigurationReferenceRelationship, error,
		)
		RemoveByReference(
			ctx context.Context, session sqlx.Session, projectID, referenceType, referenceID string,
		) (sql.Result, error)
	}

	customProtobufConfigurationReferenceRelationshipModel struct {
		*defaultProtobufConfigurationReferenceRelationshipModel

		conn sqlx.SqlConn
	}
)

// NewProtobufConfigurationReferenceRelationshipModel returns a model for the database table.
func NewProtobufConfigurationReferenceRelationshipModel(
	conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option,
) ProtobufConfigurationReferenceRelationshipModel {
	return &customProtobufConfigurationReferenceRelationshipModel{
		defaultProtobufConfigurationReferenceRelationshipModel: newProtobufConfigurationReferenceRelationshipModel(
			conn, c, opts...,
		),
		conn: conn,
	}
}

func (m *customProtobufConfigurationReferenceRelationshipModel) Table() string {
	return m.table
}

func (m *customProtobufConfigurationReferenceRelationshipModel) Fields() []string {
	return protobufConfigurationReferenceRelationshipFieldNames
}

func (m *customProtobufConfigurationReferenceRelationshipModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customProtobufConfigurationReferenceRelationshipModel) InsertBuilder(data *ProtobufConfigurationReferenceRelationship) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(protobufConfigurationReferenceRelationshipInsertFields...).Values(
		data.ProjectId, data.ReferenceType, data.ReferenceId, data.ConfigId, data.Deleted, data.CreatedBy,
		data.UpdatedBy,
	)
}

func (m *customProtobufConfigurationReferenceRelationshipModel) UpdateBuilder(data *ProtobufConfigurationReferenceRelationship) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`deleted`":    data.Deleted,
		"`updated_by`": data.UpdatedBy,
		"`deleted_by`": data.DeletedBy,
		"`deleted_at`": data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customProtobufConfigurationReferenceRelationshipModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(protobufConfigurationReferenceRelationshipFieldNames...).Where(
		"`deleted` = ?", constants.NotDeleted,
	).From(m.table)
}

func (m *customProtobufConfigurationReferenceRelationshipModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customProtobufConfigurationReferenceRelationshipModel) FindCount(
	ctx context.Context, countBuilder squirrel.SelectBuilder,
) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customProtobufConfigurationReferenceRelationshipModel) FindNoCacheByQuery(
	ctx context.Context, selectBuilder squirrel.SelectBuilder,
) ([]*ProtobufConfigurationReferenceRelationship, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*ProtobufConfigurationReferenceRelationship
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customProtobufConfigurationReferenceRelationshipModel) FindByReference(
	ctx context.Context, projectID, referenceType, referenceID string,
) ([]*ProtobufConfigurationReferenceRelationship, error) {
	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().
			Where(
				"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?",
				projectID, referenceType, referenceID,
			),
	)
}

func (m *customProtobufConfigurationReferenceRelationshipModel) FindByConfigID(
	ctx context.Context, projectID, configID string,
) (
	[]*ProtobufConfigurationReferenceRelationship, error,
) {
	return m.FindNoCacheByQuery(
		ctx, m.SelectBuilder().Where("`project_id` = ? AND `config_id` = ?", projectID, configID),
	)
}

func (m *customProtobufConfigurationReferenceRelationshipModel) RemoveByReference(
	ctx context.Context, session sqlx.Session, projectID, referenceType, referenceID string,
) (sql.Result, error) {
	keys := m.getKeysByReference(ctx, projectID, referenceType, referenceID)

	return m.ExecCtx(
		ctx, func(ctx context.Context, conn sqlx.SqlConn) (sql.Result, error) {
			/*
				SQL:
				UPDATE `protobuf_configuration_reference_relationship`
				SET `deleted` = ?, `deleted_by` = ?, `deleted_at` = ?
				WHERE `project_id` = ?
				  AND `reference_type` = ?
				  AND `reference_id` = ?
			*/
			stmt, values, err := squirrel.Update(m.table).
				SetMap(
					squirrel.Eq{
						"`deleted`":    constants.HasDeleted,
						"`deleted_by`": userinfo.FromContext(ctx).Account,
						"`deleted_at`": time.Now(),
					},
				).
				Where(
					"`project_id` = ? AND `reference_type` = ? AND `reference_id` = ?",
					projectID, referenceType, referenceID,
				).
				ToSql()
			if err != nil {
				return nil, err
			}

			if session != nil {
				return session.ExecCtx(ctx, stmt, values...)
			}
			return conn.ExecCtx(ctx, stmt, values...)
		}, keys...,
	)
}

func (m *customProtobufConfigurationReferenceRelationshipModel) getKeysByReference(
	ctx context.Context, projectID, referenceType, referenceID string,
) []string {
	cs, err := m.FindByReference(ctx, projectID, referenceType, referenceID)
	if err != nil {
		return make([]string, 0)
	}

	keys := make([]string, 0, len(cs))
	for _, c := range cs {
		keys = append(keys, fmt.Sprintf("%s%v", cacheManagerProtobufConfigurationReferenceRelationshipIdPrefix, c.Id))
	}

	return keys
}
