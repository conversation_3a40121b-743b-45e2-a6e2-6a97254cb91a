// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

package buf.validate.conformance.cases;

import "buf/validate/validate.proto";

message RequiredProto2ScalarOptional {
  optional string val = 1 [(buf.validate.field).required = true];
}

message RequiredProto2ScalarOptionalDefault {
  optional string val = 1 [
    (buf.validate.field).required = true,
    default = "foo"
  ];
}

message RequiredProto2ScalarRequired {
  required string val = 1 [(buf.validate.field).required = true];
}

message RequiredProto2Message {
  optional Msg val = 1 [(buf.validate.field).required = true];
  message Msg {
    optional string val = 1;
  }
}

message RequiredProto2Oneof {
  oneof val {
    string a = 1 [(buf.validate.field).required = true];
    string b = 2;
  }
}

message RequiredProto2Repeated {
  repeated string val = 1 [(buf.validate.field).required = true];
}

message RequiredProto2Map {
  map<string, string> val = 1 [(buf.validate.field).required = true];
}
