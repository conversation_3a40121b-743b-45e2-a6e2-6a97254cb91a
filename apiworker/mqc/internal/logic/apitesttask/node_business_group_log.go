package apitesttask

type BusinessGroupNode_Log struct {
	*BaseComboNode_Log
}

type BusinessGroupNodeLogWriter struct {
	*BaseComboNodeLogWriter

	log *BusinessGroupNode_Log
}

func NewBusinessGroupNodeLogWriter(bw *BaseComboNodeLogWriter) *BusinessGroupNodeLogWriter {
	// 次生writer类，由于使用的不是interface,因此需要引用基础类
	writer := &BusinessGroupNodeLogWriter{
		BaseComboNodeLogWriter: bw,
		log: &BusinessGroupNode_Log{
			BaseComboNode_Log: bw.log,
		},
	}
	return writer
}

func (writer *BusinessGroupNodeLogWriter) toJson() string {
	return log2Json(writer.log)
}
