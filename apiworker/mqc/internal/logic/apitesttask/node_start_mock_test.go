package apitesttask

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func mock_default_start_execution_data() *managerpb.ApiExecutionData {
	return &managerpb.ApiExecutionData{
		Id:   utils.GenNanoId("START_"),
		Type: managerpb.ApiExecutionDataType_START,
		Data: &managerpb.ApiExecutionData_Start{
			Start: &managerpb.StartComponent{},
		},
	}
}
