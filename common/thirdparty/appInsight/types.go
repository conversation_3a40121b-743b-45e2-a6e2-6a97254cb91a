package appInsight

import "time"

// Config 应用可观测平台（天相）配置
type Config struct {
	BaseURL string `json:",default=http://tt-telemetry-web.ttyuyin.com"` // http://testing-tt-telemetry-web.ttyuyin.com
}

type queryRangeMetricsReq struct {
	MetricName string     `json:"__metric_name"`
	Metric     MetricType `json:"metric"`
	Start      int64      `json:"start"`
	End        int64      `json:"end"`
	Labels     *Labels    `json:"labels"`
	Version    string     `json:"version,default=v2"`
}

type queryRangeMetricsResp struct {
	Code int64         `json:"code"`
	Msg  string        `json:"msg"`
	Data []*MetricData `json:"data"`
}

type Labels struct {
	Namespace   []string `json:"d_namespace"`
	Workload    []string `json:"d_workload"`
	RequestPath []string `json:"request_path,omitempty"`
}

type MetricData struct {
	Key    string         `json:"key"`
	Labels map[string]any `json:"labels"`
	Points [][]float64    `json:"points"`
}

type QueryRangeMetricsReq struct {
	Metric    MetricType `json:"metric"`
	Workload  string     `json:"workload"`
	Namespace string     `json:"namespace"`
	Method    string     `json:"method,omitempty,optional"`
	StartedAt time.Time  `json:"started_at"`
	EndedAt   time.Time  `json:"ended_at"`
}

type QueryRangeMetricsResp struct {
	Points []Point
}

type Point struct {
	Timestamp Time
	Value     SampleValue
}

// Time is the number of seconds since the epoch
// (1970-01-01 00:00 UTC) excluding leap seconds.
type Time int64

// A SampleValue is a representation of a value
// for a given sample at a given time.
type SampleValue float64

type TimeRange struct {
	StartedAt time.Time
	EndedAt   time.Time
}

type QueryResults2MetricsReq struct {
	Metric    MetricType `json:"metric"`
	Workload  string     `json:"workload"`
	Namespace string     `json:"namespace"`
	Method    string     `json:"method,omitempty,optional"`
	// queries count must less than 5
	TimeRanges []TimeRange `json:"time_ranges"`
}

type QueryResults2MetricsResp struct {
	Points []*SampleValue
}

type queryResultMetricsReq struct {
	MetricName string     `json:"__metric_name"`
	Metric     MetricType `json:"metric"`
	Start      int64      `json:"start"`
	End        int64      `json:"end"`
	Labels     *Labels    `json:"labels"`
	Version    string     `json:"version,default=v2"`
}

type queryResultsMetricsReq map[string]*queryResultMetricsReq

type queryResultsMetricsRespData struct {
	Key    string         `json:"key"`
	Labels map[string]any `json:"labels"`
	Point  *SampleValue   `json:"point"`
}

type queryResultsMetricsResp struct {
	Code int64                                     `json:"code"`
	Msg  string                                    `json:"msg"`
	Data map[string][]*queryResultsMetricsRespData `json:"data"`
}
