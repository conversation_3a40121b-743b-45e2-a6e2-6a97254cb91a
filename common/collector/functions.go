package collector

import "time"

func DefaultInterval(dataType DataType) time.Duration {
	switch dataType {
	case CPU:
		return defaultIntervalOfCPU
	case MEMORY:
		return defaultIntervalOfMemory
	case FPS:
		return defaultIntervalOfFPS
	default:
		return time.Second
	}
}

func DefaultSeries(dataType DataType) Series {
	switch dataType {
	case CPU:
		return SeriesOfUsage
	case MEMORY:
		return SeriesOfPSS
	case FPS:
		return SeriesOfFPS
	default:
		return ""
	}
}

func drain[T any](ch <-chan T) {
	// drain the channel
	for range ch {
	}
}
