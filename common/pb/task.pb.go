// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: common/task.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PeriodicPlanTaskInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ProjectId      string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                    // 项目ID
	PlanId         string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                             // 计划ID
	CronExpression string                 `protobuf:"bytes,3,opt,name=cron_expression,json=cronExpression,proto3" json:"cron_expression,omitempty"`     // Cron表达式
	PlanType       PlanType               `protobuf:"varint,4,opt,name=plan_type,json=planType,proto3,enum=common.PlanType" json:"plan_type,omitempty"` // 计划类型
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PeriodicPlanTaskInfo) Reset() {
	*x = PeriodicPlanTaskInfo{}
	mi := &file_common_task_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PeriodicPlanTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PeriodicPlanTaskInfo) ProtoMessage() {}

func (x *PeriodicPlanTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_task_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PeriodicPlanTaskInfo.ProtoReflect.Descriptor instead.
func (*PeriodicPlanTaskInfo) Descriptor() ([]byte, []int) {
	return file_common_task_proto_rawDescGZIP(), []int{0}
}

func (x *PeriodicPlanTaskInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PeriodicPlanTaskInfo) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *PeriodicPlanTaskInfo) GetCronExpression() string {
	if x != nil {
		return x.CronExpression
	}
	return ""
}

func (x *PeriodicPlanTaskInfo) GetPlanType() PlanType {
	if x != nil {
		return x.PlanType
	}
	return PlanType_API
}

type ParsePythonProjectTaskInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ProjectId      string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                // 项目ID
	Config         *GitConfig             `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`                                                       // Git配置
	TriggerMode    TriggerMode            `protobuf:"varint,3,opt,name=trigger_mode,json=triggerMode,proto3,enum=common.TriggerMode" json:"trigger_mode,omitempty"` // 触发方式
	TriggerAccount string                 `protobuf:"bytes,4,opt,name=trigger_account,json=triggerAccount,proto3" json:"trigger_account,omitempty"`                 // 触发者用户ID
	TriggerTime    int64                  `protobuf:"varint,5,opt,name=trigger_time,json=triggerTime,proto3" json:"trigger_time,omitempty"`                         // 触发时间戳（单位：毫秒）
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ParsePythonProjectTaskInfo) Reset() {
	*x = ParsePythonProjectTaskInfo{}
	mi := &file_common_task_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParsePythonProjectTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParsePythonProjectTaskInfo) ProtoMessage() {}

func (x *ParsePythonProjectTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_task_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParsePythonProjectTaskInfo.ProtoReflect.Descriptor instead.
func (*ParsePythonProjectTaskInfo) Descriptor() ([]byte, []int) {
	return file_common_task_proto_rawDescGZIP(), []int{1}
}

func (x *ParsePythonProjectTaskInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ParsePythonProjectTaskInfo) GetConfig() *GitConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *ParsePythonProjectTaskInfo) GetTriggerMode() TriggerMode {
	if x != nil {
		return x.TriggerMode
	}
	return TriggerMode_NULL
}

func (x *ParsePythonProjectTaskInfo) GetTriggerAccount() string {
	if x != nil {
		return x.TriggerAccount
	}
	return ""
}

func (x *ParsePythonProjectTaskInfo) GetTriggerTime() int64 {
	if x != nil {
		return x.TriggerTime
	}
	return 0
}

type ParsePythonProjectTaskResult struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ProjectId      string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                // 项目ID
	ConfigId       string                 `protobuf:"bytes,2,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`                   // Git配置ID
	RootNode       *Node                  `protobuf:"bytes,3,opt,name=root_node,json=rootNode,proto3" json:"root_node,omitempty"`                   // Git项目根节点
	TriggerAccount string                 `protobuf:"bytes,4,opt,name=trigger_account,json=triggerAccount,proto3" json:"trigger_account,omitempty"` // 触发者用户ID
	TriggerTime    int64                  `protobuf:"varint,5,opt,name=trigger_time,json=triggerTime,proto3" json:"trigger_time,omitempty"`         // 触发时间戳（单位：毫秒）
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ParsePythonProjectTaskResult) Reset() {
	*x = ParsePythonProjectTaskResult{}
	mi := &file_common_task_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParsePythonProjectTaskResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParsePythonProjectTaskResult) ProtoMessage() {}

func (x *ParsePythonProjectTaskResult) ProtoReflect() protoreflect.Message {
	mi := &file_common_task_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParsePythonProjectTaskResult.ProtoReflect.Descriptor instead.
func (*ParsePythonProjectTaskResult) Descriptor() ([]byte, []int) {
	return file_common_task_proto_rawDescGZIP(), []int{2}
}

func (x *ParsePythonProjectTaskResult) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *ParsePythonProjectTaskResult) GetConfigId() string {
	if x != nil {
		return x.ConfigId
	}
	return ""
}

func (x *ParsePythonProjectTaskResult) GetRootNode() *Node {
	if x != nil {
		return x.RootNode
	}
	return nil
}

func (x *ParsePythonProjectTaskResult) GetTriggerAccount() string {
	if x != nil {
		return x.TriggerAccount
	}
	return ""
}

func (x *ParsePythonProjectTaskResult) GetTriggerTime() int64 {
	if x != nil {
		return x.TriggerTime
	}
	return 0
}

type ProtobufTarget struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Path          string                 `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`                                     // 路径
	ImportPaths   []string               `protobuf:"bytes,2,rep,name=import_paths,json=importPaths,proto3" json:"import_paths,omitempty"`    // 依赖导入路径列表
	ExcludePaths  []string               `protobuf:"bytes,3,rep,name=exclude_paths,json=excludePaths,proto3" json:"exclude_paths,omitempty"` // 排除路径列表
	ExcludeFiles  []string               `protobuf:"bytes,4,rep,name=exclude_files,json=excludeFiles,proto3" json:"exclude_files,omitempty"` // 排除文件列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProtobufTarget) Reset() {
	*x = ProtobufTarget{}
	mi := &file_common_task_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProtobufTarget) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProtobufTarget) ProtoMessage() {}

func (x *ProtobufTarget) ProtoReflect() protoreflect.Message {
	mi := &file_common_task_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProtobufTarget.ProtoReflect.Descriptor instead.
func (*ProtobufTarget) Descriptor() ([]byte, []int) {
	return file_common_task_proto_rawDescGZIP(), []int{3}
}

func (x *ProtobufTarget) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *ProtobufTarget) GetImportPaths() []string {
	if x != nil {
		return x.ImportPaths
	}
	return nil
}

func (x *ProtobufTarget) GetExcludePaths() []string {
	if x != nil {
		return x.ExcludePaths
	}
	return nil
}

func (x *ProtobufTarget) GetExcludeFiles() []string {
	if x != nil {
		return x.ExcludeFiles
	}
	return nil
}

type PerfTestTaskInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                           // 项目ID
	TaskId          string                 `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                                    // 任务ID
	ExecuteId       string                 `protobuf:"bytes,3,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`                                           // 压测用例执行ID
	SuiteExecuteId  string                 `protobuf:"bytes,4,opt,name=suite_execute_id,json=suiteExecuteId,proto3" json:"suite_execute_id,omitempty"`                          // 压测集合执行ID
	PlanExecuteId   string                 `protobuf:"bytes,5,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`                             // 压测计划执行ID
	TriggerMode     TriggerMode            `protobuf:"varint,6,opt,name=trigger_mode,json=triggerMode,proto3,enum=common.TriggerMode" json:"trigger_mode,omitempty"`            // 触发模式
	ExecuteMode     PerfTaskExecutionMode  `protobuf:"varint,21,opt,name=execute_mode,json=executeMode,proto3,enum=common.PerfTaskExecutionMode" json:"execute_mode,omitempty"` // 执行方式
	Protocol        Protocol               `protobuf:"varint,22,opt,name=protocol,proto3,enum=common.Protocol" json:"protocol,omitempty"`                                       // 协议
	ProtobufTargets []*ProtobufTarget      `protobuf:"bytes,23,rep,name=protobuf_targets,json=protobufTargets,proto3" json:"protobuf_targets,omitempty"`                        // Protobuf相关信息
	GeneralConfig   *GeneralConfig         `protobuf:"bytes,24,opt,name=general_config,json=generalConfig,proto3" json:"general_config,omitempty"`                              // 通用配置
	Keepalive       *PerfKeepalive         `protobuf:"bytes,25,opt,name=keepalive,proto3" json:"keepalive,omitempty"`                                                           // Deprecated: 压测保活参数
	PerfCasePath    string                 `protobuf:"bytes,26,opt,name=perf_case_path,json=perfCasePath,proto3" json:"perf_case_path,omitempty"`                               // Deprecated: 压测用例文件路径
	PerfDataPath    string                 `protobuf:"bytes,27,opt,name=perf_data_path,json=perfDataPath,proto3" json:"perf_data_path,omitempty"`                               // 压测数据文件路径
	Duration        uint32                 `protobuf:"varint,28,opt,name=duration,proto3" json:"duration,omitempty"`                                                            // 压测时长
	Times           uint32                 `protobuf:"varint,29,opt,name=times,proto3" json:"times,omitempty"`                                                                  // 压测次数
	PerfCase        *PerfCaseContentV2     `protobuf:"bytes,30,opt,name=perf_case,json=perfCase,proto3" json:"perf_case,omitempty"`                                             // 压测用例
	RateLimits      *PerfRateLimits        `protobuf:"bytes,31,opt,name=rate_limits,json=rateLimits,proto3" json:"rate_limits,omitempty"`                                       // 压测相关的限流配置（包括：认证接口、心跳接口）
	Timeout         uint32                 `protobuf:"varint,41,opt,name=timeout,proto3" json:"timeout,omitempty"`                                                              // 任务执行的超时时间
	TotalOfVu       uint32                 `protobuf:"varint,42,opt,name=total_of_vu,json=totalOfVu,proto3" json:"total_of_vu,omitempty"`                                       // 总虚拟用户数
	TotalOfLg       uint32                 `protobuf:"varint,43,opt,name=total_of_lg,json=totalOfLg,proto3" json:"total_of_lg,omitempty"`                                       // 总施压机数
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *PerfTestTaskInfo) Reset() {
	*x = PerfTestTaskInfo{}
	mi := &file_common_task_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfTestTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfTestTaskInfo) ProtoMessage() {}

func (x *PerfTestTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_task_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfTestTaskInfo.ProtoReflect.Descriptor instead.
func (*PerfTestTaskInfo) Descriptor() ([]byte, []int) {
	return file_common_task_proto_rawDescGZIP(), []int{4}
}

func (x *PerfTestTaskInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PerfTestTaskInfo) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *PerfTestTaskInfo) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *PerfTestTaskInfo) GetSuiteExecuteId() string {
	if x != nil {
		return x.SuiteExecuteId
	}
	return ""
}

func (x *PerfTestTaskInfo) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *PerfTestTaskInfo) GetTriggerMode() TriggerMode {
	if x != nil {
		return x.TriggerMode
	}
	return TriggerMode_NULL
}

func (x *PerfTestTaskInfo) GetExecuteMode() PerfTaskExecutionMode {
	if x != nil {
		return x.ExecuteMode
	}
	return PerfTaskExecutionMode_PTEM_NULL
}

func (x *PerfTestTaskInfo) GetProtocol() Protocol {
	if x != nil {
		return x.Protocol
	}
	return Protocol_PROTOCOL_NULL
}

func (x *PerfTestTaskInfo) GetProtobufTargets() []*ProtobufTarget {
	if x != nil {
		return x.ProtobufTargets
	}
	return nil
}

func (x *PerfTestTaskInfo) GetGeneralConfig() *GeneralConfig {
	if x != nil {
		return x.GeneralConfig
	}
	return nil
}

func (x *PerfTestTaskInfo) GetKeepalive() *PerfKeepalive {
	if x != nil {
		return x.Keepalive
	}
	return nil
}

func (x *PerfTestTaskInfo) GetPerfCasePath() string {
	if x != nil {
		return x.PerfCasePath
	}
	return ""
}

func (x *PerfTestTaskInfo) GetPerfDataPath() string {
	if x != nil {
		return x.PerfDataPath
	}
	return ""
}

func (x *PerfTestTaskInfo) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *PerfTestTaskInfo) GetTimes() uint32 {
	if x != nil {
		return x.Times
	}
	return 0
}

func (x *PerfTestTaskInfo) GetPerfCase() *PerfCaseContentV2 {
	if x != nil {
		return x.PerfCase
	}
	return nil
}

func (x *PerfTestTaskInfo) GetRateLimits() *PerfRateLimits {
	if x != nil {
		return x.RateLimits
	}
	return nil
}

func (x *PerfTestTaskInfo) GetTimeout() uint32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *PerfTestTaskInfo) GetTotalOfVu() uint32 {
	if x != nil {
		return x.TotalOfVu
	}
	return 0
}

func (x *PerfTestTaskInfo) GetTotalOfLg() uint32 {
	if x != nil {
		return x.TotalOfLg
	}
	return 0
}

type LarkChatDisbandedTaskInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChatId        string                 `protobuf:"bytes,1,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"` // 群组ID
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                   // 群名称
	External      bool                   `protobuf:"varint,3,opt,name=external,proto3" json:"external,omitempty"`          // 是否是外部群
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LarkChatDisbandedTaskInfo) Reset() {
	*x = LarkChatDisbandedTaskInfo{}
	mi := &file_common_task_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LarkChatDisbandedTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LarkChatDisbandedTaskInfo) ProtoMessage() {}

func (x *LarkChatDisbandedTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_task_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LarkChatDisbandedTaskInfo.ProtoReflect.Descriptor instead.
func (*LarkChatDisbandedTaskInfo) Descriptor() ([]byte, []int) {
	return file_common_task_proto_rawDescGZIP(), []int{5}
}

func (x *LarkChatDisbandedTaskInfo) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *LarkChatDisbandedTaskInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LarkChatDisbandedTaskInfo) GetExternal() bool {
	if x != nil {
		return x.External
	}
	return false
}

type LarkChatUpdatedTaskInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChatId        string                 `protobuf:"bytes,1,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"` // 群组ID
	Avatar        string                 `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`               // 群头像URL
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                   // 群名称
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`     // 群描述
	External      bool                   `protobuf:"varint,5,opt,name=external,proto3" json:"external,omitempty"`          // 是否是外部群
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LarkChatUpdatedTaskInfo) Reset() {
	*x = LarkChatUpdatedTaskInfo{}
	mi := &file_common_task_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LarkChatUpdatedTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LarkChatUpdatedTaskInfo) ProtoMessage() {}

func (x *LarkChatUpdatedTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_task_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LarkChatUpdatedTaskInfo.ProtoReflect.Descriptor instead.
func (*LarkChatUpdatedTaskInfo) Descriptor() ([]byte, []int) {
	return file_common_task_proto_rawDescGZIP(), []int{6}
}

func (x *LarkChatUpdatedTaskInfo) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *LarkChatUpdatedTaskInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *LarkChatUpdatedTaskInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LarkChatUpdatedTaskInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LarkChatUpdatedTaskInfo) GetExternal() bool {
	if x != nil {
		return x.External
	}
	return false
}

type LarkChatBotDeletedTaskInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChatId        string                 `protobuf:"bytes,1,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"` // 群组ID
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                   // 群名称
	External      bool                   `protobuf:"varint,3,opt,name=external,proto3" json:"external,omitempty"`          // 是否是外部群
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LarkChatBotDeletedTaskInfo) Reset() {
	*x = LarkChatBotDeletedTaskInfo{}
	mi := &file_common_task_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LarkChatBotDeletedTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LarkChatBotDeletedTaskInfo) ProtoMessage() {}

func (x *LarkChatBotDeletedTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_task_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LarkChatBotDeletedTaskInfo.ProtoReflect.Descriptor instead.
func (*LarkChatBotDeletedTaskInfo) Descriptor() ([]byte, []int) {
	return file_common_task_proto_rawDescGZIP(), []int{7}
}

func (x *LarkChatBotDeletedTaskInfo) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *LarkChatBotDeletedTaskInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LarkChatBotDeletedTaskInfo) GetExternal() bool {
	if x != nil {
		return x.External
	}
	return false
}

type SaveDevicePerfDataTaskInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskId        string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                  // 任务ID
	ExecuteId     string                 `protobuf:"bytes,2,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`                         // UI用例执行ID
	ProjectId     string                 `protobuf:"bytes,3,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                         // 项目ID
	Udid          string                 `protobuf:"bytes,11,opt,name=udid,proto3" json:"udid,omitempty"`                                                   // 设备编号
	Usage         DeviceUsage            `protobuf:"varint,12,opt,name=usage,proto3,enum=common.DeviceUsage" json:"usage,omitempty"`                        // 设备用途
	DataType      PerfDataType           `protobuf:"varint,13,opt,name=data_type,json=dataType,proto3,enum=common.PerfDataType" json:"data_type,omitempty"` // 数据类型
	Interval      int64                  `protobuf:"varint,14,opt,name=interval,proto3" json:"interval,omitempty"`                                          // 采集间隔，单位毫秒
	Series        string                 `protobuf:"bytes,15,opt,name=series,proto3" json:"series,omitempty"`                                               // 指标名称
	Unit          string                 `protobuf:"bytes,16,opt,name=unit,proto3" json:"unit,omitempty"`                                                   // 单位
	X             string                 `protobuf:"bytes,17,opt,name=x,proto3" json:"x,omitempty"`                                                         // X轴数据
	Y             string                 `protobuf:"bytes,18,opt,name=y,proto3" json:"y,omitempty"`                                                         // Y轴数据
	ExecutedBy    string                 `protobuf:"bytes,21,opt,name=executed_by,json=executedBy,proto3" json:"executed_by,omitempty"`                     // 执行者的用户ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveDevicePerfDataTaskInfo) Reset() {
	*x = SaveDevicePerfDataTaskInfo{}
	mi := &file_common_task_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveDevicePerfDataTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveDevicePerfDataTaskInfo) ProtoMessage() {}

func (x *SaveDevicePerfDataTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_task_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveDevicePerfDataTaskInfo.ProtoReflect.Descriptor instead.
func (*SaveDevicePerfDataTaskInfo) Descriptor() ([]byte, []int) {
	return file_common_task_proto_rawDescGZIP(), []int{8}
}

func (x *SaveDevicePerfDataTaskInfo) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *SaveDevicePerfDataTaskInfo) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *SaveDevicePerfDataTaskInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SaveDevicePerfDataTaskInfo) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *SaveDevicePerfDataTaskInfo) GetUsage() DeviceUsage {
	if x != nil {
		return x.Usage
	}
	return DeviceUsage_DU_NULL
}

func (x *SaveDevicePerfDataTaskInfo) GetDataType() PerfDataType {
	if x != nil {
		return x.DataType
	}
	return PerfDataType_PerfDataType_NULL
}

func (x *SaveDevicePerfDataTaskInfo) GetInterval() int64 {
	if x != nil {
		return x.Interval
	}
	return 0
}

func (x *SaveDevicePerfDataTaskInfo) GetSeries() string {
	if x != nil {
		return x.Series
	}
	return ""
}

func (x *SaveDevicePerfDataTaskInfo) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *SaveDevicePerfDataTaskInfo) GetX() string {
	if x != nil {
		return x.X
	}
	return ""
}

func (x *SaveDevicePerfDataTaskInfo) GetY() string {
	if x != nil {
		return x.Y
	}
	return ""
}

func (x *SaveDevicePerfDataTaskInfo) GetExecutedBy() string {
	if x != nil {
		return x.ExecutedBy
	}
	return ""
}

type StabilityCaseTaskInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ProjectId       string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                     // 项目ID
	PlanId          string                 `protobuf:"bytes,2,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                              // 计划ID
	TaskId          string                 `protobuf:"bytes,3,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                              // 任务ID
	ExecuteId       string                 `protobuf:"bytes,4,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`                                     // 稳定性测试用例执行ID
	PlanExecuteId   string                 `protobuf:"bytes,6,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`                       // 稳定性测试计划执行ID
	TriggerMode     TriggerMode            `protobuf:"varint,7,opt,name=trigger_mode,json=triggerMode,proto3,enum=common.TriggerMode" json:"trigger_mode,omitempty"`      // 触发模式
	AccountConfig   *AccountConfig         `protobuf:"bytes,11,opt,name=account_config,json=accountConfig,proto3" json:"account_config,omitempty"`                        // 池账号配置
	DeviceType      DeviceType             `protobuf:"varint,21,opt,name=device_type,json=deviceType,proto3,enum=common.DeviceType" json:"device_type,omitempty"`         // 设备类型（真机、云手机）
	PlatformType    PlatformType           `protobuf:"varint,22,opt,name=platform_type,json=platformType,proto3,enum=common.PlatformType" json:"platform_type,omitempty"` // 平台类型（Android、iOS）
	Udid            string                 `protobuf:"bytes,23,opt,name=udid,proto3" json:"udid,omitempty"`                                                               // 设备编号（固定前缀：表示随机设备，否则：表示指定设备）
	PackageName     string                 `protobuf:"bytes,31,opt,name=package_name,json=packageName,proto3" json:"package_name,omitempty"`                              // 包名
	AppDownloadLink string                 `protobuf:"bytes,32,opt,name=app_download_link,json=appDownloadLink,proto3" json:"app_download_link,omitempty"`                // App下载地址
	Activities      []string               `protobuf:"bytes,33,rep,name=activities,proto3" json:"activities,omitempty"`                                                   // 指定的Activity列表
	CustomScript    *StabilityCustomScript `protobuf:"bytes,34,opt,name=custom_script,json=customScript,proto3" json:"custom_script,omitempty"`                           // 自定义脚本
	Duration        uint32                 `protobuf:"varint,41,opt,name=duration,proto3" json:"duration,omitempty"`                                                      // 执行时长（单位为：分钟）
	ExecutedBy      string                 `protobuf:"bytes,51,opt,name=executed_by,json=executedBy,proto3" json:"executed_by,omitempty"`                                 // 执行者的用户ID
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *StabilityCaseTaskInfo) Reset() {
	*x = StabilityCaseTaskInfo{}
	mi := &file_common_task_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StabilityCaseTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StabilityCaseTaskInfo) ProtoMessage() {}

func (x *StabilityCaseTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_task_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StabilityCaseTaskInfo.ProtoReflect.Descriptor instead.
func (*StabilityCaseTaskInfo) Descriptor() ([]byte, []int) {
	return file_common_task_proto_rawDescGZIP(), []int{9}
}

func (x *StabilityCaseTaskInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *StabilityCaseTaskInfo) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *StabilityCaseTaskInfo) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *StabilityCaseTaskInfo) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *StabilityCaseTaskInfo) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *StabilityCaseTaskInfo) GetTriggerMode() TriggerMode {
	if x != nil {
		return x.TriggerMode
	}
	return TriggerMode_NULL
}

func (x *StabilityCaseTaskInfo) GetAccountConfig() *AccountConfig {
	if x != nil {
		return x.AccountConfig
	}
	return nil
}

func (x *StabilityCaseTaskInfo) GetDeviceType() DeviceType {
	if x != nil {
		return x.DeviceType
	}
	return DeviceType_DT_NULL
}

func (x *StabilityCaseTaskInfo) GetPlatformType() PlatformType {
	if x != nil {
		return x.PlatformType
	}
	return PlatformType_PT_NULL
}

func (x *StabilityCaseTaskInfo) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *StabilityCaseTaskInfo) GetPackageName() string {
	if x != nil {
		return x.PackageName
	}
	return ""
}

func (x *StabilityCaseTaskInfo) GetAppDownloadLink() string {
	if x != nil {
		return x.AppDownloadLink
	}
	return ""
}

func (x *StabilityCaseTaskInfo) GetActivities() []string {
	if x != nil {
		return x.Activities
	}
	return nil
}

func (x *StabilityCaseTaskInfo) GetCustomScript() *StabilityCustomScript {
	if x != nil {
		return x.CustomScript
	}
	return nil
}

func (x *StabilityCaseTaskInfo) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *StabilityCaseTaskInfo) GetExecutedBy() string {
	if x != nil {
		return x.ExecutedBy
	}
	return ""
}

type SaveDeviceStepTaskInfo struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	TaskId    string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`          // 任务ID
	ExecuteId string                 `protobuf:"bytes,2,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"` // 用例执行ID
	ProjectId string                 `protobuf:"bytes,3,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"` // 项目ID
	Udid      string                 `protobuf:"bytes,4,opt,name=udid,proto3" json:"udid,omitempty"`
	// 注：步骤ID在保存时，会自动生成，无需手动设置
	Usage         DeviceUsage            `protobuf:"varint,11,opt,name=usage,proto3,enum=common.DeviceUsage" json:"usage,omitempty"`    // 设备用途
	Stage         TestStage              `protobuf:"varint,12,opt,name=stage,proto3,enum=common.TestStage" json:"stage,omitempty"`      // 阶段
	Index         int64                  `protobuf:"varint,13,opt,name=index,proto3" json:"index,omitempty"`                            // 步骤索引
	Name          string                 `protobuf:"bytes,14,opt,name=name,proto3" json:"name,omitempty"`                               // 步骤名称
	Status        string                 `protobuf:"bytes,15,opt,name=status,proto3" json:"status,omitempty"`                           // 执行状态（结果）
	Content       string                 `protobuf:"bytes,16,opt,name=content,proto3" json:"content,omitempty"`                         // 步骤内容
	Artifacts     []*Artifact            `protobuf:"bytes,17,rep,name=artifacts,proto3" json:"artifacts,omitempty"`                     // 产出物
	ExecutedBy    string                 `protobuf:"bytes,31,opt,name=executed_by,json=executedBy,proto3" json:"executed_by,omitempty"` // 执行者的用户ID
	StartedAt     *timestamppb.Timestamp `protobuf:"bytes,51,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`    // 开始时间
	EndedAt       *timestamppb.Timestamp `protobuf:"bytes,52,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`          // 结束时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SaveDeviceStepTaskInfo) Reset() {
	*x = SaveDeviceStepTaskInfo{}
	mi := &file_common_task_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveDeviceStepTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveDeviceStepTaskInfo) ProtoMessage() {}

func (x *SaveDeviceStepTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_task_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveDeviceStepTaskInfo.ProtoReflect.Descriptor instead.
func (*SaveDeviceStepTaskInfo) Descriptor() ([]byte, []int) {
	return file_common_task_proto_rawDescGZIP(), []int{10}
}

func (x *SaveDeviceStepTaskInfo) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *SaveDeviceStepTaskInfo) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *SaveDeviceStepTaskInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SaveDeviceStepTaskInfo) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *SaveDeviceStepTaskInfo) GetUsage() DeviceUsage {
	if x != nil {
		return x.Usage
	}
	return DeviceUsage_DU_NULL
}

func (x *SaveDeviceStepTaskInfo) GetStage() TestStage {
	if x != nil {
		return x.Stage
	}
	return TestStage_TS_NULL
}

func (x *SaveDeviceStepTaskInfo) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *SaveDeviceStepTaskInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SaveDeviceStepTaskInfo) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SaveDeviceStepTaskInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *SaveDeviceStepTaskInfo) GetArtifacts() []*Artifact {
	if x != nil {
		return x.Artifacts
	}
	return nil
}

func (x *SaveDeviceStepTaskInfo) GetExecutedBy() string {
	if x != nil {
		return x.ExecutedBy
	}
	return ""
}

func (x *SaveDeviceStepTaskInfo) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *SaveDeviceStepTaskInfo) GetEndedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.EndedAt
	}
	return nil
}

var File_common_task_proto protoreflect.FileDescriptor

var file_common_task_proto_rawDesc = []byte{
	0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x6f, 0x64, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x65, 0x72, 0x66, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x73, 0x74, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf3, 0x01, 0x0a, 0x14,
	0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x69, 0x63, 0x50, 0x6c, 0x61, 0x6e, 0x54, 0x61, 0x73, 0x6b,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72, 0x19, 0x32,
	0x17, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x3a,
	0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x31, 0x24, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x2b, 0xfa, 0x42, 0x28, 0x72, 0x26, 0x32, 0x24, 0x28, 0x3f, 0x3a,
	0x5e, 0x70, 0x6c, 0x61, 0x6e, 0x7c, 0x5e, 0x75, 0x69, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x7c, 0x5e,
	0x70, 0x65, 0x72, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x29, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b,
	0x3f, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x72, 0x6f,
	0x6e, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x63, 0x72, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x6c, 0x61, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x22, 0xb4, 0x02, 0x0a, 0x1a, 0x50, 0x61, 0x72, 0x73, 0x65, 0x50, 0x79, 0x74, 0x68, 0x6f,
	0x6e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72, 0x19, 0x32, 0x17, 0x28, 0x3f, 0x3a,
	0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x7c,
	0x5e, 0x31, 0x24, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x33, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x42, 0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x18, 0x01, 0x18, 0x03, 0x52, 0x0b, 0x74, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x32, 0x0a, 0x0f, 0x74, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x0e, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a, 0x0c,
	0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x74, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xae, 0x02, 0x0a, 0x1c, 0x50, 0x61, 0x72,
	0x73, 0x65, 0x50, 0x79, 0x74, 0x68, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa,
	0x42, 0x1b, 0x72, 0x19, 0x32, 0x17, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x31, 0x24, 0x29, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1d, 0xfa, 0x42, 0x1a,
	0x72, 0x18, 0x32, 0x16, 0x28, 0x3f, 0x3a, 0x5e, 0x67, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x09, 0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x6e, 0x6f, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x08, 0x72, 0x6f, 0x6f, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x32, 0x0a, 0x0f, 0x74, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x0e, 0x74,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2a, 0x0a,
	0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xd0, 0x01, 0x0a, 0x0e, 0x50, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x04,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x33, 0x0a, 0x0c, 0x69, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x42,
	0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x18, 0x01, 0x22, 0x04, 0x72, 0x02, 0x10, 0x01, 0x28,
	0x01, 0x52, 0x0b, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x61, 0x74, 0x68, 0x73, 0x12, 0x35,
	0x0a, 0x0d, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x18, 0x01, 0x22,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x50, 0x61, 0x74, 0x68, 0x73, 0x12, 0x35, 0x0a, 0x0d, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x42, 0x10, 0xfa, 0x42,
	0x0d, 0x92, 0x01, 0x0a, 0x18, 0x01, 0x22, 0x04, 0x72, 0x02, 0x10, 0x01, 0x28, 0x01, 0x52, 0x0c,
	0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x22, 0xef, 0x08, 0x0a,
	0x10, 0x50, 0x65, 0x72, 0x66, 0x54, 0x65, 0x73, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72, 0x19, 0x32, 0x17, 0x28, 0x3f,
	0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f,
	0x7c, 0x5e, 0x31, 0x24, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x30, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x72, 0x12, 0x32, 0x10, 0x28, 0x3f, 0x3a, 0x5e, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b,
	0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x28,
	0x3f, 0x3a, 0x5e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b,
	0x3f, 0x29, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x44, 0x0a,
	0x10, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13,
	0x28, 0x3f, 0x3a, 0x5e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x3a, 0x2e,
	0x2b, 0x3f, 0x29, 0x52, 0x0e, 0x73, 0x75, 0x69, 0x74, 0x65, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42,
	0x17, 0x72, 0x15, 0x32, 0x13, 0x28, 0x3f, 0x3a, 0x5e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67,
	0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f,
	0x64, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x18, 0x01, 0x18, 0x02, 0x52, 0x0b,
	0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x4a, 0x0a, 0x0c, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x54,
	0x61, 0x73, 0x6b, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x36, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12,
	0x41, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x5f, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x73, 0x12, 0x46, 0x0a, 0x0e, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x67, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3d, 0x0a, 0x09, 0x6b, 0x65,
	0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x4b, 0x65, 0x65, 0x70, 0x61,
	0x6c, 0x69, 0x76, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x08, 0x01, 0x52, 0x09,
	0x6b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65, 0x12, 0x30, 0x0a, 0x0e, 0x70, 0x65, 0x72,
	0x66, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72, 0x05, 0x10, 0x01, 0xd0, 0x01, 0x01, 0x52, 0x0c, 0x70,
	0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x2d, 0x0a, 0x0e, 0x70,
	0x65, 0x72, 0x66, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x1b, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x65,
	0x72, 0x66, 0x44, 0x61, 0x74, 0x61, 0x50, 0x61, 0x74, 0x68, 0x12, 0x23, 0x0a, 0x08, 0x64, 0x75,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x2a, 0x02, 0x28, 0x00, 0x52, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1d, 0x0a, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x00, 0x52, 0x05, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x40,
	0x0a, 0x09, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x43,
	0x61, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x32, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x70, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65,
	0x12, 0x37, 0x0a, 0x0b, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18,
	0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x65, 0x72, 0x66, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x52, 0x0a, 0x72,
	0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x07, 0x74, 0x69, 0x6d,
	0x65, 0x6f, 0x75, 0x74, 0x18, 0x29, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a,
	0x02, 0x20, 0x00, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x27, 0x0a, 0x0b,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6f, 0x66, 0x5f, 0x76, 0x75, 0x18, 0x2a, 0x20, 0x01, 0x28,
	0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x20, 0x00, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x4f, 0x66, 0x56, 0x75, 0x12, 0x27, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6f,
	0x66, 0x5f, 0x6c, 0x67, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a,
	0x02, 0x20, 0x00, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x66, 0x4c, 0x67, 0x22, 0x64,
	0x0a, 0x19, 0x4c, 0x61, 0x72, 0x6b, 0x43, 0x68, 0x61, 0x74, 0x44, 0x69, 0x73, 0x62, 0x61, 0x6e,
	0x64, 0x65, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x63,
	0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x68,
	0x61, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x22, 0x9c, 0x01, 0x0a, 0x17, 0x4c, 0x61, 0x72, 0x6b, 0x43, 0x68, 0x61,
	0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x17, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x22, 0x65, 0x0a, 0x1a, 0x4c, 0x61, 0x72, 0x6b, 0x43, 0x68, 0x61, 0x74, 0x42,
	0x6f, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x22, 0x9e, 0x04, 0x0a, 0x1a, 0x53,
	0x61, 0x76, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x50, 0x65, 0x72, 0x66, 0x44, 0x61, 0x74,
	0x61, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a, 0x07, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x72,
	0x12, 0x32, 0x10, 0x28, 0x3f, 0x3a, 0x5e, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x3a, 0x2e,
	0x2b, 0x3f, 0x29, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x28, 0x3f, 0x3a, 0x5e, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x09, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72,
	0x19, 0x32, 0x17, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x31, 0x24, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x04,
	0x75, 0x64, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02,
	0x20, 0x00, 0x52, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3b, 0x0a, 0x09, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x08, 0x64, 0x61,
	0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x21, 0x0a, 0x06, 0x73,
	0x65, 0x72, 0x69, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06,
	0x72, 0x04, 0x10, 0x01, 0x18, 0x10, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x65, 0x73, 0x12, 0x1d,
	0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x00, 0x18, 0x08, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x12, 0x17, 0x0a,
	0x01, 0x78, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10,
	0x01, 0x18, 0x40, 0x52, 0x01, 0x78, 0x12, 0x17, 0x0a, 0x01, 0x79, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x01, 0x79, 0x12,
	0x2a, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52,
	0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x42, 0x79, 0x22, 0xae, 0x07, 0x0a, 0x15,
	0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x61, 0x73, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15,
	0x32, 0x13, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64,
	0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x3a, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x21, 0xfa, 0x42, 0x1e, 0x72, 0x1c, 0x32, 0x1a, 0x28, 0x3f, 0x3a, 0x5e, 0x73, 0x74,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x3a,
	0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x07,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0xfa,
	0x42, 0x14, 0x72, 0x12, 0x32, 0x10, 0x28, 0x3f, 0x3a, 0x5e, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69,
	0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x39,
	0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x28, 0x3f, 0x3a, 0x5e, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x09,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x0f, 0x70, 0x6c, 0x61,
	0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x28, 0x3f, 0x3a, 0x5e, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x0d,
	0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x40, 0x0a,
	0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02,
	0x20, 0x00, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x12,
	0x46, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3d, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x0d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f,
	0x72, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x75,
	0x64, 0x69, 0x64, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x04, 0x75, 0x64, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x11, 0x61, 0x70, 0x70, 0x5f, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x0b, 0xfa, 0x42, 0x08, 0x72, 0x06, 0xd0, 0x01, 0x01, 0x88, 0x01, 0x01, 0x52, 0x0f, 0x61, 0x70,
	0x70, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x30, 0x0a,
	0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x21, 0x20, 0x03, 0x28,
	0x09, 0x42, 0x10, 0xfa, 0x42, 0x0d, 0x92, 0x01, 0x0a, 0x18, 0x01, 0x22, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x28, 0x01, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12,
	0x42, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x12, 0x23, 0x0a, 0x08, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x29, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x20, 0x00, 0x52, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x33, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x64, 0x42, 0x79, 0x4a, 0x04, 0x08, 0x05, 0x10, 0x06, 0x52, 0x10, 0x73, 0x75, 0x69, 0x74,
	0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x22, 0xe0, 0x04, 0x0a,
	0x16, 0x53, 0x61, 0x76, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x53, 0x74, 0x65, 0x70, 0x54,
	0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x72, 0x12, 0x32,
	0x10, 0x28, 0x3f, 0x3a, 0x5e, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f,
	0x29, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa,
	0x42, 0x17, 0x72, 0x15, 0x32, 0x13, 0x28, 0x3f, 0x3a, 0x5e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b, 0x72, 0x19, 0x32,
	0x17, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x3a,
	0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x31, 0x24, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x75, 0x64, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x55, 0x73, 0x61, 0x67, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x05, 0x75, 0x73, 0x61, 0x67, 0x65, 0x12, 0x27, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x2e, 0x0a, 0x09, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x73, 0x18, 0x11,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x72,
	0x74, 0x69, 0x66, 0x61, 0x63, 0x74, 0x52, 0x09, 0x61, 0x72, 0x74, 0x69, 0x66, 0x61, 0x63, 0x74,
	0x73, 0x12, 0x2a, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18,
	0x40, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x39, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x33, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x34, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x42,
	0x3c, 0x5a, 0x3a, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69,
	0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_task_proto_rawDescOnce sync.Once
	file_common_task_proto_rawDescData = file_common_task_proto_rawDesc
)

func file_common_task_proto_rawDescGZIP() []byte {
	file_common_task_proto_rawDescOnce.Do(func() {
		file_common_task_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_task_proto_rawDescData)
	})
	return file_common_task_proto_rawDescData
}

var file_common_task_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_common_task_proto_goTypes = []any{
	(*PeriodicPlanTaskInfo)(nil),         // 0: common.PeriodicPlanTaskInfo
	(*ParsePythonProjectTaskInfo)(nil),   // 1: common.ParsePythonProjectTaskInfo
	(*ParsePythonProjectTaskResult)(nil), // 2: common.ParsePythonProjectTaskResult
	(*ProtobufTarget)(nil),               // 3: common.ProtobufTarget
	(*PerfTestTaskInfo)(nil),             // 4: common.PerfTestTaskInfo
	(*LarkChatDisbandedTaskInfo)(nil),    // 5: common.LarkChatDisbandedTaskInfo
	(*LarkChatUpdatedTaskInfo)(nil),      // 6: common.LarkChatUpdatedTaskInfo
	(*LarkChatBotDeletedTaskInfo)(nil),   // 7: common.LarkChatBotDeletedTaskInfo
	(*SaveDevicePerfDataTaskInfo)(nil),   // 8: common.SaveDevicePerfDataTaskInfo
	(*StabilityCaseTaskInfo)(nil),        // 9: common.StabilityCaseTaskInfo
	(*SaveDeviceStepTaskInfo)(nil),       // 10: common.SaveDeviceStepTaskInfo
	(PlanType)(0),                        // 11: common.PlanType
	(*GitConfig)(nil),                    // 12: common.GitConfig
	(TriggerMode)(0),                     // 13: common.TriggerMode
	(*Node)(nil),                         // 14: common.Node
	(PerfTaskExecutionMode)(0),           // 15: common.PerfTaskExecutionMode
	(Protocol)(0),                        // 16: common.Protocol
	(*GeneralConfig)(nil),                // 17: common.GeneralConfig
	(*PerfKeepalive)(nil),                // 18: common.PerfKeepalive
	(*PerfCaseContentV2)(nil),            // 19: common.PerfCaseContentV2
	(*PerfRateLimits)(nil),               // 20: common.PerfRateLimits
	(DeviceUsage)(0),                     // 21: common.DeviceUsage
	(PerfDataType)(0),                    // 22: common.PerfDataType
	(*AccountConfig)(nil),                // 23: common.AccountConfig
	(DeviceType)(0),                      // 24: common.DeviceType
	(PlatformType)(0),                    // 25: common.PlatformType
	(*StabilityCustomScript)(nil),        // 26: common.StabilityCustomScript
	(TestStage)(0),                       // 27: common.TestStage
	(*Artifact)(nil),                     // 28: common.Artifact
	(*timestamppb.Timestamp)(nil),        // 29: google.protobuf.Timestamp
}
var file_common_task_proto_depIdxs = []int32{
	11, // 0: common.PeriodicPlanTaskInfo.plan_type:type_name -> common.PlanType
	12, // 1: common.ParsePythonProjectTaskInfo.config:type_name -> common.GitConfig
	13, // 2: common.ParsePythonProjectTaskInfo.trigger_mode:type_name -> common.TriggerMode
	14, // 3: common.ParsePythonProjectTaskResult.root_node:type_name -> common.Node
	13, // 4: common.PerfTestTaskInfo.trigger_mode:type_name -> common.TriggerMode
	15, // 5: common.PerfTestTaskInfo.execute_mode:type_name -> common.PerfTaskExecutionMode
	16, // 6: common.PerfTestTaskInfo.protocol:type_name -> common.Protocol
	3,  // 7: common.PerfTestTaskInfo.protobuf_targets:type_name -> common.ProtobufTarget
	17, // 8: common.PerfTestTaskInfo.general_config:type_name -> common.GeneralConfig
	18, // 9: common.PerfTestTaskInfo.keepalive:type_name -> common.PerfKeepalive
	19, // 10: common.PerfTestTaskInfo.perf_case:type_name -> common.PerfCaseContentV2
	20, // 11: common.PerfTestTaskInfo.rate_limits:type_name -> common.PerfRateLimits
	21, // 12: common.SaveDevicePerfDataTaskInfo.usage:type_name -> common.DeviceUsage
	22, // 13: common.SaveDevicePerfDataTaskInfo.data_type:type_name -> common.PerfDataType
	13, // 14: common.StabilityCaseTaskInfo.trigger_mode:type_name -> common.TriggerMode
	23, // 15: common.StabilityCaseTaskInfo.account_config:type_name -> common.AccountConfig
	24, // 16: common.StabilityCaseTaskInfo.device_type:type_name -> common.DeviceType
	25, // 17: common.StabilityCaseTaskInfo.platform_type:type_name -> common.PlatformType
	26, // 18: common.StabilityCaseTaskInfo.custom_script:type_name -> common.StabilityCustomScript
	21, // 19: common.SaveDeviceStepTaskInfo.usage:type_name -> common.DeviceUsage
	27, // 20: common.SaveDeviceStepTaskInfo.stage:type_name -> common.TestStage
	28, // 21: common.SaveDeviceStepTaskInfo.artifacts:type_name -> common.Artifact
	29, // 22: common.SaveDeviceStepTaskInfo.started_at:type_name -> google.protobuf.Timestamp
	29, // 23: common.SaveDeviceStepTaskInfo.ended_at:type_name -> google.protobuf.Timestamp
	24, // [24:24] is the sub-list for method output_type
	24, // [24:24] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_common_task_proto_init() }
func file_common_task_proto_init() {
	if File_common_task_proto != nil {
		return
	}
	file_common_config_proto_init()
	file_common_enum_proto_init()
	file_common_node_proto_init()
	file_common_perf_proto_init()
	file_common_stability_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_task_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_task_proto_goTypes,
		DependencyIndexes: file_common_task_proto_depIdxs,
		MessageInfos:      file_common_task_proto_msgTypes,
	}.Build()
	File_common_task_proto = out.File
	file_common_task_proto_rawDesc = nil
	file_common_task_proto_goTypes = nil
	file_common_task_proto_depIdxs = nil
}
