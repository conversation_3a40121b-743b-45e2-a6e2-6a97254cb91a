package model

import (
	"context"

	"github.com/Masterminds/squirrel"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ RuleModel = (*customRuleModel)(nil)

	ruleInsertFields = stringx.Remove(
		ruleFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`",
		"`deleted_at`",
	)
)

type (
	// RuleModel is an interface to be customized, add more methods here,
	// and implement the added methods in customRuleModel.
	RuleModel interface {
		ruleModel
		types.DBModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *Rule) squirrel.InsertBuilder
		UpdateBuilder(data *Rule) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*Rule, error)

		FindAll(ctx context.Context) ([]*Rule, error)
	}

	customRuleModel struct {
		*defaultRuleModel

		conn sqlx.SqlConn
	}
)

// NewRuleModel returns a model for the database table.
func NewRuleModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) RuleModel {
	return &customRuleModel{
		defaultRuleModel: newRuleModel(conn, c, opts...),
		conn:             conn,
	}
}

func (m *customRuleModel) Table() string {
	return m.table
}

func (m *customRuleModel) Fields() []string {
	return ruleFieldNames
}

func (m *customRuleModel) Trans(
	ctx context.Context, fn func(context context.Context, session sqlx.Session) error,
) error {
	return m.TransactCtx(ctx, fn)
}

func (m *customRuleModel) InsertBuilder(data *Rule) squirrel.InsertBuilder {
	return squirrel.Insert(m.table).Columns(ruleInsertFields...).Values(
		data.RuleId, data.Name, data.SourceState, data.DestinationState, data.Duration, data.Times, data.Deleted,
		data.CreatedBy, data.UpdatedBy,
	)
}

func (m *customRuleModel) UpdateBuilder(data *Rule) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		"`name`":              data.Name,
		"`source_state`":      data.SourceState,
		"`destination_state`": data.DestinationState,
		"`duration`":          data.Duration,
		"`times`":             data.Times,
		"`deleted`":           data.Deleted,
		"`updated_by`":        data.UpdatedBy,
		"`deleted_by`":        data.DeletedBy,
		"`deleted_at`":        data.DeletedAt,
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customRuleModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(ruleFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customRuleModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customRuleModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.QueryRowNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customRuleModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) (
	[]*Rule, error,
) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*Rule
	err = m.QueryRowsNoCacheCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}

func (m *customRuleModel) FindAll(ctx context.Context) ([]*Rule, error) {
	return m.FindNoCacheByQuery(ctx, m.SelectBuilder())
}
