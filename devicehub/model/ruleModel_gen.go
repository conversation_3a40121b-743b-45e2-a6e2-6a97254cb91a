// Code generated by goctl. DO NOT EDIT.

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	ruleTableName           = "`rule`"
	ruleFieldNames          = builder.RawFieldNames(&Rule{})
	ruleRows                = strings.Join(ruleFieldNames, ",")
	ruleRowsExpectAutoSet   = strings.Join(stringx.Remove(ruleFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	ruleRowsWithPlaceHolder = strings.Join(stringx.Remove(ruleFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"

	cacheDevicehubRuleIdPrefix     = "cache:devicehub:rule:id:"
	cacheDevicehubRuleRuleIdPrefix = "cache:devicehub:rule:ruleId:"
)

type (
	ruleModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *Rule) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*Rule, error)
		FindOneByRuleId(ctx context.Context, ruleId string) (*Rule, error)
		Update(ctx context.Context, session sqlx.Session, data *Rule) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error

		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultRuleModel struct {
		sqlc.CachedConn
		table string
	}

	Rule struct {
		Id               int64          `db:"id"`                // 自增ID
		RuleId           string         `db:"rule_id"`           // 规则ID
		Name             string         `db:"name"`              // 规则名称
		SourceState      string         `db:"source_state"`      // 原状态（空闲中、使用中、释放中、已下线、已预留）
		DestinationState string         `db:"destination_state"` // 目标状态（空闲中、使用中、释放中、已下线、已预留）
		Duration         int64          `db:"duration"`          // 持续时间（单位：秒）
		Times            int64          `db:"times"`             // 告警次数
		Deleted          int64          `db:"deleted"`           // 逻辑删除标识（未删除、已删除）
		CreatedBy        string         `db:"created_by"`        // 创建者的用户ID
		UpdatedBy        string         `db:"updated_by"`        // 最近一次更新者的用户ID
		DeletedBy        sql.NullString `db:"deleted_by"`        // 删除者的用户ID
		CreatedAt        time.Time      `db:"created_at"`        // 创建时间
		UpdatedAt        time.Time      `db:"updated_at"`        // 更新时间
		DeletedAt        sql.NullTime   `db:"deleted_at"`        // 删除时间
	}
)

func newRuleModel(conn sqlx.SqlConn, c cache.CacheConf, opts ...cache.Option) *defaultRuleModel {
	return &defaultRuleModel{
		CachedConn: sqlc.NewConn(conn, c, opts...),
		table:      "`rule`",
	}
}

func (m *defaultRuleModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	devicehubRuleIdKey := fmt.Sprintf("%s%v", cacheDevicehubRuleIdPrefix, id)
	devicehubRuleRuleIdKey := fmt.Sprintf("%s%v", cacheDevicehubRuleRuleIdPrefix, data.RuleId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, id)
		}
		return conn.ExecCtx(ctx, query, id)
	}, devicehubRuleIdKey, devicehubRuleRuleIdKey)
	return err
}

func (m *defaultRuleModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {
	data, err := m.FindOne(ctx, id)
	if err != nil {
		return err
	}

	devicehubRuleIdKey := fmt.Sprintf("%s%v", cacheDevicehubRuleIdPrefix, id)
	devicehubRuleRuleIdKey := fmt.Sprintf("%s%v", cacheDevicehubRuleRuleIdPrefix, data.RuleId)
	_, err = m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	}, devicehubRuleIdKey, devicehubRuleRuleIdKey)
	return err
}

func (m *defaultRuleModel) FindOne(ctx context.Context, id int64) (*Rule, error) {
	devicehubRuleIdKey := fmt.Sprintf("%s%v", cacheDevicehubRuleIdPrefix, id)
	var resp Rule
	err := m.QueryRowCtx(ctx, &resp, devicehubRuleIdKey, func(ctx context.Context, conn sqlx.SqlConn, v any) error {
		query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", ruleRows, m.table)
		return conn.QueryRowCtx(ctx, v, query, id, constants.NotDeleted)
	})
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultRuleModel) FindOneByRuleId(ctx context.Context, ruleId string) (*Rule, error) {
	devicehubRuleRuleIdKey := fmt.Sprintf("%s%v", cacheDevicehubRuleRuleIdPrefix, ruleId)
	var resp Rule
	err := m.QueryRowIndexCtx(ctx, &resp, devicehubRuleRuleIdKey, m.formatPrimary, func(ctx context.Context, conn sqlx.SqlConn, v any) (i any, e error) {
		query := fmt.Sprintf("select %s from %s where `rule_id` = ? and `deleted` = ? limit 1", ruleRows, m.table)
		if err := conn.QueryRowCtx(ctx, &resp, query, ruleId, constants.NotDeleted); err != nil {
			return nil, err
		}
		return resp.Id, nil
	}, m.queryPrimary)
	switch err {
	case nil:
		return &resp, nil
	case sqlc.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultRuleModel) Insert(ctx context.Context, session sqlx.Session, data *Rule) (sql.Result, error) {
	devicehubRuleIdKey := fmt.Sprintf("%s%v", cacheDevicehubRuleIdPrefix, data.Id)
	devicehubRuleRuleIdKey := fmt.Sprintf("%s%v", cacheDevicehubRuleRuleIdPrefix, data.RuleId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, ruleRowsExpectAutoSet)
		if session != nil {
			return session.ExecCtx(ctx, query, data.RuleId, data.Name, data.SourceState, data.DestinationState, data.Duration, data.Times, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
		}
		return conn.ExecCtx(ctx, query, data.RuleId, data.Name, data.SourceState, data.DestinationState, data.Duration, data.Times, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}, devicehubRuleIdKey, devicehubRuleRuleIdKey)
}

func (m *defaultRuleModel) Update(ctx context.Context, session sqlx.Session, newData *Rule) (sql.Result, error) {
	data, err := m.FindOne(ctx, newData.Id)
	if err != nil {
		return nil, err
	}

	devicehubRuleIdKey := fmt.Sprintf("%s%v", cacheDevicehubRuleIdPrefix, data.Id)
	devicehubRuleRuleIdKey := fmt.Sprintf("%s%v", cacheDevicehubRuleRuleIdPrefix, data.RuleId)

	return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, ruleRowsWithPlaceHolder)
		if session != nil {
			return session.ExecCtx(ctx, query, newData.RuleId, newData.Name, newData.SourceState, newData.DestinationState, newData.Duration, newData.Times, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
		}
		return conn.ExecCtx(ctx, query, newData.RuleId, newData.Name, newData.SourceState, newData.DestinationState, newData.Duration, newData.Times, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}, devicehubRuleIdKey, devicehubRuleRuleIdKey)
}

func (m *defaultRuleModel) formatPrimary(primary any) string {
	return fmt.Sprintf("%s%v", cacheDevicehubRuleIdPrefix, primary)
}

func (m *defaultRuleModel) queryPrimary(ctx context.Context, conn sqlx.SqlConn, v, primary any) error {
	query := fmt.Sprintf("select %s from %s where `id` = ? limit 1", ruleRows, m.table)
	return conn.QueryRowCtx(ctx, v, query, primary)
}

func (m *defaultRuleModel) tableName() string {
	return m.table
}
