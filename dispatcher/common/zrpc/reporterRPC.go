package zrpc

import (
	"context"
	"flag"

	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"

	reporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/reporter"
)

type ReporterRpc struct {
	conf zrpc.RpcClientConf

	Name string
	Cli  reporter.Reporter
}

func NewReporterRpc(conf zrpc.RpcClientConf) *ReporterRpc {
	cli := &ReporterRpc{
		conf: conf,
		Name: conf.Etcd.Key,
		Cli:  reporter.NewReporter(zrpc.MustNewClient(conf, clientinterceptors.UnaryUserInfoClientOption())),
	}

	return cli
}

func (cli *ReporterRpc) Create(ctx context.Context, req *reporter.PutRecordRequest) (
	*reporter.CreateRecordResponse, error,
) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_Create(req)
	}

	return cli.Cli.CreateRecord(ctx, req)
}

func (cli *ReporterRpc) Modify(ctx context.Context, req *reporter.PutRecordRequest) (
	*reporter.ModifyRecordResponse, error,
) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_Modify(req)
	}

	return cli.Cli.ModifyRecord(ctx, req)
}

func (cli *ReporterRpc) Get(
	ctx context.Context, req *reporter.GetExecuteRecordRequest,
) (*reporter.GetExecuteRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_Get(req)
	}

	return cli.Cli.GetExecuteRecord(ctx, req)
}

func (cli *ReporterRpc) GetParent(
	ctx context.Context, req *reporter.GetParentRecordRequest,
) (*reporter.GetParentRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_GetParent(req)
	}

	return cli.Cli.GetParentRecord(ctx, req)
}

func (cli *ReporterRpc) GetChild(
	ctx context.Context, req *reporter.GetChildrenRecordRequest,
) (*reporter.GetChildrenRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_GetChild(req)
	}

	return cli.Cli.GetChildrenRecord(ctx, req)
}

func (cli *ReporterRpc) CreateCaseRecord(
	ctx context.Context, req *reporter.PutRecordRequest,
) (*reporter.CreateRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mockDefaultReporterRPCCreateCaseRecord(req)
	}

	return cli.Cli.CreateRecord(ctx, req)
}

func (cli *ReporterRpc) ModifyCaseRecord(
	ctx context.Context, req *reporter.PutRecordRequest,
) (*reporter.ModifyRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mockDefaultReporterRPCModifyCaseRecord(req)
	}

	return cli.Cli.ModifyRecord(ctx, req)
}

func (cli *ReporterRpc) CreateInterfaceRecord(
	ctx context.Context, req *reporter.PutInterfaceRecordRequest,
) (*reporter.CreateInterfaceRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_CreateInterfaceRecord(req)
	}

	return cli.Cli.CreateInterfaceRecord(ctx, req)
}

func (cli *ReporterRpc) ModifyInterfaceRecord(
	ctx context.Context, req *reporter.PutInterfaceRecordRequest,
) (*reporter.ModifyInterfaceRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_ModifyInterfaceRecord(req)
	}

	return cli.Cli.ModifyInterfaceRecord(ctx, req)
}

func (cli *ReporterRpc) CreateSuiteRecord(
	ctx context.Context, req *reporter.PutSuiteRecordRequest,
) (*reporter.CreateSuiteRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_CreateSuiteRecord(req)
	}

	return cli.Cli.CreateSuiteRecord(ctx, req)
}

func (cli *ReporterRpc) ModifySuiteRecord(
	ctx context.Context, req *reporter.PutSuiteRecordRequest,
) (*reporter.ModifySuiteRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_ModifySuiteRecord(req)
	}

	return cli.Cli.ModifySuiteRecord(ctx, req)
}

func (cli *ReporterRpc) CreateServiceRecord(
	ctx context.Context, req *reporter.PutServiceRecordRequest,
) (*reporter.CreateServiceRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_CreateServiceRecord(req)
	}

	return cli.Cli.CreateServiceRecord(ctx, req)
}

func (cli *ReporterRpc) ModifyServiceRecord(
	ctx context.Context, req *reporter.PutServiceRecordRequest,
) (*reporter.ModifyServiceRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_ModifyServiceRecord(req)
	}

	return cli.Cli.ModifyServiceRecord(ctx, req)
}

func (cli *ReporterRpc) CreatePlanRecord(
	ctx context.Context, req *reporter.PutPlanRecordRequest,
) (*reporter.CreatePlanRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_CreatePlanRecord(req)
	}

	return cli.Cli.CreatePlanRecord(ctx, req)
}

func (cli *ReporterRpc) ModifyPlanRecord(
	ctx context.Context, req *reporter.PutPlanRecordRequest,
) (*reporter.ModifyPlanRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_ModifyPlanRecord(req)
	}

	return cli.Cli.ModifyPlanRecord(ctx, req)
}

func (cli *ReporterRpc) GetPlanRecord(
	ctx context.Context, req *reporter.GetPlanRecordRequest,
) (*reporter.GetPlanRecordResponse, error) {
	if flag.Lookup("test.v") != nil {
		return mock_default_reporterRpc_GetPlanRecord(req)
	}

	return cli.Cli.GetPlanRecord(ctx, req)
}

func (cli *ReporterRpc) GetPlanCasesInfo(
	ctx context.Context, req *reporter.GetPlanCasesInfoRequest,
) (*reporter.GetPlanCasesInfoResponse, error) {
	return cli.Cli.GetPlanCasesInfo(ctx, req)
}
