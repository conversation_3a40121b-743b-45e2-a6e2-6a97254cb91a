syntax = "v1"

import "task_config_types.api"

@server (
    prefix: dispatcher/v1
    group: task
)

service dispatcher {
    @doc "publish task"
    @handler publish
    post /task/publish (PublishReq) returns (PublishResp)

    @doc "publish plan task"
    @handler plan
    post /task/plan (PlanReq) returns (PlanResp)

    @doc "stop task"
    @handler stop
    post /task/stop (StopReq) returns (StopResp)
}
