// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: dispatcher/dispatcher.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"

	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.TriggerMode(0)

	_ = pb1.ApiExecutionDataType(0)
)

// Validate checks the field values on PublishReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PublishReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublishReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PublishReqMultiError, or
// nil if none found.
func (m *PublishReq) ValidateAll() error {
	return m.validate(true)
}

func (m *PublishReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TriggerMode

	// no validation rules for TriggerRule

	// no validation rules for ProjectId

	// no validation rules for TaskId

	// no validation rules for ExecuteId

	// no validation rules for ExecuteType

	// no validation rules for PublishType

	// no validation rules for UserId

	if all {
		switch v := interface{}(m.GetSubEnvInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PublishReqValidationError{
					field:  "SubEnvInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PublishReqValidationError{
					field:  "SubEnvInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubEnvInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PublishReqValidationError{
				field:  "SubEnvInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTriggerUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PublishReqValidationError{
					field:  "TriggerUser",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PublishReqValidationError{
					field:  "TriggerUser",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTriggerUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PublishReqValidationError{
				field:  "TriggerUser",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PurposeType

	if _, ok := pb.PriorityType_name[int32(m.GetPriorityType())]; !ok {
		err := PublishReqValidationError{
			field:  "PriorityType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetApprovers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  fmt.Sprintf("Approvers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  fmt.Sprintf("Approvers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishReqValidationError{
					field:  fmt.Sprintf("Approvers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Debug

	switch v := m.Data.(type) {
	case *PublishReq_Plan:
		if v == nil {
			err := PublishReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPlan()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "Plan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "Plan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPlan()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishReqValidationError{
					field:  "Plan",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PublishReq_Interface:
		if v == nil {
			err := PublishReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInterface()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "Interface",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "Interface",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInterface()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishReqValidationError{
					field:  "Interface",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PublishReq_Suite:
		if v == nil {
			err := PublishReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSuite()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "Suite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "Suite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSuite()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishReqValidationError{
					field:  "Suite",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PublishReq_Case:
		if v == nil {
			err := PublishReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCase()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "Case",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "Case",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCase()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishReqValidationError{
					field:  "Case",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PublishReq_InterfaceCase:
		if v == nil {
			err := PublishReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInterfaceCase()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "InterfaceCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "InterfaceCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInterfaceCase()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishReqValidationError{
					field:  "InterfaceCase",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PublishReq_ComponentGroup:
		if v == nil {
			err := PublishReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetComponentGroup()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "ComponentGroup",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "ComponentGroup",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetComponentGroup()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishReqValidationError{
					field:  "ComponentGroup",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PublishReq_UiCase:
		if v == nil {
			err := PublishReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUiCase()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "UiCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "UiCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUiCase()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishReqValidationError{
					field:  "UiCase",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PublishReq_UiSuite:
		if v == nil {
			err := PublishReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUiSuite()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "UiSuite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "UiSuite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUiSuite()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishReqValidationError{
					field:  "UiSuite",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PublishReq_UiPlan:
		if v == nil {
			err := PublishReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUiPlan()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "UiPlan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "UiPlan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUiPlan()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishReqValidationError{
					field:  "UiPlan",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PublishReq_PrecisionSuite:
		if v == nil {
			err := PublishReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPrecisionSuite()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "PrecisionSuite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "PrecisionSuite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrecisionSuite()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishReqValidationError{
					field:  "PrecisionSuite",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PublishReq_PrecisionInterface:
		if v == nil {
			err := PublishReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPrecisionInterface()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "PrecisionInterface",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "PrecisionInterface",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrecisionInterface()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishReqValidationError{
					field:  "PrecisionInterface",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PublishReq_Service:
		if v == nil {
			err := PublishReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetService()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "Service",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "Service",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetService()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishReqValidationError{
					field:  "Service",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PublishReq_PerfCase:
		if v == nil {
			err := PublishReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPerfCase()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "PerfCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "PerfCase",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPerfCase()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishReqValidationError{
					field:  "PerfCase",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PublishReq_PerfSuite:
		if v == nil {
			err := PublishReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPerfSuite()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "PerfSuite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "PerfSuite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPerfSuite()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishReqValidationError{
					field:  "PerfSuite",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PublishReq_PerfPlan:
		if v == nil {
			err := PublishReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPerfPlan()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "PerfPlan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "PerfPlan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPerfPlan()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishReqValidationError{
					field:  "PerfPlan",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PublishReq_StabilityPlan:
		if v == nil {
			err := PublishReqValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStabilityPlan()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "StabilityPlan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PublishReqValidationError{
						field:  "StabilityPlan",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStabilityPlan()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PublishReqValidationError{
					field:  "StabilityPlan",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return PublishReqMultiError(errors)
	}

	return nil
}

// PublishReqMultiError is an error wrapping multiple validation errors
// returned by PublishReq.ValidateAll() if the designated constraints aren't met.
type PublishReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublishReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublishReqMultiError) AllErrors() []error { return m }

// PublishReqValidationError is the validation error returned by
// PublishReq.Validate if the designated constraints aren't met.
type PublishReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublishReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublishReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublishReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublishReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublishReqValidationError) ErrorName() string { return "PublishReqValidationError" }

// Error satisfies the builtin error interface
func (e PublishReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublishReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublishReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublishReqValidationError{}

// Validate checks the field values on PublishResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PublishResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublishResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PublishRespMultiError, or
// nil if none found.
func (m *PublishResp) ValidateAll() error {
	return m.validate(true)
}

func (m *PublishResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for TaskId

	// no validation rules for ExecuteId

	// no validation rules for Version

	if len(errors) > 0 {
		return PublishRespMultiError(errors)
	}

	return nil
}

// PublishRespMultiError is an error wrapping multiple validation errors
// returned by PublishResp.ValidateAll() if the designated constraints aren't met.
type PublishRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublishRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublishRespMultiError) AllErrors() []error { return m }

// PublishRespValidationError is the validation error returned by
// PublishResp.Validate if the designated constraints aren't met.
type PublishRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublishRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublishRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublishRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublishRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublishRespValidationError) ErrorName() string { return "PublishRespValidationError" }

// Error satisfies the builtin error interface
func (e PublishRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublishResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublishRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublishRespValidationError{}

// Validate checks the field values on StopMetadata with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StopMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StopMetadata with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StopMetadataMultiError, or
// nil if none found.
func (m *StopMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *StopMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _StopMetadata_StopType_NotInLookup[m.GetStopType()]; ok {
		err := StopMetadataValidationError{
			field:  "StopType",
			reason: "value must not be in list [StopType_Unknown]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Reason

	switch v := m.Detail.(type) {
	case *StopMetadata_Rule:
		if v == nil {
			err := StopMetadataValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRule()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StopMetadataValidationError{
						field:  "Rule",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StopMetadataValidationError{
						field:  "Rule",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRule()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StopMetadataValidationError{
					field:  "Rule",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return StopMetadataMultiError(errors)
	}

	return nil
}

// StopMetadataMultiError is an error wrapping multiple validation errors
// returned by StopMetadata.ValidateAll() if the designated constraints aren't met.
type StopMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StopMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StopMetadataMultiError) AllErrors() []error { return m }

// StopMetadataValidationError is the validation error returned by
// StopMetadata.Validate if the designated constraints aren't met.
type StopMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StopMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StopMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StopMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StopMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StopMetadataValidationError) ErrorName() string { return "StopMetadataValidationError" }

// Error satisfies the builtin error interface
func (e StopMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStopMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StopMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StopMetadataValidationError{}

var _StopMetadata_StopType_NotInLookup = map[StopType]struct{}{
	0: {},
}

// Validate checks the field values on StopReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StopReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StopReq with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in StopReqMultiError, or nil if none found.
func (m *StopReq) ValidateAll() error {
	return m.validate(true)
}

func (m *StopReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_StopReq_ProjectId_Pattern.MatchString(m.GetProjectId()) {
		err := StopReqValidationError{
			field:  "ProjectId",
			reason: "value does not match regex pattern \"(?:^project_id:.+?|^1$)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_StopReq_TaskId_Pattern.MatchString(m.GetTaskId()) {
		err := StopReqValidationError{
			field:  "TaskId",
			reason: "value does not match regex pattern \"(?:^task_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_StopReq_Id_Pattern.MatchString(m.GetId()) {
		err := StopReqValidationError{
			field:  "Id",
			reason: "value does not match regex pattern \"(?:component_group|case|interface_case|suite|interface_document|plan|ui_plan|perf_plan)_id:.+?\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _StopReq_ExecuteType_InLookup[m.GetExecuteType()]; !ok {
		err := StopReqValidationError{
			field:  "ExecuteType",
			reason: "value must be in list [API_COMPONENT_GROUP API_CASE API_SUITE API_PLAN INTERFACE_CASE INTERFACE_DOCUMENT UI_CASE UI_SUITE UI_PLAN API_SERVICE PERF_CASE PERF_SUITE PERF_PLAN]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_StopReq_ExecuteId_Pattern.MatchString(m.GetExecuteId()) {
		err := StopReqValidationError{
			field:  "ExecuteId",
			reason: "value does not match regex pattern \"(?:^execute_id:.+?)\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetMetadata() == nil {
		err := StopReqValidationError{
			field:  "Metadata",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StopReqValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StopReqValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StopReqValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StopReqMultiError(errors)
	}

	return nil
}

// StopReqMultiError is an error wrapping multiple validation errors returned
// by StopReq.ValidateAll() if the designated constraints aren't met.
type StopReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StopReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StopReqMultiError) AllErrors() []error { return m }

// StopReqValidationError is the validation error returned by StopReq.Validate
// if the designated constraints aren't met.
type StopReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StopReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StopReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StopReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StopReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StopReqValidationError) ErrorName() string { return "StopReqValidationError" }

// Error satisfies the builtin error interface
func (e StopReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStopReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StopReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StopReqValidationError{}

var _StopReq_ProjectId_Pattern = regexp.MustCompile("(?:^project_id:.+?|^1$)")

var _StopReq_TaskId_Pattern = regexp.MustCompile("(?:^task_id:.+?)")

var _StopReq_Id_Pattern = regexp.MustCompile("(?:component_group|case|interface_case|suite|interface_document|plan|ui_plan|perf_plan)_id:.+?")

var _StopReq_ExecuteType_InLookup = map[pb1.ApiExecutionDataType]struct{}{
	1:  {},
	2:  {},
	3:  {},
	4:  {},
	5:  {},
	6:  {},
	7:  {},
	8:  {},
	9:  {},
	10: {},
	91: {},
	92: {},
	93: {},
}

var _StopReq_ExecuteId_Pattern = regexp.MustCompile("(?:^execute_id:.+?)")

// Validate checks the field values on StopResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StopResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StopResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StopRespMultiError, or nil
// if none found.
func (m *StopResp) ValidateAll() error {
	return m.validate(true)
}

func (m *StopResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return StopRespMultiError(errors)
	}

	return nil
}

// StopRespMultiError is an error wrapping multiple validation errors returned
// by StopResp.ValidateAll() if the designated constraints aren't met.
type StopRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StopRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StopRespMultiError) AllErrors() []error { return m }

// StopRespValidationError is the validation error returned by
// StopResp.Validate if the designated constraints aren't met.
type StopRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StopRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StopRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StopRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StopRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StopRespValidationError) ErrorName() string { return "StopRespValidationError" }

// Error satisfies the builtin error interface
func (e StopRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStopResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StopRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StopRespValidationError{}

// Validate checks the field values on SearchTaskInfoReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchTaskInfoReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchTaskInfoReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchTaskInfoReqMultiError, or nil if none found.
func (m *SearchTaskInfoReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchTaskInfoReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	if all {
		switch v := interface{}(m.GetCondition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchTaskInfoReqValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchTaskInfoReqValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCondition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchTaskInfoReqValidationError{
				field:  "Condition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPagination()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchTaskInfoReqValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchTaskInfoReqValidationError{
					field:  "Pagination",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPagination()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchTaskInfoReqValidationError{
				field:  "Pagination",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSorts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchTaskInfoReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchTaskInfoReqValidationError{
						field:  fmt.Sprintf("Sorts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchTaskInfoReqValidationError{
					field:  fmt.Sprintf("Sorts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchTaskInfoReqMultiError(errors)
	}

	return nil
}

// SearchTaskInfoReqMultiError is an error wrapping multiple validation errors
// returned by SearchTaskInfoReq.ValidateAll() if the designated constraints
// aren't met.
type SearchTaskInfoReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchTaskInfoReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchTaskInfoReqMultiError) AllErrors() []error { return m }

// SearchTaskInfoReqValidationError is the validation error returned by
// SearchTaskInfoReq.Validate if the designated constraints aren't met.
type SearchTaskInfoReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchTaskInfoReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchTaskInfoReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchTaskInfoReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchTaskInfoReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchTaskInfoReqValidationError) ErrorName() string {
	return "SearchTaskInfoReqValidationError"
}

// Error satisfies the builtin error interface
func (e SearchTaskInfoReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchTaskInfoReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchTaskInfoReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchTaskInfoReqValidationError{}

// Validate checks the field values on SearchTaskInfoResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchTaskInfoResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchTaskInfoResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchTaskInfoRespMultiError, or nil if none found.
func (m *SearchTaskInfoResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchTaskInfoResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CurrentPage

	// no validation rules for PageSize

	// no validation rules for TotalCount

	// no validation rules for TotalPage

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchTaskInfoRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchTaskInfoRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchTaskInfoRespValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchTaskInfoRespMultiError(errors)
	}

	return nil
}

// SearchTaskInfoRespMultiError is an error wrapping multiple validation errors
// returned by SearchTaskInfoResp.ValidateAll() if the designated constraints
// aren't met.
type SearchTaskInfoRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchTaskInfoRespMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchTaskInfoRespMultiError) AllErrors() []error { return m }

// SearchTaskInfoRespValidationError is the validation error returned by
// SearchTaskInfoResp.Validate if the designated constraints aren't met.
type SearchTaskInfoRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchTaskInfoRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchTaskInfoRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchTaskInfoRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchTaskInfoRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchTaskInfoRespValidationError) ErrorName() string {
	return "SearchTaskInfoRespValidationError"
}

// Error satisfies the builtin error interface
func (e SearchTaskInfoRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchTaskInfoResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchTaskInfoRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchTaskInfoRespValidationError{}

// Validate checks the field values on SearchTaskInfoItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchTaskInfoItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchTaskInfoItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchTaskInfoItemMultiError, or nil if none found.
func (m *SearchTaskInfoItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchTaskInfoItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for TaskId

	// no validation rules for PlanId

	// no validation rules for PlanName

	// no validation rules for TriggerMode

	if _, ok := pb.ExecuteStatus_name[int32(m.GetTaskExecuteStatus())]; !ok {
		err := SearchTaskInfoItemValidationError{
			field:  "TaskExecuteStatus",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := pb.PriorityType_name[int32(m.GetPriorityType())]; !ok {
		err := SearchTaskInfoItemValidationError{
			field:  "PriorityType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := pb.ExecutedResult_name[int32(m.GetTaskExecutedResult())]; !ok {
		err := SearchTaskInfoItemValidationError{
			field:  "TaskExecutedResult",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for TotalCase

	// no validation rules for FinishedCase

	// no validation rules for SuccessCase

	// no validation rules for TotalSuite

	// no validation rules for FinishedSuite

	// no validation rules for SuccessSuite

	// no validation rules for ExecuteBy

	// no validation rules for CostTime

	// no validation rules for CreateTime

	// no validation rules for WaitTime

	// no validation rules for StartedAt

	// no validation rules for EndedAt

	// no validation rules for ExecuteId

	// no validation rules for ReportViewUrl

	// no validation rules for ReportDownloadUrl

	// no validation rules for UpdateAt

	if len(errors) > 0 {
		return SearchTaskInfoItemMultiError(errors)
	}

	return nil
}

// SearchTaskInfoItemMultiError is an error wrapping multiple validation errors
// returned by SearchTaskInfoItem.ValidateAll() if the designated constraints
// aren't met.
type SearchTaskInfoItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchTaskInfoItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchTaskInfoItemMultiError) AllErrors() []error { return m }

// SearchTaskInfoItemValidationError is the validation error returned by
// SearchTaskInfoItem.Validate if the designated constraints aren't met.
type SearchTaskInfoItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchTaskInfoItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchTaskInfoItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchTaskInfoItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchTaskInfoItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchTaskInfoItemValidationError) ErrorName() string {
	return "SearchTaskInfoItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchTaskInfoItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchTaskInfoItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchTaskInfoItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchTaskInfoItemValidationError{}

// Validate checks the field values on PublishReq_SubEnvInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PublishReq_SubEnvInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublishReq_SubEnvInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PublishReq_SubEnvInfoMultiError, or nil if none found.
func (m *PublishReq_SubEnvInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PublishReq_SubEnvInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubEnvName

	if len(errors) > 0 {
		return PublishReq_SubEnvInfoMultiError(errors)
	}

	return nil
}

// PublishReq_SubEnvInfoMultiError is an error wrapping multiple validation
// errors returned by PublishReq_SubEnvInfo.ValidateAll() if the designated
// constraints aren't met.
type PublishReq_SubEnvInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublishReq_SubEnvInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublishReq_SubEnvInfoMultiError) AllErrors() []error { return m }

// PublishReq_SubEnvInfoValidationError is the validation error returned by
// PublishReq_SubEnvInfo.Validate if the designated constraints aren't met.
type PublishReq_SubEnvInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublishReq_SubEnvInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublishReq_SubEnvInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublishReq_SubEnvInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublishReq_SubEnvInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublishReq_SubEnvInfoValidationError) ErrorName() string {
	return "PublishReq_SubEnvInfoValidationError"
}

// Error satisfies the builtin error interface
func (e PublishReq_SubEnvInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublishReq_SubEnvInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublishReq_SubEnvInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublishReq_SubEnvInfoValidationError{}

// Validate checks the field values on PublishReq_TriggerUser with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PublishReq_TriggerUser) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublishReq_TriggerUser with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PublishReq_TriggerUserMultiError, or nil if none found.
func (m *PublishReq_TriggerUser) ValidateAll() error {
	return m.validate(true)
}

func (m *PublishReq_TriggerUser) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Email

	// no validation rules for Account

	if len(errors) > 0 {
		return PublishReq_TriggerUserMultiError(errors)
	}

	return nil
}

// PublishReq_TriggerUserMultiError is an error wrapping multiple validation
// errors returned by PublishReq_TriggerUser.ValidateAll() if the designated
// constraints aren't met.
type PublishReq_TriggerUserMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublishReq_TriggerUserMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublishReq_TriggerUserMultiError) AllErrors() []error { return m }

// PublishReq_TriggerUserValidationError is the validation error returned by
// PublishReq_TriggerUser.Validate if the designated constraints aren't met.
type PublishReq_TriggerUserValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublishReq_TriggerUserValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublishReq_TriggerUserValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublishReq_TriggerUserValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublishReq_TriggerUserValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublishReq_TriggerUserValidationError) ErrorName() string {
	return "PublishReq_TriggerUserValidationError"
}

// Error satisfies the builtin error interface
func (e PublishReq_TriggerUserValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublishReq_TriggerUser.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublishReq_TriggerUserValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublishReq_TriggerUserValidationError{}

// Validate checks the field values on PublishReq_ApproverUser with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PublishReq_ApproverUser) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublishReq_ApproverUser with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PublishReq_ApproverUserMultiError, or nil if none found.
func (m *PublishReq_ApproverUser) ValidateAll() error {
	return m.validate(true)
}

func (m *PublishReq_ApproverUser) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Email

	// no validation rules for Account

	if len(errors) > 0 {
		return PublishReq_ApproverUserMultiError(errors)
	}

	return nil
}

// PublishReq_ApproverUserMultiError is an error wrapping multiple validation
// errors returned by PublishReq_ApproverUser.ValidateAll() if the designated
// constraints aren't met.
type PublishReq_ApproverUserMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublishReq_ApproverUserMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublishReq_ApproverUserMultiError) AllErrors() []error { return m }

// PublishReq_ApproverUserValidationError is the validation error returned by
// PublishReq_ApproverUser.Validate if the designated constraints aren't met.
type PublishReq_ApproverUserValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublishReq_ApproverUserValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublishReq_ApproverUserValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublishReq_ApproverUserValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublishReq_ApproverUserValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublishReq_ApproverUserValidationError) ErrorName() string {
	return "PublishReq_ApproverUserValidationError"
}

// Error satisfies the builtin error interface
func (e PublishReq_ApproverUserValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublishReq_ApproverUser.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublishReq_ApproverUserValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublishReq_ApproverUserValidationError{}
