// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: dispatcher/dispatcher.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	pb1 "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	rpc "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/rpc"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PublishReq struct {
	state       protoimpl.MessageState   `protogen:"open.v1"`
	TriggerMode pb.TriggerMode           `protobuf:"varint,1,opt,name=trigger_mode,json=triggerMode,proto3,enum=common.TriggerMode" json:"trigger_mode,omitempty"`           // 触发模式
	TriggerRule string                   `protobuf:"bytes,2,opt,name=trigger_rule,json=triggerRule,proto3" json:"trigger_rule,omitempty"`                                    // 触发规则
	ProjectId   string                   `protobuf:"bytes,3,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                          // 项目id
	TaskId      string                   `protobuf:"bytes,4,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                                   // 任务id
	ExecuteId   string                   `protobuf:"bytes,5,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`                                          // 执行id
	ExecuteType pb1.ApiExecutionDataType `protobuf:"varint,6,opt,name=execute_type,json=executeType,proto3,enum=manager.ApiExecutionDataType" json:"execute_type,omitempty"` // 执行类型
	PublishType PublishType              `protobuf:"varint,7,opt,name=publish_type,json=publishType,proto3,enum=dispatcher.PublishType" json:"publish_type,omitempty"`       // 推送类型
	UserId      string                   `protobuf:"bytes,8,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// Types that are valid to be assigned to Data:
	//
	//	*PublishReq_Plan
	//	*PublishReq_Interface
	//	*PublishReq_Suite
	//	*PublishReq_Case
	//	*PublishReq_InterfaceCase
	//	*PublishReq_ComponentGroup
	//	*PublishReq_UiCase
	//	*PublishReq_UiSuite
	//	*PublishReq_UiPlan
	//	*PublishReq_PrecisionSuite
	//	*PublishReq_PrecisionInterface
	//	*PublishReq_Service
	//	*PublishReq_PerfCase
	//	*PublishReq_PerfSuite
	//	*PublishReq_PerfPlan
	//	*PublishReq_StabilityPlan
	Data          isPublishReq_Data          `protobuf_oneof:"data"`
	SubEnvInfo    *PublishReq_SubEnvInfo     `protobuf:"bytes,9,opt,name=sub_env_info,json=subEnvInfo,proto3" json:"sub_env_info,omitempty"`
	TriggerUser   *PublishReq_TriggerUser    `protobuf:"bytes,10,opt,name=trigger_user,json=triggerUser,proto3" json:"trigger_user,omitempty"`
	PurposeType   pb.PurposeType             `protobuf:"varint,11,opt,name=purpose_type,json=purposeType,proto3,enum=common.PurposeType" json:"purpose_type,omitempty"`     // 计划用途
	PriorityType  pb.PriorityType            `protobuf:"varint,12,opt,name=priority_type,json=priorityType,proto3,enum=common.PriorityType" json:"priority_type,omitempty"` // 优先级
	Approvers     []*PublishReq_ApproverUser `protobuf:"bytes,13,rep,name=approvers,proto3" json:"approvers,omitempty"`                                                     // 审批人
	Debug         bool                       `protobuf:"varint,99,opt,name=debug,proto3" json:"debug,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublishReq) Reset() {
	*x = PublishReq{}
	mi := &file_dispatcher_dispatcher_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublishReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishReq) ProtoMessage() {}

func (x *PublishReq) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_dispatcher_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishReq.ProtoReflect.Descriptor instead.
func (*PublishReq) Descriptor() ([]byte, []int) {
	return file_dispatcher_dispatcher_proto_rawDescGZIP(), []int{0}
}

func (x *PublishReq) GetTriggerMode() pb.TriggerMode {
	if x != nil {
		return x.TriggerMode
	}
	return pb.TriggerMode(0)
}

func (x *PublishReq) GetTriggerRule() string {
	if x != nil {
		return x.TriggerRule
	}
	return ""
}

func (x *PublishReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PublishReq) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *PublishReq) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *PublishReq) GetExecuteType() pb1.ApiExecutionDataType {
	if x != nil {
		return x.ExecuteType
	}
	return pb1.ApiExecutionDataType(0)
}

func (x *PublishReq) GetPublishType() PublishType {
	if x != nil {
		return x.PublishType
	}
	return PublishType_PublishType_UNKNOWN
}

func (x *PublishReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PublishReq) GetData() isPublishReq_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *PublishReq) GetPlan() *PlanPublishInfo {
	if x != nil {
		if x, ok := x.Data.(*PublishReq_Plan); ok {
			return x.Plan
		}
	}
	return nil
}

func (x *PublishReq) GetInterface() *InterfaceDocumentPublishInfo {
	if x != nil {
		if x, ok := x.Data.(*PublishReq_Interface); ok {
			return x.Interface
		}
	}
	return nil
}

func (x *PublishReq) GetSuite() *SuitePublishInfo {
	if x != nil {
		if x, ok := x.Data.(*PublishReq_Suite); ok {
			return x.Suite
		}
	}
	return nil
}

func (x *PublishReq) GetCase() *CasePublishInfo {
	if x != nil {
		if x, ok := x.Data.(*PublishReq_Case); ok {
			return x.Case
		}
	}
	return nil
}

func (x *PublishReq) GetInterfaceCase() *InterfaceCasePublishInfo {
	if x != nil {
		if x, ok := x.Data.(*PublishReq_InterfaceCase); ok {
			return x.InterfaceCase
		}
	}
	return nil
}

func (x *PublishReq) GetComponentGroup() *ComponentGroupPublishInfo {
	if x != nil {
		if x, ok := x.Data.(*PublishReq_ComponentGroup); ok {
			return x.ComponentGroup
		}
	}
	return nil
}

func (x *PublishReq) GetUiCase() *UICasePublishInfo {
	if x != nil {
		if x, ok := x.Data.(*PublishReq_UiCase); ok {
			return x.UiCase
		}
	}
	return nil
}

func (x *PublishReq) GetUiSuite() *UISuitePublishInfo {
	if x != nil {
		if x, ok := x.Data.(*PublishReq_UiSuite); ok {
			return x.UiSuite
		}
	}
	return nil
}

func (x *PublishReq) GetUiPlan() *UIPlanPublishInfo {
	if x != nil {
		if x, ok := x.Data.(*PublishReq_UiPlan); ok {
			return x.UiPlan
		}
	}
	return nil
}

func (x *PublishReq) GetPrecisionSuite() *PrecisionSuitePublishInfo {
	if x != nil {
		if x, ok := x.Data.(*PublishReq_PrecisionSuite); ok {
			return x.PrecisionSuite
		}
	}
	return nil
}

func (x *PublishReq) GetPrecisionInterface() *PrecisionInterfaceDocumentPublishInfo {
	if x != nil {
		if x, ok := x.Data.(*PublishReq_PrecisionInterface); ok {
			return x.PrecisionInterface
		}
	}
	return nil
}

func (x *PublishReq) GetService() *ServicePublishInfo {
	if x != nil {
		if x, ok := x.Data.(*PublishReq_Service); ok {
			return x.Service
		}
	}
	return nil
}

func (x *PublishReq) GetPerfCase() *PerfCasePublishInfo {
	if x != nil {
		if x, ok := x.Data.(*PublishReq_PerfCase); ok {
			return x.PerfCase
		}
	}
	return nil
}

func (x *PublishReq) GetPerfSuite() *PerfSuitePublishInfo {
	if x != nil {
		if x, ok := x.Data.(*PublishReq_PerfSuite); ok {
			return x.PerfSuite
		}
	}
	return nil
}

func (x *PublishReq) GetPerfPlan() *PerfPlanPublishInfo {
	if x != nil {
		if x, ok := x.Data.(*PublishReq_PerfPlan); ok {
			return x.PerfPlan
		}
	}
	return nil
}

func (x *PublishReq) GetStabilityPlan() *StabilityPlanPublishInfo {
	if x != nil {
		if x, ok := x.Data.(*PublishReq_StabilityPlan); ok {
			return x.StabilityPlan
		}
	}
	return nil
}

func (x *PublishReq) GetSubEnvInfo() *PublishReq_SubEnvInfo {
	if x != nil {
		return x.SubEnvInfo
	}
	return nil
}

func (x *PublishReq) GetTriggerUser() *PublishReq_TriggerUser {
	if x != nil {
		return x.TriggerUser
	}
	return nil
}

func (x *PublishReq) GetPurposeType() pb.PurposeType {
	if x != nil {
		return x.PurposeType
	}
	return pb.PurposeType(0)
}

func (x *PublishReq) GetPriorityType() pb.PriorityType {
	if x != nil {
		return x.PriorityType
	}
	return pb.PriorityType(0)
}

func (x *PublishReq) GetApprovers() []*PublishReq_ApproverUser {
	if x != nil {
		return x.Approvers
	}
	return nil
}

func (x *PublishReq) GetDebug() bool {
	if x != nil {
		return x.Debug
	}
	return false
}

type isPublishReq_Data interface {
	isPublishReq_Data()
}

type PublishReq_Plan struct {
	Plan *PlanPublishInfo `protobuf:"bytes,31,opt,name=plan,proto3,oneof"`
}

type PublishReq_Interface struct {
	Interface *InterfaceDocumentPublishInfo `protobuf:"bytes,32,opt,name=interface,proto3,oneof"`
}

type PublishReq_Suite struct {
	Suite *SuitePublishInfo `protobuf:"bytes,33,opt,name=suite,proto3,oneof"`
}

type PublishReq_Case struct {
	Case *CasePublishInfo `protobuf:"bytes,34,opt,name=case,proto3,oneof"`
}

type PublishReq_InterfaceCase struct {
	InterfaceCase *InterfaceCasePublishInfo `protobuf:"bytes,35,opt,name=interface_case,json=interfaceCase,proto3,oneof"`
}

type PublishReq_ComponentGroup struct {
	ComponentGroup *ComponentGroupPublishInfo `protobuf:"bytes,36,opt,name=component_group,json=componentGroup,proto3,oneof"`
}

type PublishReq_UiCase struct {
	UiCase *UICasePublishInfo `protobuf:"bytes,37,opt,name=ui_case,json=uiCase,proto3,oneof"`
}

type PublishReq_UiSuite struct {
	UiSuite *UISuitePublishInfo `protobuf:"bytes,38,opt,name=ui_suite,json=uiSuite,proto3,oneof"`
}

type PublishReq_UiPlan struct {
	UiPlan *UIPlanPublishInfo `protobuf:"bytes,39,opt,name=ui_plan,json=uiPlan,proto3,oneof"`
}

type PublishReq_PrecisionSuite struct {
	PrecisionSuite *PrecisionSuitePublishInfo `protobuf:"bytes,40,opt,name=precision_suite,json=precisionSuite,proto3,oneof"`
}

type PublishReq_PrecisionInterface struct {
	PrecisionInterface *PrecisionInterfaceDocumentPublishInfo `protobuf:"bytes,41,opt,name=precision_interface,json=precisionInterface,proto3,oneof"`
}

type PublishReq_Service struct {
	Service *ServicePublishInfo `protobuf:"bytes,42,opt,name=service,proto3,oneof"`
}

type PublishReq_PerfCase struct {
	PerfCase *PerfCasePublishInfo `protobuf:"bytes,43,opt,name=perf_case,json=perfCase,proto3,oneof"`
}

type PublishReq_PerfSuite struct {
	PerfSuite *PerfSuitePublishInfo `protobuf:"bytes,44,opt,name=perf_suite,json=perfSuite,proto3,oneof"`
}

type PublishReq_PerfPlan struct {
	PerfPlan *PerfPlanPublishInfo `protobuf:"bytes,45,opt,name=perf_plan,json=perfPlan,proto3,oneof"`
}

type PublishReq_StabilityPlan struct {
	StabilityPlan *StabilityPlanPublishInfo `protobuf:"bytes,46,opt,name=stability_plan,json=stabilityPlan,proto3,oneof"`
}

func (*PublishReq_Plan) isPublishReq_Data() {}

func (*PublishReq_Interface) isPublishReq_Data() {}

func (*PublishReq_Suite) isPublishReq_Data() {}

func (*PublishReq_Case) isPublishReq_Data() {}

func (*PublishReq_InterfaceCase) isPublishReq_Data() {}

func (*PublishReq_ComponentGroup) isPublishReq_Data() {}

func (*PublishReq_UiCase) isPublishReq_Data() {}

func (*PublishReq_UiSuite) isPublishReq_Data() {}

func (*PublishReq_UiPlan) isPublishReq_Data() {}

func (*PublishReq_PrecisionSuite) isPublishReq_Data() {}

func (*PublishReq_PrecisionInterface) isPublishReq_Data() {}

func (*PublishReq_Service) isPublishReq_Data() {}

func (*PublishReq_PerfCase) isPublishReq_Data() {}

func (*PublishReq_PerfSuite) isPublishReq_Data() {}

func (*PublishReq_PerfPlan) isPublishReq_Data() {}

func (*PublishReq_StabilityPlan) isPublishReq_Data() {}

type PublishResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	TaskId        string                 `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	ExecuteId     string                 `protobuf:"bytes,3,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`
	Version       string                 `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublishResp) Reset() {
	*x = PublishResp{}
	mi := &file_dispatcher_dispatcher_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublishResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishResp) ProtoMessage() {}

func (x *PublishResp) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_dispatcher_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishResp.ProtoReflect.Descriptor instead.
func (*PublishResp) Descriptor() ([]byte, []int) {
	return file_dispatcher_dispatcher_proto_rawDescGZIP(), []int{1}
}

func (x *PublishResp) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PublishResp) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *PublishResp) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *PublishResp) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type StopMetadata struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	StopType StopType               `protobuf:"varint,1,opt,name=stop_type,json=stopType,proto3,enum=dispatcher.StopType" json:"stop_type,omitempty"` // 停止类型
	Reason   string                 `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`                                               // 停止原因
	// Types that are valid to be assigned to Detail:
	//
	//	*StopMetadata_Rule
	Detail        isStopMetadata_Detail `protobuf_oneof:"detail"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StopMetadata) Reset() {
	*x = StopMetadata{}
	mi := &file_dispatcher_dispatcher_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StopMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopMetadata) ProtoMessage() {}

func (x *StopMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_dispatcher_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopMetadata.ProtoReflect.Descriptor instead.
func (*StopMetadata) Descriptor() ([]byte, []int) {
	return file_dispatcher_dispatcher_proto_rawDescGZIP(), []int{2}
}

func (x *StopMetadata) GetStopType() StopType {
	if x != nil {
		return x.StopType
	}
	return StopType_StopType_Unknown
}

func (x *StopMetadata) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *StopMetadata) GetDetail() isStopMetadata_Detail {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *StopMetadata) GetRule() *StopDetailOfPerfStopRule {
	if x != nil {
		if x, ok := x.Detail.(*StopMetadata_Rule); ok {
			return x.Rule
		}
	}
	return nil
}

type isStopMetadata_Detail interface {
	isStopMetadata_Detail()
}

type StopMetadata_Rule struct {
	Rule *StopDetailOfPerfStopRule `protobuf:"bytes,11,opt,name=rule,proto3,oneof"` // 压测停止规则
}

func (*StopMetadata_Rule) isStopMetadata_Detail() {}

type StopReq struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	ProjectId     string                   `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                          // 项目ID
	TaskId        string                   `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                                   // 任务ID
	Id            string                   `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`                                                                         // 执行对象的ID，如：组件组ID、用例ID、集合ID、计划ID
	ExecuteType   pb1.ApiExecutionDataType `protobuf:"varint,4,opt,name=execute_type,json=executeType,proto3,enum=manager.ApiExecutionDataType" json:"execute_type,omitempty"` // 执行类型
	ExecuteId     string                   `protobuf:"bytes,5,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`                                          // 执行ID
	Metadata      *StopMetadata            `protobuf:"bytes,11,opt,name=metadata,proto3" json:"metadata,omitempty"`                                                            // 元数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StopReq) Reset() {
	*x = StopReq{}
	mi := &file_dispatcher_dispatcher_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StopReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopReq) ProtoMessage() {}

func (x *StopReq) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_dispatcher_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopReq.ProtoReflect.Descriptor instead.
func (*StopReq) Descriptor() ([]byte, []int) {
	return file_dispatcher_dispatcher_proto_rawDescGZIP(), []int{3}
}

func (x *StopReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *StopReq) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *StopReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StopReq) GetExecuteType() pb1.ApiExecutionDataType {
	if x != nil {
		return x.ExecuteType
	}
	return pb1.ApiExecutionDataType(0)
}

func (x *StopReq) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *StopReq) GetMetadata() *StopMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type StopResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StopResp) Reset() {
	*x = StopResp{}
	mi := &file_dispatcher_dispatcher_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StopResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopResp) ProtoMessage() {}

func (x *StopResp) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_dispatcher_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopResp.ProtoReflect.Descriptor instead.
func (*StopResp) Descriptor() ([]byte, []int) {
	return file_dispatcher_dispatcher_proto_rawDescGZIP(), []int{4}
}

type SearchTaskInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"` // 项目ID
	Condition     *rpc.Condition         `protobuf:"bytes,2,opt,name=condition,proto3" json:"condition,omitempty"`                  // 查询条件
	Pagination    *rpc.Pagination        `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`                // 查询分页
	Sorts         []*rpc.SortField       `protobuf:"bytes,4,rep,name=sorts,proto3" json:"sorts,omitempty"`                          // 查询排序
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchTaskInfoReq) Reset() {
	*x = SearchTaskInfoReq{}
	mi := &file_dispatcher_dispatcher_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchTaskInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTaskInfoReq) ProtoMessage() {}

func (x *SearchTaskInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_dispatcher_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTaskInfoReq.ProtoReflect.Descriptor instead.
func (*SearchTaskInfoReq) Descriptor() ([]byte, []int) {
	return file_dispatcher_dispatcher_proto_rawDescGZIP(), []int{5}
}

func (x *SearchTaskInfoReq) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchTaskInfoReq) GetCondition() *rpc.Condition {
	if x != nil {
		return x.Condition
	}
	return nil
}

func (x *SearchTaskInfoReq) GetPagination() *rpc.Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *SearchTaskInfoReq) GetSorts() []*rpc.SortField {
	if x != nil {
		return x.Sorts
	}
	return nil
}

type SearchTaskInfoResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CurrentPage   uint64                 `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"` // 当前页
	PageSize      uint64                 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`          // 每页大小
	TotalCount    uint64                 `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`    // 总数
	TotalPage     uint64                 `protobuf:"varint,4,opt,name=total_page,json=totalPage,proto3" json:"total_page,omitempty"`       // 总页数
	Items         []*SearchTaskInfoItem  `protobuf:"bytes,5,rep,name=Items,proto3" json:"Items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchTaskInfoResp) Reset() {
	*x = SearchTaskInfoResp{}
	mi := &file_dispatcher_dispatcher_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchTaskInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTaskInfoResp) ProtoMessage() {}

func (x *SearchTaskInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_dispatcher_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTaskInfoResp.ProtoReflect.Descriptor instead.
func (*SearchTaskInfoResp) Descriptor() ([]byte, []int) {
	return file_dispatcher_dispatcher_proto_rawDescGZIP(), []int{6}
}

func (x *SearchTaskInfoResp) GetCurrentPage() uint64 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *SearchTaskInfoResp) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SearchTaskInfoResp) GetTotalCount() uint64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *SearchTaskInfoResp) GetTotalPage() uint64 {
	if x != nil {
		return x.TotalPage
	}
	return 0
}

func (x *SearchTaskInfoResp) GetItems() []*SearchTaskInfoItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type SearchTaskInfoItem struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ProjectId          string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                                          // 项目ID
	TaskId             string                 `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                                                   // 任务ID
	PlanId             string                 `protobuf:"bytes,3,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                                                   // 计划ID
	PlanName           string                 `protobuf:"bytes,4,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                                                             // 计划名称
	TriggerMode        string                 `protobuf:"bytes,5,opt,name=trigger_mode,json=triggerMode,proto3" json:"trigger_mode,omitempty"`                                                    // 触发类型
	TaskExecuteStatus  pb.ExecuteStatus       `protobuf:"varint,6,opt,name=task_execute_status,json=taskExecuteStatus,proto3,enum=common.ExecuteStatus" json:"task_execute_status,omitempty"`     // 执行状态(0排队中,1执行中,2已完成,3已停止)
	PriorityType       pb.PriorityType        `protobuf:"varint,7,opt,name=priority_type,json=priorityType,proto3,enum=common.PriorityType" json:"priority_type,omitempty"`                       // 优先级策略(0 1 2 3 4 => Default、Middle、High、Ultra、Low)
	TaskExecutedResult pb.ExecutedResult      `protobuf:"varint,8,opt,name=task_executed_result,json=taskExecutedResult,proto3,enum=common.ExecutedResult" json:"task_executed_result,omitempty"` // 执行结果(0缺省,1成功,2失败,3异常)
	TotalCase          int64                  `protobuf:"varint,9,opt,name=total_case,json=totalCase,proto3" json:"total_case,omitempty"`                                                         // 总测试用例数
	FinishedCase       int64                  `protobuf:"varint,10,opt,name=finished_case,json=finishedCase,proto3" json:"finished_case,omitempty"`                                               // 已经执行的测试用例数
	SuccessCase        int64                  `protobuf:"varint,11,opt,name=success_case,json=successCase,proto3" json:"success_case,omitempty"`                                                  // 执行成功的测试用例数
	TotalSuite         int64                  `protobuf:"varint,12,opt,name=total_suite,json=totalSuite,proto3" json:"total_suite,omitempty"`                                                     // 测试集合总数
	FinishedSuite      int64                  `protobuf:"varint,13,opt,name=finished_suite,json=finishedSuite,proto3" json:"finished_suite,omitempty"`                                            // 执行完的测试集合数
	SuccessSuite       int64                  `protobuf:"varint,14,opt,name=success_suite,json=successSuite,proto3" json:"success_suite,omitempty"`                                               // 执行成功的测试集合数
	ExecuteBy          string                 `protobuf:"bytes,15,opt,name=execute_by,json=executeBy,proto3" json:"execute_by,omitempty"`                                                         // 创建者
	CostTime           int64                  `protobuf:"varint,16,opt,name=cost_time,json=costTime,proto3" json:"cost_time,omitempty"`                                                           // 执行耗时
	CreateTime         int64                  `protobuf:"varint,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`                                                     // 创建时间
	WaitTime           int64                  `protobuf:"varint,18,opt,name=wait_time,json=waitTime,proto3" json:"wait_time,omitempty"`                                                           // 排队耗时
	StartedAt          int64                  `protobuf:"varint,19,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`                                                        // 开始时间
	EndedAt            int64                  `protobuf:"varint,20,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`                                                              // 结束时间
	ExecuteId          string                 `protobuf:"bytes,21,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`                                                         // 执行ID
	ReportViewUrl      string                 `protobuf:"bytes,22,opt,name=report_view_url,json=reportViewUrl,proto3" json:"report_view_url,omitempty"`                                           // 查看地址
	ReportDownloadUrl  string                 `protobuf:"bytes,23,opt,name=report_download_url,json=reportDownloadUrl,proto3" json:"report_download_url,omitempty"`                               // 下载地址
	UpdateAt           int64                  `protobuf:"varint,24,opt,name=update_at,json=updateAt,proto3" json:"update_at,omitempty"`                                                           // 更新时间
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *SearchTaskInfoItem) Reset() {
	*x = SearchTaskInfoItem{}
	mi := &file_dispatcher_dispatcher_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchTaskInfoItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchTaskInfoItem) ProtoMessage() {}

func (x *SearchTaskInfoItem) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_dispatcher_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchTaskInfoItem.ProtoReflect.Descriptor instead.
func (*SearchTaskInfoItem) Descriptor() ([]byte, []int) {
	return file_dispatcher_dispatcher_proto_rawDescGZIP(), []int{7}
}

func (x *SearchTaskInfoItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchTaskInfoItem) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *SearchTaskInfoItem) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *SearchTaskInfoItem) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

func (x *SearchTaskInfoItem) GetTriggerMode() string {
	if x != nil {
		return x.TriggerMode
	}
	return ""
}

func (x *SearchTaskInfoItem) GetTaskExecuteStatus() pb.ExecuteStatus {
	if x != nil {
		return x.TaskExecuteStatus
	}
	return pb.ExecuteStatus(0)
}

func (x *SearchTaskInfoItem) GetPriorityType() pb.PriorityType {
	if x != nil {
		return x.PriorityType
	}
	return pb.PriorityType(0)
}

func (x *SearchTaskInfoItem) GetTaskExecutedResult() pb.ExecutedResult {
	if x != nil {
		return x.TaskExecutedResult
	}
	return pb.ExecutedResult(0)
}

func (x *SearchTaskInfoItem) GetTotalCase() int64 {
	if x != nil {
		return x.TotalCase
	}
	return 0
}

func (x *SearchTaskInfoItem) GetFinishedCase() int64 {
	if x != nil {
		return x.FinishedCase
	}
	return 0
}

func (x *SearchTaskInfoItem) GetSuccessCase() int64 {
	if x != nil {
		return x.SuccessCase
	}
	return 0
}

func (x *SearchTaskInfoItem) GetTotalSuite() int64 {
	if x != nil {
		return x.TotalSuite
	}
	return 0
}

func (x *SearchTaskInfoItem) GetFinishedSuite() int64 {
	if x != nil {
		return x.FinishedSuite
	}
	return 0
}

func (x *SearchTaskInfoItem) GetSuccessSuite() int64 {
	if x != nil {
		return x.SuccessSuite
	}
	return 0
}

func (x *SearchTaskInfoItem) GetExecuteBy() string {
	if x != nil {
		return x.ExecuteBy
	}
	return ""
}

func (x *SearchTaskInfoItem) GetCostTime() int64 {
	if x != nil {
		return x.CostTime
	}
	return 0
}

func (x *SearchTaskInfoItem) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *SearchTaskInfoItem) GetWaitTime() int64 {
	if x != nil {
		return x.WaitTime
	}
	return 0
}

func (x *SearchTaskInfoItem) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *SearchTaskInfoItem) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *SearchTaskInfoItem) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *SearchTaskInfoItem) GetReportViewUrl() string {
	if x != nil {
		return x.ReportViewUrl
	}
	return ""
}

func (x *SearchTaskInfoItem) GetReportDownloadUrl() string {
	if x != nil {
		return x.ReportDownloadUrl
	}
	return ""
}

func (x *SearchTaskInfoItem) GetUpdateAt() int64 {
	if x != nil {
		return x.UpdateAt
	}
	return 0
}

type PublishReq_SubEnvInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SubEnvName    string                 `protobuf:"bytes,1,opt,name=sub_env_name,json=subEnvName,proto3" json:"sub_env_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublishReq_SubEnvInfo) Reset() {
	*x = PublishReq_SubEnvInfo{}
	mi := &file_dispatcher_dispatcher_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublishReq_SubEnvInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishReq_SubEnvInfo) ProtoMessage() {}

func (x *PublishReq_SubEnvInfo) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_dispatcher_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishReq_SubEnvInfo.ProtoReflect.Descriptor instead.
func (*PublishReq_SubEnvInfo) Descriptor() ([]byte, []int) {
	return file_dispatcher_dispatcher_proto_rawDescGZIP(), []int{0, 0}
}

func (x *PublishReq_SubEnvInfo) GetSubEnvName() string {
	if x != nil {
		return x.SubEnvName
	}
	return ""
}

type PublishReq_TriggerUser struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Account       string                 `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublishReq_TriggerUser) Reset() {
	*x = PublishReq_TriggerUser{}
	mi := &file_dispatcher_dispatcher_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublishReq_TriggerUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishReq_TriggerUser) ProtoMessage() {}

func (x *PublishReq_TriggerUser) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_dispatcher_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishReq_TriggerUser.ProtoReflect.Descriptor instead.
func (*PublishReq_TriggerUser) Descriptor() ([]byte, []int) {
	return file_dispatcher_dispatcher_proto_rawDescGZIP(), []int{0, 1}
}

func (x *PublishReq_TriggerUser) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *PublishReq_TriggerUser) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

type PublishReq_ApproverUser struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Account       string                 `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublishReq_ApproverUser) Reset() {
	*x = PublishReq_ApproverUser{}
	mi := &file_dispatcher_dispatcher_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublishReq_ApproverUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublishReq_ApproverUser) ProtoMessage() {}

func (x *PublishReq_ApproverUser) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_dispatcher_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublishReq_ApproverUser.ProtoReflect.Descriptor instead.
func (*PublishReq_ApproverUser) Descriptor() ([]byte, []int) {
	return file_dispatcher_dispatcher_proto_rawDescGZIP(), []int{0, 2}
}

func (x *PublishReq_ApproverUser) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *PublishReq_ApproverUser) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

var File_dispatcher_dispatcher_proto protoreflect.FileDescriptor

var file_dispatcher_dispatcher_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x64, 0x69, 0x73,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x64,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x73, 0x71, 0x6c, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x2f, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x15, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x62,
	0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x64, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xac, 0x0f, 0x0a, 0x0a, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52,
	0x65, 0x71, 0x12, 0x36, 0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x6d, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x74,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x64,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x04, 0x70,
	0x6c, 0x61, 0x6e, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x64, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x6c, 0x61, 0x6e, 0x50, 0x75, 0x62, 0x6c, 0x69,
	0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x04, 0x70, 0x6c, 0x61, 0x6e, 0x12, 0x48,
	0x0a, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x09, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x12, 0x34, 0x0a, 0x05, 0x73, 0x75, 0x69, 0x74,
	0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x2e, 0x53, 0x75, 0x69, 0x74, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x05, 0x73, 0x75, 0x69, 0x74, 0x65, 0x12, 0x31,
	0x0a, 0x04, 0x63, 0x61, 0x73, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x64,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x50, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x04, 0x63, 0x61, 0x73,
	0x65, 0x12, 0x4d, 0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x63,
	0x61, 0x73, 0x65, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x64, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65,
	0x43, 0x61, 0x73, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x48,
	0x00, 0x52, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x43, 0x61, 0x73, 0x65,
	0x12, 0x50, 0x0a, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x64, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x48, 0x00, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x12, 0x38, 0x0a, 0x07, 0x75, 0x69, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x25, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x2e, 0x55, 0x49, 0x43, 0x61, 0x73, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e,
	0x66, 0x6f, 0x48, 0x00, 0x52, 0x06, 0x75, 0x69, 0x43, 0x61, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x08,
	0x75, 0x69, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x18, 0x26, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x53, 0x75,
	0x69, 0x74, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00,
	0x52, 0x07, 0x75, 0x69, 0x53, 0x75, 0x69, 0x74, 0x65, 0x12, 0x38, 0x0a, 0x07, 0x75, 0x69, 0x5f,
	0x70, 0x6c, 0x61, 0x6e, 0x18, 0x27, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x64, 0x69, 0x73,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x50, 0x6c, 0x61, 0x6e, 0x50, 0x75,
	0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x06, 0x75, 0x69, 0x50,
	0x6c, 0x61, 0x6e, 0x12, 0x50, 0x0a, 0x0f, 0x70, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x18, 0x28, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x64,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x65, 0x63, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x53, 0x75, 0x69, 0x74, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49,
	0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0e, 0x70, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x53, 0x75, 0x69, 0x74, 0x65, 0x12, 0x64, 0x0a, 0x13, 0x70, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x18, 0x29, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x50, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61,
	0x63, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x12, 0x70, 0x72, 0x65, 0x63, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x07, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64,
	0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x07,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3e, 0x0a, 0x09, 0x70, 0x65, 0x72, 0x66, 0x5f,
	0x63, 0x61, 0x73, 0x65, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x64, 0x69, 0x73,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x08, 0x70,
	0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x66, 0x5f,
	0x73, 0x75, 0x69, 0x74, 0x65, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x64, 0x69,
	0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x53, 0x75, 0x69,
	0x74, 0x65, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52,
	0x09, 0x70, 0x65, 0x72, 0x66, 0x53, 0x75, 0x69, 0x74, 0x65, 0x12, 0x3e, 0x0a, 0x09, 0x70, 0x65,
	0x72, 0x66, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x50,
	0x6c, 0x61, 0x6e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00,
	0x52, 0x08, 0x70, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x4d, 0x0a, 0x0e, 0x73, 0x74,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x18, 0x2e, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x53, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x6c, 0x61, 0x6e, 0x50, 0x75, 0x62,
	0x6c, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x43, 0x0a, 0x0c, 0x73, 0x75, 0x62,
	0x5f, 0x65, 0x6e, 0x76, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x62,
	0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x71, 0x2e, 0x53, 0x75, 0x62, 0x45, 0x6e, 0x76, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x45, 0x6e, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x45,
	0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65,
	0x72, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x71, 0x2e, 0x54, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x55, 0x73, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x0c, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0b, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a,
	0x0d, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72,
	0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x41, 0x0a, 0x09, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x72, 0x73, 0x18,
	0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68,
	0x65, 0x72, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x71, 0x2e, 0x41, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x09, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x62, 0x75, 0x67, 0x18, 0x63,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x64, 0x65, 0x62, 0x75, 0x67, 0x1a, 0x2e, 0x0a, 0x0a, 0x53,
	0x75, 0x62, 0x45, 0x6e, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0c, 0x73, 0x75, 0x62,
	0x5f, 0x65, 0x6e, 0x76, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x73, 0x75, 0x62, 0x45, 0x6e, 0x76, 0x4e, 0x61, 0x6d, 0x65, 0x1a, 0x3d, 0x0a, 0x0b, 0x54,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x3e, 0x0a, 0x0c, 0x41, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x7e, 0x0a, 0x0b, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0xa9, 0x01, 0x0a, 0x0c, 0x53, 0x74, 0x6f, 0x70, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x09, 0x73, 0x74, 0x6f, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x08, 0x73, 0x74, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x3a, 0x0a, 0x04, 0x72, 0x75, 0x6c, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4f, 0x66,
	0x50, 0x65, 0x72, 0x66, 0x53, 0x74, 0x6f, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x04,
	0x72, 0x75, 0x6c, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0xd0,
	0x03, 0x0a, 0x07, 0x53, 0x74, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e,
	0xfa, 0x42, 0x1b, 0x72, 0x19, 0x32, 0x17, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x31, 0x24, 0x29, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x07, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x17, 0xfa, 0x42, 0x14, 0x72,
	0x12, 0x32, 0x10, 0x28, 0x3f, 0x3a, 0x5e, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x3a, 0x2e,
	0x2b, 0x3f, 0x29, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x75, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x65, 0xfa, 0x42, 0x62, 0x72, 0x60, 0x32, 0x5e,
	0x28, 0x3f, 0x3a, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x7c, 0x63, 0x61, 0x73, 0x65, 0x7c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x66, 0x61, 0x63,
	0x65, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x7c, 0x73, 0x75, 0x69, 0x74, 0x65, 0x7c, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x66, 0x61, 0x63, 0x65, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x7c,
	0x70, 0x6c, 0x61, 0x6e, 0x7c, 0x75, 0x69, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x7c, 0x70, 0x65, 0x72,
	0x66, 0x5f, 0x70, 0x6c, 0x61, 0x6e, 0x29, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x62, 0x0a, 0x0c, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x2e, 0x41, 0x70, 0x69, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x42, 0x20, 0xfa, 0x42, 0x1d, 0x82, 0x01, 0x1a, 0x18,
	0x01, 0x18, 0x02, 0x18, 0x03, 0x18, 0x04, 0x18, 0x05, 0x18, 0x06, 0x18, 0x07, 0x18, 0x08, 0x18,
	0x09, 0x18, 0x0a, 0x18, 0x5b, 0x18, 0x5c, 0x18, 0x5d, 0x52, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1a, 0xfa, 0x42, 0x17, 0x72,
	0x15, 0x32, 0x13, 0x28, 0x3f, 0x3a, 0x5e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x3e, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72,
	0x2e, 0x53, 0x74, 0x6f, 0x70, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x0a, 0x0a, 0x08, 0x53, 0x74, 0x6f, 0x70, 0x52, 0x65, 0x73, 0x70, 0x22, 0xcc, 0x01,
	0x0a, 0x11, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x64, 0x12, 0x33, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x71, 0x6c, 0x62, 0x75, 0x69, 0x6c, 0x64,
	0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x63, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x73, 0x71,
	0x6c, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x2e, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x70, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2b, 0x0a, 0x05, 0x73, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x73, 0x71, 0x6c, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x2e, 0x53, 0x6f, 0x72, 0x74,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x05, 0x73, 0x6f, 0x72, 0x74, 0x73, 0x22, 0xca, 0x01, 0x0a,
	0x12, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x50, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50,
	0x61, 0x67, 0x65, 0x12, 0x34, 0x0a, 0x05, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x05, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xab, 0x07, 0x0a, 0x12, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64,
	0x65, 0x12, 0x4f, 0x0a, 0x13, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x11, 0x74, 0x61, 0x73, 0x6b, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x43, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0c, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x52, 0x0a, 0x14, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x12, 0x74, 0x61, 0x73, 0x6b, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x61, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x43, 0x61, 0x73, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61,
	0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x75, 0x69, 0x74,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x75,
	0x69, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x5f,
	0x73, 0x75, 0x69, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x66, 0x69, 0x6e,
	0x69, 0x73, 0x68, 0x65, 0x64, 0x53, 0x75, 0x69, 0x74, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0c, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x53, 0x75, 0x69, 0x74, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x62, 0x79, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x42, 0x79, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x63, 0x6f, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x77, 0x61, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x77, 0x61, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x76, 0x69, 0x65,
	0x77, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x56, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x74, 0x32, 0xcc, 0x01, 0x0a, 0x0a, 0x44, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x12, 0x3a, 0x0a, 0x07, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73,
	0x68, 0x12, 0x16, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x64, 0x69, 0x73, 0x70,
	0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x31, 0x0a, 0x04, 0x53, 0x74, 0x6f, 0x70, 0x12, 0x13, 0x2e, 0x64, 0x69, 0x73,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x1a,
	0x14, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x6f,
	0x70, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4f, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54,
	0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74,
	0x63, 0x68, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x42, 0x44, 0x5a, 0x42, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62,
	0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73,
	0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f,
	0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x61,
	0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_dispatcher_dispatcher_proto_rawDescOnce sync.Once
	file_dispatcher_dispatcher_proto_rawDescData = file_dispatcher_dispatcher_proto_rawDesc
)

func file_dispatcher_dispatcher_proto_rawDescGZIP() []byte {
	file_dispatcher_dispatcher_proto_rawDescOnce.Do(func() {
		file_dispatcher_dispatcher_proto_rawDescData = protoimpl.X.CompressGZIP(file_dispatcher_dispatcher_proto_rawDescData)
	})
	return file_dispatcher_dispatcher_proto_rawDescData
}

var file_dispatcher_dispatcher_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_dispatcher_dispatcher_proto_goTypes = []any{
	(*PublishReq)(nil),                            // 0: dispatcher.PublishReq
	(*PublishResp)(nil),                           // 1: dispatcher.PublishResp
	(*StopMetadata)(nil),                          // 2: dispatcher.StopMetadata
	(*StopReq)(nil),                               // 3: dispatcher.StopReq
	(*StopResp)(nil),                              // 4: dispatcher.StopResp
	(*SearchTaskInfoReq)(nil),                     // 5: dispatcher.SearchTaskInfoReq
	(*SearchTaskInfoResp)(nil),                    // 6: dispatcher.SearchTaskInfoResp
	(*SearchTaskInfoItem)(nil),                    // 7: dispatcher.SearchTaskInfoItem
	(*PublishReq_SubEnvInfo)(nil),                 // 8: dispatcher.PublishReq.SubEnvInfo
	(*PublishReq_TriggerUser)(nil),                // 9: dispatcher.PublishReq.TriggerUser
	(*PublishReq_ApproverUser)(nil),               // 10: dispatcher.PublishReq.ApproverUser
	(pb.TriggerMode)(0),                           // 11: common.TriggerMode
	(pb1.ApiExecutionDataType)(0),                 // 12: manager.ApiExecutionDataType
	(PublishType)(0),                              // 13: dispatcher.PublishType
	(*PlanPublishInfo)(nil),                       // 14: dispatcher.PlanPublishInfo
	(*InterfaceDocumentPublishInfo)(nil),          // 15: dispatcher.InterfaceDocumentPublishInfo
	(*SuitePublishInfo)(nil),                      // 16: dispatcher.SuitePublishInfo
	(*CasePublishInfo)(nil),                       // 17: dispatcher.CasePublishInfo
	(*InterfaceCasePublishInfo)(nil),              // 18: dispatcher.InterfaceCasePublishInfo
	(*ComponentGroupPublishInfo)(nil),             // 19: dispatcher.ComponentGroupPublishInfo
	(*UICasePublishInfo)(nil),                     // 20: dispatcher.UICasePublishInfo
	(*UISuitePublishInfo)(nil),                    // 21: dispatcher.UISuitePublishInfo
	(*UIPlanPublishInfo)(nil),                     // 22: dispatcher.UIPlanPublishInfo
	(*PrecisionSuitePublishInfo)(nil),             // 23: dispatcher.PrecisionSuitePublishInfo
	(*PrecisionInterfaceDocumentPublishInfo)(nil), // 24: dispatcher.PrecisionInterfaceDocumentPublishInfo
	(*ServicePublishInfo)(nil),                    // 25: dispatcher.ServicePublishInfo
	(*PerfCasePublishInfo)(nil),                   // 26: dispatcher.PerfCasePublishInfo
	(*PerfSuitePublishInfo)(nil),                  // 27: dispatcher.PerfSuitePublishInfo
	(*PerfPlanPublishInfo)(nil),                   // 28: dispatcher.PerfPlanPublishInfo
	(*StabilityPlanPublishInfo)(nil),              // 29: dispatcher.StabilityPlanPublishInfo
	(pb.PurposeType)(0),                           // 30: common.PurposeType
	(pb.PriorityType)(0),                          // 31: common.PriorityType
	(StopType)(0),                                 // 32: dispatcher.StopType
	(*StopDetailOfPerfStopRule)(nil),              // 33: dispatcher.StopDetailOfPerfStopRule
	(*rpc.Condition)(nil),                         // 34: sqlbuilder.Condition
	(*rpc.Pagination)(nil),                        // 35: sqlbuilder.Pagination
	(*rpc.SortField)(nil),                         // 36: sqlbuilder.SortField
	(pb.ExecuteStatus)(0),                         // 37: common.ExecuteStatus
	(pb.ExecutedResult)(0),                        // 38: common.ExecutedResult
}
var file_dispatcher_dispatcher_proto_depIdxs = []int32{
	11, // 0: dispatcher.PublishReq.trigger_mode:type_name -> common.TriggerMode
	12, // 1: dispatcher.PublishReq.execute_type:type_name -> manager.ApiExecutionDataType
	13, // 2: dispatcher.PublishReq.publish_type:type_name -> dispatcher.PublishType
	14, // 3: dispatcher.PublishReq.plan:type_name -> dispatcher.PlanPublishInfo
	15, // 4: dispatcher.PublishReq.interface:type_name -> dispatcher.InterfaceDocumentPublishInfo
	16, // 5: dispatcher.PublishReq.suite:type_name -> dispatcher.SuitePublishInfo
	17, // 6: dispatcher.PublishReq.case:type_name -> dispatcher.CasePublishInfo
	18, // 7: dispatcher.PublishReq.interface_case:type_name -> dispatcher.InterfaceCasePublishInfo
	19, // 8: dispatcher.PublishReq.component_group:type_name -> dispatcher.ComponentGroupPublishInfo
	20, // 9: dispatcher.PublishReq.ui_case:type_name -> dispatcher.UICasePublishInfo
	21, // 10: dispatcher.PublishReq.ui_suite:type_name -> dispatcher.UISuitePublishInfo
	22, // 11: dispatcher.PublishReq.ui_plan:type_name -> dispatcher.UIPlanPublishInfo
	23, // 12: dispatcher.PublishReq.precision_suite:type_name -> dispatcher.PrecisionSuitePublishInfo
	24, // 13: dispatcher.PublishReq.precision_interface:type_name -> dispatcher.PrecisionInterfaceDocumentPublishInfo
	25, // 14: dispatcher.PublishReq.service:type_name -> dispatcher.ServicePublishInfo
	26, // 15: dispatcher.PublishReq.perf_case:type_name -> dispatcher.PerfCasePublishInfo
	27, // 16: dispatcher.PublishReq.perf_suite:type_name -> dispatcher.PerfSuitePublishInfo
	28, // 17: dispatcher.PublishReq.perf_plan:type_name -> dispatcher.PerfPlanPublishInfo
	29, // 18: dispatcher.PublishReq.stability_plan:type_name -> dispatcher.StabilityPlanPublishInfo
	8,  // 19: dispatcher.PublishReq.sub_env_info:type_name -> dispatcher.PublishReq.SubEnvInfo
	9,  // 20: dispatcher.PublishReq.trigger_user:type_name -> dispatcher.PublishReq.TriggerUser
	30, // 21: dispatcher.PublishReq.purpose_type:type_name -> common.PurposeType
	31, // 22: dispatcher.PublishReq.priority_type:type_name -> common.PriorityType
	10, // 23: dispatcher.PublishReq.approvers:type_name -> dispatcher.PublishReq.ApproverUser
	32, // 24: dispatcher.StopMetadata.stop_type:type_name -> dispatcher.StopType
	33, // 25: dispatcher.StopMetadata.rule:type_name -> dispatcher.StopDetailOfPerfStopRule
	12, // 26: dispatcher.StopReq.execute_type:type_name -> manager.ApiExecutionDataType
	2,  // 27: dispatcher.StopReq.metadata:type_name -> dispatcher.StopMetadata
	34, // 28: dispatcher.SearchTaskInfoReq.condition:type_name -> sqlbuilder.Condition
	35, // 29: dispatcher.SearchTaskInfoReq.pagination:type_name -> sqlbuilder.Pagination
	36, // 30: dispatcher.SearchTaskInfoReq.sorts:type_name -> sqlbuilder.SortField
	7,  // 31: dispatcher.SearchTaskInfoResp.Items:type_name -> dispatcher.SearchTaskInfoItem
	37, // 32: dispatcher.SearchTaskInfoItem.task_execute_status:type_name -> common.ExecuteStatus
	31, // 33: dispatcher.SearchTaskInfoItem.priority_type:type_name -> common.PriorityType
	38, // 34: dispatcher.SearchTaskInfoItem.task_executed_result:type_name -> common.ExecutedResult
	0,  // 35: dispatcher.Dispatcher.Publish:input_type -> dispatcher.PublishReq
	3,  // 36: dispatcher.Dispatcher.Stop:input_type -> dispatcher.StopReq
	5,  // 37: dispatcher.Dispatcher.SearchTaskInfo:input_type -> dispatcher.SearchTaskInfoReq
	1,  // 38: dispatcher.Dispatcher.Publish:output_type -> dispatcher.PublishResp
	4,  // 39: dispatcher.Dispatcher.Stop:output_type -> dispatcher.StopResp
	6,  // 40: dispatcher.Dispatcher.SearchTaskInfo:output_type -> dispatcher.SearchTaskInfoResp
	38, // [38:41] is the sub-list for method output_type
	35, // [35:38] is the sub-list for method input_type
	35, // [35:35] is the sub-list for extension type_name
	35, // [35:35] is the sub-list for extension extendee
	0,  // [0:35] is the sub-list for field type_name
}

func init() { file_dispatcher_dispatcher_proto_init() }
func file_dispatcher_dispatcher_proto_init() {
	if File_dispatcher_dispatcher_proto != nil {
		return
	}
	file_dispatcher_base_proto_init()
	file_dispatcher_publish_proto_init()
	file_dispatcher_dispatcher_proto_msgTypes[0].OneofWrappers = []any{
		(*PublishReq_Plan)(nil),
		(*PublishReq_Interface)(nil),
		(*PublishReq_Suite)(nil),
		(*PublishReq_Case)(nil),
		(*PublishReq_InterfaceCase)(nil),
		(*PublishReq_ComponentGroup)(nil),
		(*PublishReq_UiCase)(nil),
		(*PublishReq_UiSuite)(nil),
		(*PublishReq_UiPlan)(nil),
		(*PublishReq_PrecisionSuite)(nil),
		(*PublishReq_PrecisionInterface)(nil),
		(*PublishReq_Service)(nil),
		(*PublishReq_PerfCase)(nil),
		(*PublishReq_PerfSuite)(nil),
		(*PublishReq_PerfPlan)(nil),
		(*PublishReq_StabilityPlan)(nil),
	}
	file_dispatcher_dispatcher_proto_msgTypes[2].OneofWrappers = []any{
		(*StopMetadata_Rule)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_dispatcher_dispatcher_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_dispatcher_dispatcher_proto_goTypes,
		DependencyIndexes: file_dispatcher_dispatcher_proto_depIdxs,
		MessageInfos:      file_dispatcher_dispatcher_proto_msgTypes,
	}.Build()
	File_dispatcher_dispatcher_proto = out.File
	file_dispatcher_dispatcher_proto_rawDesc = nil
	file_dispatcher_dispatcher_proto_goTypes = nil
	file_dispatcher_dispatcher_proto_depIdxs = nil
}
