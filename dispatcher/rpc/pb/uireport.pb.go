// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: dispatcher/uireport.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UIReportCallback struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ProjectId         string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                           // 项目id
	TaskId            string                 `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                    // 任务id
	PlanId            string                 `protobuf:"bytes,3,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                    // 计划id
	PlanExecuteId     string                 `protobuf:"bytes,4,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`             // 计划执行id
	ReportViewUrl     string                 `protobuf:"bytes,5,opt,name=report_view_url,json=reportViewUrl,proto3" json:"report_view_url,omitempty"`             // 查看报告地址
	ReportDownloadUrl string                 `protobuf:"bytes,6,opt,name=report_download_url,json=reportDownloadUrl,proto3" json:"report_download_url,omitempty"` // 下载报告地址
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UIReportCallback) Reset() {
	*x = UIReportCallback{}
	mi := &file_dispatcher_uireport_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIReportCallback) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIReportCallback) ProtoMessage() {}

func (x *UIReportCallback) ProtoReflect() protoreflect.Message {
	mi := &file_dispatcher_uireport_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIReportCallback.ProtoReflect.Descriptor instead.
func (*UIReportCallback) Descriptor() ([]byte, []int) {
	return file_dispatcher_uireport_proto_rawDescGZIP(), []int{0}
}

func (x *UIReportCallback) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *UIReportCallback) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *UIReportCallback) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *UIReportCallback) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *UIReportCallback) GetReportViewUrl() string {
	if x != nil {
		return x.ReportViewUrl
	}
	return ""
}

func (x *UIReportCallback) GetReportDownloadUrl() string {
	if x != nil {
		return x.ReportDownloadUrl
	}
	return ""
}

var File_dispatcher_uireport_proto protoreflect.FileDescriptor

var file_dispatcher_uireport_proto_rawDesc = []byte{
	0x0a, 0x19, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x75, 0x69, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x64, 0x69, 0x73,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x22, 0xe3, 0x01, 0x0a, 0x10, 0x55, 0x49, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61,
	0x73, 0x6b, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x26, 0x0a,
	0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x76, 0x69, 0x65, 0x77, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x56, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c, 0x12, 0x2e, 0x0a,
	0x13, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x42, 0x44, 0x5a,
	0x42, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x2f, 0x64, 0x69, 0x73, 0x70, 0x61, 0x74, 0x63, 0x68, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63,
	0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_dispatcher_uireport_proto_rawDescOnce sync.Once
	file_dispatcher_uireport_proto_rawDescData = file_dispatcher_uireport_proto_rawDesc
)

func file_dispatcher_uireport_proto_rawDescGZIP() []byte {
	file_dispatcher_uireport_proto_rawDescOnce.Do(func() {
		file_dispatcher_uireport_proto_rawDescData = protoimpl.X.CompressGZIP(file_dispatcher_uireport_proto_rawDescData)
	})
	return file_dispatcher_uireport_proto_rawDescData
}

var file_dispatcher_uireport_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_dispatcher_uireport_proto_goTypes = []any{
	(*UIReportCallback)(nil), // 0: dispatcher.UIReportCallback
}
var file_dispatcher_uireport_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_dispatcher_uireport_proto_init() }
func file_dispatcher_uireport_proto_init() {
	if File_dispatcher_uireport_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_dispatcher_uireport_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_dispatcher_uireport_proto_goTypes,
		DependencyIndexes: file_dispatcher_uireport_proto_depIdxs,
		MessageInfos:      file_dispatcher_uireport_proto_msgTypes,
	}.Build()
	File_dispatcher_uireport_proto = out.File
	file_dispatcher_uireport_proto_rawDesc = nil
	file_dispatcher_uireport_proto_goTypes = nil
	file_dispatcher_uireport_proto_depIdxs = nil
}
