package svc

import (
	red "github.com/redis/go-redis/v9"
	"github.com/zeromicro/go-zero/core/stores/redis"

	consumerv2 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
	qetredis "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redis"

	rediscommon "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/redis/task"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/common/zrpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/mqc/internal/config"
)

type ServiceContext struct {
	Config config.Config

	Redis  red.UniversalClient
	RedisX *redis.Redis

	DispatcherRpc        *zrpc.DispatcherRpc
	LarkProxyRpc         *zrpc.LarkProxyRpc
	ManagerRpc           *zrpc.ManagerRpc
	NotifierRpc          *zrpc.NotifierRpc
	PerfReporterRpc      *zrpc.PerfReporterRpc
	ReporterRpc          *zrpc.ReporterRpc
	UIReporterRpc        *zrpc.UIReporterRpc
	StabilityReporterRpc *zrpc.StabilityReporterRpc
	UserRpc              *zrpc.UserRpc

	Consumer               *consumerv2.Consumer
	UIWorkerProducer       *producer.Producer
	PerfWorkerProducer     *producer.Producer
	DispatcherProducer     *producer.Producer
	ManagerProducer        *producer.Producer
	ReporterProducer       *producer.Producer
	DispatcherTaskProducer *producer.Producer

	ProbeBaseURL  string
	AppInsightURL string

	RCacheService     *rediscommon.RCacheService
	TaskInfoProcessor *task.InfoProcessor
}

func NewServiceContext(c config.Config) *ServiceContext {
	cacheService := rediscommon.NewRCacheService(c.Redis, rediscommon.DispatcherRedisKey)

	return &ServiceContext{
		Config: c,

		Redis:  qetredis.NewClient(c.Redis),
		RedisX: redis.MustNewRedis(c.Redis, redis.WithDB(c.Redis.DB)),

		DispatcherRpc:   zrpc.NewDispatcherRpc(c.Dispatcher),
		LarkProxyRpc:    zrpc.NewLarkProxyRpc(c.LarkProxy),
		ManagerRpc:      zrpc.NewManagerRpc(c.Manager),
		NotifierRpc:     zrpc.NewNotifierRpc(c.Notifier),
		PerfReporterRpc: zrpc.NewPerfReporterRpc(c.Reporter),
		ReporterRpc:     zrpc.NewReporterRpc(c.Reporter),
		UIReporterRpc:   zrpc.NewUIReporterRpc(c.Reporter),
		UserRpc:         zrpc.NewUserRpc(c.User),

		Consumer:               consumerv2.NewConsumer(c.Consumer),
		UIWorkerProducer:       producer.NewProducer(c.UIWorkerProducer),
		PerfWorkerProducer:     producer.NewProducer(c.PerfWorkerProducer),
		DispatcherProducer:     producer.NewProducer(c.DispatcherProducer),
		DispatcherTaskProducer: producer.NewProducer(c.DispatcherTaskProducer),
		ManagerProducer:        producer.NewProducer(c.ManagerProducer),
		ReporterProducer:       producer.NewProducer(c.ReporterProducer),

		ProbeBaseURL:  c.ProbeBaseURL,
		AppInsightURL: c.AppInsightBaseURL,

		RCacheService:     cacheService,
		TaskInfoProcessor: task.NewTaskInfoProcessor(cacheService),
	}
}
