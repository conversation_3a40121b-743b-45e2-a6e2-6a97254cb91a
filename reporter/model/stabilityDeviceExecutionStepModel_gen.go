// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	stabilityDeviceExecutionStepTableName           = "`stability_device_execution_step`"
	stabilityDeviceExecutionStepFieldNames          = builder.RawFieldNames(&StabilityDeviceExecutionStep{})
	stabilityDeviceExecutionStepRows                = strings.Join(stabilityDeviceExecutionStepFieldNames, ",")
	stabilityDeviceExecutionStepRowsExpectAutoSet   = strings.Join(stringx.Remove(stabilityDeviceExecutionStepFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	stabilityDeviceExecutionStepRowsWithPlaceHolder = strings.Join(stringx.Remove(stabilityDeviceExecutionStepFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	stabilityDeviceExecutionStepModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *StabilityDeviceExecutionStep) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*StabilityDeviceExecutionStep, error)
		FindOneByStepId(ctx context.Context, stepId string) (*StabilityDeviceExecutionStep, error)
		Update(ctx context.Context, session sqlx.Session, data *StabilityDeviceExecutionStep) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultStabilityDeviceExecutionStepModel struct {
		conn  sqlx.SqlConn
		table string
	}

	StabilityDeviceExecutionStep struct {
		Id        int64          `db:"id"`         // 自增ID
		TaskId    string         `db:"task_id"`    // 任务ID
		ExecuteId string         `db:"execute_id"` // 用例执行ID
		ProjectId string         `db:"project_id"` // 项目ID
		Udid      string         `db:"udid"`       // 设备编号
		StepId    string         `db:"step_id"`    // 步骤ID
		Stage     int64          `db:"stage"`      // 阶段（前置步骤、测试步骤、后置步骤）
		Index     int64          `db:"index"`      // 步骤索引
		Name      string         `db:"name"`       // 步骤名称
		Status    string         `db:"status"`     // 状态（成功、失败）
		StartedAt time.Time      `db:"started_at"` // 开始时间
		EndedAt   time.Time      `db:"ended_at"`   // 结束时间
		Deleted   int64          `db:"deleted"`    // 逻辑删除标识（未删除、已删除）
		CreatedBy string         `db:"created_by"` // 创建者的用户ID
		UpdatedBy string         `db:"updated_by"` // 最近一次更新者的用户ID
		DeletedBy sql.NullString `db:"deleted_by"` // 删除者的用户ID
		CreatedAt time.Time      `db:"created_at"` // 创建时间
		UpdatedAt time.Time      `db:"updated_at"` // 更新时间
		DeletedAt sql.NullTime   `db:"deleted_at"` // 删除时间
	}
)

func newStabilityDeviceExecutionStepModel(conn sqlx.SqlConn) *defaultStabilityDeviceExecutionStepModel {
	return &defaultStabilityDeviceExecutionStepModel{
		conn:  conn,
		table: "`stability_device_execution_step`",
	}
}

func (m *defaultStabilityDeviceExecutionStepModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultStabilityDeviceExecutionStepModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultStabilityDeviceExecutionStepModel) FindOne(ctx context.Context, id int64) (*StabilityDeviceExecutionStep, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", stabilityDeviceExecutionStepRows, m.table)
	var resp StabilityDeviceExecutionStep
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultStabilityDeviceExecutionStepModel) FindOneByStepId(ctx context.Context, stepId string) (*StabilityDeviceExecutionStep, error) {
	var resp StabilityDeviceExecutionStep
	query := fmt.Sprintf("select %s from %s where `step_id` = ? and `deleted` = ? limit 1", stabilityDeviceExecutionStepRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, stepId, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultStabilityDeviceExecutionStepModel) Insert(ctx context.Context, session sqlx.Session, data *StabilityDeviceExecutionStep) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, stabilityDeviceExecutionStepRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ProjectId, data.Udid, data.StepId, data.Stage, data.Index, data.Name, data.Status, data.StartedAt, data.EndedAt, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}
	return m.conn.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ProjectId, data.Udid, data.StepId, data.Stage, data.Index, data.Name, data.Status, data.StartedAt, data.EndedAt, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
}

func (m *defaultStabilityDeviceExecutionStepModel) Update(ctx context.Context, session sqlx.Session, newData *StabilityDeviceExecutionStep) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, stabilityDeviceExecutionStepRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, newData.TaskId, newData.ExecuteId, newData.ProjectId, newData.Udid, newData.StepId, newData.Stage, newData.Index, newData.Name, newData.Status, newData.StartedAt, newData.EndedAt, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}
	return m.conn.ExecCtx(ctx, query, newData.TaskId, newData.ExecuteId, newData.ProjectId, newData.Udid, newData.StepId, newData.Stage, newData.Index, newData.Name, newData.Status, newData.StartedAt, newData.EndedAt, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
}

func (m *defaultStabilityDeviceExecutionStepModel) tableName() string {
	return m.table
}
