// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: reporter/stareporter.proto

package pb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = pb.TriggerMode(0)
)

// Validate checks the field values on StabilityPlanRecordItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StabilityPlanRecordItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StabilityPlanRecordItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StabilityPlanRecordItemMultiError, or nil if none found.
func (m *StabilityPlanRecordItem) ValidateAll() error {
	return m.validate(true)
}

func (m *StabilityPlanRecordItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for PlanId

	// no validation rules for PlanName

	// no validation rules for TriggerMode

	// no validation rules for TaskId

	// no validation rules for ExecuteId

	// no validation rules for Status

	// no validation rules for SuccessDevice

	// no validation rules for FailureDevice

	// no validation rules for TotalDevice

	// no validation rules for CostTime

	// no validation rules for TargetDuration

	// no validation rules for ExecutedBy

	// no validation rules for StartedAt

	// no validation rules for EndedAt

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	// no validation rules for Cleaned

	if len(errors) > 0 {
		return StabilityPlanRecordItemMultiError(errors)
	}

	return nil
}

// StabilityPlanRecordItemMultiError is an error wrapping multiple validation
// errors returned by StabilityPlanRecordItem.ValidateAll() if the designated
// constraints aren't met.
type StabilityPlanRecordItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StabilityPlanRecordItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StabilityPlanRecordItemMultiError) AllErrors() []error { return m }

// StabilityPlanRecordItemValidationError is the validation error returned by
// StabilityPlanRecordItem.Validate if the designated constraints aren't met.
type StabilityPlanRecordItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StabilityPlanRecordItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StabilityPlanRecordItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StabilityPlanRecordItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StabilityPlanRecordItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StabilityPlanRecordItemValidationError) ErrorName() string {
	return "StabilityPlanRecordItemValidationError"
}

// Error satisfies the builtin error interface
func (e StabilityPlanRecordItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStabilityPlanRecordItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StabilityPlanRecordItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StabilityPlanRecordItemValidationError{}

// Validate checks the field values on StabilityPlanMetaData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StabilityPlanMetaData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StabilityPlanMetaData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StabilityPlanMetaDataMultiError, or nil if none found.
func (m *StabilityPlanMetaData) ValidateAll() error {
	return m.validate(true)
}

func (m *StabilityPlanMetaData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAccountConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StabilityPlanMetaDataValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StabilityPlanMetaDataValidationError{
					field:  "AccountConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StabilityPlanMetaDataValidationError{
				field:  "AccountConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLarkChats() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StabilityPlanMetaDataValidationError{
						field:  fmt.Sprintf("LarkChats[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StabilityPlanMetaDataValidationError{
						field:  fmt.Sprintf("LarkChats[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StabilityPlanMetaDataValidationError{
					field:  fmt.Sprintf("LarkChats[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for DeviceType

	// no validation rules for PlatformType

	if all {
		switch v := interface{}(m.GetDevices()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StabilityPlanMetaDataValidationError{
					field:  "Devices",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StabilityPlanMetaDataValidationError{
					field:  "Devices",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevices()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StabilityPlanMetaDataValidationError{
				field:  "Devices",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PackageName

	// no validation rules for AppVersion

	// no validation rules for AppDownloadLink

	if all {
		switch v := interface{}(m.GetCustomScript()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StabilityPlanMetaDataValidationError{
					field:  "CustomScript",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StabilityPlanMetaDataValidationError{
					field:  "CustomScript",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomScript()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StabilityPlanMetaDataValidationError{
				field:  "CustomScript",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Duration

	if len(errors) > 0 {
		return StabilityPlanMetaDataMultiError(errors)
	}

	return nil
}

// StabilityPlanMetaDataMultiError is an error wrapping multiple validation
// errors returned by StabilityPlanMetaData.ValidateAll() if the designated
// constraints aren't met.
type StabilityPlanMetaDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StabilityPlanMetaDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StabilityPlanMetaDataMultiError) AllErrors() []error { return m }

// StabilityPlanMetaDataValidationError is the validation error returned by
// StabilityPlanMetaData.Validate if the designated constraints aren't met.
type StabilityPlanMetaDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StabilityPlanMetaDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StabilityPlanMetaDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StabilityPlanMetaDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StabilityPlanMetaDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StabilityPlanMetaDataValidationError) ErrorName() string {
	return "StabilityPlanMetaDataValidationError"
}

// Error satisfies the builtin error interface
func (e StabilityPlanMetaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStabilityPlanMetaData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StabilityPlanMetaDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StabilityPlanMetaDataValidationError{}

// Validate checks the field values on StabilityDeviceRecordItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StabilityDeviceRecordItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StabilityDeviceRecordItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StabilityDeviceRecordItemMultiError, or nil if none found.
func (m *StabilityDeviceRecordItem) ValidateAll() error {
	return m.validate(true)
}

func (m *StabilityDeviceRecordItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProjectId

	// no validation rules for TaskId

	// no validation rules for ExecuteId

	// no validation rules for PlanExecuteId

	// no validation rules for Status

	// no validation rules for CrashCount

	// no validation rules for AnrCount

	// no validation rules for CostTime

	// no validation rules for StartedAt

	// no validation rules for EndedAt

	if all {
		switch v := interface{}(m.GetDevice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StabilityDeviceRecordItemValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StabilityDeviceRecordItemValidationError{
					field:  "Device",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDevice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StabilityDeviceRecordItemValidationError{
				field:  "Device",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StabilityDeviceRecordItemMultiError(errors)
	}

	return nil
}

// StabilityDeviceRecordItemMultiError is an error wrapping multiple validation
// errors returned by StabilityDeviceRecordItem.ValidateAll() if the
// designated constraints aren't met.
type StabilityDeviceRecordItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StabilityDeviceRecordItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StabilityDeviceRecordItemMultiError) AllErrors() []error { return m }

// StabilityDeviceRecordItemValidationError is the validation error returned by
// StabilityDeviceRecordItem.Validate if the designated constraints aren't met.
type StabilityDeviceRecordItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StabilityDeviceRecordItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StabilityDeviceRecordItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StabilityDeviceRecordItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StabilityDeviceRecordItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StabilityDeviceRecordItemValidationError) ErrorName() string {
	return "StabilityDeviceRecordItemValidationError"
}

// Error satisfies the builtin error interface
func (e StabilityDeviceRecordItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStabilityDeviceRecordItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StabilityDeviceRecordItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StabilityDeviceRecordItemValidationError{}

// Validate checks the field values on StabilityDeviceStep with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StabilityDeviceStep) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StabilityDeviceStep with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StabilityDeviceStepMultiError, or nil if none found.
func (m *StabilityDeviceStep) ValidateAll() error {
	return m.validate(true)
}

func (m *StabilityDeviceStep) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for StepId

	// no validation rules for Stage

	// no validation rules for Index

	// no validation rules for Name

	// no validation rules for Status

	// no validation rules for Content

	// no validation rules for StartedAt

	// no validation rules for EndedAt

	if len(errors) > 0 {
		return StabilityDeviceStepMultiError(errors)
	}

	return nil
}

// StabilityDeviceStepMultiError is an error wrapping multiple validation
// errors returned by StabilityDeviceStep.ValidateAll() if the designated
// constraints aren't met.
type StabilityDeviceStepMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StabilityDeviceStepMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StabilityDeviceStepMultiError) AllErrors() []error { return m }

// StabilityDeviceStepValidationError is the validation error returned by
// StabilityDeviceStep.Validate if the designated constraints aren't met.
type StabilityDeviceStepValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StabilityDeviceStepValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StabilityDeviceStepValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StabilityDeviceStepValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StabilityDeviceStepValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StabilityDeviceStepValidationError) ErrorName() string {
	return "StabilityDeviceStepValidationError"
}

// Error satisfies the builtin error interface
func (e StabilityDeviceStepValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStabilityDeviceStep.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StabilityDeviceStepValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StabilityDeviceStepValidationError{}
