package stabilityreporterlogic

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type GetStabilityDeviceActivityLogic struct {
	*BaseLogic
}

func NewGetStabilityDeviceActivityLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetStabilityDeviceActivityLogic {
	return &GetStabilityDeviceActivityLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetStabilityDeviceActivity 获取稳测执行报告的设备Activity统计
func (l *GetStabilityDeviceActivityLogic) GetStabilityDeviceActivity(in *pb.GetStabilityDeviceActivityReq) (
	out *pb.GetStabilityDeviceActivityResp, err error,
) {
	records, err := l.svcCtx.StabilityDeviceActivityRecordModel.FindByProjectIDTaskIDUdid(l.ctx, in.ProjectId, in.TaskId, in.Udid)
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find stability device activity records, project_id: %s, task_id: %s, udid: %s, error: %+v",
			in.GetProjectId(), in.GetTaskId(), in.GetUdid(), err,
		)
	}

	out = &pb.GetStabilityDeviceActivityResp{
		Activities: make([]*pb.DeviceActivity, 0, len(records)),
	}
	for _, record := range records {
		item := &pb.DeviceActivity{}
		if err = utils.Copy(item, record, l.converters...); err != nil {
			return nil, errors.Wrapf(
				errorx.Err(errorx.CopyToStructFailure, err.Error()),
				"failed to copy stability device activity record to response, record: %+v, error: %+v",
				record, err,
			)
		}

		out.Activities = append(out.Activities, item)
	}

	return out, nil
}
