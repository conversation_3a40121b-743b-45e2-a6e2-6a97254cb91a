package stabilityreporterlogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type CreateStabilityDeviceRecordLogic struct {
	*BaseLogic
}

func NewCreateStabilityDeviceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateStabilityDeviceRecordLogic {
	return &CreateStabilityDeviceRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreateStabilityDeviceRecord 创建稳测设备的执行记录
func (l *CreateStabilityDeviceRecordLogic) CreateStabilityDeviceRecord(in *pb.PutStabilityDeviceRecordReq) (out *pb.CreateStabilityDeviceRecordResp, err error) {
	var (
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
		projectID = in.GetProjectId()
	)

	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockStabilityDeviceRecordTaskIDExecuteIDProjectIDPrefix, taskID, executeID, projectID,
	)

	fn := func() error {
		_, err := l.svcCtx.StabilityDeviceExecutionRecordModel.FindOneByProjectIdTaskIdExecuteId(
			l.ctx, taskID, executeID, projectID,
		)
		if err != nil {
			if !errors.Is(err, model.ErrNotFound) {
				return err
			}
		} else {
			return errorx.Errorf(
				errorx.AlreadyExists,
				"the stability device execution record already exists, task_id: %s, execute_id: %s, project_id: %s",
				taskID, executeID, projectID,
			)
		}

		record := l.ConvertStabilityDeviceModel(in, nil)
		return l.svcCtx.StabilityDeviceExecutionRecordModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				if _, err = l.svcCtx.StabilityDeviceExecutionRecordModel.Insert(context, session, record); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to create stability device execution record, task_id: %s, execute_id: %s, project_id: %s, plan_execute_id: %s, error: %+v",
						taskID, executeID, projectID, record.PlanExecuteId, err,
					)
				}

				return nil
			},
		)
	}
	if err = caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, redislock.WithTimeout(lockTimeout)); err != nil {
		return nil, err
	}

	return &pb.CreateStabilityDeviceRecordResp{}, nil
}
