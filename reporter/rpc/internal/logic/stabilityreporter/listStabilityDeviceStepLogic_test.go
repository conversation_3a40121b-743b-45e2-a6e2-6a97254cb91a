package stabilityreporterlogic_test

import (
	"context"
	"database/sql"
	"testing"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/config"
)

func TestListStabilityDeviceStepLogic(t *testing.T) {
	var c config.Config
	conf.MustLoad("../../../etc/reporter.yaml", &c)

	ctx := context.Background()
	sqlConn := sqlx.NewMysql(c.DB.DataSource)

	stabilityStepModel := model.NewStabilityDeviceExecutionStepModel(sqlConn)
	stabilityStepCtxModel := model.NewStabilityDeviceExecutionStepContentModel(sqlConn)
	uiStepCtxModel := model.NewUiCaseExecutionStepContentModel(sqlConn)

	stabilitySteps, err := stabilityStepModel.FindNoCacheByQuery(ctx, stabilityStepModel.SelectBuilder())
	if err != nil {
		t.Fatal(err)
	}

	for _, stabilityStep := range stabilitySteps {
		uiStepCtxs, err := uiStepCtxModel.FindNoCacheByQuery(ctx, uiStepCtxModel.SelectBuilder().Where("`step_id` = ?", stabilityStep.StepId))
		if err != nil {
			t.Fatal(err)
		}
		for _, uiStepCtx := range uiStepCtxs {
			_, err = stabilityStepCtxModel.Insert(ctx, sqlConn, &model.StabilityDeviceExecutionStepContent{
				TaskId: stabilityStep.TaskId,
				StepId: stabilityStep.StepId,
				Content: sql.NullString{
					String: uiStepCtx.Content.String,
					Valid:  uiStepCtx.Content.Valid,
				},
				Index:     uiStepCtx.Index,
				CreatedBy: "T1704",
				UpdatedBy: "T1704",
			})
			if err != nil {
				t.Fatal(err)
			}
		}
	}
}
