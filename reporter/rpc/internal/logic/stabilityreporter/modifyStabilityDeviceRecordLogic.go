package stabilityreporterlogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/redislock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ModifyStabilityDeviceRecordLogic struct {
	*BaseLogic
}

func NewModifyStabilityDeviceRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyStabilityDeviceRecordLogic {
	return &ModifyStabilityDeviceRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ModifyStabilityDeviceRecord 修改稳测设备的执行记录
func (l *ModifyStabilityDeviceRecordLogic) ModifyStabilityDeviceRecord(in *pb.PutStabilityDeviceRecordReq) (out *pb.ModifyStabilityDeviceRecordResp, err error) {
	var (
		taskID    = in.GetTaskId()
		executeID = in.GetExecuteId()
		projectID = in.GetProjectId()
	)

	key := fmt.Sprintf(
		"%s:%s:%s:%s", common.ConstLockStabilityDeviceRecordTaskIDExecuteIDProjectIDPrefix, taskID, executeID, projectID,
	)

	fn := func() error {
		origin, err := l.svcCtx.StabilityDeviceExecutionRecordModel.FindOneByProjectIdTaskIdExecuteId(
			l.ctx, taskID, executeID, projectID,
		)
		if err != nil {
			if !errors.Is(err, model.ErrNotFound) {
				return errorx.Errorf(
					errorx.DBError,
					"failed to find stability device execution record, task_id: %s, execute_id: %s, project_id: %s, error: %+v",
					taskID, executeID, projectID, err,
				)
			} else {
				return errorx.Errorf(
					errorx.NotExists,
					"the stability device execution record doesn't exist, task_id: %s, execute_id: %s, project_id: %s",
					taskID, executeID, projectID,
				)
			}
		}

		record := l.ConvertStabilityDeviceModel(in, origin)
		return l.svcCtx.StabilityDeviceExecutionRecordModel.Trans(
			l.ctx, func(context context.Context, session sqlx.Session) error {
				if _, err = l.svcCtx.StabilityDeviceExecutionRecordModel.Update(context, session, record); err != nil {
					return errors.Wrapf(
						errorx.Err(errorx.DBError, err.Error()),
						"failed to modify stability device execution record, task_id: %s, execute_id: %s, project_id: %s, plan_execute_id: %s, error: %+v",
						taskID, executeID, projectID, record.PlanExecuteId, err,
					)
				}

				return nil
			},
		)
	}
	if err = caller.LockWithOptionDo(l.svcCtx.Redis, key, fn, redislock.WithTimeout(lockTimeout)); err != nil {
		return nil, err
	}
	return &pb.ModifyStabilityDeviceRecordResp{}, nil
}
