package uireporterlogic

import (
	"context"
	"strings"

	"github.com/pkg/errors"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetUICaseStepLogic struct {
	*BaseLogic
}

func NewGetUICaseStepLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUICaseStepLogic {
	return &GetUICaseStepLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetUICaseStep 获取UI用例执行步骤
func (l *GetUICaseStepLogic) GetUICaseStep(in *pb.GetUICaseStepReq) (out *pb.GetUICaseStepResp, err error) {
	record, err := l.svcCtx.UICaseExecutionStepModel.FindOneByStepId(l.ctx, in.GetStepId())
	if err != nil {
		if !errors.Is(err, model.ErrNotFound) {
			return nil, errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find ui case execution step record, step_id: %s, error: %+v",
				in.GetStepId(), err,
			)
		} else {
			return nil, errors.WithStack(
				errorx.Errorf(
					errorx.NotExists,
					"the ui case execution step record doesn't exist, step_id: %s",
					in.GetStepId(),
				),
			)
		}
	}

	out = &pb.GetUICaseStepResp{Step: &pb.UICaseStep{}}
	if err = utils.Copy(out.Step, record, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy ui case execution step record to response, record: %+v, error: %+v",
			record, err,
		)
	}

	records, err := l.svcCtx.UICaseExecutionStepContentModel.FindByTaskIDStepID(l.ctx, in.GetTaskId(), in.GetStepId())
	if err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.DBError, err.Error()),
			"failed to find ui case execution step content records, step_id: %s, error: %+v",
			in.GetStepId(), err,
		)
	}

	var builder strings.Builder
	for _, r := range records {
		if r.Content.Valid && r.Content.String != "" {
			builder.WriteString(r.Content.String)
		}
	}
	out.Step.Content = builder.String()

	return out, nil
}
