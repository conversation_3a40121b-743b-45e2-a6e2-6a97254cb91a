package reporterlogic

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"

	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/structpb"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetPlanRecordLogic struct {
	*BaseLogic
}

func NewGetPlanRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPlanRecordLogic {
	return &GetPlanRecordLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// GetPlanRecord 计划执行记录详情
func (l *GetPlanRecordLogic) GetPlanRecord(in *pb.GetPlanRecordRequest) (resp *pb.GetPlanRecordResponse, err error) {
	defer func() {
		if err != nil {
			l.Logger.Errorf(err.Error())
		}
	}()

	selectBuilder := l.svcCtx.PlanExecutionRecordModel.SelectBuilder()
	selectBuilder = selectBuilder.Where(
		"`task_id` = ?  AND `project_id` = ? AND `execute_id` = ? AND `plan_execute_id` = ? ",
		in.TaskId, in.ProjectId, in.PlanExecuteId, in.PlanExecuteId,
	).Limit(1)

	planRecords, err2 := l.svcCtx.PlanExecutionRecordModel.FindByQuery(l.ctx, selectBuilder)

	msg := fmt.Sprintf(
		"plan planRecord detail with task_id[%s] project_id[%s] and plan_execute_id[%s] and plan_execute_id[%s] ",
		in.TaskId, in.ProjectId, in.PlanExecuteId, in.PlanExecuteId,
	)

	if err2 != nil || len(planRecords) == 0 {
		errorMsg := fmt.Sprintf("failed to find %s, error: %+v", msg, err2)
		err = errors.Wrap(errorx.Err(errorx.DBError, errorMsg), errorMsg)
		return nil, err
	}

	var (
		successSuite, failureSuite int64
		suiteItems                 []*pb.GetPlanRecordResponse_SuiteItem
		interfaceDocumentItems     []*pb.GetPlanRecordResponse_InterfaceDocumentItem
		serviceItems               []*pb.GetPlanRecordResponse_ServiceItem
	)

	// 获取关联`集合`执行情况
	selectSuiteRecordBuilder := l.svcCtx.SuiteExecutionRecordModel.SelectBuilderWithEmptyContent().Where(
		"`task_id` = ? AND `project_id` = ? AND `plan_execute_id` = ? ",
		in.TaskId, in.ProjectId, in.PlanExecuteId,
	)
	suiteRecords, err3 := l.svcCtx.SuiteExecutionRecordModel.FindByQuery(l.ctx, selectSuiteRecordBuilder)
	if err3 != nil {
		err = err3
		return nil, err
	}

	// 获取关联`接口`执行情况
	selectInterfaceRecordBuilder := l.svcCtx.InterfaceExecutionRecordModel.SelectBuilderWithEmptyContent().Where(
		"`task_id` = ? AND `project_id` = ? AND `plan_execute_id` = ? ",
		in.TaskId, in.ProjectId, in.PlanExecuteId,
	)

	interfaceRecords, err4 := l.svcCtx.InterfaceExecutionRecordModel.FindByQuery(l.ctx, selectInterfaceRecordBuilder)
	if err4 != nil {
		err = err4
		return nil, err
	}

	// 获取关联`服务`执行情况
	selectServiceRecordBuilder := l.svcCtx.ServiceExecutionRecordModel.SelectBuilderWithEmptyContent().Where(
		"`task_id` = ? AND `project_id` = ? AND `plan_execute_id` = ? ",
		in.TaskId, in.ProjectId, in.PlanExecuteId,
	)

	serviceRecords, err5 := l.svcCtx.ServiceExecutionRecordModel.FindByQuery(l.ctx, selectServiceRecordBuilder)
	if err5 != nil {
		err = err5
		return nil, err
	}

	for _, suiteRecord := range suiteRecords {
		if suiteRecord.Status.String == "Success" {
			successSuite += 1
		} else if suiteRecord.Status.String == "Failure" {
			failureSuite += 1
		}

		var item pb.GetPlanRecordResponse_SuiteItem
		_ = copier.Copy(&item, suiteRecord)

		suiteItems = append(suiteItems, &item)
	}

	for _, interfaceRecord := range interfaceRecords {
		if interfaceRecord.Status.String == "Success" {
			successSuite += 1
		} else if interfaceRecord.Status.String == "Failure" {
			failureSuite += 1
		}

		var item pb.GetPlanRecordResponse_InterfaceDocumentItem
		_ = copier.Copy(&item, interfaceRecord)

		interfaceDocumentItems = append(interfaceDocumentItems, &item)
	}

	for _, serviceRecord := range serviceRecords {
		if serviceRecord.Status.String == "Success" {
			successSuite += 1
		} else if serviceRecord.Status.String == "Failure" {
			failureSuite += 1
		}

		var item pb.GetPlanRecordResponse_ServiceItem
		_ = copier.Copy(&item, serviceRecord)

		serviceItems = append(serviceItems, &item)
	}

	// 计划关联的集合先按status排序（失败的排在前面），再按集合开始执行时间倒序排序
	sort.Slice(
		suiteItems, func(i, j int) bool {
			if suiteItems[i].Status != suiteItems[j].Status {
				return suiteItems[i].Status < suiteItems[j].Status
			}
			return suiteItems[i].StartedAt > suiteItems[j].StartedAt
		},
	)
	sort.Slice(
		interfaceDocumentItems, func(i, j int) bool {
			if interfaceDocumentItems[i].Status != interfaceDocumentItems[j].Status {
				return interfaceDocumentItems[i].Status < interfaceDocumentItems[j].Status
			}
			return interfaceDocumentItems[i].StartedAt > interfaceDocumentItems[j].StartedAt
		},
	)
	sort.Slice(
		serviceItems, func(i, j int) bool {
			if serviceItems[i].Status != serviceItems[j].Status {
				return serviceItems[i].Status < serviceItems[j].Status
			}
			return serviceItems[i].StartedAt > serviceItems[j].StartedAt
		},
	)

	planRecord := planRecords[0]

	var content structpb.Struct
	_ = protobuf.UnmarshalJSON([]byte(planRecord.Content.String), &content)

	var serviceCasesContent structpb.ListValue
	_ = protobuf.UnmarshalJSONFromString(planRecord.ServiceCasesContent.String, &serviceCasesContent)

	var generalConfig *commonpb.GeneralConfig
	var accountConfig []*commonpb.AccountConfig
	_ = json.Unmarshal([]byte(planRecord.GeneralConfig.String), &generalConfig)
	_ = json.Unmarshal([]byte(planRecord.AccountConfig.String), &accountConfig)

	var approvers []string
	if planRecord.ApprovedBy.Valid {
		_ = json.Unmarshal([]byte(planRecord.ApprovedBy.String), &approvers)
	}

	resp = &pb.GetPlanRecordResponse{
		CostTime:            planRecord.CostTime,
		ExecutedBy:          planRecord.ExecutedBy,
		StartedAt:           planRecord.StartedAt,
		EndedAt:             planRecord.EndedAt.Int64,
		TotalSuite:          planRecord.TotalSuite,
		SuccessSuite:        successSuite,
		FailureSuite:        failureSuite,
		TotalCase:           planRecord.TotalCase,
		SuccessCase:         planRecord.SuccessCase,
		FailureCase:         planRecord.FailureCase,
		PlanId:              planRecord.PlanId,
		PlanName:            planRecord.PlanName,
		PurposeType:         planRecord.PlanPurpose,
		Content:             &content,
		ServiceCasesContent: &serviceCasesContent,
		GeneralConfig:       generalConfig,
		AccountConfig:       accountConfig,
		Status:              planRecord.Status.String,
		TriggerMode:         planRecord.TriggerMode,
		Type:                planRecord.TriggerMode,
		Approvers:           approvers,
	}
	if suiteItems == nil {
		resp.SuiteItems = make([]*pb.GetPlanRecordResponse_SuiteItem, 0)
	} else {
		resp.SuiteItems = suiteItems
	}

	if interfaceDocumentItems == nil {
		resp.InterfaceDocumentItems = make([]*pb.GetPlanRecordResponse_InterfaceDocumentItem, 0)
	} else {
		resp.InterfaceDocumentItems = interfaceDocumentItems
	}

	if serviceItems == nil {
		resp.ServiceItems = make([]*pb.GetPlanRecordResponse_ServiceItem, 0)
	} else {
		resp.ServiceItems = serviceItems
	}

	return resp, nil
}
