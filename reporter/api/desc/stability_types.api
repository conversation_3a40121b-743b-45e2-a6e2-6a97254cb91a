syntax = "v1"

import "types.api"

type (
    StabilityPlanRecordItem {
        ProjectId      string        `json:"project_id"`      // 项目ID
        PlanId         string        `json:"plan_id"`         // 计划ID
        PlanName       string        `json:"plan_name"`       // 计划名称
        TriggerMode    string        `json:"trigger_mode"`    // 触发方式
        TaskId         string        `json:"task_id"`         // 任务ID
        ExecuteId      string        `json:"execute_id"`      // 执行ID
        Status         string        `json:"status"`          // 执行状态（结果）
        SuccessDevice  uint32        `json:"success_device"`  // 成功的设备数
        FailureDevice  uint32        `json:"failure_device"`  // 失败的设备数
        TotalDevice    uint32        `json:"total_device"`    // 测试设备总数
        CostTime       int64         `json:"cost_time"`       // 实际执行时长（单位：秒）
        TargetDuration uint32        `json:"target_duration"` // 目标运行时长（单位：分）
        ExecutedBy     *FullUserInfo `json:"executed_by"`     // 执行者
        StartedAt      int64         `json:"started_at"`      // 开始时间
        EndedAt        int64         `json:"ended_at"`        // 结束时间
        CreatedAt      int64         `json:"created_at"`      // 创建时间
        UpdatedAt      int64         `json:"updated_at"`      // 更新时间
        Cleaned        bool          `json:"cleaned"`         // 是否被清理
    }
    StabilityPlanMetaData {
        //Type            string        `json:"type"`              // 计划类型
        AccountConfig   string        `json:"account_config"`    // 账号配置
        LarkChats       []string      `json:"lark_chats"`        // 飞书群组
        DeviceType      int8          `json:"device_type"`       // 设备类型（真机、云手机）
        PlatformType    int8          `json:"platform_type"`     // 测试系统（Android、IOS）
        Devices         []string      `json:"devices"`           // 选择设备
        PackageName     string        `json:"package_name"`      // 包名，用于启动app
        AppVersion      string        `json:"app_version"`       // APP版本
        AppDownloadLink string        `json:"app_download_link"` // APP下载地址
        Activities      []string      `json:"activities"`        // 启动的Activity
        CustomScript    string        `json:"custom_script"`     // 自定义脚本
        Duration        uint32        `json:"duration"`          // 运行时长（单位：分钟）
        //MaintainedBy    *FullUserInfo `json:"maintained_by"`     // 维护者
    }
    StabilityDeviceRecordItem {
        ProjectId      string        `json:"project_id"`      // 项目ID
        TaskId         string        `json:"task_id"`         // 任务ID
        ExecuteId      string        `json:"execute_id"`      // 执行ID
        PlanExecuteId  string        `json:"plan_execute_id"` // 计划执行ID
        Status         string        `json:"status"`          // 执行状态（结果）
        CrashCount     uint32        `json:"crash_count"`     // crash数量
        AnrCount       uint32        `json:"anr_count"`       // anr数量
        CostTime       int64         `json:"cost_time"`       // 实际执行时长（单位：秒）
        StartedAt      int64         `json:"started_at"`      // 开始时间
        EndedAt        int64         `json:"ended_at"`        // 结束时间
        *Device 
    }
    Device {}
    StabilityDeviceStep {
        TaskId    string `json:"task_id"`    // 任务ID
        StepId    string `json:"step_id"`    // 步骤ID
        Stage     int8   `json:"stage"`      // 阶段
        Index     int64  `json:"index"`      // 步骤索引
        Name      string `json:"name"`       // 步骤名称
        Status    string `json:"status"`     // 执行状态（结果）
        Content   string `json:"content"`    // 步骤内容
        StartedAt int64  `json:"started_at"` // 开始时间
        EndedAt   int64  `json:"ended_at"`   // 结束时间
    }
)

// 获取稳测计划的执行记录
type (
    ListStabilityPlanRecordReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        PlanId string `json:"plan_id" validate:"required" zh:"计划ID"`
        Pagination *Pagination `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    ListStabilityPlanRecordResp {
        CurrentPage uint64                     `json:"current_page"`
        PageSize    uint64                     `json:"page_size"`
        TotalCount  uint64                     `json:"total_count"`
        TotalPage   uint64                     `json:"total_page"`
        Items       []*StabilityPlanRecordItem `json:"items"`
    }
)

// 搜索稳测的执行记录
type (
    SearchStabilityPlanRecordReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        Condition *Condition `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchStabilityPlanRecordResp {
        CurrentPage uint64                     `json:"current_page"`
        PageSize    uint64                     `json:"page_size"`
        TotalCount  uint64                     `json:"total_count"`
        TotalPage   uint64                     `json:"total_page"`
        Items       []*StabilityPlanRecordItem `json:"items"`
    }
)

// 获取稳测执行报告的计划信息
type (
    GetStabilityPlanRecordReq {
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        TaskId    string `form:"task_id" validate:"required" zh:"任务ID"`
        ExecuteId string `form:"execute_id" validate:"required" zh:"执行ID"`
    }
    GetStabilityPlanRecordResp {
        *StabilityPlanRecordItem
        *StabilityPlanExecuteData
    }
)

// 搜索稳测执行报告的总览设备
type (
    SearchStabilityDeviceRecordReq {
        ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
        TaskId    string `json:"task_id" validate:"required" zh:"任务ID"`
        ExecuteId string `json:"execute_id" validate:"required" zh:"执行ID"`
        Condition *Condition `json:"condition,omitempty,optional" zh:"查询条件"`
        Pagination *Pagination `json:"pagination,omitempty,optional" zh:"查询分页"`
        Sort []*SortField `json:"sort,omitempty,optional" zh:"查询排序"`
    }
    SearchStabilityDeviceRecordResp {
        CurrentPage uint64                     `json:"current_page"`
        PageSize    uint64                     `json:"page_size"`
        TotalCount  uint64                     `json:"total_count"`
        TotalPage   uint64                     `json:"total_page"`
        Items       []*StabilityPlanRecordItem `json:"items"`                                              
    }
)

// 获取稳测执行报告的设备步骤日志
type (
    ListStabilityDeviceStepReq{
        ProjectId   string `json:"project_id" validate:"required" zh:"项目ID"`
        TaskId      string `json:"task_id" validate:"required" zh:"任务ID"`
        Udid        string `json:"udid" validate:"required" zh:"设备编号"`
        WithContent bool   `json:"with_content,omitempty" zh:"是否返回步骤内容"`
    }
    ListStabilityDeviceStepResp{
        TotalCount  uint64                 `json:"total_count"`
        Items       []*StabilityDeviceStep `json:"items"`
    }
)

// 获取稳测执行报告的设备性能数据
type (
    GetStabilityDevicePerfDataReq{
        TaskId    string `form:"task_id" validate:"required" zh:"任务ID"`
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        Udid      string `form:"udid,omitempty,optional" validate:"omitempty,gte=1,lte=64" zh:"设备编号"`
        DataType  string `form:"data_type,default=CPU" validate:"required,oneof=CPU MEMORY FPS" zh:"数据类型"`
    }
    GetStabilityDevicePerfDataResp{
        *DevicePerfData
    }
)

// 获取稳测执行报告的设备Activity统计
type (
    GetStabilityDeviceActivityReq{
        TaskId    string `form:"task_id" validate:"required" zh:"任务ID"`
        ProjectId string `form:"project_id" validate:"required" zh:"项目ID"`
        Udid      string `form:"udid,omitempty,optional" validate:"omitempty,gte=1,lte=64" zh:"设备编号"`
    }
    GetStabilityDeviceActivityResp{
        Activities []*DeviceActivity `json:"activities"` // 设备活动列表
    }
)