package perfReporter

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/threading"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/response"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sse"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/wiseEyes"
	dispatcherpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
)

var (
	replyEvent = []byte("reply")
	errorEvent = []byte("error")
)

type GetPerfCaseLogLogic struct {
	*BaseLogic
}

func NewGetPerfCaseLogLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPerfCaseLogLogic {
	return &GetPerfCaseLogLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetPerfCaseLogLogic) GetPerfCaseLog(req *types.GetPerfCaseLogReq) (stream *sse.Stream, err error) {
	record, err := model.CheckPerfCaseExecutionRecordByExecuteID(
		l.ctx, l.svcCtx.PerfCaseExecutionRecordModel, req.TaskId, req.ExecuteId, req.ProjectId,
	)
	if err != nil {
		return nil, err
	}

	completed, err := isCompleted(record)
	if err != nil {
		return nil, err
	}

	var (
		query    = fmt.Sprintf(`__TAG__.pod_label_app: "perftool" AND "%s"`, req.ExecuteId)
		from, to int64
	)

	now := time.Now().UnixMilli()
	// set the start time 1 minute earlier to prevent log loss issues caused by server clock de-synchronization
	if record.StartedAt.Valid && !record.StartedAt.Time.IsZero() {
		from = record.StartedAt.Time.UnixMilli() - time.Minute.Milliseconds()
	} else {
		from = record.CreatedAt.UnixMilli() - time.Minute.Milliseconds()
	}
	if record.EndedAt.Valid && !record.EndedAt.Time.IsZero() {
		// set the end time 1 minute later to prevent log loss issues due to server clock de-synchronization
		to = record.EndedAt.Time.UnixMilli() + time.Minute.Milliseconds()
	} else {
		to = now
	}

	streamID := strconv.Itoa(int(now))
	streamIDBytes := []byte(streamID)
	stream = sse.NewStream(streamID, 1024)

	in := &wiseEyes.SearchLogReq{
		TopicName: l.svcCtx.Config.WiseEyes.Topic,
		Query:     query,
		From:      from,
		To:        to,
		Context:   "",
		Sort:      "asc",
	}
	fn := func() {
		defer stream.Quit()

		for {
			select {
			case <-l.ctx.Done():
				return
			default:
				var out *wiseEyes.SearchLogResp
				out, err = l.svcCtx.WiseEyesClient.SearchLog(in)
				if err != nil {
					stream.Event <- &sse.Event{
						ID:    streamIDBytes,
						Data:  jsonx.MarshalIgnoreError(response.Error(err)),
						Event: errorEvent,
					}
					return
				}

				for _, item := range out.Response.Results {
					if item.Time > in.From {
						in.From = item.Time
					}

					var content string
					if item.LogJson == "" && item.RawLog == "" {
						continue
					} else if item.LogJson != "" {
						content = item.LogJson
					} else if item.RawLog != "" {
						content = item.RawLog
					}

					stream.Event <- &sse.Event{
						ID: streamIDBytes,
						Data: jsonx.MarshalIgnoreError(
							response.Success(
								&types.GetPerfCaseLogResp{
									Content: content,
								},
							),
						),
						Event: replyEvent,
					}
				}

				// there are still logs not returned
				if !out.Response.ListOver {
					in.Context = out.Response.Context
				} else {
					in.Context = ""
				}
			}

			if completed && in.Context == "" {
				return
			} else if in.Context == "" {
				time.Sleep(2 * time.Second)
			}

			record, err = model.CheckPerfCaseExecutionRecordByExecuteID(
				l.ctx, l.svcCtx.PerfCaseExecutionRecordModel, req.TaskId, req.ExecuteId, req.ProjectId,
			)
			if err != nil {
				stream.Event <- &sse.Event{
					ID:    streamIDBytes,
					Data:  jsonx.MarshalIgnoreError(response.Error(err)),
					Event: errorEvent,
				}
				return
			}

			completed, err = isCompleted(record)
			if err != nil {
				stream.Event <- &sse.Event{
					ID:    streamIDBytes,
					Data:  jsonx.MarshalIgnoreError(response.Error(err)),
					Event: errorEvent,
				}
				return
			}

			if record.EndedAt.Valid && !record.EndedAt.Time.IsZero() {
				// set the end time 1 minute later to prevent log loss issues due to server clock de-synchronization
				in.To = record.EndedAt.Time.UnixMilli() + time.Minute.Milliseconds()
			} else {
				in.To = time.Now().UnixMilli()
			}
		}
	}

	threading.GoSafe(fn)

	return stream, err
}

func isCompleted(record *model.PerfCaseExecutionRecord) (bool, error) {
	if !record.Status.Valid || record.Status.String == "" {
		return false, nil
	}

	var state dispatcherpb.ComponentState
	cs, ok := dispatcherpb.ComponentState_value[record.Status.String]
	if !ok {
		return false, errorx.Errorf(
			errorx.TypeError,
			"invalid status of perf case execution record, task_id: %s, execute_id: %s, project_id: %s, status: %s",
			record.TaskId, record.ExecuteId, record.ProjectId, record.Status.String,
		)
	}

	state = dispatcherpb.ComponentState(cs)
	switch state {
	case dispatcherpb.ComponentState_Pending, dispatcherpb.ComponentState_Init, dispatcherpb.ComponentState_Started:
		return false, nil
	default:
		return true, nil
	}
}
