package saveDeviceStepTask

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/caller"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/mqc/internal/svc"
)

const defaultContentSliceLength = 1024

type contentSlice struct {
	content string
	index   int64
}

type Logic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewLogic(ctx context.Context, svcCtx *svc.ServiceContext) *Logic {
	return &Logic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *Logic) SaveDeviceStep(info *commonpb.SaveDeviceStepTaskInfo) error {
	switch info.GetUsage() {
	case commonpb.DeviceUsage_UI_TESTING:
		// TODO: 待实现
		return nil
	case commonpb.DeviceUsage_STABILITY_TESTING:
		return l.saveStabilityDeviceStep(info)
	default:
		return errors.Errorf("unknown device usage: %s", protobuf.GetEnumStringOf(info.GetUsage()))
	}
}

func (l *Logic) saveStabilityDeviceStep(info *commonpb.SaveDeviceStepTaskInfo) error {
	key := fmt.Sprintf(
		"%s:%s:%s:%s:%s:%s",
		common.ConstLockStabilityDeviceStepTaskIDExecuteIDProjectIDStageIndexPrefix,
		info.GetTaskId(), info.GetExecuteId(), info.GetProjectId(),
		info.GetStage().String(), strconv.Itoa(int(info.GetIndex())),
	)
	fn := func() error {
		record, err := l.svcCtx.StabilityDeviceExecutionStepModel.FindByStageIndex(
			l.ctx, info.GetTaskId(), info.GetExecuteId(), info.GetProjectId(),
			int64(info.GetStage()), info.GetIndex(),
		)
		if err != nil && !errors.Is(err, model.ErrNotFound) {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find stability device step, info: %s, error: %+v",
				protobuf.MarshalJSONIgnoreError(info), err,
			)
		}

		if record == nil {
			var (
				taskID     = info.GetTaskId()
				stepID     = utils.GenStepID()
				executedBy = info.GetExecutedBy()

				now = time.Now()
			)

			record = &model.StabilityDeviceExecutionStep{
				TaskId:    info.GetTaskId(),
				ExecuteId: info.GetExecuteId(),
				ProjectId: info.GetProjectId(),
				Udid:      info.GetUdid(),
				StepId:    utils.GenStepID(),
				Stage:     int64(info.GetStage()),
				Index:     info.GetIndex(),
				Name:      info.GetName(),
				Status:    info.GetStatus(),
				StartedAt: info.GetStartedAt().AsTime(),
				EndedAt:   info.GetEndedAt().AsTime(),
				CreatedBy: executedBy,
				UpdatedBy: executedBy,
				CreatedAt: now,
				UpdatedAt: now,
			}
			if _, err = l.svcCtx.StabilityDeviceExecutionStepModel.Insert(l.ctx, nil, record); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.StabilityDeviceExecutionStepModel.Table(), jsonx.MarshalIgnoreError(record), err,
				)
			}
			err = l.svcCtx.StabilityDeviceExecutionStepContentModel.Trans(
				l.ctx,
				func(context context.Context, session sqlx.Session) error {
					return mr.MapReduceVoid[contentSlice, any](
						func(source chan<- contentSlice) {
							var (
								_content = []rune(info.GetContent())
								_length  = len(_content)
								_index   = 1
							)

							for i := 0; i < _length; i += defaultContentSliceLength {
								end := i + defaultContentSliceLength
								if end > _length {
									end = _length
								}

								source <- contentSlice{
									content: string(_content[i:end]),
									index:   int64(_index),
								}
								_index += 1
							}
						}, func(item contentSlice, writer mr.Writer[any], cancel func(error)) {
							var (
								_err error

								_now = time.Now()
							)
							defer func() {
								if _err != nil {
									cancel(_err)
								}
							}()

							_data := &model.StabilityDeviceExecutionStepContent{
								TaskId: taskID,
								StepId: stepID,
								Content: sql.NullString{
									String: item.content,
									Valid:  true,
								},
								Index:     item.index,
								CreatedBy: executedBy,
								UpdatedBy: executedBy,
								CreatedAt: _now,
								UpdatedAt: _now,
							}
							if _, _err = l.svcCtx.StabilityDeviceExecutionStepContentModel.Insert(
								l.ctx, session, _data,
							); _err != nil {
								_err = errors.Wrapf(
									errorx.Err(errorx.DBError, err.Error()),
									"failed to insert table[%s] with values[%+v], error: %+v",
									l.svcCtx.StabilityDeviceExecutionStepContentModel.Table(), _data, _err,
								)
								return
							}
						}, func(pipe <-chan any, cancel func(error)) {
						}, mr.WithContext(context),
					)
				},
			)
		} else {
			l.Errorf(
				"stability device step already exists and not allowed to be updated, project_id: %s, task_id: %s, execute_id: %s, stage: %d, index: %d, name: %s",
				record.ProjectId, record.TaskId, record.ExecuteId, record.Stage, record.Index, record.Name,
			)
		}

		return nil
	}
	return caller.LockDo(l.svcCtx.Redis, key, fn)
}
