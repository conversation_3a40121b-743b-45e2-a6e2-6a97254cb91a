FROM golang:1.18.1-alpine AS builder

LABEL stage=gobuilder

<PERSON>N<PERSON> CGO_ENABLED 0
ENV GO111MODULE="on" \
    GOPROXY="http://yw-nexus.ttyuyin.com:8081/repository/group-go/" \
    GONOSUMDB="registry.ttyuyin.com,gitlab.ttyuyin.com"

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    apk update --no-cache && \
    apk add --no-cache tzdata git make && \
    git config --global http.sslVerify false

WORKDIR /build

COPY . .
RUN rm -f go.work* && cd reporter && go mod download && make api-linux


FROM scratch

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /usr/share/zoneinfo/Asia/Shanghai
ENV TZ Asia/Shanghai

WORKDIR /app
COPY --from=builder /build/reporter/bin/reporter.api.linux /app/reporter
COPY --from=builder /build/reporter/api/etc/reporter.yaml /app/etc/reporter.yaml
ENV PATH=${PATH}:/app

EXPOSE 14001

CMD ["reporter"]
