# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "mapsplatformdatasets_proto",
    srcs = [
        "data_source.proto",
        "dataset.proto",
        "maps_platform_datasets.proto",
        "maps_platform_datasets_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "mapsplatformdatasets_proto_with_info",
    deps = [
        ":mapsplatformdatasets_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "mapsplatformdatasets_java_proto",
    deps = [":mapsplatformdatasets_proto"],
)

java_grpc_library(
    name = "mapsplatformdatasets_java_grpc",
    srcs = [":mapsplatformdatasets_proto"],
    deps = [":mapsplatformdatasets_java_proto"],
)

java_gapic_library(
    name = "mapsplatformdatasets_java_gapic",
    srcs = [":mapsplatformdatasets_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "mapsplatformdatasets_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "mapsplatformdatasets_v1.yaml",
    test_deps = [
        ":mapsplatformdatasets_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":mapsplatformdatasets_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "mapsplatformdatasets_java_gapic_test_suite",
    test_classes = [
        "com.google.maps.mapsplatformdatasets.v1.MapsPlatformDatasetsClientHttpJsonTest",
        "com.google.maps.mapsplatformdatasets.v1.MapsPlatformDatasetsClientTest",
    ],
    runtime_deps = [":mapsplatformdatasets_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-maps-mapsplatformdatasets-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":mapsplatformdatasets_java_gapic",
        ":mapsplatformdatasets_java_grpc",
        ":mapsplatformdatasets_java_proto",
        ":mapsplatformdatasets_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "mapsplatformdatasets_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/maps/mapsplatformdatasets/apiv1/mapsplatformdatasetspb",
    protos = [":mapsplatformdatasets_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "mapsplatformdatasets_go_gapic",
    srcs = [":mapsplatformdatasets_proto_with_info"],
    grpc_service_config = "mapsplatformdatasets_grpc_service_config.json",
    importpath = "cloud.google.com/go/maps/mapsplatformdatasets/apiv1;mapsplatformdatasets",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "mapsplatformdatasets_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":mapsplatformdatasets_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-maps-mapsplatformdatasets-v1-go",
    deps = [
        ":mapsplatformdatasets_go_gapic",
        ":mapsplatformdatasets_go_gapic_srcjar-metadata.srcjar",
        ":mapsplatformdatasets_go_gapic_srcjar-snippets.srcjar",
        ":mapsplatformdatasets_go_gapic_srcjar-test.srcjar",
        ":mapsplatformdatasets_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "mapsplatformdatasets_py_gapic",
    srcs = [":mapsplatformdatasets_proto"],
    grpc_service_config = "mapsplatformdatasets_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "mapsplatformdatasets_v1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "mapsplatformdatasets_py_gapic_test",
    srcs = [
        "mapsplatformdatasets_py_gapic_pytest.py",
        "mapsplatformdatasets_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":mapsplatformdatasets_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "maps-mapsplatformdatasets-v1-py",
    deps = [
        ":mapsplatformdatasets_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "mapsplatformdatasets_php_proto",
    deps = [":mapsplatformdatasets_proto"],
)

php_gapic_library(
    name = "mapsplatformdatasets_php_gapic",
    srcs = [":mapsplatformdatasets_proto_with_info"],
    grpc_service_config = "mapsplatformdatasets_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "mapsplatformdatasets_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":mapsplatformdatasets_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-maps-mapsplatformdatasets-v1-php",
    deps = [
        ":mapsplatformdatasets_php_gapic",
        ":mapsplatformdatasets_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "mapsplatformdatasets_nodejs_gapic",
    package_name = "@googlemaps/maps-platform-datasets",
    src = ":mapsplatformdatasets_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "mapsplatformdatasets_grpc_service_config.json",
    package = "google.maps.mapsplatformdatasets.v1",
    rest_numeric_enums = True,
    service_yaml = "mapsplatformdatasets_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "maps-mapsplatformdatasets-v1-nodejs",
    deps = [
        ":mapsplatformdatasets_nodejs_gapic",
        ":mapsplatformdatasets_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "mapsplatformdatasets_ruby_proto",
    deps = [":mapsplatformdatasets_proto"],
)

ruby_grpc_library(
    name = "mapsplatformdatasets_ruby_grpc",
    srcs = [":mapsplatformdatasets_proto"],
    deps = [":mapsplatformdatasets_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "mapsplatformdatasets_ruby_gapic",
    srcs = [":mapsplatformdatasets_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-maps-mapsplatformdatasets-v1"],
    grpc_service_config = "mapsplatformdatasets_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "mapsplatformdatasets_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":mapsplatformdatasets_ruby_grpc",
        ":mapsplatformdatasets_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-maps-mapsplatformdatasets-v1-ruby",
    deps = [
        ":mapsplatformdatasets_ruby_gapic",
        ":mapsplatformdatasets_ruby_grpc",
        ":mapsplatformdatasets_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "mapsplatformdatasets_csharp_proto",
    extra_opts = [],
    deps = [":mapsplatformdatasets_proto"],
)

csharp_grpc_library(
    name = "mapsplatformdatasets_csharp_grpc",
    srcs = [":mapsplatformdatasets_proto"],
    deps = [":mapsplatformdatasets_csharp_proto"],
)

csharp_gapic_library(
    name = "mapsplatformdatasets_csharp_gapic",
    srcs = [":mapsplatformdatasets_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "mapsplatformdatasets_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "mapsplatformdatasets_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":mapsplatformdatasets_csharp_grpc",
        ":mapsplatformdatasets_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-maps-mapsplatformdatasets-v1-csharp",
    deps = [
        ":mapsplatformdatasets_csharp_gapic",
        ":mapsplatformdatasets_csharp_grpc",
        ":mapsplatformdatasets_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "mapsplatformdatasets_cc_proto",
    deps = [":mapsplatformdatasets_proto"],
)

cc_grpc_library(
    name = "mapsplatformdatasets_cc_grpc",
    srcs = [":mapsplatformdatasets_proto"],
    grpc_only = True,
    deps = [":mapsplatformdatasets_cc_proto"],
)
