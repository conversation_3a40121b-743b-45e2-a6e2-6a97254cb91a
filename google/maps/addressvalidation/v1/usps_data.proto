// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.maps.addressvalidation.v1;

option cc_enable_arenas = true;
option csharp_namespace = "Google.Maps.AddressValidation.V1";
option go_package = "cloud.google.com/go/maps/addressvalidation/apiv1/addressvalidationpb;addressvalidationpb";
option java_multiple_files = true;
option java_outer_classname = "UspsDataProto";
option java_package = "com.google.maps.addressvalidation.v1";
option objc_class_prefix = "GMPAVV1";
option php_namespace = "Google\\Maps\\AddressValidation\\V1";
option ruby_package = "Google::Maps::AddressValidation::V1";

// USPS representation of a US address.
message UspsAddress {
  // First address line.
  string first_address_line = 1;

  // Firm name.
  string firm = 2;

  // Second address line.
  string second_address_line = 3;

  // Puerto Rican urbanization name.
  string urbanization = 4;

  // City + state + postal code.
  string city_state_zip_address_line = 5;

  // City name.
  string city = 6;

  // 2 letter state code.
  string state = 7;

  // Postal code e.g. 10009.
  string zip_code = 8;

  // 4-digit postal code extension e.g. 5023.
  string zip_code_extension = 9;
}

// The USPS data for the address. `uspsData` is not guaranteed to be fully
// populated for every US or PR address sent to the Address Validation API. It's
// recommended to integrate the backup address fields in the response if you
// utilize uspsData as the primary part of the response.
message UspsData {
  // USPS standardized address.
  UspsAddress standardized_address = 1;

  // 2 digit delivery point code
  string delivery_point_code = 2;

  // The delivery point check digit. This number is added to the end of the
  // delivery_point_barcode for mechanically scanned mail. Adding all the
  // digits of the delivery_point_barcode, delivery_point_check_digit, postal
  // code, and ZIP+4 together should yield a number divisible by 10.
  string delivery_point_check_digit = 3;

  // The possible values for DPV confirmation. Returns a single character or
  // returns no value.
  //
  // * `N`: Primary and any secondary number information failed to
  // DPV confirm.
  // * `D`: Address was DPV confirmed for the primary number only, and the
  // secondary number information was missing.
  // * `S`: Address was DPV confirmed for the primary number only, and the
  // secondary number information was present but not confirmed.
  // * `Y`: Address was DPV confirmed for primary and any secondary numbers.
  // * Empty: If the response does not contain a `dpv_confirmation` value, the
  // address was not submitted for DPV confirmation.
  string dpv_confirmation = 4;

  // The footnotes from delivery point validation.
  // Multiple footnotes may be strung together in the same string.
  //
  // * `AA`: Input address matched to the ZIP+4 file
  // * `A1`: Input address was not matched to the ZIP+4 file
  // * `BB`: Matched to DPV (all components)
  // * `CC`: Secondary number not matched and not required
  // * `C1`: Secondary number not matched but required
  // * `N1`: High-rise address missing secondary number
  // * `M1`: Primary number missing
  // * `M3`: Primary number invalid
  // * `P1`: Input address PO, RR or HC box number missing
  // * `P3`: Input address PO, RR, or HC Box number invalid
  // * `F1`: Input address matched to a military address
  // * `G1`: Input address matched to a general delivery address
  // * `U1`: Input address matched to a unique ZIP code
  // * `PB`: Input address matched to PBSA record
  // * `RR`: DPV confirmed address with PMB information
  // * `R1`: DPV confirmed address without PMB information
  // * `R7`: Carrier Route R777 or R779 record
  // * `IA`: Informed Address identified
  // * `TA`: Primary number matched by dropping a trailing alpha
  string dpv_footnote = 5;

  // Indicates if the address is a CMRA (Commercial Mail Receiving Agency)--a
  // private business receiving mail for clients. Returns a single character.
  //
  // * `Y`: The address is a CMRA
  // * `N`: The address is not a CMRA
  string dpv_cmra = 6;

  // Is this place vacant?
  // Returns a single character.
  //
  // * `Y`: The address is vacant
  // * `N`: The address is not vacant
  string dpv_vacant = 7;

  // Is this a no stat address or an active address?
  // No stat addresses are ones which are not continuously occupied or addresses
  // that the USPS does not service. Returns a single character.
  //
  // * `Y`: The address is not active
  // * `N`: The address is active
  string dpv_no_stat = 8;

  // Indicates the NoStat type. Returns a reason code as int.
  //
  // * `1`: IDA (Internal Drop Address) – Addresses that do not receive mail
  // directly from the USPS but are delivered to a drop address that services
  // them.
  // * `2`: CDS - Addresses that have not yet become deliverable. For example, a
  // new subdivision where lots and primary numbers have been determined, but no
  // structure exists yet for occupancy.
  // * `3`: Collision - Addresses that do not actually DPV confirm.
  // * `4`: CMZ (College, Military and Other Types) - ZIP + 4 records USPS has
  // incorporated into the data.
  // * `5`: Regular - Indicates addresses not receiving delivery and the
  // addresses are not counted as possible deliveries.
  // * `6`: Secondary Required - The address requires secondary information.
  int32 dpv_no_stat_reason_code = 29;

  // Flag indicates mail is delivered to a single receptable at a site.
  // Returns a single character.
  //
  // * `Y`: The mail is delivered to a single receptable at a site.
  // * `N`: The mail is not delivered to a single receptable at a site.
  string dpv_drop = 30;

  // Indicates that mail is not delivered to the street address.
  // Returns a single character.
  //
  // * `Y`: The mail is not delivered to the street address.
  // * `N`: The mail is delivered to the street address.
  string dpv_throwback = 31;

  // Flag indicates mail delivery is not performed every day of the week.
  // Returns a single character.
  //
  // * `Y`: The mail delivery is not performed every day of the week.
  // * `N`: No indication the mail delivery is not performed every day of the
  // week.
  string dpv_non_delivery_days = 32;

  // Integer identifying non-delivery days. It can be interrogated using bit
  // flags:
  // 0x40 – Sunday is a non-delivery day
  // 0x20 – Monday is a non-delivery day
  // 0x10 – Tuesday is a non-delivery day
  // 0x08 – Wednesday is a non-delivery day
  // 0x04 – Thursday is a non-delivery day
  // 0x02 – Friday is a non-delivery day
  // 0x01 – Saturday is a non-delivery day
  int32 dpv_non_delivery_days_values = 33;

  // Flag indicates door is accessible, but package will not be left due to
  // security concerns.
  // Returns a single character.
  //
  // * `Y`: The package will not be left due to security concerns.
  // * `N`: No indication the package will not be left due to security concerns.
  string dpv_no_secure_location = 34;

  // Indicates the address was matched to PBSA record.
  // Returns a single character.
  //
  // * `Y`: The address was matched to PBSA record.
  // * `N`: The address was not matched to PBSA record.
  string dpv_pbsa = 35;

  // Flag indicates addresses where USPS cannot knock on a door to deliver mail.
  // Returns a single character.
  //
  // * `Y`: The door is not accessible.
  // * `N`: No indication the door is not accessible.
  string dpv_door_not_accessible = 36;

  // Indicates that more than one DPV return code is valid for the address.
  // Returns a single character.
  //
  // * `Y`: Address was DPV confirmed for primary and any secondary numbers.
  // * `N`: Primary and any secondary number information failed to
  // DPV confirm.
  // * `S`: Address was DPV confirmed for the primary number only, and the
  // secondary number information was present by not confirmed,  or a single
  // trailing alpha on a primary number was dropped to make a DPV match and
  // secondary information required.
  // * `D`: Address was DPV confirmed for the primary number only, and the
  // secondary number information was missing.
  // * `R`: Address confirmed but assigned to phantom route R777 and R779 and
  // USPS delivery is not provided.
  string dpv_enhanced_delivery_code = 37;

  // The carrier route code.
  // A four character code consisting of a one letter prefix and a three digit
  // route designator.
  //
  // Prefixes:
  //
  // * `C`: Carrier route (or city route)
  // * `R`: Rural route
  // * `H`: Highway Contract Route
  // * `B`: Post Office Box Section
  // * `G`: General delivery unit
  string carrier_route = 9;

  // Carrier route rate sort indicator.
  string carrier_route_indicator = 10;

  // The delivery address is matchable, but the EWS file indicates that an exact
  // match will be available soon.
  bool ews_no_match = 11;

  // Main post office city.
  string post_office_city = 12;

  // Main post office state.
  string post_office_state = 13;

  // Abbreviated city.
  string abbreviated_city = 14;

  // FIPS county code.
  string fips_county_code = 15;

  // County name.
  string county = 16;

  // Enhanced Line of Travel (eLOT) number.
  string elot_number = 17;

  // eLOT Ascending/Descending Flag (A/D).
  string elot_flag = 18;

  // LACSLink return code.
  string lacs_link_return_code = 19;

  // LACSLink indicator.
  string lacs_link_indicator = 20;

  // PO Box only postal code.
  bool po_box_only_postal_code = 21;

  // Footnotes from matching a street or highrise record to suite information.
  // If business name match is found, the secondary number is returned.
  //
  // * `A`: SuiteLink record match, business address improved.
  // * `00`: No match, business address is not improved.
  string suitelink_footnote = 22;

  // PMB (Private Mail Box) unit designator.
  string pmb_designator = 23;

  // PMB (Private Mail Box) number;
  string pmb_number = 24;

  // Type of the address record that matches the input address.
  //
  // * `F`: FIRM. This is a match to a Firm Record, which is the finest level of
  // match available for an address.
  // * `G`: GENERAL DELIVERY. This is a match to a General Delivery record.
  // * `H`: BUILDING / APARTMENT. This is a match to a Building or Apartment
  // record.
  // * `P`: POST OFFICE BOX. This is a match to a Post Office Box.
  // * `R`: RURAL ROUTE or HIGHWAY CONTRACT: This is a match to either a Rural
  // Route or a Highway Contract record, both of which may have associated Box
  // Number ranges.
  // * `S`: STREET RECORD: This is a match to a Street record containing a valid
  // primary number range.
  string address_record_type = 25;

  // Indicator that a default address was found, but more specific addresses
  // exists.
  bool default_address = 26;

  // Error message for USPS data retrieval. This is populated when USPS
  // processing is suspended because of the detection of artificially created
  // addresses.
  //
  // The USPS data fields might not be populated when this error is present.
  string error_message = 27;

  // Indicator that the request has been CASS processed.
  bool cass_processed = 28;
}
