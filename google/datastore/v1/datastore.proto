// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.datastore.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/routing.proto";
import "google/datastore/v1/aggregation_result.proto";
import "google/datastore/v1/entity.proto";
import "google/datastore/v1/query.proto";
import "google/datastore/v1/query_profile.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Datastore.V1";
option go_package = "cloud.google.com/go/datastore/apiv1/datastorepb;datastorepb";
option java_multiple_files = true;
option java_outer_classname = "DatastoreProto";
option java_package = "com.google.datastore.v1";
option php_namespace = "Google\\Cloud\\Datastore\\V1";
option ruby_package = "Google::Cloud::Datastore::V1";

// Each RPC normalizes the partition IDs of the keys in its input entities,
// and always returns entities with keys with normalized partition IDs.
// This applies to all keys and entities, including those in values, except keys
// with both an empty path and an empty or unset partition ID. Normalization of
// input keys sets the project ID (if not already set) to the project ID from
// the request.
//
service Datastore {
  option (google.api.default_host) = "datastore.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/datastore";

  // Looks up entities by key.
  rpc Lookup(LookupRequest) returns (LookupResponse) {
    option (google.api.http) = {
      post: "/v1/projects/{project_id}:lookup"
      body: "*"
    };
    option (google.api.routing) = {
      routing_parameters { field: "project_id" }
      routing_parameters { field: "database_id" }
    };
    option (google.api.method_signature) = "project_id,read_options,keys";
  }

  // Queries for entities.
  rpc RunQuery(RunQueryRequest) returns (RunQueryResponse) {
    option (google.api.http) = {
      post: "/v1/projects/{project_id}:runQuery"
      body: "*"
    };
    option (google.api.routing) = {
      routing_parameters { field: "project_id" }
      routing_parameters { field: "database_id" }
    };
  }

  // Runs an aggregation query.
  rpc RunAggregationQuery(RunAggregationQueryRequest)
      returns (RunAggregationQueryResponse) {
    option (google.api.http) = {
      post: "/v1/projects/{project_id}:runAggregationQuery"
      body: "*"
    };
    option (google.api.routing) = {
      routing_parameters { field: "project_id" }
      routing_parameters { field: "database_id" }
    };
  }

  // Begins a new transaction.
  rpc BeginTransaction(BeginTransactionRequest)
      returns (BeginTransactionResponse) {
    option (google.api.http) = {
      post: "/v1/projects/{project_id}:beginTransaction"
      body: "*"
    };
    option (google.api.routing) = {
      routing_parameters { field: "project_id" }
      routing_parameters { field: "database_id" }
    };
    option (google.api.method_signature) = "project_id";
  }

  // Commits a transaction, optionally creating, deleting or modifying some
  // entities.
  rpc Commit(CommitRequest) returns (CommitResponse) {
    option (google.api.http) = {
      post: "/v1/projects/{project_id}:commit"
      body: "*"
    };
    option (google.api.routing) = {
      routing_parameters { field: "project_id" }
      routing_parameters { field: "database_id" }
    };
    option (google.api.method_signature) =
        "project_id,mode,transaction,mutations";
    option (google.api.method_signature) = "project_id,mode,mutations";
  }

  // Rolls back a transaction.
  rpc Rollback(RollbackRequest) returns (RollbackResponse) {
    option (google.api.http) = {
      post: "/v1/projects/{project_id}:rollback"
      body: "*"
    };
    option (google.api.routing) = {
      routing_parameters { field: "project_id" }
      routing_parameters { field: "database_id" }
    };
    option (google.api.method_signature) = "project_id,transaction";
  }

  // Allocates IDs for the given keys, which is useful for referencing an entity
  // before it is inserted.
  rpc AllocateIds(AllocateIdsRequest) returns (AllocateIdsResponse) {
    option (google.api.http) = {
      post: "/v1/projects/{project_id}:allocateIds"
      body: "*"
    };
    option (google.api.routing) = {
      routing_parameters { field: "project_id" }
      routing_parameters { field: "database_id" }
    };
    option (google.api.method_signature) = "project_id,keys";
  }

  // Prevents the supplied keys' IDs from being auto-allocated by Cloud
  // Datastore.
  rpc ReserveIds(ReserveIdsRequest) returns (ReserveIdsResponse) {
    option (google.api.http) = {
      post: "/v1/projects/{project_id}:reserveIds"
      body: "*"
    };
    option (google.api.routing) = {
      routing_parameters { field: "project_id" }
      routing_parameters { field: "database_id" }
    };
    option (google.api.method_signature) = "project_id,keys";
  }
}

// The request for [Datastore.Lookup][google.datastore.v1.Datastore.Lookup].
message LookupRequest {
  // Required. The ID of the project against which to make the request.
  string project_id = 8 [(google.api.field_behavior) = REQUIRED];

  // The ID of the database against which to make the request.
  //
  // '(default)' is not allowed; please use empty string '' to refer the default
  // database.
  string database_id = 9;

  // The options for this lookup request.
  ReadOptions read_options = 1;

  // Required. Keys of entities to look up.
  repeated Key keys = 3 [(google.api.field_behavior) = REQUIRED];

  // The properties to return. Defaults to returning all properties.
  //
  // If this field is set and an entity has a property not referenced in the
  // mask, it will be absent from [LookupResponse.found.entity.properties][].
  //
  // The entity's key is always returned.
  PropertyMask property_mask = 5;
}

// The response for [Datastore.Lookup][google.datastore.v1.Datastore.Lookup].
message LookupResponse {
  // Entities found as `ResultType.FULL` entities. The order of results in this
  // field is undefined and has no relation to the order of the keys in the
  // input.
  repeated EntityResult found = 1;

  // Entities not found as `ResultType.KEY_ONLY` entities. The order of results
  // in this field is undefined and has no relation to the order of the keys
  // in the input.
  repeated EntityResult missing = 2;

  // A list of keys that were not looked up due to resource constraints. The
  // order of results in this field is undefined and has no relation to the
  // order of the keys in the input.
  repeated Key deferred = 3;

  // The identifier of the transaction that was started as part of this Lookup
  // request.
  //
  // Set only when
  // [ReadOptions.new_transaction][google.datastore.v1.ReadOptions.new_transaction]
  // was set in
  // [LookupRequest.read_options][google.datastore.v1.LookupRequest.read_options].
  bytes transaction = 5;

  // The time at which these entities were read or found missing.
  google.protobuf.Timestamp read_time = 7;
}

// The request for [Datastore.RunQuery][google.datastore.v1.Datastore.RunQuery].
message RunQueryRequest {
  // Required. The ID of the project against which to make the request.
  string project_id = 8 [(google.api.field_behavior) = REQUIRED];

  // The ID of the database against which to make the request.
  //
  // '(default)' is not allowed; please use empty string '' to refer the default
  // database.
  string database_id = 9;

  // Entities are partitioned into subsets, identified by a partition ID.
  // Queries are scoped to a single partition.
  // This partition ID is normalized with the standard default context
  // partition ID.
  PartitionId partition_id = 2;

  // The options for this query.
  ReadOptions read_options = 1;

  // The type of query.
  oneof query_type {
    // The query to run.
    Query query = 3;

    // The GQL query to run. This query must be a non-aggregation query.
    GqlQuery gql_query = 7;
  }

  // The properties to return.
  // This field must not be set for a projection query.
  //
  // See
  // [LookupRequest.property_mask][google.datastore.v1.LookupRequest.property_mask].
  PropertyMask property_mask = 10;

  // Optional. Explain options for the query. If set, additional query
  // statistics will be returned. If not, only query results will be returned.
  ExplainOptions explain_options = 12 [(google.api.field_behavior) = OPTIONAL];
}

// The response for
// [Datastore.RunQuery][google.datastore.v1.Datastore.RunQuery].
message RunQueryResponse {
  // A batch of query results (always present).
  QueryResultBatch batch = 1;

  // The parsed form of the `GqlQuery` from the request, if it was set.
  Query query = 2;

  // The identifier of the transaction that was started as part of this
  // RunQuery request.
  //
  // Set only when
  // [ReadOptions.new_transaction][google.datastore.v1.ReadOptions.new_transaction]
  // was set in
  // [RunQueryRequest.read_options][google.datastore.v1.RunQueryRequest.read_options].
  bytes transaction = 5;

  // Query explain metrics. This is only present when the
  // [RunQueryRequest.explain_options][google.datastore.v1.RunQueryRequest.explain_options]
  // is provided, and it is sent only once with the last response in the stream.
  ExplainMetrics explain_metrics = 9;
}

// The request for
// [Datastore.RunAggregationQuery][google.datastore.v1.Datastore.RunAggregationQuery].
message RunAggregationQueryRequest {
  // Required. The ID of the project against which to make the request.
  string project_id = 8 [(google.api.field_behavior) = REQUIRED];

  // The ID of the database against which to make the request.
  //
  // '(default)' is not allowed; please use empty string '' to refer the default
  // database.
  string database_id = 9;

  // Entities are partitioned into subsets, identified by a partition ID.
  // Queries are scoped to a single partition.
  // This partition ID is normalized with the standard default context
  // partition ID.
  PartitionId partition_id = 2;

  // The options for this query.
  ReadOptions read_options = 1;

  // The type of query.
  oneof query_type {
    // The query to run.
    AggregationQuery aggregation_query = 3;

    // The GQL query to run. This query must be an aggregation query.
    GqlQuery gql_query = 7;
  }

  // Optional. Explain options for the query. If set, additional query
  // statistics will be returned. If not, only query results will be returned.
  ExplainOptions explain_options = 11 [(google.api.field_behavior) = OPTIONAL];
}

// The response for
// [Datastore.RunAggregationQuery][google.datastore.v1.Datastore.RunAggregationQuery].
message RunAggregationQueryResponse {
  // A batch of aggregation results. Always present.
  AggregationResultBatch batch = 1;

  // The parsed form of the `GqlQuery` from the request, if it was set.
  AggregationQuery query = 2;

  // The identifier of the transaction that was started as part of this
  // RunAggregationQuery request.
  //
  // Set only when
  // [ReadOptions.new_transaction][google.datastore.v1.ReadOptions.new_transaction]
  // was set in
  // [RunAggregationQueryRequest.read_options][google.datastore.v1.RunAggregationQueryRequest.read_options].
  bytes transaction = 5;

  // Query explain metrics. This is only present when the
  // [RunAggregationQueryRequest.explain_options][google.datastore.v1.RunAggregationQueryRequest.explain_options]
  // is provided, and it is sent only once with the last response in the stream.
  ExplainMetrics explain_metrics = 9;
}

// The request for
// [Datastore.BeginTransaction][google.datastore.v1.Datastore.BeginTransaction].
message BeginTransactionRequest {
  // Required. The ID of the project against which to make the request.
  string project_id = 8 [(google.api.field_behavior) = REQUIRED];

  // The ID of the database against which to make the request.
  //
  // '(default)' is not allowed; please use empty string '' to refer the default
  // database.
  string database_id = 9;

  // Options for a new transaction.
  TransactionOptions transaction_options = 10;
}

// The response for
// [Datastore.BeginTransaction][google.datastore.v1.Datastore.BeginTransaction].
message BeginTransactionResponse {
  // The transaction identifier (always present).
  bytes transaction = 1;
}

// The request for [Datastore.Rollback][google.datastore.v1.Datastore.Rollback].
message RollbackRequest {
  // Required. The ID of the project against which to make the request.
  string project_id = 8 [(google.api.field_behavior) = REQUIRED];

  // The ID of the database against which to make the request.
  //
  // '(default)' is not allowed; please use empty string '' to refer the default
  // database.
  string database_id = 9;

  // Required. The transaction identifier, returned by a call to
  // [Datastore.BeginTransaction][google.datastore.v1.Datastore.BeginTransaction].
  bytes transaction = 1 [(google.api.field_behavior) = REQUIRED];
}

// The response for
// [Datastore.Rollback][google.datastore.v1.Datastore.Rollback]. (an empty
// message).
message RollbackResponse {}

// The request for [Datastore.Commit][google.datastore.v1.Datastore.Commit].
message CommitRequest {
  // The modes available for commits.
  enum Mode {
    // Unspecified. This value must not be used.
    MODE_UNSPECIFIED = 0;

    // Transactional: The mutations are either all applied, or none are applied.
    // Learn about transactions
    // [here](https://cloud.google.com/datastore/docs/concepts/transactions).
    TRANSACTIONAL = 1;

    // Non-transactional: The mutations may not apply as all or none.
    NON_TRANSACTIONAL = 2;
  }

  // Required. The ID of the project against which to make the request.
  string project_id = 8 [(google.api.field_behavior) = REQUIRED];

  // The ID of the database against which to make the request.
  //
  // '(default)' is not allowed; please use empty string '' to refer the default
  // database.
  string database_id = 9;

  // The type of commit to perform. Defaults to `TRANSACTIONAL`.
  Mode mode = 5;

  // Must be set when mode is `TRANSACTIONAL`.
  oneof transaction_selector {
    // The identifier of the transaction associated with the commit. A
    // transaction identifier is returned by a call to
    // [Datastore.BeginTransaction][google.datastore.v1.Datastore.BeginTransaction].
    bytes transaction = 1;

    // Options for beginning a new transaction for this request.
    // The transaction is committed when the request completes. If specified,
    // [TransactionOptions.mode][google.datastore.v1.TransactionOptions] must be
    // [TransactionOptions.ReadWrite][google.datastore.v1.TransactionOptions.ReadWrite].
    TransactionOptions single_use_transaction = 10;
  }

  // The mutations to perform.
  //
  // When mode is `TRANSACTIONAL`, mutations affecting a single entity are
  // applied in order. The following sequences of mutations affecting a single
  // entity are not permitted in a single `Commit` request:
  //
  // - `insert` followed by `insert`
  // - `update` followed by `insert`
  // - `upsert` followed by `insert`
  // - `delete` followed by `update`
  //
  // When mode is `NON_TRANSACTIONAL`, no two mutations may affect a single
  // entity.
  repeated Mutation mutations = 6;
}

// The response for [Datastore.Commit][google.datastore.v1.Datastore.Commit].
message CommitResponse {
  // The result of performing the mutations.
  // The i-th mutation result corresponds to the i-th mutation in the request.
  repeated MutationResult mutation_results = 3;

  // The number of index entries updated during the commit, or zero if none were
  // updated.
  int32 index_updates = 4;

  // The transaction commit timestamp. Not set for non-transactional commits.
  google.protobuf.Timestamp commit_time = 8;
}

// The request for
// [Datastore.AllocateIds][google.datastore.v1.Datastore.AllocateIds].
message AllocateIdsRequest {
  // Required. The ID of the project against which to make the request.
  string project_id = 8 [(google.api.field_behavior) = REQUIRED];

  // The ID of the database against which to make the request.
  //
  // '(default)' is not allowed; please use empty string '' to refer the default
  // database.
  string database_id = 9;

  // Required. A list of keys with incomplete key paths for which to allocate
  // IDs. No key may be reserved/read-only.
  repeated Key keys = 1 [(google.api.field_behavior) = REQUIRED];
}

// The response for
// [Datastore.AllocateIds][google.datastore.v1.Datastore.AllocateIds].
message AllocateIdsResponse {
  // The keys specified in the request (in the same order), each with
  // its key path completed with a newly allocated ID.
  repeated Key keys = 1;
}

// The request for
// [Datastore.ReserveIds][google.datastore.v1.Datastore.ReserveIds].
message ReserveIdsRequest {
  // Required. The ID of the project against which to make the request.
  string project_id = 8 [(google.api.field_behavior) = REQUIRED];

  // The ID of the database against which to make the request.
  //
  // '(default)' is not allowed; please use empty string '' to refer the default
  // database.
  string database_id = 9;

  // Required. A list of keys with complete key paths whose numeric IDs should
  // not be auto-allocated.
  repeated Key keys = 1 [(google.api.field_behavior) = REQUIRED];
}

// The response for
// [Datastore.ReserveIds][google.datastore.v1.Datastore.ReserveIds].
message ReserveIdsResponse {}

// A mutation to apply to an entity.
message Mutation {
  // The possible ways to resolve a conflict detected in a mutation.
  enum ConflictResolutionStrategy {
    // Unspecified. Defaults to `SERVER_VALUE`.
    STRATEGY_UNSPECIFIED = 0;

    // The server entity is kept.
    SERVER_VALUE = 1;

    // The whole commit request fails.
    FAIL = 3;
  }

  // The mutation operation.
  //
  // For `insert`, `update`, and `upsert`:
  // - The entity's key must not be reserved/read-only.
  // - No property in the entity may have a reserved name,
  //   not even a property in an entity in a value.
  // - No value in the entity may have meaning 18,
  //   not even a value in an entity in another value.
  oneof operation {
    // The entity to insert. The entity must not already exist.
    // The entity key's final path element may be incomplete.
    Entity insert = 4;

    // The entity to update. The entity must already exist.
    // Must have a complete key path.
    Entity update = 5;

    // The entity to upsert. The entity may or may not already exist.
    // The entity key's final path element may be incomplete.
    Entity upsert = 6;

    // The key of the entity to delete. The entity may or may not already exist.
    // Must have a complete key path and must not be reserved/read-only.
    Key delete = 7;
  }

  // When set, the server will detect whether or not this mutation conflicts
  // with the current version of the entity on the server. Conflicting mutations
  // are not applied, and are marked as such in MutationResult.
  oneof conflict_detection_strategy {
    // The version of the entity that this mutation is being applied
    // to. If this does not match the current version on the server, the
    // mutation conflicts.
    int64 base_version = 8;

    // The update time of the entity that this mutation is being applied
    // to. If this does not match the current update time on the server, the
    // mutation conflicts.
    google.protobuf.Timestamp update_time = 11;
  }

  // The strategy to use when a conflict is detected. Defaults to
  // `SERVER_VALUE`.
  // If this is set, then `conflict_detection_strategy` must also be set.
  ConflictResolutionStrategy conflict_resolution_strategy = 10;

  // The properties to write in this mutation.
  // None of the properties in the mask may have a reserved name, except for
  // `__key__`.
  // This field is ignored for `delete`.
  //
  // If the entity already exists, only properties referenced in the mask are
  // updated, others are left untouched.
  // Properties referenced in the mask but not in the entity are deleted.
  PropertyMask property_mask = 9;

  // Optional. The transforms to perform on the entity.
  //
  // This field can be set only when the operation is `insert`, `update`,
  // or `upsert`. If present, the transforms are be applied to the entity
  // regardless of the property mask, in order, after the operation.
  repeated PropertyTransform property_transforms = 12
      [(google.api.field_behavior) = OPTIONAL];
}

// A transformation of an entity property.
message PropertyTransform {
  // A value that is calculated by the server.
  enum ServerValue {
    // Unspecified. This value must not be used.
    SERVER_VALUE_UNSPECIFIED = 0;

    // The time at which the server processed the request, with millisecond
    // precision. If used on multiple properties (same or different entities)
    // in a transaction, all the properties will get the same server timestamp.
    REQUEST_TIME = 1;
  }

  // Optional. The name of the property.
  //
  // Property paths (a list of property names separated by dots (`.`)) may be
  // used to refer to properties inside entity values. For example `foo.bar`
  // means the property `bar` inside the entity property `foo`.
  //
  // If a property name contains a dot `.` or a backlslash `\`, then that name
  // must be escaped.
  string property = 1 [(google.api.field_behavior) = OPTIONAL];

  // The transformation to apply to the property.
  oneof transform_type {
    // Sets the property to the given server value.
    ServerValue set_to_server_value = 2;

    // Adds the given value to the property's current value.
    //
    // This must be an integer or a double value.
    // If the property is not an integer or double, or if the property does not
    // yet exist, the transformation will set the property to the given value.
    // If either of the given value or the current property value are doubles,
    // both values will be interpreted as doubles. Double arithmetic and
    // representation of double values follows IEEE 754 semantics.
    // If there is positive/negative integer overflow, the property is resolved
    // to the largest magnitude positive/negative integer.
    Value increment = 3;

    // Sets the property to the maximum of its current value and the given
    // value.
    //
    // This must be an integer or a double value.
    // If the property is not an integer or double, or if the property does not
    // yet exist, the transformation will set the property to the given value.
    // If a maximum operation is applied where the property and the input value
    // are of mixed types (that is - one is an integer and one is a double)
    // the property takes on the type of the larger operand. If the operands are
    // equivalent (e.g. 3 and 3.0), the property does not change.
    // 0, 0.0, and -0.0 are all zero. The maximum of a zero stored value and
    // zero input value is always the stored value.
    // The maximum of any numeric value x and NaN is NaN.
    Value maximum = 4;

    // Sets the property to the minimum of its current value and the given
    // value.
    //
    // This must be an integer or a double value.
    // If the property is not an integer or double, or if the property does not
    // yet exist, the transformation will set the property to the input value.
    // If a minimum operation is applied where the property and the input value
    // are of mixed types (that is - one is an integer and one is a double)
    // the property takes on the type of the smaller operand. If the operands
    // are equivalent (e.g. 3 and 3.0), the property does not change. 0, 0.0,
    // and -0.0 are all zero. The minimum of a zero stored value and zero input
    // value is always the stored value. The minimum of any numeric value x and
    // NaN is NaN.
    Value minimum = 5;

    // Appends the given elements in order if they are not already present in
    // the current property value.
    // If the property is not an array, or if the property does not yet exist,
    // it is first set to the empty array.
    //
    // Equivalent numbers of different types (e.g. 3L and 3.0) are
    // considered equal when checking if a value is missing.
    // NaN is equal to NaN, and the null value is equal to the null value.
    // If the input contains multiple equivalent values, only the first will
    // be considered.
    //
    // The corresponding transform result will be the null value.
    ArrayValue append_missing_elements = 6;

    // Removes all of the given elements from the array in the property.
    // If the property is not an array, or if the property does not yet exist,
    // it is set to the empty array.
    //
    // Equivalent numbers of different types (e.g. 3L and 3.0) are
    // considered equal when deciding whether an element should be removed.
    // NaN is equal to NaN, and the null value is equal to the null value.
    // This will remove all equivalent values if there are duplicates.
    //
    // The corresponding transform result will be the null value.
    ArrayValue remove_all_from_array = 7;
  }
}

// The result of applying a mutation.
message MutationResult {
  // The automatically allocated key.
  // Set only when the mutation allocated a key.
  Key key = 3;

  // The version of the entity on the server after processing the mutation. If
  // the mutation doesn't change anything on the server, then the version will
  // be the version of the current entity or, if no entity is present, a version
  // that is strictly greater than the version of any previous entity and less
  // than the version of any possible future entity.
  int64 version = 4;

  // The create time of the entity. This field will not be set after a 'delete'.
  google.protobuf.Timestamp create_time = 7;

  // The update time of the entity on the server after processing the mutation.
  // If the mutation doesn't change anything on the server, then the timestamp
  // will be the update timestamp of the current entity. This field will not be
  // set after a 'delete'.
  google.protobuf.Timestamp update_time = 6;

  // Whether a conflict was detected for this mutation. Always false when a
  // conflict detection strategy field is not set in the mutation.
  bool conflict_detected = 5;

  // The results of applying each
  // [PropertyTransform][google.datastore.v1.PropertyTransform], in the same
  // order of the request.
  repeated Value transform_results = 8;
}

// The set of arbitrarily nested property paths used to restrict an operation to
// only a subset of properties in an entity.
message PropertyMask {
  // The paths to the properties covered by this mask.
  //
  // A path is a list of property names separated by dots (`.`), for example
  // `foo.bar` means the property `bar` inside the entity property `foo` inside
  // the entity associated with this path.
  //
  // If a property name contains a dot `.` or a backslash `\`, then that
  // name must be escaped.
  //
  // A path must not be empty, and may not reference a value inside an
  // [array value][google.datastore.v1.Value.array_value].
  repeated string paths = 1;
}

// The options shared by read requests.
message ReadOptions {
  // The possible values for read consistencies.
  enum ReadConsistency {
    // Unspecified. This value must not be used.
    READ_CONSISTENCY_UNSPECIFIED = 0;

    // Strong consistency.
    STRONG = 1;

    // Eventual consistency.
    EVENTUAL = 2;
  }

  // For Cloud Firestore in Datastore mode, if you don't specify
  // read_consistency then all lookups and queries default to
  // `read_consistency`=`STRONG`. Note that, in Cloud Datastore, global queries
  // defaulted to `read_consistency`=`EVENTUAL`.
  //
  // Explicitly setting `read_consistency`=`EVENTUAL` will result in eventually
  // consistent lookups and queries.
  oneof consistency_type {
    // The non-transactional read consistency to use.
    ReadConsistency read_consistency = 1;

    // The identifier of the transaction in which to read. A
    // transaction identifier is returned by a call to
    // [Datastore.BeginTransaction][google.datastore.v1.Datastore.BeginTransaction].
    bytes transaction = 2;

    // Options for beginning a new transaction for this request.
    //
    // The new transaction identifier will be returned in the corresponding
    // response as either
    // [LookupResponse.transaction][google.datastore.v1.LookupResponse.transaction]
    // or
    // [RunQueryResponse.transaction][google.datastore.v1.RunQueryResponse.transaction].
    TransactionOptions new_transaction = 3;

    // Reads entities as they were at the given time. This value is only
    // supported for Cloud Firestore in Datastore mode.
    //
    // This must be a microsecond precision timestamp within the past one hour,
    // or if Point-in-Time Recovery is enabled, can additionally be a whole
    // minute timestamp within the past 7 days.
    google.protobuf.Timestamp read_time = 4;
  }
}

// Options for beginning a new transaction.
//
// Transactions can be created explicitly with calls to
// [Datastore.BeginTransaction][google.datastore.v1.Datastore.BeginTransaction]
// or implicitly by setting
// [ReadOptions.new_transaction][google.datastore.v1.ReadOptions.new_transaction]
// in read requests.
message TransactionOptions {
  // Options specific to read / write transactions.
  message ReadWrite {
    // The transaction identifier of the transaction being retried.
    bytes previous_transaction = 1;
  }

  // Options specific to read-only transactions.
  message ReadOnly {
    // Reads entities at the given time.
    //
    // This must be a microsecond precision timestamp within the past one hour,
    // or if Point-in-Time Recovery is enabled, can additionally be a whole
    // minute timestamp within the past 7 days.
    google.protobuf.Timestamp read_time = 1;
  }

  // The `mode` of the transaction, indicating whether write operations are
  // supported.
  oneof mode {
    // The transaction should allow both reads and writes.
    ReadWrite read_write = 1;

    // The transaction should only allow reads.
    ReadOnly read_only = 2;
  }
}
