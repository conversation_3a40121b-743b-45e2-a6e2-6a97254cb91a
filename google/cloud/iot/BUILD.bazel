# This build file includes a target for the Ruby wrapper library for
# google-cloud-iot.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for cloudiot.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "cloudiot_ruby_wrapper",
    srcs = ["//google/cloud/iot/v1:iot_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-iot",
        "ruby-cloud-env-prefix=IOT",
        "ruby-cloud-wrapper-of=v1:0.9",
        "ruby-cloud-product-url=https://cloud.google.com/iot",
        "ruby-cloud-api-id=cloudiot.googleapis.com",
        "ruby-cloud-api-shortname=cloudiot",
    ],
    ruby_cloud_description = "Registers and manages IoT (Internet of Things) devices that connect to the Google Cloud Platform.",
    ruby_cloud_title = "Cloud IoT",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-iot-ruby",
    deps = [
        ":cloudiot_ruby_wrapper",
    ],
)
