# This build file includes a target for the Ruby wrapper library for
# google-cloud-binary_authorization.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for binaryauthorization.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "binaryauthorization_ruby_wrapper",
    srcs = ["//google/cloud/binaryauthorization/v1:binaryauthorization_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-binary_authorization",
        "ruby-cloud-env-prefix=BINARY_AUTHORIZATION",
        "ruby-cloud-wrapper-of=v1:0.6;v1beta1:0.12",
        "ruby-cloud-product-url=https://cloud.google.com/binary-authorization/",
        "ruby-cloud-api-id=binaryauthorization.googleapis.com",
        "ruby-cloud-api-shortname=binaryauthorization",
        "ruby-cloud-service-override=BinauthzManagementServiceV1=BinauthzManagementService;SystemPolicyV1=SystemPolicy;ValidationHelperV1=ValidationHelper",
    ],
    ruby_cloud_description = "Binary Authorization is a service on Google Cloud that provides centralized software supply-chain security for applications that run on Google Kubernetes Engine (GKE) and GKE on-prem.",
    ruby_cloud_title = "Binary Authorization",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-binaryauthorization-ruby",
    deps = [
        ":binaryauthorization_ruby_wrapper",
    ],
)
