# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

type: google.api.Service
config_version: 3
name: compute.googleapis.com
title: Google Compute Engine API

apis:
- name: google.cloud.compute.v1.AcceleratorTypes
- name: google.cloud.compute.v1.Addresses
- name: google.cloud.compute.v1.Autoscalers
- name: google.cloud.compute.v1.BackendBuckets
- name: google.cloud.compute.v1.BackendServices
- name: google.cloud.compute.v1.DiskTypes
- name: google.cloud.compute.v1.Disks
- name: google.cloud.compute.v1.ExternalVpnGateways
- name: google.cloud.compute.v1.FirewallPolicies
- name: google.cloud.compute.v1.Firewalls
- name: google.cloud.compute.v1.ForwardingRules
- name: google.cloud.compute.v1.GlobalAddresses
- name: google.cloud.compute.v1.GlobalForwardingRules
- name: google.cloud.compute.v1.GlobalNetworkEndpointGroups
- name: google.cloud.compute.v1.GlobalOperations
- name: google.cloud.compute.v1.GlobalOrganizationOperations
- name: google.cloud.compute.v1.GlobalPublicDelegatedPrefixes
- name: google.cloud.compute.v1.HealthChecks
- name: google.cloud.compute.v1.ImageFamilyViews
- name: google.cloud.compute.v1.Images
- name: google.cloud.compute.v1.InstanceGroupManagers
- name: google.cloud.compute.v1.InstanceGroups
- name: google.cloud.compute.v1.InstanceTemplates
- name: google.cloud.compute.v1.Instances
- name: google.cloud.compute.v1.InterconnectAttachments
- name: google.cloud.compute.v1.InterconnectLocations
- name: google.cloud.compute.v1.Interconnects
- name: google.cloud.compute.v1.LicenseCodes
- name: google.cloud.compute.v1.Licenses
- name: google.cloud.compute.v1.MachineTypes
- name: google.cloud.compute.v1.NetworkEndpointGroups
- name: google.cloud.compute.v1.Networks
- name: google.cloud.compute.v1.NodeGroups
- name: google.cloud.compute.v1.NodeTemplates
- name: google.cloud.compute.v1.NodeTypes
- name: google.cloud.compute.v1.PacketMirrorings
- name: google.cloud.compute.v1.Projects
- name: google.cloud.compute.v1.PublicAdvertisedPrefixes
- name: google.cloud.compute.v1.PublicDelegatedPrefixes
- name: google.cloud.compute.v1.RegionAutoscalers
- name: google.cloud.compute.v1.RegionBackendServices
- name: google.cloud.compute.v1.RegionCommitments
- name: google.cloud.compute.v1.RegionDiskTypes
- name: google.cloud.compute.v1.RegionDisks
- name: google.cloud.compute.v1.RegionHealthCheckServices
- name: google.cloud.compute.v1.RegionHealthChecks
- name: google.cloud.compute.v1.RegionInstanceGroupManagers
- name: google.cloud.compute.v1.RegionInstanceGroups
- name: google.cloud.compute.v1.RegionInstances
- name: google.cloud.compute.v1.RegionNetworkEndpointGroups
- name: google.cloud.compute.v1.RegionNotificationEndpoints
- name: google.cloud.compute.v1.RegionOperations
- name: google.cloud.compute.v1.RegionSslCertificates
- name: google.cloud.compute.v1.RegionTargetHttpProxies
- name: google.cloud.compute.v1.RegionTargetHttpsProxies
- name: google.cloud.compute.v1.RegionUrlMaps
- name: google.cloud.compute.v1.Regions
- name: google.cloud.compute.v1.Reservations
- name: google.cloud.compute.v1.ResourcePolicies
- name: google.cloud.compute.v1.Routers
- name: google.cloud.compute.v1.Routes
- name: google.cloud.compute.v1.SecurityPolicies
- name: google.cloud.compute.v1.ServiceAttachments
- name: google.cloud.compute.v1.Snapshots
- name: google.cloud.compute.v1.SslCertificates
- name: google.cloud.compute.v1.SslPolicies
- name: google.cloud.compute.v1.Subnetworks
- name: google.cloud.compute.v1.TargetGrpcProxies
- name: google.cloud.compute.v1.TargetHttpProxies
- name: google.cloud.compute.v1.TargetHttpsProxies
- name: google.cloud.compute.v1.TargetInstances
- name: google.cloud.compute.v1.TargetPools
- name: google.cloud.compute.v1.TargetSslProxies
- name: google.cloud.compute.v1.TargetTcpProxies
- name: google.cloud.compute.v1.TargetVpnGateways
- name: google.cloud.compute.v1.UrlMaps
- name: google.cloud.compute.v1.VpnGateways
- name: google.cloud.compute.v1.VpnTunnels
- name: google.cloud.compute.v1.ZoneOperations
- name: google.cloud.compute.v1.Zones

authentication:
  rules:
  - selector: '*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/compute,
        https://www.googleapis.com/auth/cloud-platform
