// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.osconfig.v1alpha;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.OsConfig.V1Alpha";
option go_package = "cloud.google.com/go/osconfig/apiv1alpha/osconfigpb;osconfigpb";
option java_multiple_files = true;
option java_outer_classname = "VulnerabilityProto";
option java_package = "com.google.cloud.osconfig.v1alpha";
option php_namespace = "Google\\Cloud\\OsConfig\\V1alpha";
option ruby_package = "Google::Cloud::OsConfig::V1alpha";

// This API resource represents the vulnerability report for a specified
// Compute Engine virtual machine (VM) instance at a given point in time.
//
// For more information, see [Vulnerability
// reports](https://cloud.google.com/compute/docs/instances/os-inventory-management#vulnerability-reports).
message VulnerabilityReport {
  option (google.api.resource) = {
    type: "osconfig.googleapis.com/VulnerabilityReport"
    pattern: "projects/{project}/locations/{location}/instances/{instance}/vulnerabilityReport"
  };

  // A vulnerability affecting the VM instance.
  message Vulnerability {
    // Contains metadata information for the vulnerability. This information is
    // collected from the upstream feed of the operating system.
    message Details {
      // A reference for this vulnerability.
      message Reference {
        // The url of the reference.
        string url = 1;

        // The source of the reference e.g. NVD.
        string source = 2;
      }

      // The CVE of the vulnerability. CVE cannot be
      // empty and the combination of <cve, classification> should be unique
      // across vulnerabilities for a VM.
      string cve = 1;

      // The CVSS V2 score of this vulnerability. CVSS V2 score is on a scale of
      // 0 - 10 where 0 indicates low severity and 10 indicates high severity.
      float cvss_v2_score = 2;

      // The full description of the CVSSv3 for this vulnerability from NVD.
      CVSSv3 cvss_v3 = 3;

      // Assigned severity/impact ranking from the distro.
      string severity = 4;

      // The note or description describing the vulnerability from the distro.
      string description = 5;

      // Corresponds to the references attached to the `VulnerabilityDetails`.
      repeated Reference references = 6;
    }

    // OS inventory item that is affected by a vulnerability or fixed as a
    // result of a vulnerability.
    message Item {
      // Corresponds to the `INSTALLED_PACKAGE` inventory item on the VM.
      // This field displays the inventory items affected by this vulnerability.
      // If the vulnerability report was not updated after the VM inventory
      // update, these values might not display in VM inventory. For some
      // operating systems, this field might be empty.
      string installed_inventory_item_id = 1;

      // Corresponds to the `AVAILABLE_PACKAGE` inventory item on the VM.
      // If the vulnerability report was not updated after the VM inventory
      // update, these values might not display in VM inventory. If there is no
      // available fix, the field is empty. The `inventory_item` value specifies
      // the latest `SoftwarePackage` available to the VM that fixes the
      // vulnerability.
      string available_inventory_item_id = 2;

      // The recommended [CPE URI](https://cpe.mitre.org/specification/) update
      // that contains a fix for this vulnerability.
      string fixed_cpe_uri = 3;

      // The upstream OS patch, packages or KB that fixes the vulnerability.
      string upstream_fix = 4;
    }

    // Contains metadata as per the upstream feed of the operating system and
    // NVD.
    Details details = 1;

    // Corresponds to the `INSTALLED_PACKAGE` inventory item on the VM.
    // This field displays the inventory items affected by this vulnerability.
    // If the vulnerability report was not updated after the VM inventory
    // update, these values might not display in VM inventory. For some distros,
    // this field may be empty.
    repeated string installed_inventory_item_ids = 2 [deprecated = true];

    // Corresponds to the `AVAILABLE_PACKAGE` inventory item on the VM.
    // If the vulnerability report was not updated after the VM inventory
    // update, these values might not display in VM inventory. If there is no
    // available fix, the field is empty. The `inventory_item` value specifies
    // the latest `SoftwarePackage` available to the VM that fixes the
    // vulnerability.
    repeated string available_inventory_item_ids = 3 [deprecated = true];

    // The timestamp for when the vulnerability was first detected.
    google.protobuf.Timestamp create_time = 4;

    // The timestamp for when the vulnerability was last modified.
    google.protobuf.Timestamp update_time = 5;

    // List of items affected by the vulnerability.
    repeated Item items = 6;
  }

  // Output only. The `vulnerabilityReport` API resource name.
  //
  // Format:
  // `projects/{project_number}/locations/{location}/instances/{instance_id}/vulnerabilityReport`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. List of vulnerabilities affecting the VM.
  repeated Vulnerability vulnerabilities = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The timestamp for when the last vulnerability report was generated for the
  // VM.
  google.protobuf.Timestamp update_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A request message for getting the vulnerability report for the specified VM.
message GetVulnerabilityReportRequest {
  // Required. API resource name for vulnerability resource.
  //
  // Format:
  // `projects/{project}/locations/{location}/instances/{instance}/vulnerabilityReport`
  //
  // For `{project}`, either `project-number` or `project-id` can be provided.
  // For `{instance}`, either Compute Engine `instance-id` or `instance-name`
  // can be provided.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "osconfig.googleapis.com/VulnerabilityReport"
    }
  ];
}

// A request message for listing vulnerability reports for all VM instances in
// the specified location.
message ListVulnerabilityReportsRequest {
  // Required. The parent resource name.
  //
  // Format: `projects/{project}/locations/{location}/instances/-`
  //
  // For `{project}`, either `project-number` or `project-id` can be provided.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "compute.googleapis.com/Instance"
    }
  ];

  // The maximum number of results to return.
  int32 page_size = 2;

  // A pagination token returned from a previous call to
  // `ListVulnerabilityReports` that indicates where this listing
  // should continue from.
  string page_token = 3;

  // If provided, this field specifies the criteria that must be met by a
  // `vulnerabilityReport` API resource to be included in the response.
  string filter = 4;
}

// A response message for listing vulnerability reports for all VM instances in
// the specified location.
message ListVulnerabilityReportsResponse {
  // List of vulnerabilityReport objects.
  repeated VulnerabilityReport vulnerability_reports = 1;

  // The pagination token to retrieve the next page of vulnerabilityReports
  // object.
  string next_page_token = 2;
}

// Common Vulnerability Scoring System version 3.
// For details, see https://www.first.org/cvss/specification-document
message CVSSv3 {
  // This metric reflects the context by which vulnerability exploitation is
  // possible.
  enum AttackVector {
    // Invalid value.
    ATTACK_VECTOR_UNSPECIFIED = 0;

    // The vulnerable component is bound to the network stack and the set of
    // possible attackers extends beyond the other options listed below, up to
    // and including the entire Internet.
    ATTACK_VECTOR_NETWORK = 1;

    // The vulnerable component is bound to the network stack, but the attack is
    // limited at the protocol level to a logically adjacent topology.
    ATTACK_VECTOR_ADJACENT = 2;

    // The vulnerable component is not bound to the network stack and the
    // attacker's path is via read/write/execute capabilities.
    ATTACK_VECTOR_LOCAL = 3;

    // The attack requires the attacker to physically touch or manipulate the
    // vulnerable component.
    ATTACK_VECTOR_PHYSICAL = 4;
  }

  // This metric describes the conditions beyond the attacker's control that
  // must exist in order to exploit the vulnerability.
  enum AttackComplexity {
    // Invalid value.
    ATTACK_COMPLEXITY_UNSPECIFIED = 0;

    // Specialized access conditions or extenuating circumstances do not exist.
    // An attacker can expect repeatable success when attacking the vulnerable
    // component.
    ATTACK_COMPLEXITY_LOW = 1;

    // A successful attack depends on conditions beyond the attacker's control.
    // That is, a successful attack cannot be accomplished at will, but requires
    // the attacker to invest in some measurable amount of effort in preparation
    // or execution against the vulnerable component before a successful attack
    // can be expected.
    ATTACK_COMPLEXITY_HIGH = 2;
  }

  // This metric describes the level of privileges an attacker must possess
  // before successfully exploiting the vulnerability.
  enum PrivilegesRequired {
    // Invalid value.
    PRIVILEGES_REQUIRED_UNSPECIFIED = 0;

    // The attacker is unauthorized prior to attack, and therefore does not
    // require any access to settings or files of the vulnerable system to
    // carry out an attack.
    PRIVILEGES_REQUIRED_NONE = 1;

    // The attacker requires privileges that provide basic user capabilities
    // that could normally affect only settings and files owned by a user.
    // Alternatively, an attacker with Low privileges has the ability to access
    // only non-sensitive resources.
    PRIVILEGES_REQUIRED_LOW = 2;

    // The attacker requires privileges that provide significant (e.g.,
    // administrative) control over the vulnerable component allowing access to
    // component-wide settings and files.
    PRIVILEGES_REQUIRED_HIGH = 3;
  }

  // This metric captures the requirement for a human user, other than the
  // attacker, to participate in the successful compromise of the vulnerable
  // component.
  enum UserInteraction {
    // Invalid value.
    USER_INTERACTION_UNSPECIFIED = 0;

    // The vulnerable system can be exploited without interaction from any user.
    USER_INTERACTION_NONE = 1;

    // Successful exploitation of this vulnerability requires a user to take
    // some action before the vulnerability can be exploited.
    USER_INTERACTION_REQUIRED = 2;
  }

  // The Scope metric captures whether a vulnerability in one vulnerable
  // component impacts resources in components beyond its security scope.
  enum Scope {
    // Invalid value.
    SCOPE_UNSPECIFIED = 0;

    // An exploited vulnerability can only affect resources managed by the same
    // security authority.
    SCOPE_UNCHANGED = 1;

    // An exploited vulnerability can affect resources beyond the security scope
    // managed by the security authority of the vulnerable component.
    SCOPE_CHANGED = 2;
  }

  // The Impact metrics capture the effects of a successfully exploited
  // vulnerability on the component that suffers the worst outcome that is most
  // directly and predictably associated with the attack.
  enum Impact {
    // Invalid value.
    IMPACT_UNSPECIFIED = 0;

    // High impact.
    IMPACT_HIGH = 1;

    // Low impact.
    IMPACT_LOW = 2;

    // No impact.
    IMPACT_NONE = 3;
  }

  // The base score is a function of the base metric scores.
  // https://www.first.org/cvss/specification-document#Base-Metrics
  float base_score = 1;

  // The Exploitability sub-score equation is derived from the Base
  // Exploitability metrics.
  // https://www.first.org/cvss/specification-document#2-1-Exploitability-Metrics
  float exploitability_score = 2;

  // The Impact sub-score equation is derived from the Base Impact metrics.
  float impact_score = 3;

  // This metric reflects the context by which vulnerability exploitation is
  // possible.
  AttackVector attack_vector = 5;

  // This metric describes the conditions beyond the attacker's control that
  // must exist in order to exploit the vulnerability.
  AttackComplexity attack_complexity = 6;

  // This metric describes the level of privileges an attacker must possess
  // before successfully exploiting the vulnerability.
  PrivilegesRequired privileges_required = 7;

  // This metric captures the requirement for a human user, other than the
  // attacker, to participate in the successful compromise of the vulnerable
  // component.
  UserInteraction user_interaction = 8;

  // The Scope metric captures whether a vulnerability in one vulnerable
  // component impacts resources in components beyond its security scope.
  Scope scope = 9;

  // This metric measures the impact to the confidentiality of the information
  // resources managed by a software component due to a successfully exploited
  // vulnerability.
  Impact confidentiality_impact = 10;

  // This metric measures the impact to integrity of a successfully exploited
  // vulnerability.
  Impact integrity_impact = 11;

  // This metric measures the impact to the availability of the impacted
  // component resulting from a successfully exploited vulnerability.
  Impact availability_impact = 12;
}
