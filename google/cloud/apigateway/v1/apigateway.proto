// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.apigateway.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ApiGateway.V1";
option go_package = "cloud.google.com/go/apigateway/apiv1/apigatewaypb;apigatewaypb";
option java_multiple_files = true;
option java_package = "com.google.cloud.apigateway.v1";
option php_namespace = "Google\\Cloud\\ApiGateway\\V1";
option ruby_package = "Google::Cloud::ApiGateway::V1";
option (google.api.resource_definition) = {
  type: "iam.googleapis.com/ServiceAccount"
  pattern: "projects/{project}/serviceAccounts/{service_account}"
};
option (google.api.resource_definition) = {
  type: "servicemanagement.googleapis.com/ManagedService"
  pattern: "services/{service}"
};
option (google.api.resource_definition) = {
  type: "servicemanagement.googleapis.com/Service"
  pattern: "services/{service}/configs/{config}"
};

// An API that can be served by one or more Gateways.
message Api {
  option (google.api.resource) = {
    type: "apigateway.googleapis.com/Api"
    pattern: "projects/{project}/locations/global/apis/{api}"
  };

  // All the possible API states.
  enum State {
    // API does not have a state yet.
    STATE_UNSPECIFIED = 0;

    // API is being created.
    CREATING = 1;

    // API is active.
    ACTIVE = 2;

    // API creation failed.
    FAILED = 3;

    // API is being deleted.
    DELETING = 4;

    // API is being updated.
    UPDATING = 5;
  }

  // Output only. Resource name of the API.
  // Format: projects/{project}/locations/global/apis/{api}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Created time.
  google.protobuf.Timestamp create_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Updated time.
  google.protobuf.Timestamp update_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Resource labels to represent user-provided metadata.
  // Refer to cloud documentation on labels for more details.
  // https://cloud.google.com/compute/docs/labeling-resources
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Display name.
  string display_name = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Immutable. The name of a Google Managed Service (
  // https://cloud.google.com/service-infrastructure/docs/glossary#managed). If
  // not specified, a new Service will automatically be created in the same
  // project as this API.
  string managed_service = 7 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "servicemanagement.googleapis.com/ManagedService"
    }
  ];

  // Output only. State of the API.
  State state = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// An API Configuration is a combination of settings for both the Managed
// Service and Gateways serving this API Config.
message ApiConfig {
  option (google.api.resource) = {
    type: "apigateway.googleapis.com/ApiConfig"
    pattern: "projects/{project}/locations/global/apis/{api}/configs/{api_config}"
  };

  // A lightweight description of a file.
  message File {
    // The file path (full or relative path). This is typically the path of the
    // file when it is uploaded.
    string path = 1;

    // The bytes that constitute the file.
    bytes contents = 2;
  }

  // An OpenAPI Specification Document describing an API.
  message OpenApiDocument {
    // The OpenAPI Specification document file.
    File document = 1;
  }

  // A gRPC service definition.
  message GrpcServiceDefinition {
    // Input only. File descriptor set, generated by protoc.
    //
    // To generate, use protoc with imports and source info included.
    // For an example test.proto file, the following command would put the value
    // in a new file named out.pb.
    //
    // $ protoc --include_imports --include_source_info test.proto -o out.pb
    File file_descriptor_set = 1 [(google.api.field_behavior) = INPUT_ONLY];

    // Optional. Uncompiled proto files associated with the descriptor set, used for
    // display purposes (server-side compilation is not supported). These
    // should match the inputs to 'protoc' command used to generate
    // file_descriptor_set.
    repeated File source = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // All the possible API Config states.
  enum State {
    // API Config does not have a state yet.
    STATE_UNSPECIFIED = 0;

    // API Config is being created and deployed to the API Controller.
    CREATING = 1;

    // API Config is ready for use by Gateways.
    ACTIVE = 2;

    // API Config creation failed.
    FAILED = 3;

    // API Config is being deleted.
    DELETING = 4;

    // API Config is being updated.
    UPDATING = 5;

    // API Config settings are being activated in downstream systems.
    // API Configs in this state cannot be used by Gateways.
    ACTIVATING = 6;
  }

  // Output only. Resource name of the API Config.
  // Format: projects/{project}/locations/global/apis/{api}/configs/{api_config}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Created time.
  google.protobuf.Timestamp create_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Updated time.
  google.protobuf.Timestamp update_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Resource labels to represent user-provided metadata.
  // Refer to cloud documentation on labels for more details.
  // https://cloud.google.com/compute/docs/labeling-resources
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Display name.
  string display_name = 5 [(google.api.field_behavior) = OPTIONAL];

  // Immutable. The Google Cloud IAM Service Account that Gateways serving this config
  // should use to authenticate to other services. This may either be the
  // Service Account's email
  // (`{ACCOUNT_ID}@{PROJECT}.iam.gserviceaccount.com`) or its full resource
  // name (`projects/{PROJECT}/accounts/{UNIQUE_ID}`). This is most often used
  // when the service is a GCP resource such as a Cloud Run Service or an
  // IAP-secured service.
  string gateway_service_account = 14 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "iam.googleapis.com/ServiceAccount"
    }
  ];

  // Output only. The ID of the associated Service Config (
  // https://cloud.google.com/service-infrastructure/docs/glossary#config).
  string service_config_id = 12 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "servicemanagement.googleapis.com/Service"
    }
  ];

  // Output only. State of the API Config.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. OpenAPI specification documents. If specified, grpc_services and
  // managed_service_configs must not be included.
  repeated OpenApiDocument openapi_documents = 9 [(google.api.field_behavior) = OPTIONAL];

  // Optional. gRPC service definition files. If specified, openapi_documents must
  // not be included.
  repeated GrpcServiceDefinition grpc_services = 10 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Service Configuration files. At least one must be included when using gRPC
  // service definitions. See
  // https://cloud.google.com/endpoints/docs/grpc/grpc-service-config#service_configuration_overview
  // for the expected file contents.
  //
  // If multiple files are specified, the files are merged with the following
  // rules:
  // * All singular scalar fields are merged using "last one wins" semantics in
  // the order of the files uploaded.
  // * Repeated fields are concatenated.
  // * Singular embedded messages are merged using these rules for nested
  // fields.
  repeated File managed_service_configs = 11 [(google.api.field_behavior) = OPTIONAL];
}

// A Gateway is an API-aware HTTP proxy. It performs API-Method and/or
// API-Consumer specific actions based on an API Config such as authentication,
// policy enforcement, and backend selection.
message Gateway {
  option (google.api.resource) = {
    type: "apigateway.googleapis.com/Gateway"
    pattern: "projects/{project}/locations/{location}/gateways/{gateway}"
  };

  // All the possible Gateway states.
  enum State {
    // Gateway does not have a state yet.
    STATE_UNSPECIFIED = 0;

    // Gateway is being created.
    CREATING = 1;

    // Gateway is running and ready for requests.
    ACTIVE = 2;

    // Gateway creation failed.
    FAILED = 3;

    // Gateway is being deleted.
    DELETING = 4;

    // Gateway is being updated.
    UPDATING = 5;
  }

  // Output only. Resource name of the Gateway.
  // Format: projects/{project}/locations/{location}/gateways/{gateway}
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Created time.
  google.protobuf.Timestamp create_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Updated time.
  google.protobuf.Timestamp update_time = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Resource labels to represent user-provided metadata.
  // Refer to cloud documentation on labels for more details.
  // https://cloud.google.com/compute/docs/labeling-resources
  map<string, string> labels = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Display name.
  string display_name = 5 [(google.api.field_behavior) = OPTIONAL];

  // Required. Resource name of the API Config for this Gateway.
  // Format: projects/{project}/locations/global/apis/{api}/configs/{apiConfig}
  string api_config = 6 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigateway.googleapis.com/ApiConfig"
    }
  ];

  // Output only. The current state of the Gateway.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The default API Gateway host name of the form
  // `{gateway_id}-{hash}.{region_code}.gateway.dev`.
  string default_hostname = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for ApiGatewayService.ListGateways
message ListGatewaysRequest {
  // Required. Parent resource of the Gateway, of the form:
  // `projects/*/locations/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Page size.
  int32 page_size = 2;

  // Page token.
  string page_token = 3;

  // Filter.
  string filter = 4;

  // Order by parameters.
  string order_by = 5;
}

// Response message for ApiGatewayService.ListGateways
message ListGatewaysResponse {
  // Gateways.
  repeated Gateway gateways = 1;

  // Next page token.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable_locations = 3;
}

// Request message for ApiGatewayService.GetGateway
message GetGatewayRequest {
  // Required. Resource name of the form:
  // `projects/*/locations/*/gateways/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigateway.googleapis.com/Gateway"
    }
  ];
}

// Request message for ApiGatewayService.CreateGateway
message CreateGatewayRequest {
  // Required. Parent resource of the Gateway, of the form:
  // `projects/*/locations/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Identifier to assign to the Gateway. Must be unique within scope of
  // the parent resource.
  string gateway_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Gateway resource.
  Gateway gateway = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for ApiGatewayService.UpdateGateway
message UpdateGatewayRequest {
  // Field mask is used to specify the fields to be overwritten in the
  // Gateway resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1;

  // Required. Gateway resource.
  Gateway gateway = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for ApiGatewayService.DeleteGateway
message DeleteGatewayRequest {
  // Required. Resource name of the form:
  // `projects/*/locations/*/gateways/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigateway.googleapis.com/Gateway"
    }
  ];
}

// Request message for ApiGatewayService.ListApis
message ListApisRequest {
  // Required. Parent resource of the API, of the form:
  // `projects/*/locations/global`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Page size.
  int32 page_size = 2;

  // Page token.
  string page_token = 3;

  // Filter.
  string filter = 4;

  // Order by parameters.
  string order_by = 5;
}

// Response message for ApiGatewayService.ListApis
message ListApisResponse {
  // APIs.
  repeated Api apis = 1;

  // Next page token.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable_locations = 3;
}

// Request message for ApiGatewayService.GetApi
message GetApiRequest {
  // Required. Resource name of the form:
  // `projects/*/locations/global/apis/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigateway.googleapis.com/Api"
    }
  ];
}

// Request message for ApiGatewayService.CreateApi
message CreateApiRequest {
  // Required. Parent resource of the API, of the form:
  // `projects/*/locations/global`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. Identifier to assign to the API. Must be unique within scope of
  // the parent resource.
  string api_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. API resource.
  Api api = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for ApiGatewayService.UpdateApi
message UpdateApiRequest {
  // Field mask is used to specify the fields to be overwritten in the
  // Api resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1;

  // Required. API resource.
  Api api = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for ApiGatewayService.DeleteApi
message DeleteApiRequest {
  // Required. Resource name of the form:
  // `projects/*/locations/global/apis/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigateway.googleapis.com/Api"
    }
  ];
}

// Request message for ApiGatewayService.ListApiConfigs
message ListApiConfigsRequest {
  // Required. Parent resource of the API Config, of the form:
  // `projects/*/locations/global/apis/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigateway.googleapis.com/Api"
    }
  ];

  // Page size.
  int32 page_size = 2;

  // Page token.
  string page_token = 3;

  // Filter.
  string filter = 4;

  // Order by parameters.
  string order_by = 5;
}

// Response message for ApiGatewayService.ListApiConfigs
message ListApiConfigsResponse {
  // API Configs.
  repeated ApiConfig api_configs = 1;

  // Next page token.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable_locations = 3;
}

// Request message for ApiGatewayService.GetApiConfig
message GetApiConfigRequest {
  // Enum to control which fields should be included in the response.
  enum ConfigView {
    CONFIG_VIEW_UNSPECIFIED = 0;

    // Do not include configuration source files.
    BASIC = 1;

    // Include configuration source files.
    FULL = 2;
  }

  // Required. Resource name of the form:
  // `projects/*/locations/global/apis/*/configs/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigateway.googleapis.com/ApiConfig"
    }
  ];

  // Specifies which fields of the API Config are returned in the response.
  // Defaults to `BASIC` view.
  ConfigView view = 3;
}

// Request message for ApiGatewayService.CreateApiConfig
message CreateApiConfigRequest {
  // Required. Parent resource of the API Config, of the form:
  // `projects/*/locations/global/apis/*`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigateway.googleapis.com/Api"
    }
  ];

  // Required. Identifier to assign to the API Config. Must be unique within scope of
  // the parent resource.
  string api_config_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. API resource.
  ApiConfig api_config = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message for ApiGatewayService.UpdateApiConfig
message UpdateApiConfigRequest {
  // Field mask is used to specify the fields to be overwritten in the
  // ApiConfig resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1;

  // Required. API Config resource.
  ApiConfig api_config = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message for ApiGatewayService.DeleteApiConfig
message DeleteApiConfigRequest {
  // Required. Resource name of the form:
  // `projects/*/locations/global/apis/*/configs/*`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "apigateway.googleapis.com/ApiConfig"
    }
  ];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Diagnostic information from configuration processing.
  message Diagnostic {
    // Location of the diagnostic.
    string location = 1;

    // The diagnostic message.
    string message = 2;
  }

  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a [google.rpc.Status.code][google.rpc.Status.code] of 1,
  // corresponding to `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Diagnostics generated during processing of configuration source files.
  repeated Diagnostic diagnostics = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}
