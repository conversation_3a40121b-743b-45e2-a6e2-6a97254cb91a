# This file was automatically generated by BuildFileGener<PERSON>

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "schema_proto",
    srcs = [
        "annotation_payload.proto",
        "annotation_spec_color.proto",
        "data_item_payload.proto",
        "dataset_metadata.proto",
        "geometry.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:field_behavior_proto",
        "//google/type:color_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "schema_java_proto",
    deps = [":schema_proto"],
)

java_grpc_library(
    name = "schema_java_grpc",
    srcs = [":schema_proto"],
    deps = [":schema_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "schema_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/aiplatform/apiv1beta1/schema/schemapb",
    protos = [":schema_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/type:color_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "schema_moved_proto",
    srcs = [":schema_proto"],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:field_behavior_proto",
        "//google/type:color_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

py_proto_library(
    name = "schema_py_proto",
    deps = [":schema_moved_proto"],
)

py_grpc_library(
    name = "schema_py_grpc",
    srcs = [":schema_moved_proto"],
    deps = [":schema_py_proto"],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "schema_php_proto",
    deps = [":schema_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "schema_ruby_proto",
    deps = [":schema_proto"],
)

ruby_grpc_library(
    name = "schema_ruby_grpc",
    srcs = [":schema_proto"],
    deps = [":schema_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "schema_csharp_proto",
    deps = [":schema_proto"],
)

csharp_grpc_library(
    name = "schema_csharp_grpc",
    srcs = [":schema_proto"],
    deps = [":schema_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
# Put your C++ code here
