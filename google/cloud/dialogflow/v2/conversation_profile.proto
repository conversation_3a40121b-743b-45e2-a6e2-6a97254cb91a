// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.v2;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/v2/audio_config.proto";
import "google/cloud/dialogflow/v2/participant.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.V2";
option go_package = "cloud.google.com/go/dialogflow/apiv2/dialogflowpb;dialogflowpb";
option java_multiple_files = true;
option java_outer_classname = "ConversationProfileProto";
option java_package = "com.google.cloud.dialogflow.v2";
option objc_class_prefix = "DF";
option (google.api.resource_definition) = {
  type: "dialogflow.googleapis.com/CXSecuritySettings"
  pattern: "projects/{project}/locations/{location}/securitySettings/{security_settings}"
};

// Service for managing
// [ConversationProfiles][google.cloud.dialogflow.v2.ConversationProfile].
service ConversationProfiles {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Returns the list of all conversation profiles in the specified project.
  rpc ListConversationProfiles(ListConversationProfilesRequest)
      returns (ListConversationProfilesResponse) {
    option (google.api.http) = {
      get: "/v2/{parent=projects/*}/conversationProfiles"
      additional_bindings {
        get: "/v2/{parent=projects/*/locations/*}/conversationProfiles"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified conversation profile.
  rpc GetConversationProfile(GetConversationProfileRequest)
      returns (ConversationProfile) {
    option (google.api.http) = {
      get: "/v2/{name=projects/*/conversationProfiles/*}"
      additional_bindings {
        get: "/v2/{name=projects/*/locations/*/conversationProfiles/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a conversation profile in the specified project.
  //
  // [ConversationProfile.create_time][google.cloud.dialogflow.v2.ConversationProfile.create_time]
  // and
  // [ConversationProfile.update_time][google.cloud.dialogflow.v2.ConversationProfile.update_time]
  // aren't populated in the response. You can retrieve them via
  // [GetConversationProfile][google.cloud.dialogflow.v2.ConversationProfiles.GetConversationProfile]
  // API.
  rpc CreateConversationProfile(CreateConversationProfileRequest)
      returns (ConversationProfile) {
    option (google.api.http) = {
      post: "/v2/{parent=projects/*}/conversationProfiles"
      body: "conversation_profile"
      additional_bindings {
        post: "/v2/{parent=projects/*/locations/*}/conversationProfiles"
        body: "conversation_profile"
      }
    };
    option (google.api.method_signature) = "parent,conversation_profile";
  }

  // Updates the specified conversation profile.
  //
  // [ConversationProfile.create_time][google.cloud.dialogflow.v2.ConversationProfile.create_time]
  // and
  // [ConversationProfile.update_time][google.cloud.dialogflow.v2.ConversationProfile.update_time]
  // aren't populated in the response. You can retrieve them via
  // [GetConversationProfile][google.cloud.dialogflow.v2.ConversationProfiles.GetConversationProfile]
  // API.
  rpc UpdateConversationProfile(UpdateConversationProfileRequest)
      returns (ConversationProfile) {
    option (google.api.http) = {
      patch: "/v2/{conversation_profile.name=projects/*/conversationProfiles/*}"
      body: "conversation_profile"
      additional_bindings {
        patch: "/v2/{conversation_profile.name=projects/*/locations/*/conversationProfiles/*}"
        body: "conversation_profile"
      }
    };
    option (google.api.method_signature) = "conversation_profile,update_mask";
  }

  // Deletes the specified conversation profile.
  rpc DeleteConversationProfile(DeleteConversationProfileRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v2/{name=projects/*/conversationProfiles/*}"
      additional_bindings {
        delete: "/v2/{name=projects/*/locations/*/conversationProfiles/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Adds or updates a suggestion feature in a conversation profile.
  // If the conversation profile contains the type of suggestion feature for
  // the participant role, it will update it. Otherwise it will insert the
  // suggestion feature.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [SetSuggestionFeatureConfigOperationMetadata][google.cloud.dialogflow.v2.SetSuggestionFeatureConfigOperationMetadata]
  // - `response`:
  // [ConversationProfile][google.cloud.dialogflow.v2.ConversationProfile]
  //
  // If a long running operation to add or update suggestion feature
  // config for the same conversation profile, participant role and suggestion
  // feature type exists, please cancel the existing long running operation
  // before sending such request, otherwise the request will be rejected.
  rpc SetSuggestionFeatureConfig(SetSuggestionFeatureConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{conversation_profile=projects/*/conversationProfiles/*}:setSuggestionFeatureConfig"
      body: "*"
      additional_bindings {
        post: "/v2/{conversation_profile=projects/*/locations/*/conversationProfiles/*}:setSuggestionFeatureConfig"
        body: "*"
      }
    };
    option (google.api.method_signature) = "conversation_profile";
    option (google.api.method_signature) =
        "conversation_profile,participant_role,suggestion_feature_config";
    option (google.longrunning.operation_info) = {
      response_type: "ConversationProfile"
      metadata_type: "SetSuggestionFeatureConfigOperationMetadata"
    };
  }

  // Clears a suggestion feature from a conversation profile for the given
  // participant role.
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/es/docs/how/long-running-operations).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [ClearSuggestionFeatureConfigOperationMetadata][google.cloud.dialogflow.v2.ClearSuggestionFeatureConfigOperationMetadata]
  // - `response`:
  // [ConversationProfile][google.cloud.dialogflow.v2.ConversationProfile]
  rpc ClearSuggestionFeatureConfig(ClearSuggestionFeatureConfigRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v2/{conversation_profile=projects/*/conversationProfiles/*}:clearSuggestionFeatureConfig"
      body: "*"
      additional_bindings {
        post: "/v2/{conversation_profile=projects/*/locations/*/conversationProfiles/*}:clearSuggestionFeatureConfig"
        body: "*"
      }
    };
    option (google.api.method_signature) = "conversation_profile";
    option (google.api.method_signature) =
        "conversation_profile,participant_role,suggestion_feature_type";
    option (google.longrunning.operation_info) = {
      response_type: "ConversationProfile"
      metadata_type: "ClearSuggestionFeatureConfigOperationMetadata"
    };
  }
}

// Defines the services to connect to incoming Dialogflow conversations.
message ConversationProfile {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/ConversationProfile"
    pattern: "projects/{project}/conversationProfiles/{conversation_profile}"
    pattern: "projects/{project}/locations/{location}/conversationProfiles/{conversation_profile}"
  };

  // The unique identifier of this conversation profile.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/conversationProfiles/<Conversation Profile ID>`.
  string name = 1;

  // Required. Human readable name for this profile. Max length 1024 bytes.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Output only. Create time of the conversation profile.
  google.protobuf.Timestamp create_time = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Update time of the conversation profile.
  google.protobuf.Timestamp update_time = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Configuration for an automated agent to use with this profile.
  AutomatedAgentConfig automated_agent_config = 3;

  // Configuration for agent assistance to use with this profile.
  HumanAgentAssistantConfig human_agent_assistant_config = 4;

  // Configuration for connecting to a live agent.
  //
  // Currently, this feature is not general available, please contact Google
  // to get access.
  HumanAgentHandoffConfig human_agent_handoff_config = 5;

  // Configuration for publishing conversation lifecycle events.
  NotificationConfig notification_config = 6;

  // Configuration for logging conversation lifecycle events.
  LoggingConfig logging_config = 7;

  // Configuration for publishing new message events. Event will be sent in
  // format of [ConversationEvent][google.cloud.dialogflow.v2.ConversationEvent]
  NotificationConfig new_message_event_notification_config = 8;

  // Optional. Configuration for publishing transcription intermediate results.
  // Event will be sent in format of
  // [ConversationEvent][google.cloud.dialogflow.v2.ConversationEvent]. If
  // configured, the following information will be populated as
  // [ConversationEvent][google.cloud.dialogflow.v2.ConversationEvent] Pub/Sub
  // message attributes:
  // - "participant_id"
  // - "participant_role"
  // - "message_id"
  NotificationConfig new_recognition_result_notification_config = 21
      [(google.api.field_behavior) = OPTIONAL];

  // Settings for speech transcription.
  SpeechToTextConfig stt_config = 9;

  // Language code for the conversation profile. If not specified, the language
  // is en-US. Language at ConversationProfile should be set for all non en-US
  // languages.
  // This should be a [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt)
  // language tag. Example: "en-US".
  string language_code = 10;

  // The time zone of this conversational profile from the
  // [time zone database](https://www.iana.org/time-zones), e.g.,
  // America/New_York, Europe/Paris. Defaults to America/New_York.
  string time_zone = 14;

  // Name of the CX SecuritySettings reference for the agent.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/securitySettings/<Security Settings ID>`.
  string security_settings = 13 [(google.api.resource_reference) = {
    type: "dialogflow.googleapis.com/CXSecuritySettings"
  }];

  // Configuration for Text-to-Speech synthesization.
  //
  // Used by Phone Gateway to specify synthesization options. If agent defines
  // synthesization options as well, agent settings overrides the option here.
  SynthesizeSpeechConfig tts_config = 18;
}

// The request message for
// [ConversationProfiles.ListConversationProfiles][google.cloud.dialogflow.v2.ConversationProfiles.ListConversationProfiles].
message ListConversationProfilesRequest {
  // Required. The project to list all conversation profiles from.
  // Format: `projects/<Project ID>/locations/<Location ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/ConversationProfile"
    }
  ];

  // The maximum number of items to return in a single page. By
  // default 100 and at most 1000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request.
  string page_token = 3;
}

// The response message for
// [ConversationProfiles.ListConversationProfiles][google.cloud.dialogflow.v2.ConversationProfiles.ListConversationProfiles].
message ListConversationProfilesResponse {
  // The list of project conversation profiles. There is a maximum number
  // of items returned based on the page_size field in the request.
  repeated ConversationProfile conversation_profiles = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// The request message for
// [ConversationProfiles.GetConversationProfile][google.cloud.dialogflow.v2.ConversationProfiles.GetConversationProfile].
message GetConversationProfileRequest {
  // Required. The resource name of the conversation profile.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/conversationProfiles/<Conversation Profile ID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/ConversationProfile"
    }
  ];
}

// The request message for
// [ConversationProfiles.CreateConversationProfile][google.cloud.dialogflow.v2.ConversationProfiles.CreateConversationProfile].
message CreateConversationProfileRequest {
  // Required. The project to create a conversation profile for.
  // Format: `projects/<Project ID>/locations/<Location ID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/ConversationProfile"
    }
  ];

  // Required. The conversation profile to create.
  ConversationProfile conversation_profile = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [ConversationProfiles.UpdateConversationProfile][google.cloud.dialogflow.v2.ConversationProfiles.UpdateConversationProfile].
message UpdateConversationProfileRequest {
  // Required. The conversation profile to update.
  ConversationProfile conversation_profile = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The mask to control which fields to update.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [ConversationProfiles.DeleteConversationProfile][google.cloud.dialogflow.v2.ConversationProfiles.DeleteConversationProfile].
//
// This operation fails if the conversation profile is still referenced from
// a phone number.
message DeleteConversationProfileRequest {
  // Required. The name of the conversation profile to delete.
  // Format: `projects/<Project ID>/locations/<Location
  // ID>/conversationProfiles/<Conversation Profile ID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/ConversationProfile"
    }
  ];
}

// Defines the Automated Agent to connect to a conversation.
message AutomatedAgentConfig {
  // Required. ID of the Dialogflow agent environment to use.
  //
  // This project needs to either be the same project as the conversation or you
  // need to grant `service-<Conversation Project
  // Number>@gcp-sa-dialogflow.iam.gserviceaccount.com` the `Dialogflow API
  // Service Agent` role in this project.
  //
  // - For ES agents, use format: `projects/<Project ID>/locations/<Location
  // ID>/agent/environments/<Environment ID or '-'>`. If environment is not
  // specified, the default `draft` environment is used. Refer to
  // [DetectIntentRequest](/dialogflow/docs/reference/rpc/google.cloud.dialogflow.v2#google.cloud.dialogflow.v2.DetectIntentRequest)
  // for more details.
  //
  // - For CX agents, use format `projects/<Project ID>/locations/<Location
  // ID>/agents/<Agent ID>/environments/<Environment ID
  // or '-'>`. If environment is not specified, the default `draft` environment
  // is used.
  string agent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Agent"
    }
  ];

  // Optional. Configure lifetime of the Dialogflow session.
  // By default, a Dialogflow CX session remains active and its data is stored
  // for 30 minutes after the last request is sent for the session.
  // This value should be no longer than 1 day.
  google.protobuf.Duration session_ttl = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Defines the Human Agent Assist to connect to a conversation.
message HumanAgentAssistantConfig {
  // Settings of suggestion trigger.
  message SuggestionTriggerSettings {
    // Do not trigger if last utterance is small talk.
    bool no_smalltalk = 1;

    // Only trigger suggestion if participant role of last utterance is
    // END_USER.
    bool only_end_user = 2;
  }

  // Config for suggestion features.
  message SuggestionFeatureConfig {
    // The suggestion feature.
    SuggestionFeature suggestion_feature = 5;

    // Automatically iterates all participants and tries to compile
    // suggestions.
    //
    // Supported features: ARTICLE_SUGGESTION, FAQ, DIALOGFLOW_ASSIST,
    // KNOWLEDGE_ASSIST.
    bool enable_event_based_suggestion = 3;

    // Optional. Disable the logging of search queries sent by human agents. It
    // can prevent those queries from being stored at answer records.
    //
    // Supported features: KNOWLEDGE_SEARCH.
    bool disable_agent_query_logging = 14
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Enable query suggestion even if we can't find its answer.
    // By default, queries are suggested only if we find its answer.
    // Supported features: KNOWLEDGE_ASSIST
    bool enable_query_suggestion_when_no_answer = 15
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Enable including conversation context during query answer
    // generation. Supported features: KNOWLEDGE_SEARCH.
    bool enable_conversation_augmented_query = 16
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Enable query suggestion only.
    // Supported features: KNOWLEDGE_ASSIST
    bool enable_query_suggestion_only = 17
        [(google.api.field_behavior) = OPTIONAL];

    // Settings of suggestion trigger.
    //
    // Currently, only ARTICLE_SUGGESTION and FAQ will use this field.
    SuggestionTriggerSettings suggestion_trigger_settings = 10;

    // Configs of query.
    SuggestionQueryConfig query_config = 6;

    // Configs of custom conversation model.
    ConversationModelConfig conversation_model_config = 7;

    // Configs for processing conversation.
    ConversationProcessConfig conversation_process_config = 8;
  }

  // Detail human agent assistant config.
  message SuggestionConfig {
    // Configuration of different suggestion features. One feature can have only
    // one config.
    repeated SuggestionFeatureConfig feature_configs = 2;

    // If `group_suggestion_responses` is false, and there are multiple
    // `feature_configs` in `event based suggestion` or
    // StreamingAnalyzeContent, we will try to deliver suggestions to customers
    // as soon as we get new suggestion. Different type of suggestions based on
    // the same context will be in  separate Pub/Sub event or
    // `StreamingAnalyzeContentResponse`.
    //
    // If `group_suggestion_responses` set to true. All the suggestions to the
    // same participant based on the same context will be grouped into a single
    // Pub/Sub event or StreamingAnalyzeContentResponse.
    bool group_suggestion_responses = 3;

    // Optional. List of various generator resource names used in the
    // conversation profile.
    repeated string generators = 4 [
      (google.api.field_behavior) = OPTIONAL,
      (google.api.resource_reference) = {
        type: "dialogflow.googleapis.com/Generator"
      }
    ];

    // Optional. When disable_high_latency_features_sync_delivery is true and
    // using the AnalyzeContent API, we will not deliver the responses from high
    // latency features in the API response. The
    // human_agent_assistant_config.notification_config must be configured and
    // enable_event_based_suggestion must be set to true to receive the
    // responses from high latency features in Pub/Sub. High latency feature(s):
    // KNOWLEDGE_ASSIST
    bool disable_high_latency_features_sync_delivery = 5
        [(google.api.field_behavior) = OPTIONAL];
  }

  // Config for suggestion query.
  message SuggestionQueryConfig {
    // Knowledge base source settings.
    //
    // Supported features: ARTICLE_SUGGESTION, FAQ.
    message KnowledgeBaseQuerySource {
      // Required. Knowledge bases to query. Format:
      // `projects/<Project ID>/locations/<Location
      // ID>/knowledgeBases/<Knowledge Base ID>`. Currently, at most 5 knowledge
      // bases are supported.
      repeated string knowledge_bases = 1 [
        (google.api.field_behavior) = REQUIRED,
        (google.api.resource_reference) = {
          type: "dialogflow.googleapis.com/KnowledgeBase"
        }
      ];
    }

    // Document source settings.
    //
    // Supported features: SMART_REPLY, SMART_COMPOSE.
    message DocumentQuerySource {
      // Required. Knowledge documents to query from. Format:
      // `projects/<Project ID>/locations/<Location
      // ID>/knowledgeBases/<KnowledgeBase ID>/documents/<Document ID>`.
      // Currently, at most 5 documents are supported.
      repeated string documents = 1 [
        (google.api.field_behavior) = REQUIRED,
        (google.api.resource_reference) = {
          type: "dialogflow.googleapis.com/Document"
        }
      ];
    }

    // Dialogflow source setting.
    //
    // Supported feature: DIALOGFLOW_ASSIST.
    message DialogflowQuerySource {
      // The configuration used for human agent side Dialogflow assist
      // suggestion.
      message HumanAgentSideConfig {
        // Optional. The name of a dialogflow virtual agent used for intent
        // detection and suggestion triggered by human agent.
        // Format: `projects/<Project ID>/locations/<Location ID>/agent`.
        string agent = 1 [
          (google.api.field_behavior) = OPTIONAL,
          (google.api.resource_reference) = {
            type: "dialogflow.googleapis.com/Agent"
          }
        ];
      }

      // Required. The name of a Dialogflow virtual agent used for end user side
      // intent detection and suggestion. Format: `projects/<Project
      // ID>/locations/<Location ID>/agent`. When multiple agents are allowed in
      // the same Dialogflow project.
      string agent = 1 [
        (google.api.field_behavior) = REQUIRED,
        (google.api.resource_reference) = {
          type: "dialogflow.googleapis.com/Agent"
        }
      ];

      // Optional. The Dialogflow assist configuration for human agent.
      HumanAgentSideConfig human_agent_side_config = 3
          [(google.api.field_behavior) = OPTIONAL];
    }

    // Settings that determine how to filter recent conversation context when
    // generating suggestions.
    message ContextFilterSettings {
      // If set to true, the last message from virtual agent (hand off message)
      // and the message before it (trigger message of hand off) are dropped.
      bool drop_handoff_messages = 1;

      // If set to true, all messages from virtual agent are dropped.
      bool drop_virtual_agent_messages = 2;

      // If set to true, all messages from ivr stage are dropped.
      bool drop_ivr_messages = 3;
    }

    // Custom sections to return when requesting a summary of a conversation.
    // This is only supported when `baseline_model_version` == '2.0'.
    //
    // Supported features: CONVERSATION_SUMMARIZATION,
    // CONVERSATION_SUMMARIZATION_VOICE.
    message Sections {
      // Selectable sections to return when requesting a summary of a
      // conversation.
      enum SectionType {
        // Undefined section type, does not return anything.
        SECTION_TYPE_UNSPECIFIED = 0;

        // What the customer needs help with or has question about.
        // Section name: "situation".
        SITUATION = 1;

        // What the agent does to help the customer.
        // Section name: "action".
        ACTION = 2;

        // Result of the customer service. A single word describing the result
        // of the conversation.
        // Section name: "resolution".
        RESOLUTION = 3;

        // Reason for cancellation if the customer requests for a cancellation.
        // "N/A" otherwise.
        // Section name: "reason_for_cancellation".
        REASON_FOR_CANCELLATION = 4;

        // "Unsatisfied" or "Satisfied" depending on the customer's feelings at
        // the end of the conversation.
        // Section name: "customer_satisfaction".
        CUSTOMER_SATISFACTION = 5;

        // Key entities extracted from the conversation, such as ticket number,
        // order number, dollar amount, etc.
        // Section names are prefixed by "entities/".
        ENTITIES = 6;
      }

      // The selected sections chosen to return when requesting a summary of a
      // conversation. A duplicate selected section will be treated as a single
      // selected section. If section types are not provided, the default will
      // be {SITUATION, ACTION, RESULT}.
      repeated SectionType section_types = 1;
    }

    // Source of query.
    oneof query_source {
      // Query from knowledgebase. It is used by:
      // ARTICLE_SUGGESTION, FAQ.
      KnowledgeBaseQuerySource knowledge_base_query_source = 1;

      // Query from knowledge base document. It is used by:
      // SMART_REPLY, SMART_COMPOSE.
      DocumentQuerySource document_query_source = 2;

      // Query from Dialogflow agent. It is used by DIALOGFLOW_ASSIST.
      DialogflowQuerySource dialogflow_query_source = 3;
    }

    // Maximum number of results to return. Currently, if unset, defaults to 10.
    // And the max number is 20.
    int32 max_results = 4;

    // Confidence threshold of query result.
    //
    // Agent Assist gives each suggestion a score in the range [0.0, 1.0], based
    // on the relevance between the suggestion and the current conversation
    // context. A score of 0.0 has no relevance, while a score of 1.0 has high
    // relevance. Only suggestions with a score greater than or equal to the
    // value of this field are included in the results.
    //
    // For a baseline model (the default), the recommended value is in the range
    // [0.05, 0.1].
    //
    // For a custom model, there is no recommended value. Tune this value by
    // starting from a very low value and slowly increasing until you have
    // desired results.
    //
    // If this field is not set, it defaults to 0.0, which means that all
    // suggestions are returned.
    //
    // Supported features: ARTICLE_SUGGESTION, FAQ, SMART_REPLY, SMART_COMPOSE,
    // KNOWLEDGE_SEARCH, KNOWLEDGE_ASSIST, ENTITY_EXTRACTION.
    float confidence_threshold = 5;

    // Determines how recent conversation context is filtered when generating
    // suggestions. If unspecified, no messages will be dropped.
    ContextFilterSettings context_filter_settings = 7;

    // Optional. The customized sections chosen to return when requesting a
    // summary of a conversation.
    Sections sections = 8 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The number of recent messages to include in the context.
    // Supported features: KNOWLEDGE_ASSIST.
    int32 context_size = 9 [(google.api.field_behavior) = OPTIONAL];
  }

  // Custom conversation models used in agent assist feature.
  //
  // Supported feature: ARTICLE_SUGGESTION, SMART_COMPOSE, SMART_REPLY,
  // CONVERSATION_SUMMARIZATION.
  message ConversationModelConfig {
    // Conversation model resource name. Format: `projects/<Project
    // ID>/conversationModels/<Model ID>`.
    string model = 1 [(google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/ConversationModel"
    }];

    // Version of current baseline model. It will be ignored if
    // [model][google.cloud.dialogflow.v2.HumanAgentAssistantConfig.ConversationModelConfig.model]
    // is set. Valid versions are:
    //   Article Suggestion baseline model:
    //     - 0.9
    //     - 1.0 (default)
    //   Summarization baseline model:
    //     - 1.0
    string baseline_model_version = 8;
  }

  // Config to process conversation.
  message ConversationProcessConfig {
    // Number of recent non-small-talk sentences to use as context for article
    // and FAQ suggestion
    int32 recent_sentences_count = 2;
  }

  // Configuration for analyses to run on each conversation message.
  message MessageAnalysisConfig {
    // Enable entity extraction in conversation messages on [agent assist
    // stage](https://cloud.google.com/dialogflow/priv/docs/contact-center/basics#stages).
    // If unspecified, defaults to false.
    //
    // Currently, this feature is not general available, please contact Google
    // to get access.
    bool enable_entity_extraction = 2;

    // Enable sentiment analysis in conversation messages on [agent assist
    // stage](https://cloud.google.com/dialogflow/priv/docs/contact-center/basics#stages).
    // If unspecified, defaults to false. Sentiment analysis inspects user input
    // and identifies the prevailing subjective opinion, especially to determine
    // a user's attitude as positive, negative, or neutral:
    // https://cloud.google.com/natural-language/docs/basics#sentiment_analysis
    // For
    // [Participants.StreamingAnalyzeContent][google.cloud.dialogflow.v2.Participants.StreamingAnalyzeContent]
    // method, result will be in
    // [StreamingAnalyzeContentResponse.message.SentimentAnalysisResult][google.cloud.dialogflow.v2.StreamingAnalyzeContentResponse.message].
    // For
    // [Participants.AnalyzeContent][google.cloud.dialogflow.v2.Participants.AnalyzeContent]
    // method, result will be in
    // [AnalyzeContentResponse.message.SentimentAnalysisResult][google.cloud.dialogflow.v2.AnalyzeContentResponse.message]
    // For
    // [Conversations.ListMessages][google.cloud.dialogflow.v2.Conversations.ListMessages]
    // method, result will be in
    // [ListMessagesResponse.messages.SentimentAnalysisResult][google.cloud.dialogflow.v2.ListMessagesResponse.messages]
    // If Pub/Sub notification is configured, result will be in
    // [ConversationEvent.new_message_payload.SentimentAnalysisResult][google.cloud.dialogflow.v2.ConversationEvent.new_message_payload].
    bool enable_sentiment_analysis = 3;
  }

  // Pub/Sub topic on which to publish new agent assistant events.
  NotificationConfig notification_config = 2;

  // Configuration for agent assistance of human agent participant.
  SuggestionConfig human_agent_suggestion_config = 3;

  // Configuration for agent assistance of end user participant.
  //
  // Currently, this feature is not general available, please contact Google
  // to get access.
  SuggestionConfig end_user_suggestion_config = 4;

  // Configuration for message analysis.
  MessageAnalysisConfig message_analysis_config = 5;
}

// Defines the hand off to a live agent, typically on which external agent
// service provider to connect to a conversation.
//
// Currently, this feature is not general available, please contact Google
// to get access.
message HumanAgentHandoffConfig {
  // Configuration specific to [LivePerson](https://www.liveperson.com).
  message LivePersonConfig {
    // Required. Account number of the LivePerson account to connect. This is
    // the account number you input at the login page.
    string account_number = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Configuration specific to Salesforce Live Agent.
  message SalesforceLiveAgentConfig {
    // Required. The organization ID of the Salesforce account.
    string organization_id = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. Live Agent deployment ID.
    string deployment_id = 2 [(google.api.field_behavior) = REQUIRED];

    // Required. Live Agent chat button ID.
    string button_id = 3 [(google.api.field_behavior) = REQUIRED];

    // Required. Domain of the Live Agent endpoint for this agent. You can find
    // the endpoint URL in the `Live Agent settings` page. For example if URL
    // has the form https://d.la4-c2-phx.salesforceliveagent.com/...,
    // you should fill in d.la4-c2-phx.salesforceliveagent.com.
    string endpoint_domain = 4 [(google.api.field_behavior) = REQUIRED];
  }

  // Required. Specifies which agent service to connect for human agent handoff.
  oneof agent_service {
    // Uses [LivePerson](https://www.liveperson.com).
    LivePersonConfig live_person_config = 1;

    // Uses Salesforce Live Agent.
    SalesforceLiveAgentConfig salesforce_live_agent_config = 2;
  }
}

// Defines notification behavior.
message NotificationConfig {
  // Format of cloud pub/sub message.
  enum MessageFormat {
    // If it is unspecified, PROTO will be used.
    MESSAGE_FORMAT_UNSPECIFIED = 0;

    // Pub/Sub message will be serialized proto.
    PROTO = 1;

    // Pub/Sub message will be json.
    JSON = 2;
  }

  // Name of the Pub/Sub topic to publish conversation
  // events like
  // [CONVERSATION_STARTED][google.cloud.dialogflow.v2.ConversationEvent.Type.CONVERSATION_STARTED]
  // as serialized
  // [ConversationEvent][google.cloud.dialogflow.v2.ConversationEvent] protos.
  //
  // For telephony integration to receive notification, make sure either this
  // topic is in the same project as the conversation or you grant
  // `service-<Conversation Project
  // Number>@gcp-sa-dialogflow.iam.gserviceaccount.com` the `Dialogflow Service
  // Agent` role in the topic project.
  //
  // For chat integration to receive notification, make sure API caller has been
  // granted the `Dialogflow Service Agent` role for the topic.
  //
  // Format: `projects/<Project ID>/locations/<Location ID>/topics/<Topic ID>`.
  string topic = 1;

  // Format of message.
  MessageFormat message_format = 2;
}

// Defines logging behavior for conversation lifecycle events.
message LoggingConfig {
  // Whether to log conversation events like
  // [CONVERSATION_STARTED][google.cloud.dialogflow.v2.ConversationEvent.Type.CONVERSATION_STARTED]
  // to Stackdriver in the conversation project as JSON format
  // [ConversationEvent][google.cloud.dialogflow.v2.ConversationEvent] protos.
  bool enable_stackdriver_logging = 3;
}

// The type of Human Agent Assistant API suggestion to perform, and the maximum
// number of results to return for that type. Multiple `Feature` objects can
// be specified in the `features` list.
message SuggestionFeature {
  // Defines the type of Human Agent Assistant feature.
  enum Type {
    // Unspecified feature type.
    TYPE_UNSPECIFIED = 0;

    // Run article suggestion model for chat.
    ARTICLE_SUGGESTION = 1;

    // Run FAQ model for chat.
    FAQ = 2;

    // Run smart reply model for chat.
    SMART_REPLY = 3;

    // Run knowledge search with text input from agent or text generated query.
    KNOWLEDGE_SEARCH = 14;

    // Run knowledge assist with automatic query generation.
    KNOWLEDGE_ASSIST = 15;
  }

  // Type of Human Agent Assistant API feature to request.
  Type type = 1;
}

// The request message for
// [ConversationProfiles.SetSuggestionFeatureConfig][google.cloud.dialogflow.v2.ConversationProfiles.SetSuggestionFeatureConfig].
message SetSuggestionFeatureConfigRequest {
  // Required. The Conversation Profile to add or update the suggestion feature
  // config. Format: `projects/<Project ID>/locations/<Location
  // ID>/conversationProfiles/<Conversation Profile ID>`.
  string conversation_profile = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The participant role to add or update the suggestion feature
  // config. Only HUMAN_AGENT or END_USER can be used.
  Participant.Role participant_role = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The suggestion feature config to add or update.
  HumanAgentAssistantConfig.SuggestionFeatureConfig suggestion_feature_config =
      3 [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [ConversationProfiles.ClearSuggestionFeatureConfig][google.cloud.dialogflow.v2.ConversationProfiles.ClearSuggestionFeatureConfig].
message ClearSuggestionFeatureConfigRequest {
  // Required. The Conversation Profile to add or update the suggestion feature
  // config. Format: `projects/<Project ID>/locations/<Location
  // ID>/conversationProfiles/<Conversation Profile ID>`.
  string conversation_profile = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The participant role to remove the suggestion feature
  // config. Only HUMAN_AGENT or END_USER can be used.
  Participant.Role participant_role = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The type of the suggestion feature to remove.
  SuggestionFeature.Type suggestion_feature_type = 3
      [(google.api.field_behavior) = REQUIRED];
}

// Metadata for a
// [ConversationProfiles.SetSuggestionFeatureConfig][google.cloud.dialogflow.v2.ConversationProfiles.SetSuggestionFeatureConfig]
// operation.
message SetSuggestionFeatureConfigOperationMetadata {
  // The resource name of the conversation profile. Format:
  // `projects/<Project ID>/locations/<Location
  // ID>/conversationProfiles/<Conversation Profile ID>`
  string conversation_profile = 1;

  // Required. The participant role to add or update the suggestion feature
  // config. Only HUMAN_AGENT or END_USER can be used.
  Participant.Role participant_role = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The type of the suggestion feature to add or update.
  SuggestionFeature.Type suggestion_feature_type = 3
      [(google.api.field_behavior) = REQUIRED];

  // Timestamp whe the request was created. The time is measured on server side.
  google.protobuf.Timestamp create_time = 4;
}

// Metadata for a
// [ConversationProfiles.ClearSuggestionFeatureConfig][google.cloud.dialogflow.v2.ConversationProfiles.ClearSuggestionFeatureConfig]
// operation.
message ClearSuggestionFeatureConfigOperationMetadata {
  // The resource name of the conversation profile. Format:
  // `projects/<Project ID>/locations/<Location
  // ID>/conversationProfiles/<Conversation Profile ID>`
  string conversation_profile = 1;

  // Required. The participant role to remove the suggestion feature
  // config. Only HUMAN_AGENT or END_USER can be used.
  Participant.Role participant_role = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The type of the suggestion feature to remove.
  SuggestionFeature.Type suggestion_feature_type = 3
      [(google.api.field_behavior) = REQUIRED];

  // Timestamp whe the request was created. The time is measured on server side.
  google.protobuf.Timestamp create_time = 4;
}
