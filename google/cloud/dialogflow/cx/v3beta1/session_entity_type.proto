// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/cx/v3beta1/entity_type.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3Beta1";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3beta1/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "SessionEntityTypeProto";
option java_package = "com.google.cloud.dialogflow.cx.v3beta1";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3beta1";

// Service for managing
// [SessionEntityTypes][google.cloud.dialogflow.cx.v3beta1.SessionEntityType].
service SessionEntityTypes {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Returns the list of all session entity types in the specified session.
  rpc ListSessionEntityTypes(ListSessionEntityTypesRequest)
      returns (ListSessionEntityTypesResponse) {
    option (google.api.http) = {
      get: "/v3beta1/{parent=projects/*/locations/*/agents/*/sessions/*}/entityTypes"
      additional_bindings {
        get: "/v3beta1/{parent=projects/*/locations/*/agents/*/environments/*/sessions/*}/entityTypes"
      }
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified session entity type.
  rpc GetSessionEntityType(GetSessionEntityTypeRequest)
      returns (SessionEntityType) {
    option (google.api.http) = {
      get: "/v3beta1/{name=projects/*/locations/*/agents/*/sessions/*/entityTypes/*}"
      additional_bindings {
        get: "/v3beta1/{name=projects/*/locations/*/agents/*/environments/*/sessions/*/entityTypes/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a session entity type.
  rpc CreateSessionEntityType(CreateSessionEntityTypeRequest)
      returns (SessionEntityType) {
    option (google.api.http) = {
      post: "/v3beta1/{parent=projects/*/locations/*/agents/*/sessions/*}/entityTypes"
      body: "session_entity_type"
      additional_bindings {
        post: "/v3beta1/{parent=projects/*/locations/*/agents/*/environments/*/sessions/*}/entityTypes"
        body: "session_entity_type"
      }
    };
    option (google.api.method_signature) = "parent,session_entity_type";
  }

  // Updates the specified session entity type.
  rpc UpdateSessionEntityType(UpdateSessionEntityTypeRequest)
      returns (SessionEntityType) {
    option (google.api.http) = {
      patch: "/v3beta1/{session_entity_type.name=projects/*/locations/*/agents/*/sessions/*/entityTypes/*}"
      body: "session_entity_type"
      additional_bindings {
        patch: "/v3beta1/{session_entity_type.name=projects/*/locations/*/agents/*/environments/*/sessions/*/entityTypes/*}"
        body: "session_entity_type"
      }
    };
    option (google.api.method_signature) = "session_entity_type,update_mask";
  }

  // Deletes the specified session entity type.
  rpc DeleteSessionEntityType(DeleteSessionEntityTypeRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v3beta1/{name=projects/*/locations/*/agents/*/sessions/*/entityTypes/*}"
      additional_bindings {
        delete: "/v3beta1/{name=projects/*/locations/*/agents/*/environments/*/sessions/*/entityTypes/*}"
      }
    };
    option (google.api.method_signature) = "name";
  }
}

// Session entity types are referred to as **User** entity types and are
// entities that are built for an individual user such as favorites,
// preferences, playlists, and so on.
//
// You can redefine a session entity type at the session level to extend or
// replace a [custom entity type][google.cloud.dialogflow.cx.v3beta1.EntityType]
// at the user session level (we refer to the entity types defined at the agent
// level as "custom entity types").
//
// Note: session entity types apply to all queries, regardless of the language.
//
// For more information about entity types, see the [Dialogflow
// documentation](https://cloud.google.com/dialogflow/docs/entities-overview).
message SessionEntityType {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/SessionEntityType"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/sessions/{session}/entityTypes/{entity_type}"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/environments/{environment}/sessions/{session}/entityTypes/{entity_type}"
  };

  // The types of modifications for the session entity type.
  enum EntityOverrideMode {
    // Not specified. This value should be never used.
    ENTITY_OVERRIDE_MODE_UNSPECIFIED = 0;

    // The collection of session entities overrides the collection of entities
    // in the corresponding custom entity type.
    ENTITY_OVERRIDE_MODE_OVERRIDE = 1;

    // The collection of session entities extends the collection of entities in
    // the corresponding custom entity type.
    //
    // Note: Even in this override mode calls to `ListSessionEntityTypes`,
    // `GetSessionEntityType`, `CreateSessionEntityType` and
    // `UpdateSessionEntityType` only return the additional entities added in
    // this session entity type. If you want to get the supplemented list,
    // please call
    // [EntityTypes.GetEntityType][google.cloud.dialogflow.cx.v3beta1.EntityTypes.GetEntityType]
    // on the custom entity type and merge.
    ENTITY_OVERRIDE_MODE_SUPPLEMENT = 2;
  }

  // Required. The unique identifier of the session entity type.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/sessions/<SessionID>/entityTypes/<EntityTypeID>`
  // or
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>/sessions/<SessionID>/entityTypes/<EntityTypeID>`.
  // If `Environment ID` is not specified, we assume default 'draft'
  // environment.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Indicates whether the additional data should override or
  // supplement the custom entity type definition.
  EntityOverrideMode entity_override_mode = 3
      [(google.api.field_behavior) = REQUIRED];

  // Required. The collection of entities to override or supplement the custom
  // entity type.
  repeated EntityType.Entity entities = 4
      [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [SessionEntityTypes.ListSessionEntityTypes][google.cloud.dialogflow.cx.v3beta1.SessionEntityTypes.ListSessionEntityTypes].
message ListSessionEntityTypesRequest {
  // Required. The session to list all session entity types from.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/sessions/<SessionID>`
  // or
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>/sessions/<SessionID>`.
  // If `Environment ID` is not specified, we assume default 'draft'
  //  environment.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/SessionEntityType"
    }
  ];

  // The maximum number of items to return in a single page. By default 100 and
  // at most 1000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request.
  string page_token = 3;
}

// The response message for
// [SessionEntityTypes.ListSessionEntityTypes][google.cloud.dialogflow.cx.v3beta1.SessionEntityTypes.ListSessionEntityTypes].
message ListSessionEntityTypesResponse {
  // The list of session entity types. There will be a maximum number of items
  // returned based on the page_size field in the request.
  repeated SessionEntityType session_entity_types = 1;

  // Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;
}

// The request message for
// [SessionEntityTypes.GetSessionEntityType][google.cloud.dialogflow.cx.v3beta1.SessionEntityTypes.GetSessionEntityType].
message GetSessionEntityTypeRequest {
  // Required. The name of the session entity type.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/sessions/<SessionID>/entityTypes/<EntityTypeID>`
  // or
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>/sessions/<SessionID>/entityTypes/<EntityTypeID>`.
  // If `Environment ID` is not specified, we assume default 'draft'
  // environment.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/SessionEntityType"
    }
  ];
}

// The request message for
// [SessionEntityTypes.CreateSessionEntityType][google.cloud.dialogflow.cx.v3beta1.SessionEntityTypes.CreateSessionEntityType].
message CreateSessionEntityTypeRequest {
  // Required. The session to create a session entity type for.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/sessions/<SessionID>`
  // or
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>/sessions/<SessionID>`.
  // If `Environment ID` is not specified, we assume default 'draft'
  // environment.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/SessionEntityType"
    }
  ];

  // Required. The session entity type to create.
  SessionEntityType session_entity_type = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [SessionEntityTypes.UpdateSessionEntityType][google.cloud.dialogflow.cx.v3beta1.SessionEntityTypes.UpdateSessionEntityType].
message UpdateSessionEntityTypeRequest {
  // Required. The session entity type to update.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/sessions/<SessionID>/entityTypes/<EntityTypeID>`
  // or
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>/sessions/<SessionID>/entityTypes/<EntityTypeID>`.
  // If `Environment ID` is not specified, we assume default 'draft'
  // environment.
  SessionEntityType session_entity_type = 1
      [(google.api.field_behavior) = REQUIRED];

  // The mask to control which fields get updated.
  google.protobuf.FieldMask update_mask = 2;
}

// The request message for
// [SessionEntityTypes.DeleteSessionEntityType][google.cloud.dialogflow.cx.v3beta1.SessionEntityTypes.DeleteSessionEntityType].
message DeleteSessionEntityTypeRequest {
  // Required. The name of the session entity type to delete.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/sessions/<SessionID>/entityTypes/<EntityTypeID>`
  // or
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>/sessions/<SessionID>/entityTypes/<EntityTypeID>`.
  // If `Environment ID` is not specified, we assume default 'draft'
  // environment.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/SessionEntityType"
    }
  ];
}
