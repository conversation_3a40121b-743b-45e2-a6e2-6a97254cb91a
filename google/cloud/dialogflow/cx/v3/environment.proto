// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dialogflow.cx.v3;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dialogflow/cx/v3/test_case.proto";
import "google/cloud/dialogflow/cx/v3/webhook.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Dialogflow.Cx.V3";
option go_package = "cloud.google.com/go/dialogflow/cx/apiv3/cxpb;cxpb";
option java_multiple_files = true;
option java_outer_classname = "EnvironmentProto";
option java_package = "com.google.cloud.dialogflow.cx.v3";
option objc_class_prefix = "DF";
option ruby_package = "Google::Cloud::Dialogflow::CX::V3";

// Service for managing
// [Environments][google.cloud.dialogflow.cx.v3.Environment].
service Environments {
  option (google.api.default_host) = "dialogflow.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/dialogflow";

  // Returns the list of all environments in the specified
  // [Agent][google.cloud.dialogflow.cx.v3.Agent].
  rpc ListEnvironments(ListEnvironmentsRequest)
      returns (ListEnvironmentsResponse) {
    option (google.api.http) = {
      get: "/v3/{parent=projects/*/locations/*/agents/*}/environments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves the specified
  // [Environment][google.cloud.dialogflow.cx.v3.Environment].
  rpc GetEnvironment(GetEnvironmentRequest) returns (Environment) {
    option (google.api.http) = {
      get: "/v3/{name=projects/*/locations/*/agents/*/environments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates an [Environment][google.cloud.dialogflow.cx.v3.Environment] in the
  // specified [Agent][google.cloud.dialogflow.cx.v3.Agent].
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`: An empty [Struct
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct)
  // - `response`: [Environment][google.cloud.dialogflow.cx.v3.Environment]
  rpc CreateEnvironment(CreateEnvironmentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/{parent=projects/*/locations/*/agents/*}/environments"
      body: "environment"
    };
    option (google.api.method_signature) = "parent,environment";
    option (google.longrunning.operation_info) = {
      response_type: "Environment"
      metadata_type: "google.protobuf.Struct"
    };
  }

  // Updates the specified
  // [Environment][google.cloud.dialogflow.cx.v3.Environment].
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`: An empty [Struct
  //   message](https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#struct)
  // - `response`: [Environment][google.cloud.dialogflow.cx.v3.Environment]
  rpc UpdateEnvironment(UpdateEnvironmentRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v3/{environment.name=projects/*/locations/*/agents/*/environments/*}"
      body: "environment"
    };
    option (google.api.method_signature) = "environment,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Environment"
      metadata_type: "google.protobuf.Struct"
    };
  }

  // Deletes the specified
  // [Environment][google.cloud.dialogflow.cx.v3.Environment].
  rpc DeleteEnvironment(DeleteEnvironmentRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v3/{name=projects/*/locations/*/agents/*/environments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Looks up the history of the specified
  // [Environment][google.cloud.dialogflow.cx.v3.Environment].
  rpc LookupEnvironmentHistory(LookupEnvironmentHistoryRequest)
      returns (LookupEnvironmentHistoryResponse) {
    option (google.api.http) = {
      get: "/v3/{name=projects/*/locations/*/agents/*/environments/*}:lookupEnvironmentHistory"
    };
    option (google.api.method_signature) = "name";
  }

  // Kicks off a continuous test under the specified
  // [Environment][google.cloud.dialogflow.cx.v3.Environment].
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [RunContinuousTestMetadata][google.cloud.dialogflow.cx.v3.RunContinuousTestMetadata]
  // - `response`:
  // [RunContinuousTestResponse][google.cloud.dialogflow.cx.v3.RunContinuousTestResponse]
  rpc RunContinuousTest(RunContinuousTestRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/{environment=projects/*/locations/*/agents/*/environments/*}:runContinuousTest"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "RunContinuousTestResponse"
      metadata_type: "RunContinuousTestMetadata"
    };
  }

  // Fetches a list of continuous test results for a given environment.
  rpc ListContinuousTestResults(ListContinuousTestResultsRequest)
      returns (ListContinuousTestResultsResponse) {
    option (google.api.http) = {
      get: "/v3/{parent=projects/*/locations/*/agents/*/environments/*}/continuousTestResults"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deploys a flow to the specified
  // [Environment][google.cloud.dialogflow.cx.v3.Environment].
  //
  // This method is a [long-running
  // operation](https://cloud.google.com/dialogflow/cx/docs/how/long-running-operation).
  // The returned `Operation` type has the following method-specific fields:
  //
  // - `metadata`:
  // [DeployFlowMetadata][google.cloud.dialogflow.cx.v3.DeployFlowMetadata]
  // - `response`:
  // [DeployFlowResponse][google.cloud.dialogflow.cx.v3.DeployFlowResponse]
  rpc DeployFlow(DeployFlowRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/{environment=projects/*/locations/*/agents/*/environments/*}:deployFlow"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "DeployFlowResponse"
      metadata_type: "DeployFlowMetadata"
    };
  }
}

// Represents an environment for an agent. You can create multiple versions
// of your agent and publish them to separate environments. When you edit an
// agent, you are editing the draft agent. At any point, you can save the draft
// agent as an agent version, which is an immutable snapshot of your agent. When
// you save the draft agent, it is published to the default environment. When
// you create agent versions, you can publish them to custom environments. You
// can create a variety of custom environments for testing, development,
// production, etc.
message Environment {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/Environment"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/environments/{environment}"
  };

  // Configuration for the version.
  message VersionConfig {
    // Required. Both flow and playbook versions are supported.
    // Format for flow version:
    // projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/versions/<VersionID>.
    // Format for playbook version:
    // projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/playbooks/<PlaybookID>/versions/<VersionID>.
    string version = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.resource_reference) = {
        type: "dialogflow.googleapis.com/Version"
      }
    ];
  }

  // The configuration for continuous tests.
  message TestCasesConfig {
    // A list of test case names to run. They should be under the same agent.
    // Format of each test case name:
    // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/testCases/<TestCaseID>`
    repeated string test_cases = 1 [(google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/TestCase"
    }];

    // Whether to run test cases in
    // [TestCasesConfig.test_cases][google.cloud.dialogflow.cx.v3.Environment.TestCasesConfig.test_cases]
    // periodically. Default false. If set to true, run once a day.
    bool enable_continuous_run = 2;

    // Whether to run test cases in
    // [TestCasesConfig.test_cases][google.cloud.dialogflow.cx.v3.Environment.TestCasesConfig.test_cases]
    // before deploying a flow version to the environment. Default false.
    bool enable_predeployment_run = 3;
  }

  // Configuration for webhooks.
  message WebhookConfig {
    // The list of webhooks to override for the agent environment. The webhook
    // must exist in the agent. You can override fields in
    // [`generic_web_service`][google.cloud.dialogflow.cx.v3.Webhook.generic_web_service]
    // and
    // [`service_directory`][google.cloud.dialogflow.cx.v3.Webhook.service_directory].
    repeated Webhook webhook_overrides = 1;
  }

  // The name of the environment.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>`.
  string name = 1;

  // Required. The human-readable name of the environment (unique in an agent).
  // Limit of 64 characters.
  string display_name = 2 [(google.api.field_behavior) = REQUIRED];

  // The human-readable description of the environment. The maximum length is
  // 500 characters. If exceeded, the request is rejected.
  string description = 3;

  // A list of configurations for flow versions. You should include version
  // configs for all flows that are reachable from [`Start
  // Flow`][Agent.start_flow] in the agent. Otherwise, an error will be
  // returned.
  repeated VersionConfig version_configs = 6;

  // Output only. Update time of this environment.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The test cases config for continuous tests of this environment.
  TestCasesConfig test_cases_config = 7;

  // The webhook configuration for this environment.
  WebhookConfig webhook_config = 10;
}

// The request message for
// [Environments.ListEnvironments][google.cloud.dialogflow.cx.v3.Environments.ListEnvironments].
message ListEnvironmentsRequest {
  // Required. The [Agent][google.cloud.dialogflow.cx.v3.Agent] to list all
  // environments for. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Environment"
    }
  ];

  // The maximum number of items to return in a single page. By default 20 and
  // at most 100.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request.
  string page_token = 3;
}

// The response message for
// [Environments.ListEnvironments][google.cloud.dialogflow.cx.v3.Environments.ListEnvironments].
message ListEnvironmentsResponse {
  // The list of environments. There will be a maximum number of items
  // returned based on the page_size field in the request. The list may in some
  // cases be empty or contain fewer entries than page_size even if this isn't
  // the last page.
  repeated Environment environments = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The request message for
// [Environments.GetEnvironment][google.cloud.dialogflow.cx.v3.Environments.GetEnvironment].
message GetEnvironmentRequest {
  // Required. The name of the
  // [Environment][google.cloud.dialogflow.cx.v3.Environment]. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Environment"
    }
  ];
}

// The request message for
// [Environments.CreateEnvironment][google.cloud.dialogflow.cx.v3.Environments.CreateEnvironment].
message CreateEnvironmentRequest {
  // Required. The [Agent][google.cloud.dialogflow.cx.v3.Agent] to create an
  // [Environment][google.cloud.dialogflow.cx.v3.Environment] for. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/Environment"
    }
  ];

  // Required. The environment to create.
  Environment environment = 2 [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [Environments.UpdateEnvironment][google.cloud.dialogflow.cx.v3.Environments.UpdateEnvironment].
message UpdateEnvironmentRequest {
  // Required. The environment to update.
  Environment environment = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The mask to control which fields get updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// The request message for
// [Environments.DeleteEnvironment][google.cloud.dialogflow.cx.v3.Environments.DeleteEnvironment].
message DeleteEnvironmentRequest {
  // Required. The name of the
  // [Environment][google.cloud.dialogflow.cx.v3.Environment] to delete. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Environment"
    }
  ];
}

// The request message for
// [Environments.LookupEnvironmentHistory][google.cloud.dialogflow.cx.v3.Environments.LookupEnvironmentHistory].
message LookupEnvironmentHistoryRequest {
  // Required. Resource name of the environment to look up the history for.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Environment"
    }
  ];

  // The maximum number of items to return in a single page. By default 100 and
  // at most 1000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request.
  string page_token = 3;
}

// The response message for
// [Environments.LookupEnvironmentHistory][google.cloud.dialogflow.cx.v3.Environments.LookupEnvironmentHistory].
message LookupEnvironmentHistoryResponse {
  // Represents a list of snapshots for an environment. Time of the snapshots is
  // stored in
  // [`update_time`][google.cloud.dialogflow.cx.v3.Environment.update_time].
  repeated Environment environments = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// Represents a result from running a test case in an agent environment.
message ContinuousTestResult {
  option (google.api.resource) = {
    type: "dialogflow.googleapis.com/ContinuousTestResult"
    pattern: "projects/{project}/locations/{location}/agents/{agent}/environments/{environment}/continuousTestResults/{continuous_test_result}"
  };

  // The overall result for a continuous test run in an agent environment.
  enum AggregatedTestResult {
    // Not specified. Should never be used.
    AGGREGATED_TEST_RESULT_UNSPECIFIED = 0;

    // All the tests passed.
    PASSED = 1;

    // At least one test did not pass.
    FAILED = 2;
  }

  // The resource name for the continuous test result. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>/continuousTestResults/<ContinuousTestResultID>`.
  string name = 1;

  // The result of this continuous test run, i.e. whether all the tests in this
  // continuous test run pass or not.
  AggregatedTestResult result = 2;

  // A list of individual test case results names in this continuous test run.
  repeated string test_case_results = 3 [(google.api.resource_reference) = {
    type: "dialogflow.googleapis.com/TestCaseResult"
  }];

  // Time when the continuous testing run starts.
  google.protobuf.Timestamp run_time = 4;
}

// The request message for
// [Environments.RunContinuousTest][google.cloud.dialogflow.cx.v3.Environments.RunContinuousTest].
message RunContinuousTestRequest {
  // Required. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>`.
  string environment = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Environment"
    }
  ];
}

// The response message for
// [Environments.RunContinuousTest][google.cloud.dialogflow.cx.v3.Environments.RunContinuousTest].
message RunContinuousTestResponse {
  // The result for a continuous test run.
  ContinuousTestResult continuous_test_result = 1;
}

// Metadata returned for the
// [Environments.RunContinuousTest][google.cloud.dialogflow.cx.v3.Environments.RunContinuousTest]
// long running operation.
message RunContinuousTestMetadata {
  // The test errors.
  repeated TestError errors = 1;
}

// The request message for
// [Environments.ListContinuousTestResults][google.cloud.dialogflow.cx.v3.Environments.ListContinuousTestResults].
message ListContinuousTestResultsRequest {
  // Required. The environment to list results for.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "dialogflow.googleapis.com/ContinuousTestResult"
    }
  ];

  // The maximum number of items to return in a single page. By default 100 and
  // at most 1000.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request.
  string page_token = 3;
}

// The response message for [Environments.ListTestCaseResults][].
message ListContinuousTestResultsResponse {
  // The list of continuous test results.
  repeated ContinuousTestResult continuous_test_results = 1;

  // Token to retrieve the next page of results, or empty if there are no more
  // results in the list.
  string next_page_token = 2;
}

// The request message for
// [Environments.DeployFlow][google.cloud.dialogflow.cx.v3.Environments.DeployFlow].
message DeployFlowRequest {
  // Required. The environment to deploy the flow to.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>`.
  string environment = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Environment"
    }
  ];

  // Required. The flow version to deploy.
  // Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/flows/<FlowID>/versions/<VersionID>`.
  string flow_version = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dialogflow.googleapis.com/Version"
    }
  ];
}

// The response message for
// [Environments.DeployFlow][google.cloud.dialogflow.cx.v3.Environments.DeployFlow].
message DeployFlowResponse {
  // The updated environment where the flow is deployed.
  Environment environment = 1;

  // The name of the flow version
  // [Deployment][google.cloud.dialogflow.cx.v3.Deployment]. Format:
  // `projects/<ProjectID>/locations/<LocationID>/agents/<AgentID>/environments/<EnvironmentID>/deployments/<DeploymentID>`.
  string deployment = 2;
}

// Metadata returned for the
// [Environments.DeployFlow][google.cloud.dialogflow.cx.v3.Environments.DeployFlow]
// long running operation.
message DeployFlowMetadata {
  // Errors of running deployment tests.
  repeated TestError test_errors = 1;
}
