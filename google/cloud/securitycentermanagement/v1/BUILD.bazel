# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "securitycentermanagement_proto",
    srcs = [
        "security_center_management.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:policy_proto",
        "//google/rpc:status_proto",
        "//google/type:expr_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "securitycentermanagement_proto_with_info",
    deps = [
        ":securitycentermanagement_proto",
        "//google/cloud/location:location_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "securitycentermanagement_java_proto",
    deps = [":securitycentermanagement_proto"],
)

java_grpc_library(
    name = "securitycentermanagement_java_grpc",
    srcs = [":securitycentermanagement_proto"],
    deps = [":securitycentermanagement_java_proto"],
)

java_gapic_library(
    name = "securitycentermanagement_java_gapic",
    srcs = [":securitycentermanagement_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "securitycentermanagement_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "securitycentermanagement_v1.yaml",
    test_deps = [
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
        ":securitycentermanagement_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":securitycentermanagement_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "securitycentermanagement_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.securitycentermanagement.v1.SecurityCenterManagementClientHttpJsonTest",
        "com.google.cloud.securitycentermanagement.v1.SecurityCenterManagementClientTest",
    ],
    runtime_deps = [":securitycentermanagement_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-securitycentermanagement-v1-java",
    transport = "grpc+rest",
    deps = [
        ":securitycentermanagement_java_gapic",
        ":securitycentermanagement_java_grpc",
        ":securitycentermanagement_java_proto",
        ":securitycentermanagement_proto",
    ],
    include_samples = True,
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "securitycentermanagement_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/securitycentermanagement/apiv1/securitycentermanagementpb",
    protos = [":securitycentermanagement_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/rpc:status_go_proto",
        "//google/type:expr_go_proto",
    ],
)

go_gapic_library(
    name = "securitycentermanagement_go_gapic",
    srcs = [":securitycentermanagement_proto_with_info"],
    grpc_service_config = "securitycentermanagement_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/securitycentermanagement/apiv1;securitycentermanagement",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "securitycentermanagement_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":securitycentermanagement_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-securitycentermanagement-v1-go",
    deps = [
        ":securitycentermanagement_go_gapic",
        ":securitycentermanagement_go_gapic_srcjar-test.srcjar",
        ":securitycentermanagement_go_gapic_srcjar-metadata.srcjar",
        ":securitycentermanagement_go_gapic_srcjar-snippets.srcjar",
        ":securitycentermanagement_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "securitycentermanagement_py_gapic",
    srcs = [":securitycentermanagement_proto"],
    grpc_service_config = "securitycentermanagement_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "securitycentermanagement_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "securitycentermanagement_py_gapic_test",
    srcs = [
        "securitycentermanagement_py_gapic_pytest.py",
        "securitycentermanagement_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":securitycentermanagement_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "securitycentermanagement-v1-py",
    deps = [
        ":securitycentermanagement_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "securitycentermanagement_php_proto",
    deps = [":securitycentermanagement_proto"],
)

php_gapic_library(
    name = "securitycentermanagement_php_gapic",
    srcs = [":securitycentermanagement_proto_with_info"],
    grpc_service_config = "securitycentermanagement_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    migration_mode = "NEW_SURFACE_ONLY",
    service_yaml = "securitycentermanagement_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":securitycentermanagement_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-securitycentermanagement-v1-php",
    deps = [
        ":securitycentermanagement_php_gapic",
        ":securitycentermanagement_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "securitycentermanagement_nodejs_gapic",
    package_name = "@google-cloud/securitycentermanagement",
    src = ":securitycentermanagement_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "securitycentermanagement_v1_grpc_service_config.json",
    package = "google.cloud.securitycentermanagement.v1",
    rest_numeric_enums = True,
    service_yaml = "securitycentermanagement_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "securitycentermanagement-v1-nodejs",
    deps = [
        ":securitycentermanagement_nodejs_gapic",
        ":securitycentermanagement_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_gapic_assembly_pkg",
    "ruby_cloud_gapic_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "securitycentermanagement_ruby_proto",
    deps = [":securitycentermanagement_proto"],
)

ruby_grpc_library(
    name = "securitycentermanagement_ruby_grpc",
    srcs = [":securitycentermanagement_proto"],
    deps = [":securitycentermanagement_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "securitycentermanagement_ruby_gapic",
    srcs = [":securitycentermanagement_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-security_center_management-v1",
    ],
    grpc_service_config = "securitycentermanagement_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "securitycentermanagement_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":securitycentermanagement_ruby_grpc",
        ":securitycentermanagement_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-securitycentermanagement-v1-ruby",
    deps = [
        ":securitycentermanagement_ruby_gapic",
        ":securitycentermanagement_ruby_grpc",
        ":securitycentermanagement_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "securitycentermanagement_csharp_proto",
    extra_opts = [],
    deps = [":securitycentermanagement_proto"],
)

csharp_grpc_library(
    name = "securitycentermanagement_csharp_grpc",
    srcs = [":securitycentermanagement_proto"],
    deps = [":securitycentermanagement_csharp_proto"],
)

csharp_gapic_library(
    name = "securitycentermanagement_csharp_gapic",
    srcs = [":securitycentermanagement_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "securitycentermanagement_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "securitycentermanagement_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":securitycentermanagement_csharp_grpc",
        ":securitycentermanagement_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-securitycentermanagement-v1-csharp",
    deps = [
        ":securitycentermanagement_csharp_gapic",
        ":securitycentermanagement_csharp_grpc",
        ":securitycentermanagement_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "securitycentermanagement_cc_proto",
    deps = [":securitycentermanagement_proto"],
)

cc_grpc_library(
    name = "securitycentermanagement_cc_grpc",
    srcs = [":securitycentermanagement_proto"],
    grpc_only = True,
    deps = [":securitycentermanagement_cc_proto"],
)
