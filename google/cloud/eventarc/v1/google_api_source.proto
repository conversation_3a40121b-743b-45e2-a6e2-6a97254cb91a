// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.eventarc.v1;

import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/cloud/eventarc/v1/logging_config.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Eventarc.V1";
option go_package = "cloud.google.com/go/eventarc/apiv1/eventarcpb;eventarcpb";
option java_multiple_files = true;
option java_outer_classname = "GoogleApiSourceProto";
option java_package = "com.google.cloud.eventarc.v1";
option php_namespace = "Google\\Cloud\\Eventarc\\V1";
option ruby_package = "Google::Cloud::Eventarc::V1";

// A GoogleApiSource represents a subscription of 1P events from a MessageBus.
message GoogleApiSource {
  option (google.api.resource) = {
    type: "eventarc.googleapis.com/GoogleApiSource"
    pattern: "projects/{project}/locations/{location}/googleApiSources/{google_api_source}"
    plural: "googleApiSources"
    singular: "googleApiSource"
  };

  // Identifier. Resource name of the form
  // projects/{project}/locations/{location}/googleApiSources/{google_api_source}
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. Server assigned unique identifier for the channel. The value
  // is a UUID4 string and guaranteed to remain unchanged until the resource is
  // deleted.
  string uid = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Output only. This checksum is computed by the server based on the value of
  // other fields, and might be sent only on update and delete requests to
  // ensure that the client has an up-to-date value before proceeding.
  string etag = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The creation time.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last-modified time.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. Resource labels.
  map<string, string> labels = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Resource annotations.
  map<string, string> annotations = 7 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Resource display name.
  string display_name = 8 [(google.api.field_behavior) = OPTIONAL];

  // Required. Destination is the message bus that the GoogleApiSource is
  // delivering to. It must be point to the full resource name of a MessageBus.
  // Format:
  // "projects/{PROJECT_ID}/locations/{region}/messagesBuses/{MESSAGE_BUS_ID)
  string destination = 9 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "eventarc.googleapis.com/MessageBus"
    }
  ];

  // Optional. Resource name of a KMS crypto key (managed by the user) used to
  // encrypt/decrypt their event data.
  //
  // It must match the pattern
  // `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
  string crypto_key_name = 10 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "cloudkms.googleapis.com/CryptoKey"
    }
  ];

  // Optional. Config to control Platform logging for the GoogleApiSource.
  LoggingConfig logging_config = 11 [(google.api.field_behavior) = OPTIONAL];
}
