// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.eventarc.publishing.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/cloud/eventarc/publishing/v1/cloud_event.proto";
import "google/protobuf/any.proto";

option csharp_namespace = "Google.Cloud.Eventarc.Publishing.V1";
option go_package = "cloud.google.com/go/eventarc/publishing/apiv1/publishingpb;publishingpb";
option java_multiple_files = true;
option java_outer_classname = "PublisherProto";
option java_package = "com.google.cloud.eventarc.publishing.v1";
option php_namespace = "Google\\Cloud\\Eventarc\\Publishing\\V1";
option ruby_package = "Google::Cloud::Eventarc::Publishing::V1";

// Eventarc processes events generated by an event provider and delivers them to
// a subscriber.
//
// An event provider is a software-as-a-service (SaaS) system or
// product that can generate and deliver events through Eventarc.
//
// A third-party event provider is an event provider from outside of Google.
//
// A partner is a third-party event provider that is integrated with Eventarc.
//
// A subscriber is a GCP customer interested in receiving events.
//
// Channel is a first-class Eventarc resource that is created and managed
// by the subscriber in their GCP project. A Channel represents a subscriber's
// intent to receive events from an event provider. A Channel is associated with
// exactly one event provider.
//
// ChannelConnection is a first-class Eventarc resource that
// is created and managed by the partner in their GCP project. A
// ChannelConnection represents a connection between a partner and a
// subscriber's Channel. A ChannelConnection has a one-to-one mapping with a
// Channel.
//
// Publisher allows an event provider to publish events to Eventarc.
service Publisher {
  option (google.api.default_host) = "eventarcpublishing.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Publish events to a ChannelConnection in a partner's project.
  rpc PublishChannelConnectionEvents(PublishChannelConnectionEventsRequest)
      returns (PublishChannelConnectionEventsResponse) {
    option (google.api.http) = {
      post: "/v1/{channel_connection=projects/*/locations/*/channelConnections/*}:publishEvents"
      body: "*"
    };
  }

  // Publish events to a subscriber's channel.
  rpc PublishEvents(PublishEventsRequest) returns (PublishEventsResponse) {
    option (google.api.http) = {
      post: "/v1/{channel=projects/*/locations/*/channels/*}:publishEvents"
      body: "*"
    };
  }

  // Publish events to a message bus.
  rpc Publish(PublishRequest) returns (PublishResponse) {
    option (google.api.http) = {
      post: "/v1/{message_bus=projects/*/locations/*/messageBuses/*}:publish"
      body: "*"
    };
  }
}

// The request message for the PublishChannelConnectionEvents method.
message PublishChannelConnectionEventsRequest {
  // The channel_connection that the events are published from. For example:
  // `projects/{partner_project_id}/locations/{location}/channelConnections/{channel_connection_id}`.
  string channel_connection = 1;

  // The CloudEvents v1.0 events to publish. No other types are allowed.
  // If this field is set, then the `text_events` fields must not be set.
  repeated google.protobuf.Any events = 2;

  // The text representation of events to publish.
  // CloudEvent v1.0 in JSON format is the only allowed type. Refer to
  // https://github.com/cloudevents/spec/blob/v1.0.2/cloudevents/formats/json-format.md
  // for specification.
  // If this field is set, then the `events` fields must not be set.
  repeated string text_events = 3;
}

// The response message for the PublishChannelConnectionEvents method.
message PublishChannelConnectionEventsResponse {}

// The request message for the PublishEvents method.
message PublishEventsRequest {
  // The full name of the channel to publish to. For example:
  // `projects/{project}/locations/{location}/channels/{channel-id}`.
  string channel = 1;

  // The CloudEvents v1.0 events to publish. No other types are allowed.
  // If this field is set, then the `text_events` fields must not be set.
  repeated google.protobuf.Any events = 2;

  // The text representation of events to publish.
  // CloudEvent v1.0 in JSON format is the only allowed type. Refer to
  // https://github.com/cloudevents/spec/blob/v1.0.2/cloudevents/formats/json-format.md
  // for specification.
  // If this field is set, then the `events` fields must not be set.
  repeated string text_events = 3;
}

// The response message for the PublishEvents method.
message PublishEventsResponse {}

// The request message for the Publish method.
message PublishRequest {
  // Required. The full name of the message bus to publish events to. Format:
  // `projects/{project}/locations/{location}/messageBuses/{messageBus}`.
  string message_bus = 1 [(google.api.field_behavior) = REQUIRED];

  oneof format {
    // The Protobuf format of the CloudEvent being published. Specification can
    // be found here:
    // https://github.com/cloudevents/spec/blob/v1.0.2/cloudevents/formats/protobuf-format.md
    CloudEvent proto_message = 2;

    // The JSON format of the CloudEvent being published. Specification can be
    // found here:
    // https://github.com/cloudevents/spec/blob/v1.0.2/cloudevents/formats/json-format.md
    string json_message = 3;

    // The Avro format of the CloudEvent being published. Specification can
    // be found here:
    // https://github.com/cloudevents/spec/blob/v1.0.2/cloudevents/formats/avro-format.md
    bytes avro_message = 4;
  }
}

// The response message for the Publish method.
message PublishResponse {}
