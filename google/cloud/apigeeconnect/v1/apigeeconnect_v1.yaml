type: google.api.Service
config_version: 3
name: apigeeconnect.googleapis.com
title: Apigee Connect API

apis:
- name: google.cloud.apigeeconnect.v1.ConnectionService
- name: google.cloud.apigeeconnect.v1.Tether

authentication:
  rules:
  - selector: google.cloud.apigeeconnect.v1.ConnectionService.ListConnections
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.apigeeconnect.v1.Tether.Egress
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
