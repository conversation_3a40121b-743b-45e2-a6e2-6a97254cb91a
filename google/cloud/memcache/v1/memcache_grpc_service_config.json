{"methodConfig": [{"name": [{"service": "google.cloud.memcache.v1.CloudMemcache", "method": "ListInstances"}, {"service": "google.cloud.memcache.v1.CloudMemcache", "method": "GetInstance"}, {"service": "google.cloud.memcache.v1.CloudMemcache", "method": "CreateInstance"}, {"service": "google.cloud.memcache.v1.CloudMemcache", "method": "UpdateInstance"}, {"service": "google.cloud.memcache.v1.CloudMemcache", "method": "UpdateParameters"}, {"service": "google.cloud.memcache.v1.CloudMemcache", "method": "ApplyParameters"}, {"service": "google.cloud.memcache.v1.CloudMemcache", "method": "DeleteInstance"}, {"service": "google.cloud.memcache.v1.CloudMemcache", "method": "ApplySoftwareUpdate"}, {"service": "google.cloud.memcache.v1.CloudMemcache", "method": "RescheduleMaintenance"}], "timeout": "1200s"}]}