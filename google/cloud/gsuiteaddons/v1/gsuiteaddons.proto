// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gsuiteaddons.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/apps/script/type/calendar/calendar_addon_manifest.proto";
import "google/apps/script/type/docs/docs_addon_manifest.proto";
import "google/apps/script/type/drive/drive_addon_manifest.proto";
import "google/apps/script/type/gmail/gmail_addon_manifest.proto";
import "google/apps/script/type/script_manifest.proto";
import "google/apps/script/type/sheets/sheets_addon_manifest.proto";
import "google/apps/script/type/slides/slides_addon_manifest.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/wrappers.proto";

option csharp_namespace = "Google.Cloud.GSuiteAddOns.V1";
option go_package = "cloud.google.com/go/gsuiteaddons/apiv1/gsuiteaddonspb;gsuiteaddonspb";
option java_multiple_files = true;
option java_outer_classname = "GSuiteAddOnsProto";
option java_package = "com.google.cloud.gsuiteaddons.v1";
option php_namespace = "Google\\Cloud\\GSuiteAddOns\\V1";
option ruby_package = "Google::Cloud::GSuiteAddOns::V1";

// A service for managing Google Workspace Add-ons deployments.
//
// A Google Workspace Add-on is a third-party embedded component that can be
// installed in Google Workspace Applications like Gmail, Calendar, Drive, and
// the Google Docs, Sheets, and Slides editors. Google Workspace Add-ons can
// display UI cards, receive contextual information from the host application,
// and perform actions in the host application (See:
// https://developers.google.com/gsuite/add-ons/overview for more information).
//
// A Google Workspace Add-on deployment resource specifies metadata about the
// add-on, including a specification of the entry points in the host application
// that trigger add-on executions (see:
// https://developers.google.com/gsuite/add-ons/concepts/gsuite-manifests).
// Add-on deployments defined via the Google Workspace Add-ons API define their
// entrypoints using HTTPS URLs (See:
// https://developers.google.com/gsuite/add-ons/guides/alternate-runtimes),
//
// A Google Workspace Add-on deployment can be installed in developer mode,
// which allows an add-on developer to test the experience an end-user would see
// when installing and running the add-on in their G Suite applications.  When
// running in developer mode, more detailed error messages are exposed in the
// add-on UI to aid in debugging.
//
// A Google Workspace Add-on deployment can be published to Google Workspace
// Marketplace, which allows other Google Workspace users to discover and
// install the add-on.  See:
// https://developers.google.com/gsuite/add-ons/how-tos/publish-add-on-overview
// for details.
service GSuiteAddOns {
  option (google.api.default_host) = "gsuiteaddons.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Gets the authorization information for deployments in a given project.
  rpc GetAuthorization(GetAuthorizationRequest) returns (Authorization) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/authorization}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a deployment with the specified name and configuration.
  rpc CreateDeployment(CreateDeploymentRequest) returns (Deployment) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*}/deployments"
      body: "deployment"
    };
    option (google.api.method_signature) = "parent,deployment,deployment_id";
  }

  // Creates or replaces a deployment with the specified name.
  rpc ReplaceDeployment(ReplaceDeploymentRequest) returns (Deployment) {
    option (google.api.http) = {
      put: "/v1/{deployment.name=projects/*/deployments/*}"
      body: "deployment"
    };
    option (google.api.method_signature) = "deployment";
  }

  // Gets the deployment with the specified name.
  rpc GetDeployment(GetDeploymentRequest) returns (Deployment) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/deployments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists all deployments in a particular project.
  rpc ListDeployments(ListDeploymentsRequest)
      returns (ListDeploymentsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*}/deployments"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes the deployment with the given name.
  rpc DeleteDeployment(DeleteDeploymentRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/deployments/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Installs a deployment in developer mode.
  // See:
  // https://developers.google.com/gsuite/add-ons/how-tos/testing-gsuite-addons.
  rpc InstallDeployment(InstallDeploymentRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/deployments/*}:install"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Uninstalls a developer mode deployment.
  // See:
  // https://developers.google.com/gsuite/add-ons/how-tos/testing-gsuite-addons.
  rpc UninstallDeployment(UninstallDeploymentRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/deployments/*}:uninstall"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Fetches the install status of a developer mode deployment.
  rpc GetInstallStatus(GetInstallStatusRequest) returns (InstallStatus) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/deployments/*/installStatus}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Request message to get Google Workspace Add-ons authorization information.
message GetAuthorizationRequest {
  // Required. Name of the project for which to get the Google Workspace Add-ons
  // authorization information.
  //
  // Example: `projects/my_project/authorization`.
  string name = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gsuiteaddons.googleapis.com/Authorization"
    }
  ];
}

// The authorization information used when invoking deployment endpoints.
message Authorization {
  option (google.api.resource) = {
    type: "gsuiteaddons.googleapis.com/Authorization"
    pattern: "projects/{project}/authorization"
  };

  // The canonical full name of this resource.
  // Example:  `projects/123/authorization`
  string name = 1;

  // The email address of the service account used to authenticate requests to
  // add-on callback endpoints.
  string service_account_email = 2;

  // The OAuth client ID used to obtain OAuth access tokens for a user on the
  // add-on's behalf.
  string oauth_client_id = 3;
}

// Request message to create a deployment.
message CreateDeploymentRequest {
  // Required. Name of the project in which to create the deployment.
  //
  // Example: `projects/my_project`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Required. The id to use for this deployment.  The full name of the created
  // resource will be `projects/<project_number>/deployments/<deployment_id>`.
  string deployment_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The deployment to create (deployment.name cannot be set).
  Deployment deployment = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request message to create or replace a deployment.
message ReplaceDeploymentRequest {
  // Required. The deployment to create or replace.
  Deployment deployment = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request message to get a deployment.
message GetDeploymentRequest {
  // Required. The full resource name of the deployment to get.
  //
  // Example:  `projects/my_project/deployments/my_deployment`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gsuiteaddons.googleapis.com/Deployment"
    }
  ];
}

// Request message to list deployments for a project.
message ListDeploymentsRequest {
  // Required. Name of the project in which to create the deployment.
  //
  // Example: `projects/my_project`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // The maximum number of deployments to return. The service may return fewer
  // than this value.
  // If unspecified, at most 1000 deployments will be returned.
  // The maximum value is 1000; values above 1000 will be coerced to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListDeployments` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListDeployments` must
  // match the call that provided the page token.
  string page_token = 3;
}

// Response message to list deployments.
message ListDeploymentsResponse {
  // The list of deployments for the given project.
  repeated Deployment deployments = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message to delete a deployment.
message DeleteDeploymentRequest {
  // Required. The full resource name of the deployment to delete.
  //
  // Example:  `projects/my_project/deployments/my_deployment`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gsuiteaddons.googleapis.com/Deployment"
    }
  ];

  // The etag of the deployment to delete.
  // If this is provided, it must match the server's etag.
  string etag = 2;
}

// Request message to install a developer mode deployment.
message InstallDeploymentRequest {
  // Required. The full resource name of the deployment to install.
  //
  // Example:  `projects/my_project/deployments/my_deployment`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gsuiteaddons.googleapis.com/Deployment"
    }
  ];
}

// Request message to uninstall a developer mode deployment.
message UninstallDeploymentRequest {
  // Required. The full resource name of the deployment to install.
  //
  // Example:  `projects/my_project/deployments/my_deployment`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gsuiteaddons.googleapis.com/Deployment"
    }
  ];
}

// Request message to get the install status of a developer mode deployment.
message GetInstallStatusRequest {
  // Required. The full resource name of the deployment.
  //
  // Example:  `projects/my_project/deployments/my_deployment/installStatus`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "gsuiteaddons.googleapis.com/InstallStatus"
    }
  ];
}

// Developer mode install status of a deployment
message InstallStatus {
  option (google.api.resource) = {
    type: "gsuiteaddons.googleapis.com/InstallStatus"
    pattern: "projects/{project}/deployments/{deployment}/installStatus"
  };

  // The canonical full resource name of the deployment install status.
  //
  // Example:  `projects/123/deployments/my_deployment/installStatus`.
  string name = 1;

  // True if the deployment is installed for the user
  google.protobuf.BoolValue installed = 2;
}

// A Google Workspace Add-on deployment
message Deployment {
  option (google.api.resource) = {
    type: "gsuiteaddons.googleapis.com/Deployment"
    pattern: "projects/{project}/deployments/{deployment}"
  };

  // The deployment resource name.
  // Example:  projects/123/deployments/my_deployment.
  string name = 1;

  // The list of Google OAuth scopes for which to request consent from the end
  // user before executing an add-on endpoint.
  repeated string oauth_scopes = 2;

  // The Google Workspace Add-on configuration.
  AddOns add_ons = 3;

  // This value is computed by the server based on the version of the
  // deployment in storage, and may be sent on update and delete requests to
  // ensure the client has an up-to-date value before proceeding.
  string etag = 5;
}

// A Google Workspace Add-on configuration.
message AddOns {
  // Configuration that is common across all Google Workspace Add-ons.
  google.apps.script.type.CommonAddOnManifest common = 1;

  // Gmail add-on configuration.
  google.apps.script.type.gmail.GmailAddOnManifest gmail = 2;

  // Drive add-on configuration.
  google.apps.script.type.drive.DriveAddOnManifest drive = 5;

  // Calendar add-on configuration.
  google.apps.script.type.calendar.CalendarAddOnManifest calendar = 6;

  // Docs add-on configuration.
  google.apps.script.type.docs.DocsAddOnManifest docs = 7;

  // Sheets add-on configuration.
  google.apps.script.type.sheets.SheetsAddOnManifest sheets = 8;

  // Slides add-on configuration.
  google.apps.script.type.slides.SlidesAddOnManifest slides = 10;

  // Options for sending requests to add-on HTTP endpoints
  google.apps.script.type.HttpOptions http_options = 15;
}
