// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dataplex.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/dataplex/v1/resources.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/dataplex/apiv1/dataplexpb;dataplexpb";
option java_multiple_files = true;
option java_outer_classname = "AnalyzeProto";
option java_package = "com.google.cloud.dataplex.v1";

// Environment represents a user-visible compute infrastructure for analytics
// within a lake.
message Environment {
  option (google.api.resource) = {
    type: "dataplex.googleapis.com/Environment"
    pattern: "projects/{project}/locations/{location}/lakes/{lake}/environments/{environment}"
  };

  // Configuration for the underlying infrastructure used to run workloads.
  message InfrastructureSpec {
    // Compute resources associated with the analyze interactive workloads.
    message ComputeResources {
      // Optional. Size in GB of the disk. Default is 100 GB.
      int32 disk_size_gb = 1 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Total number of nodes in the sessions created for this
      // environment.
      int32 node_count = 2 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Max configurable nodes.
      // If max_node_count > node_count, then auto-scaling is enabled.
      int32 max_node_count = 3 [(google.api.field_behavior) = OPTIONAL];
    }

    // Software Runtime Configuration to run Analyze.
    message OsImageRuntime {
      // Required. Dataplex Image version.
      string image_version = 1 [(google.api.field_behavior) = REQUIRED];

      // Optional. List of Java jars to be included in the runtime environment.
      // Valid input includes Cloud Storage URIs to Jar binaries.
      // For example, gs://bucket-name/my/path/to/file.jar
      repeated string java_libraries = 2
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. A list of python packages to be installed.
      // Valid formats include Cloud Storage URI to a PIP installable library.
      // For example, gs://bucket-name/my/path/to/lib.tar.gz
      repeated string python_packages = 3
          [(google.api.field_behavior) = OPTIONAL];

      // Optional. Spark properties to provide configuration for use in sessions
      // created for this environment. The properties to set on daemon config
      // files. Property keys are specified in `prefix:property` format. The
      // prefix must be "spark".
      map<string, string> properties = 4
          [(google.api.field_behavior) = OPTIONAL];
    }

    // Hardware config
    oneof resources {
      // Optional. Compute resources needed for analyze interactive workloads.
      ComputeResources compute = 50 [(google.api.field_behavior) = OPTIONAL];
    }

    // Software config
    oneof runtime {
      // Required. Software Runtime Configuration for analyze interactive
      // workloads.
      OsImageRuntime os_image = 100 [(google.api.field_behavior) = REQUIRED];
    }
  }

  // Configuration for sessions created for this environment.
  message SessionSpec {
    // Optional. The idle time configuration of the session. The session will be
    // auto-terminated at the end of this period.
    google.protobuf.Duration max_idle_duration = 1
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. If True, this causes sessions to be pre-created and available
    // for faster startup to enable interactive exploration use-cases. This
    // defaults to False to avoid additional billed charges. These can only be
    // set to True for the environment with name set to "default", and with
    // default configuration.
    bool enable_fast_startup = 2 [(google.api.field_behavior) = OPTIONAL];
  }

  // Status of sessions created for this environment.
  message SessionStatus {
    // Output only. Queries over sessions to mark whether the environment is
    // currently active or not
    bool active = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // URI Endpoints to access sessions associated with the Environment.
  message Endpoints {
    // Output only. URI to serve notebook APIs
    string notebooks = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. URI to serve SQL APIs
    string sql = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The relative resource name of the environment, of the form:
  // projects/{project_id}/locations/{location_id}/lakes/{lake_id}/environment/{environment_id}
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/Environment"
    }
  ];

  // Optional. User friendly display name.
  string display_name = 2 [(google.api.field_behavior) = OPTIONAL];

  // Output only. System generated globally unique ID for the environment. This
  // ID will be different if the environment is deleted and re-created with the
  // same name.
  string uid = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Environment creation time.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the environment was last updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. User defined labels for the environment.
  map<string, string> labels = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Description of the environment.
  string description = 7 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Current state of the environment.
  State state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Infrastructure specification for the Environment.
  InfrastructureSpec infrastructure_spec = 100
      [(google.api.field_behavior) = REQUIRED];

  // Optional. Configuration for sessions created for this environment.
  SessionSpec session_spec = 101 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Status of sessions created for this environment.
  SessionStatus session_status = 102
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. URI Endpoints to access sessions associated with the
  // Environment.
  Endpoints endpoints = 200 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Content represents a user-visible notebook or a sql script
message Content {
  option (google.api.resource) = {
    type: "dataplex.googleapis.com/Content"
    pattern: "projects/{project}/locations/{location}/lakes/{lake}/content/{content}"
  };

  // Configuration for the Sql Script content.
  message SqlScript {
    // Query Engine Type of the SQL Script.
    enum QueryEngine {
      // Value was unspecified.
      QUERY_ENGINE_UNSPECIFIED = 0;

      // Spark SQL Query.
      SPARK = 2;
    }

    // Required. Query Engine to be used for the Sql Query.
    QueryEngine engine = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Configuration for Notebook content.
  message Notebook {
    // Kernel Type of the Jupyter notebook.
    enum KernelType {
      // Kernel Type unspecified.
      KERNEL_TYPE_UNSPECIFIED = 0;

      // Python 3 Kernel.
      PYTHON3 = 1;
    }

    // Required. Kernel Type of the notebook.
    KernelType kernel_type = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // Output only. The relative resource name of the content, of the form:
  // projects/{project_id}/locations/{location_id}/lakes/{lake_id}/content/{content_id}
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/Content"
    }
  ];

  // Output only. System generated globally unique ID for the content. This ID
  // will be different if the content is deleted and re-created with the same
  // name.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The path for the Content file, represented as directory
  // structure. Unique within a lake. Limited to alphanumerics, hyphens,
  // underscores, dots and slashes.
  string path = 3 [(google.api.field_behavior) = REQUIRED];

  // Output only. Content creation time.
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the content was last updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. User defined labels for the content.
  map<string, string> labels = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Description of the content.
  string description = 7 [(google.api.field_behavior) = OPTIONAL];

  // Only returned in `GetContent` requests and not in `ListContent` request.
  oneof data {
    // Required. Content data in string format.
    string data_text = 9 [(google.api.field_behavior) = REQUIRED];
  }

  // Types of content
  oneof content {
    // Sql Script related configurations.
    SqlScript sql_script = 100;

    // Notebook related configurations.
    Notebook notebook = 101;
  }
}

// Represents an active analyze session running for a user.
message Session {
  option (google.api.resource) = {
    type: "dataplex.googleapis.com/Session"
    pattern: "projects/{project}/locations/{location}/lakes/{lake}/environments/{environment}/sessions/{session}"
  };

  // Output only. The relative resource name of the content, of the form:
  // projects/{project_id}/locations/{location_id}/lakes/{lake_id}/environment/{environment_id}/sessions/{session_id}
  string name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "dataplex.googleapis.com/Session"
    }
  ];

  // Output only. Email of user running the session.
  string user_id = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Session start time.
  google.protobuf.Timestamp create_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of Session
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
}
