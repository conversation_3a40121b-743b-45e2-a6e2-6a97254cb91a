// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dataplex.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option go_package = "cloud.google.com/go/dataplex/apiv1/dataplexpb;dataplexpb";
option java_multiple_files = true;
option java_outer_classname = "DataDiscoveryProto";
option java_package = "com.google.cloud.dataplex.v1";
option (google.api.resource_definition) = {
  type: "bigquery.googleapis.com/Dataset"
  pattern: "projects/{project}/datasets/{dataset}"
};
option (google.api.resource_definition) = {
  type: "bigqueryconnection.googleapis.com/Connection"
  pattern: "projects/{project}/locations/{location}/connections/{connection}"
};

// Spec for a data discovery scan.
message DataDiscoverySpec {
  // Describes BigQuery publishing configurations.
  message BigQueryPublishingConfig {
    // Determines how discovered tables are published.
    enum TableType {
      // Table type unspecified.
      TABLE_TYPE_UNSPECIFIED = 0;

      // Default. Discovered tables are published as BigQuery external tables
      // whose data is accessed using the credentials of the user querying the
      // table.
      EXTERNAL = 1;

      // Discovered tables are published as BigLake external tables whose data
      // is accessed using the credentials of the associated BigQuery
      // connection.
      BIGLAKE = 2;
    }

    // Optional. Determines whether to  publish discovered tables as BigLake
    // external tables or non-BigLake external tables.
    TableType table_type = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The BigQuery connection used to create BigLake tables.
    // Must be in the form
    // `projects/{project_id}/locations/{location_id}/connections/{connection_id}`
    string connection = 3 [
      (google.api.field_behavior) = OPTIONAL,
      (google.api.resource_reference) = {
        type: "bigqueryconnection.googleapis.com/Connection"
      }
    ];
  }

  // Configurations related to Cloud Storage as the data source.
  message StorageConfig {
    // Describes CSV and similar semi-structured data formats.
    message CsvOptions {
      // Optional. The number of rows to interpret as header rows that should be
      // skipped when reading data rows.
      int32 header_rows = 1 [(google.api.field_behavior) = OPTIONAL];

      // Optional. The delimiter that is used to separate values. The default is
      // `,` (comma).
      string delimiter = 2 [(google.api.field_behavior) = OPTIONAL];

      // Optional. The character encoding of the data. The default is UTF-8.
      string encoding = 3 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Whether to disable the inference of data types for CSV data.
      // If true, all columns are registered as strings.
      bool type_inference_disabled = 4 [(google.api.field_behavior) = OPTIONAL];

      // Optional. The character used to quote column values. Accepts `"`
      // (double quotation mark) or `'` (single quotation mark). If unspecified,
      // defaults to `"` (double quotation mark).
      string quote = 5 [(google.api.field_behavior) = OPTIONAL];
    }

    // Describes JSON data format.
    message JsonOptions {
      // Optional. The character encoding of the data. The default is UTF-8.
      string encoding = 1 [(google.api.field_behavior) = OPTIONAL];

      // Optional. Whether to disable the inference of data types for JSON data.
      // If true, all columns are registered as their primitive types
      // (strings, number, or boolean).
      bool type_inference_disabled = 2 [(google.api.field_behavior) = OPTIONAL];
    }

    // Optional. Defines the data to include during discovery when only a subset
    // of the data should be considered. Provide a list of patterns that
    // identify the data to include. For Cloud Storage bucket assets, these
    // patterns are interpreted as glob patterns used to match object names. For
    // BigQuery dataset assets, these patterns are interpreted as patterns to
    // match table names.
    repeated string include_patterns = 1
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Defines the data to exclude during discovery. Provide a list of
    // patterns that identify the data to exclude. For Cloud Storage bucket
    // assets, these patterns are interpreted as glob patterns used to match
    // object names. For BigQuery dataset assets, these patterns are interpreted
    // as patterns to match table names.
    repeated string exclude_patterns = 2
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Configuration for CSV data.
    CsvOptions csv_options = 3 [(google.api.field_behavior) = OPTIONAL];

    // Optional. Configuration for JSON data.
    JsonOptions json_options = 4 [(google.api.field_behavior) = OPTIONAL];
  }

  // Optional. Configuration for metadata publishing.
  BigQueryPublishingConfig bigquery_publishing_config = 1
      [(google.api.field_behavior) = OPTIONAL];

  // The configurations of the data discovery scan resource.
  oneof resource_config {
    // Cloud Storage related configurations.
    StorageConfig storage_config = 100;
  }
}

// The output of a data discovery scan.
message DataDiscoveryResult {
  // Describes BigQuery publishing configurations.
  message BigQueryPublishing {
    // Output only. The BigQuery dataset to publish to. It takes the form
    // `projects/{project_id}/datasets/{dataset_id}`.
    // If not set, the service creates a default publishing dataset.
    string dataset = 1 [
      (google.api.field_behavior) = OUTPUT_ONLY,
      (google.api.resource_reference) = {
        type: "bigquery.googleapis.com/Dataset"
      }
    ];
  }

  // Output only. Configuration for metadata publishing.
  BigQueryPublishing bigquery_publishing = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
