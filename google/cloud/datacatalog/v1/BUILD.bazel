# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "datacatalog_proto",
    srcs = [
        "bigquery.proto",
        "common.proto",
        "data_source.proto",
        "datacatalog.proto",
        "dataplex_spec.proto",
        "dump_content.proto",
        "gcs_fileset_spec.proto",
        "physical_schema.proto",
        "policytagmanager.proto",
        "policytagmanagerserialization.proto",
        "schema.proto",
        "search.proto",
        "table_spec.proto",
        "tags.proto",
        "timestamps.proto",
        "usage.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "//google/longrunning:operations_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "datacatalog_proto_with_info",
    deps = [
        ":datacatalog_proto",
        "//google/cloud:common_resources_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "datacatalog_java_proto",
    deps = [":datacatalog_proto"],
)

java_grpc_library(
    name = "datacatalog_java_grpc",
    srcs = [":datacatalog_proto"],
    deps = [":datacatalog_java_proto"],
)

java_gapic_library(
    name = "datacatalog_java_gapic",
    srcs = [":datacatalog_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "datacatalog_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "datacatalog_v1.yaml",
    test_deps = [
        "//google/iam/v1:iam_java_grpc",
        ":datacatalog_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":datacatalog_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "datacatalog_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.datacatalog.v1.DataCatalogClientHttpJsonTest",
        "com.google.cloud.datacatalog.v1.DataCatalogClientTest",
        "com.google.cloud.datacatalog.v1.PolicyTagManagerClientHttpJsonTest",
        "com.google.cloud.datacatalog.v1.PolicyTagManagerClientTest",
        "com.google.cloud.datacatalog.v1.PolicyTagManagerSerializationClientHttpJsonTest",
        "com.google.cloud.datacatalog.v1.PolicyTagManagerSerializationClientTest",
    ],
    runtime_deps = [":datacatalog_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-datacatalog-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":datacatalog_java_gapic",
        ":datacatalog_java_grpc",
        ":datacatalog_java_proto",
        ":datacatalog_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "datacatalog_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/datacatalog/apiv1/datacatalogpb",
    protos = [":datacatalog_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "datacatalog_go_gapic",
    srcs = [":datacatalog_proto_with_info"],
    grpc_service_config = "datacatalog_grpc_service_config.json",
    importpath = "cloud.google.com/go/datacatalog/apiv1;datacatalog",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = False,
    service_yaml = "datacatalog_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datacatalog_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-datacatalog-v1-go",
    deps = [
        ":datacatalog_go_gapic",
        ":datacatalog_go_gapic_srcjar-metadata.srcjar",
        ":datacatalog_go_gapic_srcjar-snippets.srcjar",
        ":datacatalog_go_gapic_srcjar-test.srcjar",
        ":datacatalog_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "datacatalog_py_gapic",
    srcs = [":datacatalog_proto"],
    grpc_service_config = "datacatalog_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "datacatalog_v1.yaml",
    transport = "grpc",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "datacatalog_py_gapic_test",
    srcs = [
        "datacatalog_py_gapic_pytest.py",
        "datacatalog_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":datacatalog_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "datacatalog-v1-py",
    deps = [
        ":datacatalog_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "datacatalog_php_proto",
    deps = [":datacatalog_proto"],
)

php_gapic_library(
    name = "datacatalog_php_gapic",
    srcs = [":datacatalog_proto_with_info"],
    grpc_service_config = "datacatalog_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = False,
    service_yaml = "datacatalog_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datacatalog_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-datacatalog-v1-php",
    deps = [
        ":datacatalog_php_gapic",
        ":datacatalog_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "datacatalog_nodejs_gapic",
    package_name = "@google-cloud/datacatalog",
    src = ":datacatalog_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "datacatalog_grpc_service_config.json",
    main_service = "DataCatalog",
    mixins = "none",
    package = "google.cloud.datacatalog.v1",
    rest_numeric_enums = False,
    service_yaml = "datacatalog_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "datacatalog-v1-nodejs",
    deps = [
        ":datacatalog_nodejs_gapic",
        ":datacatalog_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "datacatalog_ruby_proto",
    deps = [":datacatalog_proto"],
)

ruby_grpc_library(
    name = "datacatalog_ruby_grpc",
    srcs = [":datacatalog_proto"],
    deps = [":datacatalog_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "datacatalog_ruby_gapic",
    srcs = [":datacatalog_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=datacatalog.googleapis.com",
        "ruby-cloud-api-shortname=datacatalog",
        "ruby-cloud-env-prefix=DATA_CATALOG",
        "ruby-cloud-gem-name=google-cloud-data_catalog-v1",
        "ruby-cloud-product-url=https://cloud.google.com/data-catalog",
    ],
    grpc_service_config = "datacatalog_grpc_service_config.json",
    rest_numeric_enums = False,
    ruby_cloud_description = "DataCatalog is a centralized and unified data catalog service for all your Cloud resources, where users and systems can discover data, explore and curate its semantics, understand how to act on it, and help govern its usage.",
    ruby_cloud_title = "Data Catalog V1",
    service_yaml = "datacatalog_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datacatalog_ruby_grpc",
        ":datacatalog_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-datacatalog-v1-ruby",
    deps = [
        ":datacatalog_ruby_gapic",
        ":datacatalog_ruby_grpc",
        ":datacatalog_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "datacatalog_csharp_proto",
    deps = [":datacatalog_proto"],
)

csharp_grpc_library(
    name = "datacatalog_csharp_grpc",
    srcs = [":datacatalog_proto"],
    deps = [":datacatalog_csharp_proto"],
)

csharp_gapic_library(
    name = "datacatalog_csharp_gapic",
    srcs = [":datacatalog_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "datacatalog_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "datacatalog_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":datacatalog_csharp_grpc",
        ":datacatalog_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-datacatalog-v1-csharp",
    deps = [
        ":datacatalog_csharp_gapic",
        ":datacatalog_csharp_grpc",
        ":datacatalog_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "datacatalog_cc_proto",
    deps = [":datacatalog_proto"],
)

cc_grpc_library(
    name = "datacatalog_cc_grpc",
    srcs = [":datacatalog_proto"],
    grpc_only = True,
    deps = [":datacatalog_cc_proto"],
)
