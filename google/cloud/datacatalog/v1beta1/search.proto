// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.datacatalog.v1beta1;

import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.DataCatalog.V1Beta1";
option go_package = "cloud.google.com/go/datacatalog/apiv1beta1/datacatalogpb;datacatalogpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.datacatalog.v1beta1";
option php_namespace = "Google\\Cloud\\DataCatalog\\V1beta1";
option ruby_package = "Google::Cloud::DataCatalog::V1beta1";

// A result that appears in the response of a search request. Each result
// captures details of one entry that matches the search.
message SearchCatalogResult {
  // Type of the search result. This field can be used to determine which Get
  // method to call to fetch the full resource.
  SearchResultType search_result_type = 1;

  // Sub-type of the search result. This is a dot-delimited description of the
  // resource's full type, and is the same as the value callers would provide in
  // the "type" search facet.  Examples: `entry.table`, `entry.dataStream`,
  // `tagTemplate`.
  string search_result_subtype = 2;

  // The relative resource name of the resource in URL format.
  // Examples:
  //
  //  * `projects/{project_id}/locations/{location_id}/entryGroups/{entry_group_id}/entries/{entry_id}`
  //  * `projects/{project_id}/tagTemplates/{tag_template_id}`
  string relative_resource_name = 3;

  // The full name of the cloud resource the entry belongs to. See:
  // https://cloud.google.com/apis/design/resource_names#full_resource_name.
  // Example:
  //
  //  * `//bigquery.googleapis.com/projects/projectId/datasets/datasetId/tables/tableId`
  string linked_resource = 4;

  // Last-modified timestamp of the entry from the managing system.
  google.protobuf.Timestamp modify_time = 7;
}

// The different types of resources that can be returned in search.
enum SearchResultType {
  // Default unknown type.
  SEARCH_RESULT_TYPE_UNSPECIFIED = 0;

  // An [Entry][google.cloud.datacatalog.v1beta1.Entry].
  ENTRY = 1;

  // A [TagTemplate][google.cloud.datacatalog.v1beta1.TagTemplate].
  TAG_TEMPLATE = 2;

  // An [EntryGroup][google.cloud.datacatalog.v1beta1.EntryGroup].
  ENTRY_GROUP = 3;
}
