# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "analyticshub_proto",
    srcs = [
        "analyticshub.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "analyticshub_proto_with_info",
    deps = [
        ":analyticshub_proto",
        "//google/cloud:common_resources_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "analyticshub_java_proto",
    deps = [":analyticshub_proto"],
)

java_grpc_library(
    name = "analyticshub_java_grpc",
    srcs = [":analyticshub_proto"],
    deps = [":analyticshub_java_proto"],
)

java_gapic_library(
    name = "analyticshub_java_gapic",
    srcs = [":analyticshub_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "analyticshub_v1_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "analyticshub_v1.yaml",
    test_deps = [
        ":analyticshub_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":analyticshub_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "analyticshub_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.bigquery.analyticshub.v1.AnalyticsHubServiceClientHttpJsonTest",
        "com.google.cloud.bigquery.analyticshub.v1.AnalyticsHubServiceClientTest",
    ],
    runtime_deps = [":analyticshub_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-bigquery-analyticshub-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":analyticshub_java_gapic",
        ":analyticshub_java_grpc",
        ":analyticshub_java_proto",
        ":analyticshub_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "analyticshub_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/bigquery/analyticshub/apiv1/analyticshubpb",
    protos = [":analyticshub_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "analyticshub_go_gapic",
    srcs = [":analyticshub_proto_with_info"],
    grpc_service_config = "analyticshub_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/bigquery/analyticshub/apiv1;analyticshub",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = False,
    service_yaml = "analyticshub_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":analyticshub_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-bigquery-analyticshub-v1-go",
    deps = [
        ":analyticshub_go_gapic",
        ":analyticshub_go_gapic_srcjar-metadata.srcjar",
        ":analyticshub_go_gapic_srcjar-snippets.srcjar",
        ":analyticshub_go_gapic_srcjar-test.srcjar",
        ":analyticshub_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "analyticshub_py_gapic",
    srcs = [":analyticshub_proto"],
    grpc_service_config = "analyticshub_v1_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=bigquery_analyticshub",
        "python-gapic-namespace=google.cloud",
    ],
    rest_numeric_enums = False,
    service_yaml = "analyticshub_v1.yaml",
    transport = "grpc",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "analyticshub_py_gapic_test",
    srcs = [
        "analyticshub_py_gapic_pytest.py",
        "analyticshub_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":analyticshub_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "bigquery-analyticshub-v1-py",
    deps = [
        ":analyticshub_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "analyticshub_php_proto",
    deps = [":analyticshub_proto"],
)

php_gapic_library(
    name = "analyticshub_php_gapic",
    srcs = [":analyticshub_proto_with_info"],
    grpc_service_config = "analyticshub_v1_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = False,
    service_yaml = "analyticshub_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":analyticshub_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-bigquery-analyticshub-v1-php",
    deps = [
        ":analyticshub_php_gapic",
        ":analyticshub_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "analyticshub_nodejs_gapic",
    package_name = "@google-cloud/bigquery-analyticshub",
    src = ":analyticshub_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "analyticshub_v1_grpc_service_config.json",
    package = "google.cloud.bigquery.analyticshub.v1",
    rest_numeric_enums = False,
    service_yaml = "analyticshub_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "bigquery-analyticshub-v1-nodejs",
    deps = [
        ":analyticshub_nodejs_gapic",
        ":analyticshub_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "analyticshub_ruby_proto",
    deps = [":analyticshub_proto"],
)

ruby_grpc_library(
    name = "analyticshub_ruby_grpc",
    srcs = [":analyticshub_proto"],
    deps = [":analyticshub_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "analyticshub_ruby_gapic",
    srcs = [":analyticshub_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=analyticshub.googleapis.com",
        "ruby-cloud-api-shortname=analyticshub",
        "ruby-cloud-gem-name=google-cloud-bigquery-analytics_hub-v1",
        "ruby-cloud-product-url=https://cloud.google.com/bigquery/docs",
    ],
    grpc_service_config = "analyticshub_v1_grpc_service_config.json",
    rest_numeric_enums = False,
    ruby_cloud_description = "Analytics Hub is a data exchange platform that enables you to share data and insights at scale across organizational boundaries with a robust security and privacy framework. With Analytics Hub, you can discover and access a data library curated by various data providers.",
    ruby_cloud_title = "Analytics Hub V1",
    service_yaml = "analyticshub_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":analyticshub_ruby_grpc",
        ":analyticshub_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigquery-analyticshub-v1-ruby",
    deps = [
        ":analyticshub_ruby_gapic",
        ":analyticshub_ruby_grpc",
        ":analyticshub_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "analyticshub_csharp_proto",
    deps = [":analyticshub_proto"],
)

csharp_grpc_library(
    name = "analyticshub_csharp_grpc",
    srcs = [":analyticshub_proto"],
    deps = [":analyticshub_csharp_proto"],
)

csharp_gapic_library(
    name = "analyticshub_csharp_gapic",
    srcs = [":analyticshub_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "analyticshub_v1_grpc_service_config.json",
    rest_numeric_enums = False,
    service_yaml = "analyticshub_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":analyticshub_csharp_grpc",
        ":analyticshub_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-bigquery-analyticshub-v1-csharp",
    deps = [
        ":analyticshub_csharp_gapic",
        ":analyticshub_csharp_grpc",
        ":analyticshub_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "analyticshub_cc_proto",
    deps = [":analyticshub_proto"],
)

cc_grpc_library(
    name = "analyticshub_cc_grpc",
    srcs = [":analyticshub_proto"],
    grpc_only = True,
    deps = [":analyticshub_cc_proto"],
)
