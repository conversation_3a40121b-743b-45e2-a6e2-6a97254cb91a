// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.v2;

import "google/protobuf/wrappers.proto";

option go_package = "cloud.google.com/go/bigquery/apiv2/bigquerypb;bigquerypb";
option java_outer_classname = "UdfProto";
option java_package = "com.google.cloud.bigquery.v2";

//
// This is used for defining User Defined Function (UDF) resources only when
// using legacy SQL.  Users of GoogleSQL should leverage either DDL (e.g.
// CREATE [TEMPORARY] FUNCTION ... ) or the Routines API to define UDF
// resources.
//
// For additional information on migrating, see:
// https://cloud.google.com/bigquery/docs/reference/standard-sql/migrating-from-legacy-sql#differences_in_user-defined_javascript_functions
message UserDefinedFunctionResource {
  // [Pick one] A code resource to load from a Google Cloud Storage URI
  // (gs://bucket/path).
  google.protobuf.StringValue resource_uri = 1;

  // [Pick one] An inline resource that contains code for a user-defined
  // function (UDF). Providing a inline code resource is equivalent to providing
  // a URI for a file containing the same code.
  google.protobuf.StringValue inline_code = 2;
}
