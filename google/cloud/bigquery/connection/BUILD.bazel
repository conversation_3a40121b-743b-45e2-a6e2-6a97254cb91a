# This build file includes a target for the Ruby wrapper library for
# google-cloud-bigquery-connection.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for bigqueryconnection.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "bigqueryconnection_ruby_wrapper",
    srcs = ["//google/cloud/bigquery/connection/v1:connection_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-bigquery-connection",
        "ruby-cloud-env-prefix=BIGQUERY_CONNECTION",
        "ruby-cloud-wrapper-of=v1:0.17",
        "ruby-cloud-product-url=https://cloud.google.com/bigquery/docs/reference/bigqueryconnection",
        "ruby-cloud-api-id=bigqueryconnection.googleapis.com",
        "ruby-cloud-api-shortname=bigqueryconnection",
    ],
    ruby_cloud_description = "The BigQuery Connection API allows users to manage BigQuery connections to external data sources.",
    ruby_cloud_title = "BigQuery Connection",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-bigquery-connection-ruby",
    deps = [
        ":bigqueryconnection_ruby_wrapper",
    ],
)
