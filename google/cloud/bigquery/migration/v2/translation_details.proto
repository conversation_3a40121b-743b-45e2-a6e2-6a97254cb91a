// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.bigquery.migration.v2;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.BigQuery.Migration.V2";
option go_package = "cloud.google.com/go/bigquery/migration/apiv2/migrationpb;migrationpb";
option java_multiple_files = true;
option java_outer_classname = "TranslationDetailsProto";
option java_package = "com.google.cloud.bigquery.migration.v2";
option php_namespace = "Google\\Cloud\\BigQuery\\Migration\\V2";

// The translation details to capture the necessary settings for a translation
// job.
message TranslationDetails {
  // The mapping from source to target SQL.
  repeated SourceTargetMapping source_target_mapping = 1;

  // The base URI for all writes to persistent storage.
  string target_base_uri = 2;

  // The default source environment values for the translation.
  SourceEnvironment source_environment = 3;

  // The list of literal targets that will be directly returned to the response.
  // Each entry consists of the constructed path, EXCLUDING the base path. Not
  // providing a target_base_uri will prevent writing to persistent storage.
  repeated string target_return_literals = 4;

  // The types of output to generate, e.g. sql, metadata,
  // lineage_from_sql_scripts, etc. If not specified, a default set of
  // targets will be generated. Some additional target types may be slower to
  // generate. See the documentation for the set of available target types.
  repeated string target_types = 5;
}

// Represents one mapping from a source SQL to a target SQL.
message SourceTargetMapping {
  // The source SQL or the path to it.
  SourceSpec source_spec = 1;

  // The target SQL or the path for it.
  TargetSpec target_spec = 2;
}

// Represents one path to the location that holds source data.
message SourceSpec {
  // The specific source SQL.
  oneof source {
    // The base URI for all files to be read in as sources for translation.
    string base_uri = 1;

    // Source literal.
    Literal literal = 2;
  }

  // Optional. The optional field to specify the encoding of the sql bytes.
  string encoding = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Represents one path to the location that holds target data.
message TargetSpec {
  // The relative path for the target data. Given source file
  // `base_uri/input/sql`, the output would be
  // `target_base_uri/sql/relative_path/input.sql`.
  string relative_path = 1;
}

// Literal data.
message Literal {
  // The literal SQL contents.
  oneof literal_data {
    // Literal string data.
    string literal_string = 2;

    // Literal byte data.
    bytes literal_bytes = 3;
  }

  // Required. The identifier of the literal entry.
  string relative_path = 1 [(google.api.field_behavior) = REQUIRED];
}

// Represents the default source environment values for the translation.
message SourceEnvironment {
  // The default database name to fully qualify SQL objects when their database
  // name is missing.
  string default_database = 1;

  // The schema search path. When SQL objects are missing schema name,
  // translation engine will search through this list to find the value.
  repeated string schema_search_path = 2;

  // Optional. Expects a validQ BigQuery dataset ID that exists, e.g.,
  // project-123.metadata_store_123.  If specified, translation will search and
  // read the required schema information from a metadata store in this dataset.
  // If metadata store doesn't exist, translation will parse the metadata file
  // and upload the schema info to a temp table in the dataset to speed up
  // future translation jobs.
  string metadata_store_dataset = 3 [(google.api.field_behavior) = OPTIONAL];
}
