{"methodConfig": [{"name": [{"service": "google.cloud.webrisk.v1.WebRiskService", "method": "ComputeThreatListDiff"}, {"service": "google.cloud.webrisk.v1.WebRiskService", "method": "SearchUris"}, {"service": "google.cloud.webrisk.v1.WebRiskService", "method": "SearchHashes"}], "timeout": "600s", "retryPolicy": {"initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.webrisk.v1.WebRiskService", "method": "CreateSubmission"}], "timeout": "600s"}]}