// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.clouddms.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/clouddms/v1/clouddms_resources.proto";
import "google/cloud/clouddms/v1/conversionworkspace_resources.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.CloudDms.V1";
option go_package = "cloud.google.com/go/clouddms/apiv1/clouddmspb;clouddmspb";
option java_multiple_files = true;
option java_outer_classname = "ClouddmsProto";
option java_package = "com.google.cloud.clouddms.v1";
option php_namespace = "Google\\Cloud\\CloudDms\\V1";
option ruby_package = "Google::Cloud::CloudDMS::V1";

// Database Migration service
service DataMigrationService {
  option (google.api.default_host) = "datamigration.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists migration jobs in a given project and location.
  rpc ListMigrationJobs(ListMigrationJobsRequest)
      returns (ListMigrationJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/migrationJobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single migration job.
  rpc GetMigrationJob(GetMigrationJobRequest) returns (MigrationJob) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/migrationJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new migration job in a given project and location.
  rpc CreateMigrationJob(CreateMigrationJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/migrationJobs"
      body: "migration_job"
    };
    option (google.api.method_signature) =
        "parent,migration_job,migration_job_id";
    option (google.longrunning.operation_info) = {
      response_type: "MigrationJob"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single migration job.
  rpc UpdateMigrationJob(UpdateMigrationJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{migration_job.name=projects/*/locations/*/migrationJobs/*}"
      body: "migration_job"
    };
    option (google.api.method_signature) = "migration_job,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "MigrationJob"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single migration job.
  rpc DeleteMigrationJob(DeleteMigrationJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/migrationJobs/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Start an already created migration job.
  rpc StartMigrationJob(StartMigrationJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/migrationJobs/*}:start"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "MigrationJob"
      metadata_type: "OperationMetadata"
    };
  }

  // Stops a running migration job.
  rpc StopMigrationJob(StopMigrationJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/migrationJobs/*}:stop"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "MigrationJob"
      metadata_type: "OperationMetadata"
    };
  }

  // Resume a migration job that is currently stopped and is resumable (was
  // stopped during CDC phase).
  rpc ResumeMigrationJob(ResumeMigrationJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/migrationJobs/*}:resume"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "MigrationJob"
      metadata_type: "OperationMetadata"
    };
  }

  // Promote a migration job, stopping replication to the destination and
  // promoting the destination to be a standalone database.
  rpc PromoteMigrationJob(PromoteMigrationJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/migrationJobs/*}:promote"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "MigrationJob"
      metadata_type: "OperationMetadata"
    };
  }

  // Verify a migration job, making sure the destination can reach the source
  // and that all configuration and prerequisites are met.
  rpc VerifyMigrationJob(VerifyMigrationJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/migrationJobs/*}:verify"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "MigrationJob"
      metadata_type: "OperationMetadata"
    };
  }

  // Restart a stopped or failed migration job, resetting the destination
  // instance to its original state and starting the migration process from
  // scratch.
  rpc RestartMigrationJob(RestartMigrationJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/migrationJobs/*}:restart"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "MigrationJob"
      metadata_type: "OperationMetadata"
    };
  }

  // Generate a SSH configuration script to configure the reverse SSH
  // connectivity.
  rpc GenerateSshScript(GenerateSshScriptRequest) returns (SshScript) {
    option (google.api.http) = {
      post: "/v1/{migration_job=projects/*/locations/*/migrationJobs/*}:generateSshScript"
      body: "*"
    };
  }

  // Generate a TCP Proxy configuration script to configure a cloud-hosted VM
  // running a TCP Proxy.
  rpc GenerateTcpProxyScript(GenerateTcpProxyScriptRequest)
      returns (TcpProxyScript) {
    option (google.api.http) = {
      post: "/v1/{migration_job=projects/*/locations/*/migrationJobs/*}:generateTcpProxyScript"
      body: "*"
    };
  }

  // Retrieves a list of all connection profiles in a given project and
  // location.
  rpc ListConnectionProfiles(ListConnectionProfilesRequest)
      returns (ListConnectionProfilesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/connectionProfiles"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single connection profile.
  rpc GetConnectionProfile(GetConnectionProfileRequest)
      returns (ConnectionProfile) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/connectionProfiles/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new connection profile in a given project and location.
  rpc CreateConnectionProfile(CreateConnectionProfileRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/connectionProfiles"
      body: "connection_profile"
    };
    option (google.api.method_signature) =
        "parent,connection_profile,connection_profile_id";
    option (google.longrunning.operation_info) = {
      response_type: "ConnectionProfile"
      metadata_type: "OperationMetadata"
    };
  }

  // Update the configuration of a single connection profile.
  rpc UpdateConnectionProfile(UpdateConnectionProfileRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{connection_profile.name=projects/*/locations/*/connectionProfiles/*}"
      body: "connection_profile"
    };
    option (google.api.method_signature) = "connection_profile,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "ConnectionProfile"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Database Migration Service connection profile.
  // A connection profile can only be deleted if it is not in use by any
  // active migration jobs.
  rpc DeleteConnectionProfile(DeleteConnectionProfileRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/connectionProfiles/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a new private connection in a given project and location.
  rpc CreatePrivateConnection(CreatePrivateConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/privateConnections"
      body: "private_connection"
    };
    option (google.api.method_signature) =
        "parent,private_connection,private_connection_id";
    option (google.longrunning.operation_info) = {
      response_type: "PrivateConnection"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets details of a single private connection.
  rpc GetPrivateConnection(GetPrivateConnectionRequest)
      returns (PrivateConnection) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/privateConnections/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Retrieves a list of private connections in a given project and location.
  rpc ListPrivateConnections(ListPrivateConnectionsRequest)
      returns (ListPrivateConnectionsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/privateConnections"
    };
    option (google.api.method_signature) = "parent";
  }

  // Deletes a single Database Migration Service private connection.
  rpc DeletePrivateConnection(DeletePrivateConnectionRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/privateConnections/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Gets details of a single conversion workspace.
  rpc GetConversionWorkspace(GetConversionWorkspaceRequest)
      returns (ConversionWorkspace) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/conversionWorkspaces/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists conversion workspaces in a given project and location.
  rpc ListConversionWorkspaces(ListConversionWorkspacesRequest)
      returns (ListConversionWorkspacesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/conversionWorkspaces"
    };
    option (google.api.method_signature) = "parent";
  }

  // Creates a new conversion workspace in a given project and location.
  rpc CreateConversionWorkspace(CreateConversionWorkspaceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/conversionWorkspaces"
      body: "conversion_workspace"
    };
    option (google.api.method_signature) =
        "parent,conversion_workspace,conversion_workspace_id";
    option (google.longrunning.operation_info) = {
      response_type: "ConversionWorkspace"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single conversion workspace.
  rpc UpdateConversionWorkspace(UpdateConversionWorkspaceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{conversion_workspace.name=projects/*/locations/*/conversionWorkspaces/*}"
      body: "conversion_workspace"
    };
    option (google.api.method_signature) = "conversion_workspace,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "ConversionWorkspace"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single conversion workspace.
  rpc DeleteConversionWorkspace(DeleteConversionWorkspaceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/conversionWorkspaces/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a new mapping rule for a given conversion workspace.
  rpc CreateMappingRule(CreateMappingRuleRequest) returns (MappingRule) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/conversionWorkspaces/*}/mappingRules"
      body: "mapping_rule"
    };
    option (google.api.method_signature) =
        "parent,mapping_rule,mapping_rule_id";
  }

  // Deletes a single mapping rule.
  rpc DeleteMappingRule(DeleteMappingRuleRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/conversionWorkspaces/*/mappingRules/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the mapping rules for a specific conversion workspace.
  rpc ListMappingRules(ListMappingRulesRequest)
      returns (ListMappingRulesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/conversionWorkspaces/*}/mappingRules"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets the details of a mapping rule.
  rpc GetMappingRule(GetMappingRuleRequest) returns (MappingRule) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/conversionWorkspaces/*/mappingRules/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Imports a snapshot of the source database into the
  // conversion workspace.
  rpc SeedConversionWorkspace(SeedConversionWorkspaceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/conversionWorkspaces/*}:seed"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ConversionWorkspace"
      metadata_type: "OperationMetadata"
    };
  }

  // Imports the mapping rules for a given conversion workspace.
  // Supports various formats of external rules files.
  rpc ImportMappingRules(ImportMappingRulesRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/conversionWorkspaces/*}/mappingRules:import"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ConversionWorkspace"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a draft tree schema for the destination database.
  rpc ConvertConversionWorkspace(ConvertConversionWorkspaceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/conversionWorkspaces/*}:convert"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ConversionWorkspace"
      metadata_type: "OperationMetadata"
    };
  }

  // Marks all the data in the conversion workspace as committed.
  rpc CommitConversionWorkspace(CommitConversionWorkspaceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/conversionWorkspaces/*}:commit"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ConversionWorkspace"
      metadata_type: "OperationMetadata"
    };
  }

  // Rolls back a conversion workspace to the last committed snapshot.
  rpc RollbackConversionWorkspace(RollbackConversionWorkspaceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/conversionWorkspaces/*}:rollback"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ConversionWorkspace"
      metadata_type: "OperationMetadata"
    };
  }

  // Applies draft tree onto a specific destination database.
  rpc ApplyConversionWorkspace(ApplyConversionWorkspaceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/conversionWorkspaces/*}:apply"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ConversionWorkspace"
      metadata_type: "OperationMetadata"
    };
  }

  // Describes the database entities tree for a specific conversion workspace
  // and a specific tree type.
  //
  // Database entities are not resources like conversion workspaces or mapping
  // rules, and they can't be created, updated or deleted. Instead, they are
  // simple data objects describing the structure of the client database.
  rpc DescribeDatabaseEntities(DescribeDatabaseEntitiesRequest)
      returns (DescribeDatabaseEntitiesResponse) {
    option (google.api.http) = {
      get: "/v1/{conversion_workspace=projects/*/locations/*/conversionWorkspaces/*}:describeDatabaseEntities"
    };
  }

  // Searches/lists the background jobs for a specific
  // conversion workspace.
  //
  // The background jobs are not resources like conversion workspaces or
  // mapping rules, and they can't be created, updated or deleted.
  // Instead, they are a way to expose the data plane jobs log.
  rpc SearchBackgroundJobs(SearchBackgroundJobsRequest)
      returns (SearchBackgroundJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{conversion_workspace=projects/*/locations/*/conversionWorkspaces/*}:searchBackgroundJobs"
    };
  }

  // Retrieves a list of committed revisions of a specific conversion
  // workspace.
  rpc DescribeConversionWorkspaceRevisions(
      DescribeConversionWorkspaceRevisionsRequest)
      returns (DescribeConversionWorkspaceRevisionsResponse) {
    option (google.api.http) = {
      get: "/v1/{conversion_workspace=projects/*/locations/*/conversionWorkspaces/*}:describeConversionWorkspaceRevisions"
    };
  }

  // Fetches a set of static IP addresses that need to be allowlisted by the
  // customer when using the static-IP connectivity method.
  rpc FetchStaticIps(FetchStaticIpsRequest) returns (FetchStaticIpsResponse) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*}:fetchStaticIps"
    };
    option (google.api.method_signature) = "name";
  }
}

// Retrieves a list of all migration jobs in a given project and location.
message ListMigrationJobsRequest {
  // Required. The parent which owns this collection of migrationJobs.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datamigration.googleapis.com/MigrationJob"
    }
  ];

  // The maximum number of migration jobs to return. The service may return
  // fewer than this value. If unspecified, at most 50 migration jobs will be
  // returned. The maximum value is 1000; values above 1000 are coerced to
  // 1000.
  int32 page_size = 2;

  // The nextPageToken value received in the previous call to
  // migrationJobs.list, used in the subsequent request to retrieve the next
  // page of results. On first call this should be left blank. When paginating,
  // all other parameters provided to migrationJobs.list must match the call
  // that provided the page token.
  string page_token = 3;

  // A filter expression that filters migration jobs listed in the response.
  // The expression must specify the field name, a comparison operator, and the
  // value that you want to use for filtering. The value must be a string,
  // a number, or a boolean. The comparison operator must be
  // either =, !=, >, or <. For example, list migration jobs created this year
  // by specifying **createTime %gt; 2020-01-01T00:00:00.000000000Z.**
  // You can also filter nested fields. For example, you could specify
  // **reverseSshConnectivity.vmIp = "*******"** to select all migration
  // jobs connecting through the specific SSH tunnel bastion.
  string filter = 4;

  // Sort the results based on the migration job name.
  // Valid values are: "name", "name asc", and "name desc".
  string order_by = 5;
}

// Response message for 'ListMigrationJobs' request.
message ListMigrationJobsResponse {
  // The list of migration jobs objects.
  repeated MigrationJob migration_jobs = 1;

  // A token which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for 'GetMigrationJob' request.
message GetMigrationJobRequest {
  // Required. Name of the migration job resource to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datamigration.googleapis.com/MigrationJob"
    }
  ];
}

// Request message to create a new Database Migration Service migration job
// in the specified project and region.
message CreateMigrationJobRequest {
  // Required. The parent which owns this collection of migration jobs.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datamigration.googleapis.com/MigrationJob"
    }
  ];

  // Required. The ID of the instance to create.
  string migration_job_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Represents a [migration
  // job](https://cloud.google.com/database-migration/docs/reference/rest/v1/projects.locations.migrationJobs)
  // object.
  MigrationJob migration_job = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. A unique ID used to identify the request. If the server receives
  // two requests with the same ID, then the second request is ignored.
  //
  // It is recommended to always set this value to a UUID.
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores
  // (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for 'UpdateMigrationJob' request.
message UpdateMigrationJobRequest {
  // Required. Field mask is used to specify the fields to be overwritten by the
  // update in the conversion workspace resource.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The migration job parameters to update.
  MigrationJob migration_job = 2 [(google.api.field_behavior) = REQUIRED];

  // A unique ID used to identify the request. If the server receives two
  // requests with the same ID, then the second request is ignored.
  //
  // It is recommended to always set this value to a UUID.
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores
  // (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 3;
}

// Request message for 'DeleteMigrationJob' request.
message DeleteMigrationJobRequest {
  // Required. Name of the migration job resource to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datamigration.googleapis.com/MigrationJob"
    }
  ];

  // A unique ID used to identify the request. If the server receives two
  // requests with the same ID, then the second request is ignored.
  //
  // It is recommended to always set this value to a UUID.
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores
  // (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 2;

  // The destination CloudSQL connection profile is always deleted with the
  // migration job. In case of force delete, the destination CloudSQL replica
  // database is also deleted.
  bool force = 3;
}

// Request message for 'StartMigrationJob' request.
message StartMigrationJobRequest {
  // Name of the migration job resource to start.
  string name = 1 [(google.api.resource_reference) = {
    type: "datamigration.googleapis.com/MigrationJob"
  }];

  // Optional. Start the migration job without running prior configuration
  // verification. Defaults to `false`.
  bool skip_validation = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for 'StopMigrationJob' request.
message StopMigrationJobRequest {
  // Name of the migration job resource to stop.
  string name = 1 [(google.api.resource_reference) = {
    type: "datamigration.googleapis.com/MigrationJob"
  }];
}

// Request message for 'ResumeMigrationJob' request.
message ResumeMigrationJobRequest {
  // Name of the migration job resource to resume.
  string name = 1 [(google.api.resource_reference) = {
    type: "datamigration.googleapis.com/MigrationJob"
  }];
}

// Request message for 'PromoteMigrationJob' request.
message PromoteMigrationJobRequest {
  // Name of the migration job resource to promote.
  string name = 1 [(google.api.resource_reference) = {
    type: "datamigration.googleapis.com/MigrationJob"
  }];
}

// Request message for 'VerifyMigrationJob' request.
message VerifyMigrationJobRequest {
  // Name of the migration job resource to verify.
  string name = 1 [(google.api.resource_reference) = {
    type: "datamigration.googleapis.com/MigrationJob"
  }];

  // Optional. Field mask is used to specify the changed fields to be verified.
  // It will not update the migration job.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The changed migration job parameters to verify.
  // It will not update the migration job.
  MigrationJob migration_job = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for 'RestartMigrationJob' request.
message RestartMigrationJobRequest {
  // Name of the migration job resource to restart.
  string name = 1 [(google.api.resource_reference) = {
    type: "datamigration.googleapis.com/MigrationJob"
  }];

  // Optional. Restart the migration job without running prior configuration
  // verification. Defaults to `false`.
  bool skip_validation = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for 'GenerateSshScript' request.
message GenerateSshScriptRequest {
  // Name of the migration job resource to generate the SSH script.
  string migration_job = 1 [(google.api.resource_reference) = {
    type: "datamigration.googleapis.com/MigrationJob"
  }];

  // Required. Bastion VM Instance name to use or to create.
  string vm = 2 [(google.api.field_behavior) = REQUIRED];

  // The VM configuration
  oneof vm_config {
    // The VM creation configuration
    VmCreationConfig vm_creation_config = 100;

    // The VM selection configuration
    VmSelectionConfig vm_selection_config = 101;
  }

  // The port that will be open on the bastion host.
  int32 vm_port = 3;
}

// VM creation configuration message
message VmCreationConfig {
  // Required. VM instance machine type to create.
  string vm_machine_type = 1 [(google.api.field_behavior) = REQUIRED];

  // The Google Cloud Platform zone to create the VM in.
  string vm_zone = 2;

  // The subnet name the vm needs to be created in.
  string subnet = 3;
}

// VM selection configuration message
message VmSelectionConfig {
  // Required. The Google Cloud Platform zone the VM is located.
  string vm_zone = 1 [(google.api.field_behavior) = REQUIRED];
}

// Response message for 'GenerateSshScript' request.
message SshScript {
  // The ssh configuration script.
  string script = 1;
}

// Request message for 'GenerateTcpProxyScript' request.
message GenerateTcpProxyScriptRequest {
  // Name of the migration job resource to generate the TCP Proxy script.
  string migration_job = 1 [(google.api.resource_reference) = {
    type: "datamigration.googleapis.com/MigrationJob"
  }];

  // Required. The name of the Compute instance that will host the proxy.
  string vm_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The type of the Compute instance that will host the proxy.
  string vm_machine_type = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The Google Cloud Platform zone to create the VM in. The fully
  // qualified name of the zone must be specified, including the region name,
  // for example "us-central1-b". If not specified, uses the "-b" zone of the
  // destination Connection Profile's region.
  string vm_zone = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. The name of the subnet the Compute instance will use for private
  // connectivity. Must be supplied in the form of
  // projects/{project}/regions/{region}/subnetworks/{subnetwork}.
  // Note: the region for the subnet must match the Compute instance region.
  string vm_subnet = 5 [(google.api.field_behavior) = REQUIRED];
}

// Response message for 'GenerateTcpProxyScript' request.
message TcpProxyScript {
  // The TCP Proxy configuration script.
  string script = 1;
}

// Request message for 'ListConnectionProfiles' request.
message ListConnectionProfilesRequest {
  // Required. The parent which owns this collection of connection profiles.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datamigration.googleapis.com/ConnectionProfile"
    }
  ];

  // The maximum number of connection profiles to return. The service may return
  // fewer than this value. If unspecified, at most 50 connection profiles will
  // be returned. The maximum value is 1000; values above 1000 are coerced
  // to 1000.
  int32 page_size = 2;

  // A page token, received from a previous `ListConnectionProfiles` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListConnectionProfiles`
  // must match the call that provided the page token.
  string page_token = 3;

  // A filter expression that filters connection profiles listed in the
  // response. The expression must specify the field name, a comparison
  // operator, and the value that you want to use for filtering. The value must
  // be a string, a number, or a boolean. The comparison operator must be either
  // =, !=, >, or <. For example, list connection profiles created this year by
  // specifying **createTime %gt; 2020-01-01T00:00:00.000000000Z**. You can
  // also filter nested fields. For example, you could specify **mySql.username
  // = %lt;my_username%gt;** to list all connection profiles configured to
  // connect with a specific username.
  string filter = 4;

  // A comma-separated list of fields to order results according to.
  string order_by = 5;
}

// Response message for 'ListConnectionProfiles' request.
message ListConnectionProfilesResponse {
  // The response list of connection profiles.
  repeated ConnectionProfile connection_profiles = 1;

  // A token which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for 'GetConnectionProfile' request.
message GetConnectionProfileRequest {
  // Required. Name of the connection profile resource to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datamigration.googleapis.com/ConnectionProfile"
    }
  ];
}

// Request message for 'CreateConnectionProfile' request.
message CreateConnectionProfileRequest {
  // Required. The parent which owns this collection of connection profiles.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datamigration.googleapis.com/ConnectionProfile"
    }
  ];

  // Required. The connection profile identifier.
  string connection_profile_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The create request body including the connection profile data
  ConnectionProfile connection_profile = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A unique ID used to identify the request. If the server receives
  // two requests with the same ID, then the second request is ignored.
  //
  // It is recommended to always set this value to a UUID.
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores
  // (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Only validate the connection profile, but don't create any
  // resources. The default is false. Only supported for Oracle connection
  // profiles.
  bool validate_only = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Create the connection profile without validating it.
  // The default is false.
  // Only supported for Oracle connection profiles.
  bool skip_validation = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for 'UpdateConnectionProfile' request.
message UpdateConnectionProfileRequest {
  // Required. Field mask is used to specify the fields to be overwritten by the
  // update in the conversion workspace resource.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The connection profile parameters to update.
  ConnectionProfile connection_profile = 2
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A unique ID used to identify the request. If the server receives
  // two requests with the same ID, then the second request is ignored.
  //
  // It is recommended to always set this value to a UUID.
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores
  // (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Only validate the connection profile, but don't update any
  // resources. The default is false. Only supported for Oracle connection
  // profiles.
  bool validate_only = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Update the connection profile without validating it.
  // The default is false.
  // Only supported for Oracle connection profiles.
  bool skip_validation = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for 'DeleteConnectionProfile' request.
message DeleteConnectionProfileRequest {
  // Required. Name of the connection profile resource to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datamigration.googleapis.com/ConnectionProfile"
    }
  ];

  // A unique ID used to identify the request. If the server receives two
  // requests with the same ID, then the second request is ignored.
  //
  // It is recommended to always set this value to a UUID.
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores
  // (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 2;

  // In case of force delete, the CloudSQL replica database is also deleted
  // (only for CloudSQL connection profile).
  bool force = 3;
}

// Request message to create a new private connection in the specified project
// and region.
message CreatePrivateConnectionRequest {
  // Required. The parent that owns the collection of PrivateConnections.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datamigration.googleapis.com/PrivateConnection"
    }
  ];

  // Required. The private connection identifier.
  string private_connection_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The private connection resource to create.
  PrivateConnection private_connection = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. A unique ID used to identify the request. If the server receives
  // two requests with the same ID, then the second request is ignored.
  //
  // It is recommended to always set this value to a UUID.
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores
  // (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, will skip validations.
  bool skip_validation = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Request message to retrieve a list of private connections in a given project
// and location.
message ListPrivateConnectionsRequest {
  // Required. The parent that owns the collection of private connections.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datamigration.googleapis.com/PrivateConnection"
    }
  ];

  // Maximum number of private connections to return.
  // If unspecified, at most 50 private connections that are returned.
  // The maximum value is 1000; values above 1000 are coerced to 1000.
  int32 page_size = 2;

  // Page token received from a previous `ListPrivateConnections` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListPrivateConnections` must match the call that provided the page
  // token.
  string page_token = 3;

  // A filter expression that filters private connections listed in the
  // response. The expression must specify the field name, a comparison
  // operator, and the value that you want to use for filtering. The value must
  // be a string, a number, or a boolean. The comparison operator must be either
  // =, !=, >, or <. For example, list private connections created this year by
  // specifying **createTime %gt; 2021-01-01T00:00:00.000000000Z**.
  string filter = 4;

  // Order by fields for the result.
  string order_by = 5;
}

// Response message for 'ListPrivateConnections' request.
message ListPrivateConnectionsResponse {
  // List of private connections.
  repeated PrivateConnection private_connections = 1;

  // A token which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message to delete a private connection.
message DeletePrivateConnectionRequest {
  // Required. The name of the private connection to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datamigration.googleapis.com/PrivateConnection"
    }
  ];

  // Optional. A unique ID used to identify the request. If the server receives
  // two requests with the same ID, then the second request is ignored.
  //
  // It is recommended to always set this value to a UUID.
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores
  // (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message to get a private connection resource.
message GetPrivateConnectionRequest {
  // Required. The name of the private connection to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datamigration.googleapis.com/PrivateConnection"
    }
  ];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Retrieve a list of all conversion workspaces in a given project and location.
message ListConversionWorkspacesRequest {
  // Required. The parent which owns this collection of conversion workspaces.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datamigration.googleapis.com/ConversionWorkspace"
    }
  ];

  // The maximum number of conversion workspaces to return. The service may
  // return fewer than this value. If unspecified, at most 50 sets are returned.
  int32 page_size = 2;

  // The nextPageToken value received in the previous call to
  // conversionWorkspaces.list, used in the subsequent request to retrieve the
  // next page of results. On first call this should be left blank. When
  // paginating, all other parameters provided to conversionWorkspaces.list must
  // match the call that provided the page token.
  string page_token = 3;

  // A filter expression that filters conversion workspaces listed in the
  // response. The expression must specify the field name, a comparison
  // operator, and the value that you want to use for filtering. The value must
  // be a string, a number, or a boolean. The comparison operator must be either
  // =, !=, >, or <. For example, list conversion workspaces created this year
  // by specifying **createTime %gt; 2020-01-01T00:00:00.000000000Z.** You can
  // also filter nested fields. For example, you could specify
  // **source.version = "12.c.1"** to select all conversion workspaces with
  // source database version equal to 12.c.1.
  string filter = 4;
}

// Response message for 'ListConversionWorkspaces' request.
message ListConversionWorkspacesResponse {
  // The list of conversion workspace objects.
  repeated ConversionWorkspace conversion_workspaces = 1;

  // A token which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for 'GetConversionWorkspace' request.
message GetConversionWorkspaceRequest {
  // Required. Name of the conversion workspace resource to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datamigration.googleapis.com/ConversionWorkspace"
    }
  ];
}

// Request message to create a new Conversion Workspace
// in the specified project and region.
message CreateConversionWorkspaceRequest {
  // Required. The parent which owns this collection of conversion workspaces.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datamigration.googleapis.com/ConversionWorkspace"
    }
  ];

  // Required. The ID of the conversion workspace to create.
  string conversion_workspace_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Represents a conversion workspace object.
  ConversionWorkspace conversion_workspace = 3
      [(google.api.field_behavior) = REQUIRED];

  // A unique ID used to identify the request. If the server receives two
  // requests with the same ID, then the second request is ignored.
  //
  // It is recommended to always set this value to a UUID.
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores
  // (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 4;
}

// Request message for 'UpdateConversionWorkspace' request.
message UpdateConversionWorkspaceRequest {
  // Required. Field mask is used to specify the fields to be overwritten by the
  // update in the conversion workspace resource.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The conversion workspace parameters to update.
  ConversionWorkspace conversion_workspace = 2
      [(google.api.field_behavior) = REQUIRED];

  // A unique ID used to identify the request. If the server receives two
  // requests with the same ID, then the second request is ignored.
  //
  // It is recommended to always set this value to a UUID.
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores
  // (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 3;
}

// Request message for 'DeleteConversionWorkspace' request.
message DeleteConversionWorkspaceRequest {
  // Required. Name of the conversion workspace resource to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datamigration.googleapis.com/ConversionWorkspace"
    }
  ];

  // A unique ID used to identify the request. If the server receives two
  // requests with the same ID, then the second request is ignored.
  //
  // It is recommended to always set this value to a UUID.
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores
  // (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 2;

  // Force delete the conversion workspace, even if there's a running migration
  // that is using the workspace.
  bool force = 3;
}

// Request message for 'CommitConversionWorkspace' request.
message CommitConversionWorkspaceRequest {
  // Required. Name of the conversion workspace resource to commit.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datamigration.googleapis.com/ConversionWorkspace"
    }
  ];

  // Optional. Optional name of the commit.
  string commit_name = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for 'RollbackConversionWorkspace' request.
message RollbackConversionWorkspaceRequest {
  // Required. Name of the conversion workspace resource to roll back to.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datamigration.googleapis.com/ConversionWorkspace"
    }
  ];
}

// Request message for 'ApplyConversionWorkspace' request.
message ApplyConversionWorkspaceRequest {
  // Required. The name of the conversion workspace resource for which to apply
  // the draft tree. Must be in the form of:
  //  projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datamigration.googleapis.com/ConversionWorkspace"
    }
  ];

  // Filter which entities to apply. Leaving this field empty will apply all of
  // the entities. Supports Google AIP 160 based filtering.
  string filter = 2;

  // Optional. Only validates the apply process, but doesn't change the
  // destination database. Only works for PostgreSQL destination connection
  // profile.
  bool dry_run = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies whether the conversion workspace is to be committed
  // automatically after the apply.
  bool auto_commit = 4 [(google.api.field_behavior) = OPTIONAL];

  // Which destination to use when applying the conversion workspace.
  oneof destination {
    // Optional. Fully qualified (Uri) name of the destination connection
    // profile.
    string connection_profile = 100 [(google.api.field_behavior) = OPTIONAL];
  }
}

// Retrieve a list of all mapping rules in a given conversion workspace.
message ListMappingRulesRequest {
  // Required. Name of the conversion workspace resource whose mapping rules are
  // listed in the form of:
  // projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datamigration.googleapis.com/ConversionWorkspace"
    }
  ];

  // The maximum number of rules to return. The service may return
  // fewer than this value.
  int32 page_size = 2;

  // The nextPageToken value received in the previous call to
  // mappingRules.list, used in the subsequent request to retrieve the next
  // page of results. On first call this should be left blank. When paginating,
  // all other parameters provided to mappingRules.list must match the call
  // that provided the page token.
  string page_token = 3;
}

// Response message for 'ListMappingRulesRequest' request.
message ListMappingRulesResponse {
  // The list of conversion workspace mapping rules.
  repeated MappingRule mapping_rules = 1;

  // A token which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for 'GetMappingRule' request.
message GetMappingRuleRequest {
  // Required. Name of the mapping rule resource to get.
  // Example: conversionWorkspaces/123/mappingRules/rule123
  //
  // In order to retrieve a previous revision of the mapping rule, also provide
  // the revision ID.
  // Example:
  // conversionWorkspace/123/mappingRules/rule123@c7cfa2a8c7cfa2a8c7cfa2a8c7cfa2a8
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datamigration.googleapis.com/MappingRule"
    }
  ];
}

// Request message for 'SeedConversionWorkspace' request.
message SeedConversionWorkspaceRequest {
  // Name of the conversion workspace resource to seed with new database
  // structure, in the form of:
  // projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.
  string name = 1 [(google.api.resource_reference) = {
    type: "datamigration.googleapis.com/ConversionWorkspace"
  }];

  // Should the conversion workspace be committed automatically after the
  // seed operation.
  bool auto_commit = 2;

  // The input to be used for seeding the conversion workspace. The input can
  // either be from the source or destination databases and it can be provided
  // through a connection profile or a DDL file.
  oneof seed_from {
    // Optional. Fully qualified (Uri) name of the source connection profile.
    string source_connection_profile = 100
        [(google.api.field_behavior) = OPTIONAL];

    // Optional. Fully qualified (Uri) name of the destination connection
    // profile.
    string destination_connection_profile = 101
        [(google.api.field_behavior) = OPTIONAL];
  }
}

// Request message for 'ConvertConversionWorkspace' request.
message ConvertConversionWorkspaceRequest {
  // Name of the conversion workspace resource to convert in the form of:
  // projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.
  string name = 1 [(google.api.resource_reference) = {
    type: "datamigration.googleapis.com/ConversionWorkspace"
  }];

  // Optional. Specifies whether the conversion workspace is to be committed
  // automatically after the conversion.
  bool auto_commit = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter the entities to convert. Leaving this field empty will
  // convert all of the entities. Supports Google AIP-160 style filtering.
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Automatically convert the full entity path for each entity
  // specified by the filter. For example, if the filter specifies a table, that
  // table schema (and database if there is one) will also be converted.
  bool convert_full_path = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for 'ImportMappingRules' request.
message ImportMappingRulesRequest {
  // Details of a single rules file.
  message RulesFile {
    // Required. The filename of the rules that needs to be converted. The
    // filename is used mainly so that future logs of the import rules job
    // contain it, and can therefore be searched by it.
    string rules_source_filename = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The text content of the rules that needs to be converted.
    string rules_content = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Required. Name of the conversion workspace resource to import the rules to
  // in the form of:
  // projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datamigration.googleapis.com/ConversionWorkspace"
    }
  ];

  // Required. The format of the rules content file.
  ImportRulesFileFormat rules_format = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. One or more rules files.
  repeated RulesFile rules_files = 3 [(google.api.field_behavior) = REQUIRED];

  // Required. Should the conversion workspace be committed automatically after
  // the import operation.
  bool auto_commit = 6 [(google.api.field_behavior) = REQUIRED];
}

// Request message for 'DescribeDatabaseEntities' request.
message DescribeDatabaseEntitiesRequest {
  // The type of a tree to return
  enum DBTreeType {
    // Unspecified tree type.
    DB_TREE_TYPE_UNSPECIFIED = 0;

    // The source database tree.
    SOURCE_TREE = 1;

    // The draft database tree.
    DRAFT_TREE = 2;

    // The destination database tree.
    DESTINATION_TREE = 3;
  }

  // Required. Name of the conversion workspace resource whose database entities
  // are described. Must be in the form of:
  // projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.
  string conversion_workspace = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datamigration.googleapis.com/ConversionWorkspace"
    }
  ];

  // Optional. The maximum number of entities to return. The service may return
  // fewer entities than the value specifies.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The nextPageToken value received in the previous call to
  // conversionWorkspace.describeDatabaseEntities, used in the subsequent
  // request to retrieve the next page of results. On first call this should be
  // left blank. When paginating, all other parameters provided to
  // conversionWorkspace.describeDatabaseEntities must match the call that
  // provided the page token.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];

  // Required. The tree to fetch.
  DBTreeType tree = 6 [(google.api.field_behavior) = REQUIRED];

  // Optional. Whether to retrieve the latest committed version of the entities
  // or the latest version. This field is ignored if a specific commit_id is
  // specified.
  bool uncommitted = 11 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Request a specific commit ID. If not specified, the entities from
  // the latest commit are returned.
  string commit_id = 12 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter the returned entities based on AIP-160 standard.
  string filter = 13 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Results view based on AIP-157
  DatabaseEntityView view = 14 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for 'DescribeDatabaseEntities' request.
message DescribeDatabaseEntitiesResponse {
  // The list of database entities for the conversion workspace.
  repeated DatabaseEntity database_entities = 1;

  // A token which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Request message for 'SearchBackgroundJobs' request.
message SearchBackgroundJobsRequest {
  // Required. Name of the conversion workspace resource whose jobs are listed,
  // in the form of:
  // projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.
  string conversion_workspace = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datamigration.googleapis.com/ConversionWorkspace"
    }
  ];

  // Optional. Whether or not to return just the most recent job per job type,
  bool return_most_recent_per_job_type = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of jobs to return. The service may return
  // fewer than this value. If unspecified, at most 100 jobs are
  // returned. The maximum value is 100; values above 100 are coerced to
  // 100.
  int32 max_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If provided, only returns jobs that completed until (not
  // including) the given timestamp.
  google.protobuf.Timestamp completed_until_time = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// Response message for 'SearchBackgroundJobs' request.
message SearchBackgroundJobsResponse {
  // The list of conversion workspace mapping rules.
  repeated BackgroundJobLogEntry jobs = 1;
}

// Request message for 'DescribeConversionWorkspaceRevisions' request.
message DescribeConversionWorkspaceRevisionsRequest {
  // Required. Name of the conversion workspace resource whose revisions are
  // listed. Must be in the form of:
  // projects/{project}/locations/{location}/conversionWorkspaces/{conversion_workspace}.
  string conversion_workspace = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "datamigration.googleapis.com/ConversionWorkspace"
    }
  ];

  // Optional. Optional filter to request a specific commit ID.
  string commit_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for 'DescribeConversionWorkspaceRevisions' request.
message DescribeConversionWorkspaceRevisionsResponse {
  // The list of conversion workspace revisions.
  repeated ConversionWorkspace revisions = 1;
}

// Request message for 'CreateMappingRule' command.
message CreateMappingRuleRequest {
  // Required. The parent which owns this collection of mapping rules.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datamigration.googleapis.com/MappingRule"
    }
  ];

  // Required. The ID of the rule to create.
  string mapping_rule_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Represents a [mapping rule]
  // (https://cloud.google.com/database-migration/reference/rest/v1/projects.locations.mappingRules)
  // object.
  MappingRule mapping_rule = 3 [(google.api.field_behavior) = REQUIRED];

  // A unique ID used to identify the request. If the server receives two
  // requests with the same ID, then the second request is ignored.
  //
  // It is recommended to always set this value to a UUID.
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores
  // (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 4;
}

// Request message for 'DeleteMappingRule' request.
message DeleteMappingRuleRequest {
  // Required. Name of the mapping rule resource to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "datamigration.googleapis.com/MappingRule"
    }
  ];

  // Optional. A unique ID used to identify the request. If the server receives
  // two requests with the same ID, then the second request is ignored.
  //
  // It is recommended to always set this value to a UUID.
  //
  // The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores
  // (_), and hyphens (-). The maximum length is 40 characters.
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for 'FetchStaticIps' request.
message FetchStaticIpsRequest {
  // Required. The resource name for the location for which static IPs should be
  // returned. Must be in the format `projects/*/locations/*`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Maximum number of IPs to return.
  int32 page_size = 2;

  // A page token, received from a previous `FetchStaticIps` call.
  string page_token = 3;
}

// Response message for a 'FetchStaticIps' request.
message FetchStaticIpsResponse {
  // List of static IPs.
  repeated string static_ips = 1;

  // A token that can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// AIP-157 Partial Response view for Database Entity.
enum DatabaseEntityView {
  // Unspecified view. Defaults to basic view.
  DATABASE_ENTITY_VIEW_UNSPECIFIED = 0;

  // Default view. Does not return DDLs or Issues.
  DATABASE_ENTITY_VIEW_BASIC = 1;

  // Return full entity details including mappings, ddl and issues.
  DATABASE_ENTITY_VIEW_FULL = 2;

  // Top-most (Database, Schema) nodes which are returned contains summary
  // details for their decendents such as the number of entities per type and
  // issues rollups. When this view is used, only a single page of result is
  // returned and the page_size property of the request is ignored. The
  // returned page will only include the top-most node types.
  DATABASE_ENTITY_VIEW_ROOT_SUMMARY = 3;
}
