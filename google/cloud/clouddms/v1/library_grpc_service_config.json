{"methodConfig": [{"name": [{"service": "google.cloud.clouddms.v1.DataMigrationService"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "CreateConnectionProfile"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "GetConnectionProfile"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "ListConnectionProfiles"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "UpdateConnectionProfile"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "DeleteConnectionProfile"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "CreateMigration<PERSON>ob"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "GetMigrationJob"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "ListMigrationJobs"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "UpdateMigrationJob"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "DeleteMigrationJob"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "StartMigrationJob"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "StopMigrationJob"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "VerifyMigrationJob"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "ResumeMigrationJob"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "RestartMigrationJob"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "PromoteMigrationJob"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "GenerateSshScript"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "CreatePrivateConnection"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "GetPrivateConnection"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "ListPrivateConnections"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "DeletePrivateConnection"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "CreateConversionWorkspace"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "GetConversionWorkspace"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "ListConversionWorkspaces"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "DeleteConversionWorkspace"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "UpdateConversionWorkspace"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "SeedConversionWorkspace"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "ImportMappingRules"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "ConvertConversionWorkspace"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "CommitConversionWorkspace"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "RollbackConversionWorkspace"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "ApplyConversionWorkspace"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "DescribeDatabaseEntities"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "SearchBackgroundJobs"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "DescribeConversionWorkspaceRevisions"}, {"service": "google.cloud.clouddms.v1.DataMigrationService", "method": "FetchStaticIps"}], "timeout": "60s"}]}