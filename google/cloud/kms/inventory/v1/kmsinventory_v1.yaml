type: google.api.Service
config_version: 3
name: kmsinventory.googleapis.com
title: KMS Inventory API

apis:
- name: google.cloud.kms.inventory.v1.KeyDashboardService
- name: google.cloud.kms.inventory.v1.KeyTrackingService

authentication:
  rules:
  - selector: google.cloud.kms.inventory.v1.KeyDashboardService.ListCryptoKeys
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.kms.inventory.v1.KeyTrackingService.GetProtectedResourcesSummary
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.kms.inventory.v1.KeyTrackingService.SearchProtectedResources
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform

publishing:
  new_issue_uri: https://issuetracker.google.com/issues/new?component=190860&template=819701
  documentation_uri: https://cloud.google.com/kms/docs/
  api_short_name: kmsinventory
  github_label: 'api: kmsinventory'
  doc_tag_prefix: kmsinventory
  organization: CLOUD
  library_settings:
  - version: google.cloud.kms.inventory.v1
    launch_stage: GA
    java_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    cpp_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    php_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    python_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    node_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    dotnet_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
      forced_namespace_aliases:
      - Google.Cloud.Kms.V1
    ruby_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
    go_settings:
      common:
        destinations:
        - PACKAGE_MANAGER
