{"methodConfig": [{"name": [{"service": "google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchTransactions"}, {"service": "google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchRules"}, {"service": "google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchResolutions"}, {"service": "google.cloud.paymentgateway.issuerswitch.v1.IssuerSwitchParticipants"}, {"service": "google.cloud.paymentgateway.issuerswitch.accountmanager.v1.AccountManagerTransactions"}, {"service": "google.cloud.paymentgateway.issuerswitch.accountmanager.v1.ManagedAccounts"}], "timeout": "60s", "retryPolicy": {"initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}]}