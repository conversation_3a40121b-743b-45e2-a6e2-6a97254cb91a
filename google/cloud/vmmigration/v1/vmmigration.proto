// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.vmmigration.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/error_details.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.VMMigration.V1";
option go_package = "cloud.google.com/go/vmmigration/apiv1/vmmigrationpb;vmmigrationpb";
option java_multiple_files = true;
option java_outer_classname = "VmMigrationProto";
option java_package = "com.google.cloud.vmmigration.v1";
option php_namespace = "Google\\Cloud\\VMMigration\\V1";
option ruby_package = "Google::Cloud::VMMigration::V1";

// VM Migration Service
service VmMigration {
  option (google.api.default_host) = "vmmigration.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists Sources in a given project and location.
  rpc ListSources(ListSourcesRequest) returns (ListSourcesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/sources"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Source.
  rpc GetSource(GetSourceRequest) returns (Source) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/sources/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Source in a given project and location.
  rpc CreateSource(CreateSourceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/sources"
      body: "source"
    };
    option (google.api.method_signature) = "parent,source,source_id";
    option (google.longrunning.operation_info) = {
      response_type: "Source"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Source.
  rpc UpdateSource(UpdateSourceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{source.name=projects/*/locations/*/sources/*}"
      body: "source"
    };
    option (google.api.method_signature) = "source,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Source"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Source.
  rpc DeleteSource(DeleteSourceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/sources/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // List remote source's inventory of VMs.
  // The remote source is the onprem vCenter (remote in the sense it's not in
  // Compute Engine). The inventory describes the list of existing VMs in that
  // source. Note that this operation lists the VMs on the remote source, as
  // opposed to listing the MigratingVms resources in the vmmigration service.
  rpc FetchInventory(FetchInventoryRequest) returns (FetchInventoryResponse) {
    option (google.api.http) = {
      get: "/v1/{source=projects/*/locations/*/sources/*}:fetchInventory"
    };
    option (google.api.method_signature) = "source";
  }

  // Lists Utilization Reports of the given Source.
  rpc ListUtilizationReports(ListUtilizationReportsRequest)
      returns (ListUtilizationReportsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/sources/*}/utilizationReports"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets a single Utilization Report.
  rpc GetUtilizationReport(GetUtilizationReportRequest)
      returns (UtilizationReport) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/sources/*/utilizationReports/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new UtilizationReport.
  rpc CreateUtilizationReport(CreateUtilizationReportRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/sources/*}/utilizationReports"
      body: "utilization_report"
    };
    option (google.api.method_signature) =
        "parent,utilization_report,utilization_report_id";
    option (google.longrunning.operation_info) = {
      response_type: "UtilizationReport"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Utilization Report.
  rpc DeleteUtilizationReport(DeleteUtilizationReportRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/sources/*/utilizationReports/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists DatacenterConnectors in a given Source.
  rpc ListDatacenterConnectors(ListDatacenterConnectorsRequest)
      returns (ListDatacenterConnectorsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/sources/*}/datacenterConnectors"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single DatacenterConnector.
  rpc GetDatacenterConnector(GetDatacenterConnectorRequest)
      returns (DatacenterConnector) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/sources/*/datacenterConnectors/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new DatacenterConnector in a given Source.
  rpc CreateDatacenterConnector(CreateDatacenterConnectorRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/sources/*}/datacenterConnectors"
      body: "datacenter_connector"
    };
    option (google.api.method_signature) =
        "parent,datacenter_connector,datacenter_connector_id";
    option (google.longrunning.operation_info) = {
      response_type: "DatacenterConnector"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single DatacenterConnector.
  rpc DeleteDatacenterConnector(DeleteDatacenterConnectorRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/sources/*/datacenterConnectors/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Upgrades the appliance relate to this DatacenterConnector to the in-place
  // updateable version.
  rpc UpgradeAppliance(UpgradeApplianceRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{datacenter_connector=projects/*/locations/*/sources/*/datacenterConnectors/*}:upgradeAppliance"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "UpgradeApplianceResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Creates a new MigratingVm in a given Source.
  rpc CreateMigratingVm(CreateMigratingVmRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/sources/*}/migratingVms"
      body: "migrating_vm"
    };
    option (google.api.method_signature) =
        "parent,migrating_vm,migrating_vm_id";
    option (google.longrunning.operation_info) = {
      response_type: "MigratingVm"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists MigratingVms in a given Source.
  rpc ListMigratingVms(ListMigratingVmsRequest)
      returns (ListMigratingVmsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/sources/*}/migratingVms"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single MigratingVm.
  rpc GetMigratingVm(GetMigratingVmRequest) returns (MigratingVm) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/sources/*/migratingVms/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates the parameters of a single MigratingVm.
  rpc UpdateMigratingVm(UpdateMigratingVmRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{migrating_vm.name=projects/*/locations/*/sources/*/migratingVms/*}"
      body: "migrating_vm"
    };
    option (google.api.method_signature) = "migrating_vm,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "MigratingVm"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single MigratingVm.
  rpc DeleteMigratingVm(DeleteMigratingVmRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/sources/*/migratingVms/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Starts migration for a VM. Starts the process of uploading
  // data and creating snapshots, in replication cycles scheduled by the policy.
  rpc StartMigration(StartMigrationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{migrating_vm=projects/*/locations/*/sources/*/migratingVms/*}:startMigration"
      body: "*"
    };
    option (google.api.method_signature) = "migrating_vm";
    option (google.longrunning.operation_info) = {
      response_type: "StartMigrationResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Resumes a migration for a VM. When called on a paused migration, will start
  // the process of uploading data and creating snapshots; when called on a
  // completed cut-over migration, will update the migration to active state and
  // start the process of uploading data and creating snapshots.
  rpc ResumeMigration(ResumeMigrationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{migrating_vm=projects/*/locations/*/sources/*/migratingVms/*}:resumeMigration"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "ResumeMigrationResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Pauses a migration for a VM. If cycle tasks are running they will be
  // cancelled, preserving source task data. Further replication cycles will not
  // be triggered while the VM is paused.
  rpc PauseMigration(PauseMigrationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{migrating_vm=projects/*/locations/*/sources/*/migratingVms/*}:pauseMigration"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "PauseMigrationResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Marks a migration as completed, deleting migration resources that are no
  // longer being used. Only applicable after cutover is done.
  rpc FinalizeMigration(FinalizeMigrationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{migrating_vm=projects/*/locations/*/sources/*/migratingVms/*}:finalizeMigration"
      body: "*"
    };
    option (google.api.method_signature) = "migrating_vm";
    option (google.longrunning.operation_info) = {
      response_type: "FinalizeMigrationResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Initiates a Clone of a specific migrating VM.
  rpc CreateCloneJob(CreateCloneJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/sources/*/migratingVms/*}/cloneJobs"
      body: "clone_job"
    };
    option (google.api.method_signature) = "parent,clone_job,clone_job_id";
    option (google.longrunning.operation_info) = {
      response_type: "CloneJob"
      metadata_type: "OperationMetadata"
    };
  }

  // Initiates the cancellation of a running clone job.
  rpc CancelCloneJob(CancelCloneJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/sources/*/migratingVms/*/cloneJobs/*}:cancel"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "CancelCloneJobResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists CloneJobs of a given migrating VM.
  rpc ListCloneJobs(ListCloneJobsRequest) returns (ListCloneJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/sources/*/migratingVms/*}/cloneJobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single CloneJob.
  rpc GetCloneJob(GetCloneJobRequest) returns (CloneJob) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/sources/*/migratingVms/*/cloneJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Initiates a Cutover of a specific migrating VM.
  // The returned LRO is completed when the cutover job resource is created
  // and the job is initiated.
  rpc CreateCutoverJob(CreateCutoverJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*/sources/*/migratingVms/*}/cutoverJobs"
      body: "cutover_job"
    };
    option (google.api.method_signature) = "parent,cutover_job,cutover_job_id";
    option (google.longrunning.operation_info) = {
      response_type: "CutoverJob"
      metadata_type: "OperationMetadata"
    };
  }

  // Initiates the cancellation of a running cutover job.
  rpc CancelCutoverJob(CancelCutoverJobRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{name=projects/*/locations/*/sources/*/migratingVms/*/cutoverJobs/*}:cancel"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "CancelCutoverJobResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists CutoverJobs of a given migrating VM.
  rpc ListCutoverJobs(ListCutoverJobsRequest)
      returns (ListCutoverJobsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/sources/*/migratingVms/*}/cutoverJobs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single CutoverJob.
  rpc GetCutoverJob(GetCutoverJobRequest) returns (CutoverJob) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/sources/*/migratingVms/*/cutoverJobs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists Groups in a given project and location.
  rpc ListGroups(ListGroupsRequest) returns (ListGroupsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/groups"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Group.
  rpc GetGroup(GetGroupRequest) returns (Group) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/groups/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Group in a given project and location.
  rpc CreateGroup(CreateGroupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/groups"
      body: "group"
    };
    option (google.api.method_signature) = "parent,group,group_id";
    option (google.longrunning.operation_info) = {
      response_type: "Group"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Group.
  rpc UpdateGroup(UpdateGroupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{group.name=projects/*/locations/*/groups/*}"
      body: "group"
    };
    option (google.api.method_signature) = "group,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Group"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Group.
  rpc DeleteGroup(DeleteGroupRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/groups/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Adds a MigratingVm to a Group.
  rpc AddGroupMigration(AddGroupMigrationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{group=projects/*/locations/*/groups/*}:addGroupMigration"
      body: "*"
    };
    option (google.api.method_signature) = "group";
    option (google.longrunning.operation_info) = {
      response_type: "AddGroupMigrationResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Removes a MigratingVm from a Group.
  rpc RemoveGroupMigration(RemoveGroupMigrationRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{group=projects/*/locations/*/groups/*}:removeGroupMigration"
      body: "*"
    };
    option (google.api.method_signature) = "group";
    option (google.longrunning.operation_info) = {
      response_type: "RemoveGroupMigrationResponse"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists TargetProjects in a given project.
  //
  // NOTE: TargetProject is a global resource; hence the only supported value
  // for location is `global`.
  rpc ListTargetProjects(ListTargetProjectsRequest)
      returns (ListTargetProjectsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*}/targetProjects"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single TargetProject.
  //
  // NOTE: TargetProject is a global resource; hence the only supported value
  // for location is `global`.
  rpc GetTargetProject(GetTargetProjectRequest) returns (TargetProject) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/targetProjects/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new TargetProject in a given project.
  //
  // NOTE: TargetProject is a global resource; hence the only supported value
  // for location is `global`.
  rpc CreateTargetProject(CreateTargetProjectRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1/{parent=projects/*/locations/*}/targetProjects"
      body: "target_project"
    };
    option (google.api.method_signature) =
        "parent,target_project,target_project_id";
    option (google.longrunning.operation_info) = {
      response_type: "TargetProject"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single TargetProject.
  //
  // NOTE: TargetProject is a global resource; hence the only supported value
  // for location is `global`.
  rpc UpdateTargetProject(UpdateTargetProjectRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1/{target_project.name=projects/*/locations/*/targetProjects/*}"
      body: "target_project"
    };
    option (google.api.method_signature) = "target_project,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "TargetProject"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single TargetProject.
  //
  // NOTE: TargetProject is a global resource; hence the only supported value
  // for location is `global`.
  rpc DeleteTargetProject(DeleteTargetProjectRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1/{name=projects/*/locations/*/targetProjects/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists ReplicationCycles in a given MigratingVM.
  rpc ListReplicationCycles(ListReplicationCyclesRequest)
      returns (ListReplicationCyclesResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=projects/*/locations/*/sources/*/migratingVms/*}/replicationCycles"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single ReplicationCycle.
  rpc GetReplicationCycle(GetReplicationCycleRequest)
      returns (ReplicationCycle) {
    option (google.api.http) = {
      get: "/v1/{name=projects/*/locations/*/sources/*/migratingVms/*/replicationCycles/*}"
    };
    option (google.api.method_signature) = "name";
  }
}

// Controls the level of details of a Utilization Report.
enum UtilizationReportView {
  // The default / unset value.
  // The API will default to FULL on single report request and BASIC for
  // multiple reports request.
  UTILIZATION_REPORT_VIEW_UNSPECIFIED = 0;

  // Get the report metadata, without the list of VMs and their utilization
  // info.
  BASIC = 1;

  // Include everything.
  FULL = 2;
}

// Controls the level of details of a Migrating VM.
enum MigratingVmView {
  // View is unspecified. The API will fallback to the default value.
  MIGRATING_VM_VIEW_UNSPECIFIED = 0;

  // Get the migrating VM basic details.
  // The basic details do not include the recent clone jobs and recent cutover
  // jobs lists.
  MIGRATING_VM_VIEW_BASIC = 1;

  // Include everything.
  MIGRATING_VM_VIEW_FULL = 2;
}

// Types of disks supported for Compute Engine VM.
enum ComputeEngineDiskType {
  // An unspecified disk type. Will be used as STANDARD.
  COMPUTE_ENGINE_DISK_TYPE_UNSPECIFIED = 0;

  // A Standard disk type.
  COMPUTE_ENGINE_DISK_TYPE_STANDARD = 1;

  // SSD hard disk type.
  COMPUTE_ENGINE_DISK_TYPE_SSD = 2;

  // An alternative to SSD persistent disks that balance performance and
  // cost.
  COMPUTE_ENGINE_DISK_TYPE_BALANCED = 3;
}

// Types of licenses used in OS adaptation.
enum ComputeEngineLicenseType {
  // The license type is the default for the OS.
  COMPUTE_ENGINE_LICENSE_TYPE_DEFAULT = 0;

  // The license type is Pay As You Go license type.
  COMPUTE_ENGINE_LICENSE_TYPE_PAYG = 1;

  // The license type is Bring Your Own License type.
  COMPUTE_ENGINE_LICENSE_TYPE_BYOL = 2;
}

// Possible values for vm boot option.
enum ComputeEngineBootOption {
  // The boot option is unknown.
  COMPUTE_ENGINE_BOOT_OPTION_UNSPECIFIED = 0;

  // The boot option is EFI.
  COMPUTE_ENGINE_BOOT_OPTION_EFI = 1;

  // The boot option is BIOS.
  COMPUTE_ENGINE_BOOT_OPTION_BIOS = 2;
}

// ReplicationCycle contains information about the current replication cycle
// status.
message ReplicationCycle {
  option (google.api.resource) = {
    type: "vmmigration.googleapis.com/ReplicationCycle"
    pattern: "projects/{project}/locations/{location}/sources/{source}/migratingVms/{migrating_vm}/replicationCycles/{replication_cycle}"
  };

  // Possible states of a replication cycle.
  enum State {
    // The state is unknown. This is used for API compatibility only and is not
    // used by the system.
    STATE_UNSPECIFIED = 0;

    // The replication cycle is running.
    RUNNING = 1;

    // The replication cycle is paused.
    PAUSED = 2;

    // The replication cycle finished with errors.
    FAILED = 3;

    // The replication cycle finished successfully.
    SUCCEEDED = 4;
  }

  // The identifier of the ReplicationCycle.
  string name = 13;

  // The cycle's ordinal number.
  int32 cycle_number = 10;

  // The time the replication cycle has started.
  google.protobuf.Timestamp start_time = 1;

  // The time the replication cycle has ended.
  google.protobuf.Timestamp end_time = 6;

  // The accumulated duration the replication cycle was paused.
  google.protobuf.Duration total_pause_duration = 7;

  // The current progress in percentage of this cycle.
  // Was replaced by 'steps' field, which breaks down the cycle progression more
  // accurately.
  int32 progress_percent = 5 [deprecated = true];

  // The cycle's steps list representing its progress.
  repeated CycleStep steps = 9;

  // State of the ReplicationCycle.
  State state = 11;

  // Provides details on the state of the cycle in case of an error.
  google.rpc.Status error = 12;
}

// CycleStep holds information about a step progress.
message CycleStep {
  oneof step {
    // Initializing replication step.
    InitializingReplicationStep initializing_replication = 3;

    // Replicating step.
    ReplicatingStep replicating = 4;

    // Post processing step.
    PostProcessingStep post_processing = 5;
  }

  // The time the cycle step has started.
  google.protobuf.Timestamp start_time = 1;

  // The time the cycle step has ended.
  google.protobuf.Timestamp end_time = 2;
}

// InitializingReplicationStep contains specific step details.
message InitializingReplicationStep {}

// ReplicatingStep contains specific step details.
message ReplicatingStep {
  // Total bytes to be handled in the step.
  int64 total_bytes = 1;

  // Replicated bytes in the step.
  int64 replicated_bytes = 2;

  // The source disks replication rate for the last 2 minutes in bytes per
  // second.
  int64 last_two_minutes_average_bytes_per_second = 3;

  // The source disks replication rate for the last 30 minutes in bytes per
  // second.
  int64 last_thirty_minutes_average_bytes_per_second = 4;
}

// PostProcessingStep contains specific step details.
message PostProcessingStep {}

// ReplicationSync contain information about the last replica sync to the cloud.
message ReplicationSync {
  // The most updated snapshot created time in the source that finished
  // replication.
  google.protobuf.Timestamp last_sync_time = 1;
}

// MigratingVm describes the VM that will be migrated from a Source environment
// and its replication state.
message MigratingVm {
  option (google.api.resource) = {
    type: "vmmigration.googleapis.com/MigratingVm"
    pattern: "projects/{project}/locations/{location}/sources/{source}/migratingVms/{migrating_vm}"
  };

  // The possible values of the state/health of source VM.
  enum State {
    // The state was not sampled by the health checks yet.
    STATE_UNSPECIFIED = 0;

    // The VM in the source is being verified.
    PENDING = 1;

    // The source VM was verified, and it's ready to start replication.
    READY = 2;

    // Migration is going through the first sync cycle.
    FIRST_SYNC = 3;

    // The replication is active, and it's running or scheduled to run.
    ACTIVE = 4;

    // The source VM is being turned off, and a final replication is currently
    // running.
    CUTTING_OVER = 7;

    // The source VM was stopped and replicated. The replication is currently
    // paused.
    CUTOVER = 8;

    // A cutover job is active and replication cycle is running the final sync.
    FINAL_SYNC = 9;

    // The replication was paused by the user and no cycles are scheduled to
    // run.
    PAUSED = 10;

    // The migrating VM is being finalized and migration resources are being
    // removed.
    FINALIZING = 11;

    // The replication process is done. The migrating VM is finalized and no
    // longer consumes billable resources.
    FINALIZED = 12;

    // The replication process encountered an unrecoverable error and was
    // aborted.
    ERROR = 13;
  }

  // The default configuration of the target VM that will be created in Google
  // Cloud as a result of the migration.
  oneof target_vm_defaults {
    // Details of the target VM in Compute Engine.
    ComputeEngineTargetDefaults compute_engine_target_defaults = 26;
  }

  // Details about the source VM.
  oneof source_vm_details {
    // Output only. Details of the VM from an AWS source.
    AwsSourceVmDetails aws_source_vm_details = 29
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The identifier of the MigratingVm.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The unique ID of the VM in the source.
  // The VM's name in vSphere can be changed, so this is not the VM's name but
  // rather its moRef id. This id is of the form vm-<num>.
  string source_vm_id = 2;

  // The display name attached to the MigratingVm by the user.
  string display_name = 18;

  // The description attached to the migrating VM by the user.
  string description = 3;

  // The replication schedule policy.
  SchedulePolicy policy = 8;

  // Output only. The time the migrating VM was created (this refers to this
  // resource and not to the time it was installed in the source).
  google.protobuf.Timestamp create_time = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last time the migrating VM resource was updated.
  google.protobuf.Timestamp update_time = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The most updated snapshot created time in the source that
  // finished replication.
  ReplicationSync last_sync = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the MigratingVm.
  State state = 23 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last time the migrating VM state was updated.
  google.protobuf.Timestamp state_time = 22
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The percentage progress of the current running replication
  // cycle.
  ReplicationCycle current_sync_info = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The group this migrating vm is included in, if any. The group
  // is represented by the full path of the appropriate
  // [Group][google.cloud.vmmigration.v1.Group] resource.
  string group = 15 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/Group"
    }
  ];

  // The labels of the migrating VM.
  map<string, string> labels = 16;

  // Output only. The recent [clone jobs][google.cloud.vmmigration.v1.CloneJob]
  // performed on the migrating VM. This field holds the vm's last completed
  // clone job and the vm's running clone job, if one exists.
  // Note: To have this field populated you need to explicitly request it via
  // the "view" parameter of the Get/List request.
  repeated CloneJob recent_clone_jobs = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Provides details on the state of the Migrating VM in case of
  // an error in replication.
  google.rpc.Status error = 19 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The recent cutover jobs performed on the migrating VM.
  // This field holds the vm's last completed cutover job and the vm's
  // running cutover job, if one exists.
  // Note: To have this field populated you need to explicitly request it via
  // the "view" parameter of the Get/List request.
  repeated CutoverJob recent_cutover_jobs = 20
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// CloneJob describes the process of creating a clone of a
// [MigratingVM][google.cloud.vmmigration.v1.MigratingVm] to the
// requested target based on the latest successful uploaded snapshots.
// While the migration cycles of a MigratingVm take place, it is possible to
// verify the uploaded VM can be started in the cloud, by creating a clone. The
// clone can be created without any downtime, and it is created using the latest
// snapshots which are already in the cloud. The cloneJob is only responsible
// for its work, not its products, which means once it is finished, it will
// never touch the instance it created. It will only delete it in case of the
// CloneJob being cancelled or upon failure to clone.
message CloneJob {
  option (google.api.resource) = {
    type: "vmmigration.googleapis.com/CloneJob"
    pattern: "projects/{project}/locations/{location}/sources/{source}/migratingVms/{migrating_vm}/cloneJobs/{clone_job}"
  };

  // Possible states of the clone job.
  enum State {
    // The state is unknown. This is used for API compatibility only and is not
    // used by the system.
    STATE_UNSPECIFIED = 0;

    // The clone job has not yet started.
    PENDING = 1;

    // The clone job is active and running.
    ACTIVE = 2;

    // The clone job finished with errors.
    FAILED = 3;

    // The clone job finished successfully.
    SUCCEEDED = 4;

    // The clone job was cancelled.
    CANCELLED = 5;

    // The clone job is being cancelled.
    CANCELLING = 6;

    // OS adaptation is running as part of the clone job to generate license.
    ADAPTING_OS = 7;
  }

  // Details of the VM to create as the target of this clone job.
  oneof target_vm_details {
    // Output only. Details of the target VM in Compute Engine.
    ComputeEngineTargetDetails compute_engine_target_details = 20
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The time the clone job was created (as an API call, not when
  // it was actually created in the target).
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the clone job was ended.
  google.protobuf.Timestamp end_time = 22
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the clone.
  string name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the clone job.
  State state = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the state was last updated.
  google.protobuf.Timestamp state_time = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Provides details for the errors that led to the Clone Job's
  // state.
  google.rpc.Status error = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The clone steps list representing its progress.
  repeated CloneStep steps = 23 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// CloneStep holds information about the clone step progress.
message CloneStep {
  oneof step {
    // Adapting OS step.
    AdaptingOSStep adapting_os = 3;

    // Preparing VM disks step.
    PreparingVMDisksStep preparing_vm_disks = 4;

    // Instantiating migrated VM step.
    InstantiatingMigratedVMStep instantiating_migrated_vm = 5;
  }

  // The time the step has started.
  google.protobuf.Timestamp start_time = 1;

  // The time the step has ended.
  google.protobuf.Timestamp end_time = 2;
}

// AdaptingOSStep contains specific step details.
message AdaptingOSStep {}

// PreparingVMDisksStep contains specific step details.
message PreparingVMDisksStep {}

// InstantiatingMigratedVMStep contains specific step details.
message InstantiatingMigratedVMStep {}

// CutoverJob message describes a cutover of a migrating VM. The CutoverJob is
// the operation of shutting down the VM, creating a snapshot and
// clonning the VM using the replicated snapshot.
message CutoverJob {
  option (google.api.resource) = {
    type: "vmmigration.googleapis.com/CutoverJob"
    pattern: "projects/{project}/locations/{location}/sources/{source}/migratingVms/{migrating_vm}/cutoverJobs/{cutover_job}"
  };

  // Possible states of the cutover job.
  enum State {
    // The state is unknown. This is used for API compatibility only and is not
    // used by the system.
    STATE_UNSPECIFIED = 0;

    // The cutover job has not yet started.
    PENDING = 1;

    // The cutover job finished with errors.
    FAILED = 2;

    // The cutover job finished successfully.
    SUCCEEDED = 3;

    // The cutover job was cancelled.
    CANCELLED = 4;

    // The cutover job is being cancelled.
    CANCELLING = 5;

    // The cutover job is active and running.
    ACTIVE = 6;

    // OS adaptation is running as part of the cutover job to generate license.
    ADAPTING_OS = 7;
  }

  // Details of the VM to create as the target of this cutover job.
  oneof target_vm_details {
    // Output only. Details of the target VM in Compute Engine.
    ComputeEngineTargetDetails compute_engine_target_details = 14
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The time the cutover job was created (as an API call, not when
  // it was actually created in the target).
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the cutover job had finished.
  google.protobuf.Timestamp end_time = 16
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the cutover job.
  string name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the cutover job.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the state was last updated.
  google.protobuf.Timestamp state_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The current progress in percentage of the cutover job.
  int32 progress_percent = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Provides details for the errors that led to the Cutover Job's
  // state.
  google.rpc.Status error = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A message providing possible extra details about the current
  // state.
  string state_message = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The cutover steps list representing its progress.
  repeated CutoverStep steps = 17 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// CutoverStep holds information about the cutover step progress.
message CutoverStep {
  oneof step {
    // A replication cycle prior cutover step.
    ReplicationCycle previous_replication_cycle = 3;

    // Shutting down VM step.
    ShuttingDownSourceVMStep shutting_down_source_vm = 4;

    // Final sync step.
    ReplicationCycle final_sync = 5;

    // Preparing VM disks step.
    PreparingVMDisksStep preparing_vm_disks = 6;

    // Instantiating migrated VM step.
    InstantiatingMigratedVMStep instantiating_migrated_vm = 7;
  }

  // The time the step has started.
  google.protobuf.Timestamp start_time = 1;

  // The time the step has ended.
  google.protobuf.Timestamp end_time = 2;
}

// ShuttingDownSourceVMStep contains specific step details.
message ShuttingDownSourceVMStep {}

// Request message for 'CreateCloneJob' request.
message CreateCloneJobRequest {
  // Required. The Clone's parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/CloneJob"
    }
  ];

  // Required. The clone job identifier.
  string clone_job_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The clone request body.
  CloneJob clone_job = 3 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4;
}

// Request message for 'CancelCloneJob' request.
message CancelCloneJobRequest {
  // Required. The clone job id
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/CloneJob"
    }
  ];
}

// Response message for 'CancelCloneJob' request.
message CancelCloneJobResponse {}

// Request message for 'ListCloneJobsRequest' request.
message ListCloneJobsRequest {
  // Required. The parent, which owns this collection of source VMs.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/CloneJob"
    }
  ];

  // Optional. The maximum number of clone jobs to return. The service may
  // return fewer than this value. If unspecified, at most 500 clone jobs will
  // be returned. The maximum value is 1000; values above 1000 will be coerced
  // to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. A page token, received from a previous `ListCloneJobs` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListCloneJobs` must
  // match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The filter request.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. the order by fields for the result.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for 'ListCloneJobs' request.
message ListCloneJobsResponse {
  // Output only. The list of clone jobs response.
  repeated CloneJob clone_jobs = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A token, which can be sent as `page_token` to retrieve the
  // next page. If this field is omitted, there are no subsequent pages.
  string next_page_token = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Locations that could not be reached.
  repeated string unreachable = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for 'GetCloneJob' request.
message GetCloneJobRequest {
  // Required. The name of the CloneJob.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/CloneJob"
    }
  ];
}

// Source message describes a specific vm migration Source resource. It contains
// the source environment information.
message Source {
  option (google.api.resource) = {
    type: "vmmigration.googleapis.com/Source"
    pattern: "projects/{project}/locations/{location}/sources/{source}"
  };

  oneof source_details {
    // Vmware type source details.
    VmwareSourceDetails vmware = 10;

    // AWS type source details.
    AwsSourceDetails aws = 12;
  }

  // Output only. The Source name.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The create time timestamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update time timestamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The labels of the source.
  map<string, string> labels = 4;

  // User-provided description of the source.
  string description = 6;
}

// VmwareSourceDetails message describes a specific source details for the
// vmware source type.
message VmwareSourceDetails {
  // The credentials username.
  string username = 1;

  // Input only. The credentials password. This is write only and can not be
  // read in a GET operation.
  string password = 2 [(google.api.field_behavior) = INPUT_ONLY];

  // The ip address of the vcenter this Source represents.
  string vcenter_ip = 3;

  // The thumbprint representing the certificate for the vcenter.
  string thumbprint = 4;
}

// AwsSourceDetails message describes a specific source details for the
// AWS source type.
message AwsSourceDetails {
  // Message describing AWS Credentials using access key id and secret.
  message AccessKeyCredentials {
    // AWS access key ID.
    string access_key_id = 1;

    // Input only. AWS secret access key.
    string secret_access_key = 2 [(google.api.field_behavior) = INPUT_ONLY];
  }

  // Tag is an AWS tag representation.
  message Tag {
    // Key of tag.
    string key = 1;

    // Value of tag.
    string value = 2;
  }

  // The possible values of the state.
  enum State {
    // The state is unknown. This is used for API compatibility only and is not
    // used by the system.
    STATE_UNSPECIFIED = 0;

    // The state was not sampled by the health checks yet.
    PENDING = 1;

    // The source is available but might not be usable yet due to invalid
    // credentials or another reason.
    // The error message will contain further details.
    FAILED = 2;

    // The source exists and its credentials were verified.
    ACTIVE = 3;
  }

  oneof credentials_type {
    // AWS Credentials using access key id and secret.
    AccessKeyCredentials access_key_creds = 11;
  }

  // Immutable. The AWS region that the source VMs will be migrated from.
  string aws_region = 3 [(google.api.field_behavior) = IMMUTABLE];

  // Output only. State of the source as determined by the health check.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Provides details on the state of the Source in case of an
  // error.
  google.rpc.Status error = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // AWS resource tags to limit the scope of the source inventory.
  repeated Tag inventory_tag_list = 10;

  // AWS security group names to limit the scope of the source
  // inventory.
  repeated string inventory_security_group_names = 7;

  // User specified tags to add to every M2VM generated resource in AWS.
  // These tags will be set in addition to the default tags that are set as part
  // of the migration process. The tags must not begin with the reserved prefix
  // `m2vm`.
  map<string, string> migration_resources_user_tags = 8;

  // Output only. The source's public IP. All communication initiated by this
  // source will originate from this IP.
  string public_ip = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// DatacenterConnector message describes a connector between the Source and
// Google Cloud, which is installed on a vmware datacenter (an OVA vm installed
// by the user) to connect the Datacenter to Google Cloud and support vm
// migration data transfer.
message DatacenterConnector {
  option (google.api.resource) = {
    type: "vmmigration.googleapis.com/DatacenterConnector"
    pattern: "projects/{project}/locations/{location}/sources/{source}/datacenterConnectors/{datacenter_connector}"
  };

  // The possible values of the state.
  enum State {
    // The state is unknown. This is used for API compatibility only and is not
    // used by the system.
    STATE_UNSPECIFIED = 0;

    // The state was not sampled by the health checks yet.
    PENDING = 1;

    // The source was sampled by health checks and is not available.
    OFFLINE = 2;

    // The source is available but might not be usable yet due to unvalidated
    // credentials or another reason. The credentials referred to are the ones
    // to the Source. The error message will contain further details.
    FAILED = 3;

    // The source exists and its credentials were verified.
    ACTIVE = 4;
  }

  // Output only. The time the connector was created (as an API call, not when
  // it was actually installed).
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last time the connector was updated with an API call.
  google.protobuf.Timestamp update_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The connector's name.
  string name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. A unique key for this connector. This key is internal to the OVA
  // connector and is supplied with its creation during the registration process
  // and can not be modified.
  string registration_id = 12 [(google.api.field_behavior) = IMMUTABLE];

  // The service account to use in the connector when communicating with the
  // cloud.
  string service_account = 5;

  // The version running in the DatacenterConnector. This is supplied by the OVA
  // connector during the registration process and can not be modified.
  string version = 6;

  // Output only. The communication channel between the datacenter connector and
  // Google Cloud.
  string bucket = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. State of the DatacenterConnector, as determined by the health
  // checks.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the state was last set.
  google.protobuf.Timestamp state_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Provides details on the state of the Datacenter Connector in
  // case of an error.
  google.rpc.Status error = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Appliance OVA version.
  // This is the OVA which is manually installed by the user and contains the
  // infrastructure for the automatically updatable components on the appliance.
  string appliance_infrastructure_version = 13
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Appliance last installed update bundle version.
  // This is the version of the automatically updatable components on the
  // appliance.
  string appliance_software_version = 14
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The available versions for updating this appliance.
  AvailableUpdates available_versions = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The status of the current / last upgradeAppliance operation.
  UpgradeStatus upgrade_status = 16 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// UpgradeStatus contains information about upgradeAppliance operation.
message UpgradeStatus {
  // The possible values of the state.
  enum State {
    // The state was not sampled by the health checks yet.
    STATE_UNSPECIFIED = 0;

    // The upgrade has started.
    RUNNING = 1;

    // The upgrade failed.
    FAILED = 2;

    // The upgrade finished successfully.
    SUCCEEDED = 3;
  }

  // The version to upgrade to.
  string version = 1;

  // The state of the upgradeAppliance operation.
  State state = 2;

  // Provides details on the state of the upgrade operation in case of an error.
  google.rpc.Status error = 3;

  // The time the operation was started.
  google.protobuf.Timestamp start_time = 4;

  // The version from which we upgraded.
  string previous_version = 5;
}

// Holds informatiom about the available versions for upgrade.
message AvailableUpdates {
  // The newest deployable version of the appliance.
  // The current appliance can't be updated into this version, and the owner
  // must manually deploy this OVA to a new appliance.
  ApplianceVersion new_deployable_appliance = 1;

  // The latest version for in place update.
  // The current appliance can be updated to this version using the API or m4c
  // CLI.
  ApplianceVersion in_place_update = 2;
}

// Describes an appliance version.
message ApplianceVersion {
  // The appliance version.
  string version = 1;

  // A link for downloading the version.
  string uri = 2;

  // Determine whether it's critical to upgrade the appliance to this version.
  bool critical = 3;

  // Link to a page that contains the version release notes.
  string release_notes_uri = 4;
}

// Request message for 'ListSources' request.
message ListSourcesRequest {
  // Required. The parent, which owns this collection of sources.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/Source"
    }
  ];

  // Optional. The maximum number of sources to return. The service may return
  // fewer than this value. If unspecified, at most 500 sources will be
  // returned. The maximum value is 1000; values above 1000 will be coerced to
  // 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. A page token, received from a previous `ListSources` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListSources` must
  // match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The filter request.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. the order by fields for the result.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for 'ListSources' request.
message ListSourcesResponse {
  // Output only. The list of sources response.
  repeated Source sources = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A token, which can be sent as `page_token` to retrieve the
  // next page. If this field is omitted, there are no subsequent pages.
  string next_page_token = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Locations that could not be reached.
  repeated string unreachable = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for 'GetSource' request.
message GetSourceRequest {
  // Required. The Source name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/Source"
    }
  ];
}

// Request message for 'CreateSource' request.
message CreateSourceRequest {
  // Required. The Source's parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/Source"
    }
  ];

  // Required. The source identifier.
  string source_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The create request body.
  Source source = 3 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4;
}

// Update message for 'UpdateSources' request.
message UpdateSourceRequest {
  // Field mask is used to specify the fields to be overwritten in the
  // Source resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1;

  // Required. The update request body.
  Source source = 2 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3;
}

// Request message for 'DeleteSource' request.
message DeleteSourceRequest {
  // Required. The Source name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/Source"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for
// [fetchInventory][google.cloud.vmmigration.v1.VmMigration.FetchInventory].
message FetchInventoryRequest {
  // Required. The name of the Source.
  string source = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/Source"
    }
  ];

  // If this flag is set to true, the source will be queried instead of using
  // cached results. Using this flag will make the call slower.
  bool force_refresh = 2;
}

// VmwareVmDetails describes a VM in vCenter.
message VmwareVmDetails {
  // Possible values for the power state of the VM.
  enum PowerState {
    // Power state is not specified.
    POWER_STATE_UNSPECIFIED = 0;

    // The VM is turned ON.
    ON = 1;

    // The VM is turned OFF.
    OFF = 2;

    // The VM is suspended. This is similar to hibernation or sleep mode.
    SUSPENDED = 3;
  }

  // Possible values for vm boot option.
  enum BootOption {
    // The boot option is unknown.
    BOOT_OPTION_UNSPECIFIED = 0;

    // The boot option is EFI.
    EFI = 1;

    // The boot option is BIOS.
    BIOS = 2;
  }

  // The VM's id in the source (note that this is not the MigratingVm's id).
  // This is the moref id of the VM.
  string vm_id = 1;

  // The id of the vCenter's datacenter this VM is contained in.
  string datacenter_id = 2;

  // The descriptive name of the vCenter's datacenter this VM is contained in.
  string datacenter_description = 3;

  // The unique identifier of the VM in vCenter.
  string uuid = 4;

  // The display name of the VM. Note that this is not necessarily unique.
  string display_name = 5;

  // The power state of the VM at the moment list was taken.
  PowerState power_state = 6;

  // The number of cpus in the VM.
  int32 cpu_count = 7;

  // The size of the memory of the VM in MB.
  int32 memory_mb = 8;

  // The number of disks the VM has.
  int32 disk_count = 9;

  // The total size of the storage allocated to the VM in MB.
  int64 committed_storage_mb = 12;

  // The VM's OS. See for example
  // https://vdc-repo.vmware.com/vmwb-repository/dcr-public/da47f910-60ac-438b-8b9b-6122f4d14524/16b7274a-bf8b-4b4c-a05e-746f2aa93c8c/doc/vim.vm.GuestOsDescriptor.GuestOsIdentifier.html
  // for types of strings this might hold.
  string guest_description = 11;

  // Output only. The VM Boot Option.
  BootOption boot_option = 13 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// AwsVmDetails describes a VM in AWS.
message AwsVmDetails {
  // Possible values for the power state of the VM.
  enum PowerState {
    // Power state is not specified.
    POWER_STATE_UNSPECIFIED = 0;

    // The VM is turned on.
    ON = 1;

    // The VM is turned off.
    OFF = 2;

    // The VM is suspended. This is similar to hibernation or sleep
    // mode.
    SUSPENDED = 3;

    // The VM is starting.
    PENDING = 4;
  }

  // The possible values for the vm boot option.
  enum BootOption {
    // The boot option is unknown.
    BOOT_OPTION_UNSPECIFIED = 0;

    // The boot option is UEFI.
    EFI = 1;

    // The boot option is LEGACY-BIOS.
    BIOS = 2;
  }

  // Possible values for the virtualization types of the VM.
  enum VmVirtualizationType {
    // The virtualization type is unknown.
    VM_VIRTUALIZATION_TYPE_UNSPECIFIED = 0;

    // The virtualziation type is HVM.
    HVM = 1;

    // The virtualziation type is PARAVIRTUAL.
    PARAVIRTUAL = 2;
  }

  // Possible values for the architectures of the VM.
  enum VmArchitecture {
    // The architecture is unknown.
    VM_ARCHITECTURE_UNSPECIFIED = 0;

    // The architecture is I386.
    I386 = 1;

    // The architecture is X86_64.
    X86_64 = 2;

    // The architecture is ARM64.
    ARM64 = 3;

    // The architecture is X86_64_MAC.
    X86_64_MAC = 4;
  }

  // The VM ID in AWS.
  string vm_id = 1;

  // The display name of the VM. Note that this value is not necessarily unique.
  string display_name = 2;

  // The id of the AWS's source this VM is connected to.
  string source_id = 3;

  // The descriptive name of the AWS's source this VM is connected to.
  string source_description = 4;

  // Output only. The power state of the VM at the moment list was taken.
  PowerState power_state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The number of cpus the VM has.
  int32 cpu_count = 6;

  // The memory size of the VM in MB.
  int32 memory_mb = 7;

  // The number of disks the VM has.
  int32 disk_count = 8;

  // The total size of the storage allocated to the VM in MB.
  int64 committed_storage_mb = 9;

  // The VM's OS.
  string os_description = 10;

  // The VM Boot Option.
  BootOption boot_option = 11;

  // The instance type of the VM.
  string instance_type = 12;

  // The VPC ID the VM belongs to.
  string vpc_id = 13;

  // The security groups the VM belongs to.
  repeated AwsSecurityGroup security_groups = 14;

  // The tags of the VM.
  map<string, string> tags = 15;

  // The AWS zone of the VM.
  string zone = 16;

  // The virtualization type.
  VmVirtualizationType virtualization_type = 17;

  // The CPU architecture.
  VmArchitecture architecture = 18;
}

// AwsSecurityGroup describes a security group of an AWS VM.
message AwsSecurityGroup {
  // The AWS security group id.
  string id = 1;

  // The AWS security group name.
  string name = 2;
}

// VmwareVmsDetails describes VMs in vCenter.
message VmwareVmsDetails {
  // The details of the vmware VMs.
  repeated VmwareVmDetails details = 1;
}

// AWSVmsDetails describes VMs in AWS.
message AwsVmsDetails {
  // The details of the AWS VMs.
  repeated AwsVmDetails details = 1;
}

// Response message for
// [fetchInventory][google.cloud.vmmigration.v1.VmMigration.FetchInventory].
message FetchInventoryResponse {
  oneof SourceVms {
    // The description of the VMs in a Source of type Vmware.
    VmwareVmsDetails vmware_vms = 1;

    // The description of the VMs in a Source of type AWS.
    AwsVmsDetails aws_vms = 3;
  }

  // Output only. The timestamp when the source was last queried (if the result
  // is from the cache).
  google.protobuf.Timestamp update_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Utilization report details the utilization (CPU, memory, etc.) of selected
// source VMs.
message UtilizationReport {
  option (google.api.resource) = {
    type: "vmmigration.googleapis.com/UtilizationReport"
    pattern: "projects/{project}/locations/{location}/sources/{source}/utilizationReports/{utilization_report}"
  };

  // Utilization report state.
  enum State {
    // The state is unknown. This value is not in use.
    STATE_UNSPECIFIED = 0;

    // The report is in the making.
    CREATING = 1;

    // Report creation completed successfully.
    SUCCEEDED = 2;

    // Report creation failed.
    FAILED = 3;
  }

  // Report time frame options.
  enum TimeFrame {
    // The time frame was not specified and will default to WEEK.
    TIME_FRAME_UNSPECIFIED = 0;

    // One week.
    WEEK = 1;

    // One month.
    MONTH = 2;

    // One year.
    YEAR = 3;
  }

  // Output only. The report unique name.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The report display name, as assigned by the user.
  string display_name = 2;

  // Output only. Current state of the report.
  State state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the state was last set.
  google.protobuf.Timestamp state_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Provides details on the state of the report in case of an
  // error.
  google.rpc.Status error = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the report was created (this refers to the time of
  // the request, not the time the report creation completed).
  google.protobuf.Timestamp create_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Time frame of the report.
  TimeFrame time_frame = 7;

  // Output only. The point in time when the time frame ends. Notice that the
  // time frame is counted backwards. For instance if the "frame_end_time" value
  // is 2021/01/20 and the time frame is WEEK then the report covers the week
  // between 2021/01/20 and 2021/01/14.
  google.protobuf.Timestamp frame_end_time = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Total number of VMs included in the report.
  int32 vm_count = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // List of utilization information per VM.
  // When sent as part of the request, the "vm_id" field is used in order to
  // specify which VMs to include in the report. In that case all other fields
  // are ignored.
  repeated VmUtilizationInfo vms = 10;
}

// Utilization information of a single VM.
message VmUtilizationInfo {
  oneof VmDetails {
    // The description of the VM in a Source of type Vmware.
    VmwareVmDetails vmware_vm_details = 1;
  }

  // The VM's ID in the source.
  string vm_id = 3;

  // Utilization metrics for this VM.
  VmUtilizationMetrics utilization = 2;
}

// Utilization metrics values for a single VM.
message VmUtilizationMetrics {
  // Max CPU usage, percent.
  int32 cpu_max_percent = 9;

  // Average CPU usage, percent.
  int32 cpu_average_percent = 10;

  // Max memory usage, percent.
  int32 memory_max_percent = 11;

  // Average memory usage, percent.
  int32 memory_average_percent = 12;

  // Max disk IO rate, in kilobytes per second.
  int64 disk_io_rate_max_kbps = 13;

  // Average disk IO rate, in kilobytes per second.
  int64 disk_io_rate_average_kbps = 14;

  // Max network throughput (combined transmit-rates and receive-rates), in
  // kilobytes per second.
  int64 network_throughput_max_kbps = 15;

  // Average network throughput (combined transmit-rates and receive-rates), in
  // kilobytes per second.
  int64 network_throughput_average_kbps = 16;
}

// Request message for 'ListUtilizationReports' request.
message ListUtilizationReportsRequest {
  // Required. The Utilization Reports parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/UtilizationReport"
    }
  ];

  // Optional. The level of details of each report.
  // Defaults to BASIC.
  UtilizationReportView view = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The maximum number of reports to return. The service may return
  // fewer than this value. If unspecified, at most 500 reports will be
  // returned. The maximum value is 1000; values above 1000 will be coerced to
  // 1000.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Required. A page token, received from a previous `ListUtilizationReports`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListUtilizationReports`
  // must match the call that provided the page token.
  string page_token = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. The filter request.
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. the order by fields for the result.
  string order_by = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for 'ListUtilizationReports' request.
message ListUtilizationReportsResponse {
  // Output only. The list of reports.
  repeated UtilizationReport utilization_reports = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A token, which can be sent as `page_token` to retrieve the
  // next page. If this field is omitted, there are no subsequent pages.
  string next_page_token = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Locations that could not be reached.
  repeated string unreachable = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for 'GetUtilizationReport' request.
message GetUtilizationReportRequest {
  // Required. The Utilization Report name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/UtilizationReport"
    }
  ];

  // Optional. The level of details of the report.
  // Defaults to FULL
  UtilizationReportView view = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for 'CreateUtilizationReport' request.
message CreateUtilizationReportRequest {
  // Required. The Utilization Report's parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/UtilizationReport"
    }
  ];

  // Required. The report to create.
  UtilizationReport utilization_report = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the report, which will become the final
  // component of the reports's resource name.
  //
  // This value maximum length is 63 characters, and valid characters
  // are /[a-z][0-9]-/. It must start with an english letter and must not
  // end with a hyphen.
  string utilization_report_id = 3 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4;
}

// Request message for 'DeleteUtilizationReport' request.
message DeleteUtilizationReportRequest {
  // Required. The Utilization Report name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/UtilizationReport"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for 'ListDatacenterConnectors' request.
message ListDatacenterConnectorsResponse {
  // Output only. The list of sources response.
  repeated DatacenterConnector datacenter_connectors = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A token, which can be sent as `page_token` to retrieve the
  // next page. If this field is omitted, there are no subsequent pages.
  string next_page_token = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Locations that could not be reached.
  repeated string unreachable = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for 'GetDatacenterConnector' request.
message GetDatacenterConnectorRequest {
  // Required. The name of the DatacenterConnector.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/DatacenterConnector"
    }
  ];
}

// Request message for 'CreateDatacenterConnector' request.
message CreateDatacenterConnectorRequest {
  // Required. The DatacenterConnector's parent.
  // Required. The Source in where the new DatacenterConnector will be created.
  // For example:
  // `projects/my-project/locations/us-central1/sources/my-source`
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/DatacenterConnector"
    }
  ];

  // Required. The datacenterConnector identifier.
  string datacenter_connector_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The create request body.
  DatacenterConnector datacenter_connector = 3
      [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4;
}

// Request message for 'DeleteDatacenterConnector' request.
message DeleteDatacenterConnectorRequest {
  // Required. The DatacenterConnector name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/DatacenterConnector"
    }
  ];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2;
}

// Request message for 'UpgradeAppliance' request.
message UpgradeApplianceRequest {
  // Required. The DatacenterConnector name.
  string datacenter_connector = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/DatacenterConnector"
    }
  ];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2;
}

// Response message for 'UpgradeAppliance' request.
message UpgradeApplianceResponse {}

// Request message for 'ListDatacenterConnectors' request.
message ListDatacenterConnectorsRequest {
  // Required. The parent, which owns this collection of connectors.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/DatacenterConnector"
    }
  ];

  // Optional. The maximum number of connectors to return. The service may
  // return fewer than this value. If unspecified, at most 500 sources will be
  // returned. The maximum value is 1000; values above 1000 will be coerced to
  // 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. A page token, received from a previous `ListDatacenterConnectors`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `ListDatacenterConnectors` must match the call that provided the page
  // token.
  string page_token = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The filter request.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. the order by fields for the result.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// ComputeEngineTargetDefaults is a collection of details for creating a VM in a
// target Compute Engine project.
message ComputeEngineTargetDefaults {
  // The name of the VM to create.
  string vm_name = 1;

  // The full path of the resource of type TargetProject which represents the
  // Compute Engine project in which to create this VM.
  string target_project = 2 [(google.api.resource_reference) = {
    type: "vmmigration.googleapis.com/TargetProject"
  }];

  // The zone in which to create the VM.
  string zone = 3;

  // The machine type series to create the VM with.
  string machine_type_series = 4;

  // The machine type to create the VM with.
  string machine_type = 5;

  // A map of network tags to associate with the VM.
  repeated string network_tags = 6;

  // List of NICs connected to this VM.
  repeated NetworkInterface network_interfaces = 7;

  // The service account to associate the VM with.
  string service_account = 8;

  // The disk type to use in the VM.
  ComputeEngineDiskType disk_type = 9;

  // A map of labels to associate with the VM.
  map<string, string> labels = 10;

  // The license type to use in OS adaptation.
  ComputeEngineLicenseType license_type = 11;

  // Output only. The OS license returned from the adaptation module report.
  AppliedLicense applied_license = 12
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Compute instance scheduling information (if empty default is used).
  ComputeScheduling compute_scheduling = 13;

  // Defines whether the instance has Secure Boot enabled.
  // This can be set to true only if the vm boot option is EFI.
  bool secure_boot = 14;

  // Output only. The VM Boot Option, as set in the source vm.
  ComputeEngineBootOption boot_option = 15
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The metadata key/value pairs to assign to the VM.
  map<string, string> metadata = 16;

  // Additional licenses to assign to the VM.
  repeated string additional_licenses = 17;

  // The hostname to assign to the VM.
  string hostname = 18;
}

// ComputeEngineTargetDetails is a collection of details for creating a VM in a
// target Compute Engine project.
message ComputeEngineTargetDetails {
  // The name of the VM to create.
  string vm_name = 1;

  // The Google Cloud target project ID or project name.
  string project = 2;

  // The zone in which to create the VM.
  string zone = 3;

  // The machine type series to create the VM with.
  string machine_type_series = 4;

  // The machine type to create the VM with.
  string machine_type = 5;

  // A map of network tags to associate with the VM.
  repeated string network_tags = 6;

  // List of NICs connected to this VM.
  repeated NetworkInterface network_interfaces = 7;

  // The service account to associate the VM with.
  string service_account = 8;

  // The disk type to use in the VM.
  ComputeEngineDiskType disk_type = 9;

  // A map of labels to associate with the VM.
  map<string, string> labels = 10;

  // The license type to use in OS adaptation.
  ComputeEngineLicenseType license_type = 11;

  // The OS license returned from the adaptation module report.
  AppliedLicense applied_license = 12;

  // Compute instance scheduling information (if empty default is used).
  ComputeScheduling compute_scheduling = 13;

  // Defines whether the instance has Secure Boot enabled.
  // This can be set to true only if the vm boot option is EFI.
  bool secure_boot = 14;

  // The VM Boot Option, as set in the source vm.
  ComputeEngineBootOption boot_option = 15;

  // The metadata key/value pairs to assign to the VM.
  map<string, string> metadata = 16;

  // Additional licenses to assign to the VM.
  repeated string additional_licenses = 17;

  // The hostname to assign to the VM.
  string hostname = 18;
}

// NetworkInterface represents a NIC of a VM.
message NetworkInterface {
  // The network to connect the NIC to.
  string network = 1;

  // The subnetwork to connect the NIC to.
  string subnetwork = 2;

  // The internal IP to define in the NIC.
  // The formats accepted are: `ephemeral` \ ipv4 address \ a named address
  // resource full path.
  string internal_ip = 3;

  // The external IP to define in the NIC.
  string external_ip = 4;
}

// AppliedLicense holds the license data returned by adaptation module report.
message AppliedLicense {
  // License types used in OS adaptation.
  enum Type {
    // Unspecified license for the OS.
    TYPE_UNSPECIFIED = 0;

    // No license available for the OS.
    NONE = 1;

    // The license type is Pay As You Go license type.
    PAYG = 2;

    // The license type is Bring Your Own License type.
    BYOL = 3;
  }

  // The license type that was used in OS adaptation.
  Type type = 1;

  // The OS license returned from the adaptation module's report.
  string os_license = 2;
}

// Node Affinity: the configuration of desired nodes onto which this Instance
// could be scheduled. Based on
// https://cloud.google.com/compute/docs/reference/rest/v1/instances/setScheduling
message SchedulingNodeAffinity {
  // Possible types of node selection operators. Valid operators are IN for
  // affinity and NOT_IN for anti-affinity.
  enum Operator {
    // An unknown, unexpected behavior.
    OPERATOR_UNSPECIFIED = 0;

    // The node resource group should be in these resources affinity.
    IN = 1;

    // The node resource group should not be in these resources affinity.
    NOT_IN = 2;
  }

  // The label key of Node resource to reference.
  string key = 1;

  // The operator to use for the node resources specified in the `values`
  // parameter.
  Operator operator = 2;

  // Corresponds to the label values of Node resource.
  repeated string values = 3;
}

// Scheduling information for VM on maintenance/restart behaviour and
// node allocation in sole tenant nodes.
message ComputeScheduling {
  enum OnHostMaintenance {
    // An unknown, unexpected behavior.
    ON_HOST_MAINTENANCE_UNSPECIFIED = 0;

    // Terminate the instance when the host machine undergoes maintenance.
    TERMINATE = 1;

    // Migrate the instance when the host machine undergoes maintenance.
    MIGRATE = 2;
  }

  // Defines whether the Instance should be automatically restarted whenever
  // it is terminated by Compute Engine (not terminated by user).
  enum RestartType {
    // Unspecified behavior. This will use the default.
    RESTART_TYPE_UNSPECIFIED = 0;

    // The Instance should be automatically restarted whenever it is
    // terminated by Compute Engine.
    AUTOMATIC_RESTART = 1;

    // The Instance isn't automatically restarted whenever it is
    // terminated by Compute Engine.
    NO_AUTOMATIC_RESTART = 2;
  }

  // How the instance should behave when the host machine undergoes
  // maintenance that may temporarily impact instance performance.
  OnHostMaintenance on_host_maintenance = 1;

  // Whether the Instance should be automatically restarted whenever it is
  // terminated by Compute Engine (not terminated by user).
  // This configuration is identical to `automaticRestart` field in Compute
  // Engine create instance under scheduling.
  // It was changed to an enum (instead of a boolean) to match the default
  // value in Compute Engine which is automatic restart.
  RestartType restart_type = 5;

  // A set of node affinity and anti-affinity configurations for sole tenant
  // nodes.
  repeated SchedulingNodeAffinity node_affinities = 3;

  // The minimum number of virtual CPUs this instance will consume when
  // running on a sole-tenant node. Ignored if no node_affinites are
  // configured.
  int32 min_node_cpus = 4;
}

// A policy for scheduling replications.
message SchedulePolicy {
  // The idle duration between replication stages.
  google.protobuf.Duration idle_duration = 1;

  // A flag to indicate whether to skip OS adaptation during the replication
  // sync. OS adaptation is a process where the VM's operating system undergoes
  // changes and adaptations to fully function on Compute Engine.
  bool skip_os_adaptation = 2;
}

// Request message for 'CreateMigratingVm' request.
message CreateMigratingVmRequest {
  // Required. The MigratingVm's parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/MigratingVm"
    }
  ];

  // Required. The migratingVm identifier.
  string migrating_vm_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The create request body.
  MigratingVm migrating_vm = 3 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4;
}

// Request message for 'LisMigratingVmsRequest' request.
message ListMigratingVmsRequest {
  // Required. The parent, which owns this collection of MigratingVms.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/MigratingVm"
    }
  ];

  // Optional. The maximum number of migrating VMs to return. The service may
  // return fewer than this value. If unspecified, at most 500 migrating VMs
  // will be returned. The maximum value is 1000; values above 1000 will be
  // coerced to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. A page token, received from a previous `ListMigratingVms` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListMigratingVms`
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The filter request.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. the order by fields for the result.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The level of details of each migrating VM.
  MigratingVmView view = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for 'ListMigratingVms' request.
message ListMigratingVmsResponse {
  // Output only. The list of Migrating VMs response.
  repeated MigratingVm migrating_vms = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A token, which can be sent as `page_token` to retrieve the
  // next page. If this field is omitted, there are no subsequent pages.
  string next_page_token = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Locations that could not be reached.
  repeated string unreachable = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for 'GetMigratingVm' request.
message GetMigratingVmRequest {
  // Required. The name of the MigratingVm.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/MigratingVm"
    }
  ];

  // Optional. The level of details of the migrating VM.
  MigratingVmView view = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for 'UpdateMigratingVm' request.
message UpdateMigratingVmRequest {
  // Field mask is used to specify the fields to be overwritten in the
  // MigratingVm resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1;

  // Required. The update request body.
  MigratingVm migrating_vm = 2 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3;
}

// Request message for 'DeleteMigratingVm' request.
message DeleteMigratingVmRequest {
  // Required. The name of the MigratingVm.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/MigratingVm"
    }
  ];
}

// Request message for 'StartMigrationRequest' request.
message StartMigrationRequest {
  // Required. The name of the MigratingVm.
  string migrating_vm = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/MigratingVm"
    }
  ];
}

// Response message for 'StartMigration' request.
message StartMigrationResponse {}

// Request message for 'PauseMigration' request.
message PauseMigrationRequest {
  // Required. The name of the MigratingVm.
  string migrating_vm = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/MigratingVm"
    }
  ];
}

// Response message for 'PauseMigration' request.
message PauseMigrationResponse {}

// Request message for 'ResumeMigration' request.
message ResumeMigrationRequest {
  // Required. The name of the MigratingVm.
  string migrating_vm = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/MigratingVm"
    }
  ];
}

// Response message for 'ResumeMigration' request.
message ResumeMigrationResponse {}

// Request message for 'FinalizeMigration' request.
message FinalizeMigrationRequest {
  // Required. The name of the MigratingVm.
  string migrating_vm = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/MigratingVm"
    }
  ];
}

// Response message for 'FinalizeMigration' request.
message FinalizeMigrationResponse {}

// TargetProject message represents a target Compute Engine project for a
// migration or a clone.
message TargetProject {
  option (google.api.resource) = {
    type: "vmmigration.googleapis.com/TargetProject"
    pattern: "projects/{project}/locations/{location}/targetProjects/{target_project}"
  };

  // Output only. The name of the target project.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // The target project ID (number) or project name.
  string project = 2;

  // The target project's description.
  string description = 3;

  // Output only. The time this target project resource was created (not related
  // to when the Compute Engine project it points to was created).
  google.protobuf.Timestamp create_time = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last time the target project resource was updated.
  google.protobuf.Timestamp update_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for 'GetTargetProject' call.
message GetTargetProjectRequest {
  // Required. The TargetProject name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/TargetProject"
    }
  ];
}

// Request message for 'ListTargetProjects' call.
message ListTargetProjectsRequest {
  // Required. The parent, which owns this collection of targets.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/TargetProject"
    }
  ];

  // Optional. The maximum number of targets to return. The service may return
  // fewer than this value. If unspecified, at most 500 targets will be
  // returned. The maximum value is 1000; values above 1000 will be coerced to
  // 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. A page token, received from a previous `ListTargets` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListTargets` must
  // match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The filter request.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. the order by fields for the result.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for 'ListTargetProjects' call.
message ListTargetProjectsResponse {
  // Output only. The list of target response.
  repeated TargetProject target_projects = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A token, which can be sent as `page_token` to retrieve the
  // next page. If this field is omitted, there are no subsequent pages.
  string next_page_token = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Locations that could not be reached.
  repeated string unreachable = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for 'CreateTargetProject' request.
message CreateTargetProjectRequest {
  // Required. The TargetProject's parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/TargetProject"
    }
  ];

  // Required. The target_project identifier.
  string target_project_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The create request body.
  TargetProject target_project = 3 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4;
}

// Update message for 'UpdateTargetProject' request.
message UpdateTargetProjectRequest {
  // Field mask is used to specify the fields to be overwritten in the
  // TargetProject resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1;

  // Required. The update request body.
  TargetProject target_project = 2 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3;
}

// Request message for 'DeleteTargetProject' request.
message DeleteTargetProjectRequest {
  // Required. The TargetProject name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/TargetProject"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Describes message for 'Group' resource. The Group is a collections of several
// MigratingVms.
message Group {
  option (google.api.resource) = {
    type: "vmmigration.googleapis.com/Group"
    pattern: "projects/{project}/locations/{location}/groups/{group}"
  };

  // Output only. The Group name.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The create time timestamp.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The update time timestamp.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // User-provided description of the group.
  string description = 4;

  // Display name is a user defined name for this group which can be updated.
  string display_name = 5;
}

// Request message for 'ListGroups' request.
message ListGroupsRequest {
  // Required. The parent, which owns this collection of groups.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/Group"
    }
  ];

  // Optional. The maximum number of groups to return. The service may return
  // fewer than this value. If unspecified, at most 500 groups will be
  // returned. The maximum value is 1000; values above 1000 will be coerced to
  // 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. A page token, received from a previous `ListGroups` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListGroups` must
  // match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The filter request.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. the order by fields for the result.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for 'ListGroups' request.
message ListGroupsResponse {
  // Output only. The list of groups response.
  repeated Group groups = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A token, which can be sent as `page_token` to retrieve the
  // next page. If this field is omitted, there are no subsequent pages.
  string next_page_token = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Locations that could not be reached.
  repeated string unreachable = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for 'GetGroup' request.
message GetGroupRequest {
  // Required. The group name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/Group"
    }
  ];
}

// Request message for 'CreateGroup' request.
message CreateGroupRequest {
  // Required. The Group's parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/Group"
    }
  ];

  // Required. The group identifier.
  string group_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The create request body.
  Group group = 3 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4;
}

// Update message for 'UpdateGroups' request.
message UpdateGroupRequest {
  // Field mask is used to specify the fields to be overwritten in the
  // Group resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1;

  // Required. The update request body.
  Group group = 2 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3;
}

// Request message for 'DeleteGroup' request.
message DeleteGroupRequest {
  // Required. The Group name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/Group"
    }
  ];

  // Optional. A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for 'AddGroupMigration' request.
message AddGroupMigrationRequest {
  // Required. The full path name of the Group to add to.
  string group = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/Group"
    }
  ];

  // The full path name of the MigratingVm to add.
  string migrating_vm = 2 [(google.api.resource_reference) = {
    type: "vmmigration.googleapis.com/MigratingVm"
  }];
}

// Response message for 'AddGroupMigration' request.
message AddGroupMigrationResponse {}

// Request message for 'RemoveMigration' request.
message RemoveGroupMigrationRequest {
  // Required. The name of the Group.
  string group = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/Group"
    }
  ];

  // The MigratingVm to remove.
  string migrating_vm = 2 [(google.api.resource_reference) = {
    type: "vmmigration.googleapis.com/MigratingVm"
  }];
}

// Response message for 'RemoveMigration' request.
message RemoveGroupMigrationResponse {}

// Request message for 'CreateCutoverJob' request.
message CreateCutoverJobRequest {
  // Required. The Cutover's parent.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/CutoverJob"
    }
  ];

  // Required. The cutover job identifier.
  string cutover_job_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The cutover request body.
  CutoverJob cutover_job = 3 [(google.api.field_behavior) = REQUIRED];

  // A request ID to identify requests. Specify a unique request ID
  // so that if you must retry your request, the server will know to ignore
  // the request if it has already been completed. The server will guarantee
  // that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4;
}

// Request message for 'CancelCutoverJob' request.
message CancelCutoverJobRequest {
  // Required. The cutover job id
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/CutoverJob"
    }
  ];
}

// Response message for 'CancelCutoverJob' request.
message CancelCutoverJobResponse {}

// Request message for 'ListCutoverJobsRequest' request.
message ListCutoverJobsRequest {
  // Required. The parent, which owns this collection of migrating VMs.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/CutoverJob"
    }
  ];

  // Optional. The maximum number of cutover jobs to return. The service may
  // return fewer than this value. If unspecified, at most 500 cutover jobs will
  // be returned. The maximum value is 1000; values above 1000 will be coerced
  // to 1000.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. A page token, received from a previous `ListCutoverJobs` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListCutoverJobs` must
  // match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The filter request.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. the order by fields for the result.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for 'ListCutoverJobs' request.
message ListCutoverJobsResponse {
  // Output only. The list of cutover jobs response.
  repeated CutoverJob cutover_jobs = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A token, which can be sent as `page_token` to retrieve the
  // next page. If this field is omitted, there are no subsequent pages.
  string next_page_token = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Locations that could not be reached.
  repeated string unreachable = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for 'GetCutoverJob' request.
message GetCutoverJobRequest {
  // Required. The name of the CutoverJob.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/CutoverJob"
    }
  ];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represents migration resource error information that can be used with
// google.rpc.Status message. MigrationError is used to present the user with
// error information in migration operations.
message MigrationError {
  // Represents resource error codes.
  enum ErrorCode {
    // Default value. This value is not used.
    ERROR_CODE_UNSPECIFIED = 0;

    // Migrate for Compute encountered an unknown error.
    UNKNOWN_ERROR = 1;

    // Migrate for Compute encountered an error while validating replication
    // source health.
    SOURCE_VALIDATION_ERROR = 2;

    // Migrate for Compute encountered an error during source data operation.
    SOURCE_REPLICATION_ERROR = 3;

    // Migrate for Compute encountered an error during target data operation.
    TARGET_REPLICATION_ERROR = 4;

    // Migrate for Compute encountered an error during OS adaptation.
    OS_ADAPTATION_ERROR = 5;

    // Migrate for Compute encountered an error in clone operation.
    CLONE_ERROR = 6;

    // Migrate for Compute encountered an error in cutover operation.
    CUTOVER_ERROR = 7;

    // Migrate for Compute encountered an error during utilization report
    // creation.
    UTILIZATION_REPORT_ERROR = 8;

    // Migrate for Compute encountered an error during appliance upgrade.
    APPLIANCE_UPGRADE_ERROR = 9;
  }

  // Output only. The error code.
  ErrorCode code = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The localized error message.
  google.rpc.LocalizedMessage error_message = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Suggested action for solving the error.
  google.rpc.LocalizedMessage action_item = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. URL(s) pointing to additional information on handling the
  // current error.
  repeated google.rpc.Help.Link help_links = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the error occurred.
  google.protobuf.Timestamp error_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Represent the source AWS VM details.
message AwsSourceVmDetails {
  // Possible values for AWS VM firmware.
  enum Firmware {
    // The firmware is unknown.
    FIRMWARE_UNSPECIFIED = 0;

    // The firmware is EFI.
    EFI = 1;

    // The firmware is BIOS.
    BIOS = 2;
  }

  // The firmware type of the source VM.
  Firmware firmware = 1;

  // The total size of the disks being migrated in bytes.
  int64 committed_storage_bytes = 2;
}

// Request message for 'LisReplicationCyclesRequest' request.
message ListReplicationCyclesRequest {
  // Required. The parent, which owns this collection of ReplicationCycles.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "vmmigration.googleapis.com/ReplicationCycle"
    }
  ];

  // Optional. The maximum number of replication cycles to return. The service
  // may return fewer than this value. If unspecified, at most 100 migrating VMs
  // will be returned. The maximum value is 100; values above 100 will be
  // coerced to 100.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. A page token, received from a previous `ListReplicationCycles`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListReplicationCycles`
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. The filter request.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. the order by fields for the result.
  string order_by = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for 'ListReplicationCycles' request.
message ListReplicationCyclesResponse {
  // Output only. The list of replication cycles response.
  repeated ReplicationCycle replication_cycles = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A token, which can be sent as `page_token` to retrieve the
  // next page. If this field is omitted, there are no subsequent pages.
  string next_page_token = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Locations that could not be reached.
  repeated string unreachable = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for 'GetReplicationCycle' request.
message GetReplicationCycleRequest {
  // Required. The name of the ReplicationCycle.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "vmmigration.googleapis.com/ReplicationCycle"
    }
  ];
}
