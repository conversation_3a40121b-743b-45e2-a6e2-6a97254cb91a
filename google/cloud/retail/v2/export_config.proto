// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.retail.v2;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.Retail.V2";
option go_package = "cloud.google.com/go/retail/apiv2/retailpb;retailpb";
option java_multiple_files = true;
option java_outer_classname = "ExportConfigProto";
option java_package = "com.google.cloud.retail.v2";
option objc_class_prefix = "RETAIL";
option php_namespace = "Google\\Cloud\\Retail\\V2";
option ruby_package = "Google::Cloud::Retail::V2";

// The output configuration setting.
message OutputConfig {
  // The Google Cloud Storage output destination configuration.
  message GcsDestination {
    // Required. The output uri prefix for saving output data to json files.
    // Some mapping examples are as follows:
    // output_uri_prefix         sample output(assuming the object is foo.json)
    // ========================  =============================================
    // gs://bucket/              gs://bucket/foo.json
    // gs://bucket/folder/       gs://bucket/folder/foo.json
    // gs://bucket/folder/item_  gs://bucket/folder/item_foo.json
    string output_uri_prefix = 1 [(google.api.field_behavior) = REQUIRED];
  }

  // The BigQuery output destination configuration.
  message BigQueryDestination {
    // Required. The ID of a BigQuery Dataset.
    string dataset_id = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The prefix of exported BigQuery tables.
    string table_id_prefix = 2 [(google.api.field_behavior) = REQUIRED];

    // Required. Describes the table type. The following values are supported:
    //
    // * `table`: A BigQuery native table.
    // * `view`: A virtual table defined by a SQL query.
    string table_type = 3 [(google.api.field_behavior) = REQUIRED];
  }

  // The configuration of destination for holding output data.
  oneof destination {
    // The Google Cloud Storage location where the output is to be written to.
    GcsDestination gcs_destination = 1;

    // The BigQuery location where the output is to be written to.
    BigQueryDestination bigquery_destination = 2;
  }
}

// Configuration of destination for Export related errors.
message ExportErrorsConfig {
  // Required. Errors destination.
  oneof destination {
    // Google Cloud Storage path for import errors. This must be an empty,
    // existing Cloud Storage bucket. Export errors will be written to a file in
    // this bucket, one per line, as a JSON-encoded
    // `google.rpc.Status` message.
    string gcs_prefix = 1;
  }
}

// Request message for the `ExportAnalyticsMetrics` method.
message ExportAnalyticsMetricsRequest {
  // Required. Full resource name of the parent catalog.
  // Expected format: `projects/*/locations/*/catalogs/*`
  string catalog = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The output location of the data.
  OutputConfig output_config = 2 [(google.api.field_behavior) = REQUIRED];

  // A filtering expression to specify restrictions on returned metrics.
  // The expression is a sequence of terms. Each term applies a restriction to
  // the returned metrics. Use this expression to restrict results to a
  // specific time range.
  //
  //   Currently we expect only one types of fields:
  //
  //    * `timestamp`: This can be specified twice, once with a
  //      less than operator and once with a greater than operator. The
  //      `timestamp` restriction should result in one, contiguous, valid,
  //      `timestamp` range.
  //
  //   Some examples of valid filters expressions:
  //
  //   * Example 1: `timestamp > "2012-04-23T18:25:43.511Z"
  //             timestamp < "2012-04-23T18:30:43.511Z"`
  //   * Example 2: `timestamp > "2012-04-23T18:25:43.511Z"`
  string filter = 3;
}

// Metadata related to the progress of the Export operation. This is
// returned by the google.longrunning.Operation.metadata field.
message ExportMetadata {
  // Operation create time.
  google.protobuf.Timestamp create_time = 1;

  // Operation last update time. If the operation is done, this is also the
  // finish time.
  google.protobuf.Timestamp update_time = 2;
}

// Response of the ExportAnalyticsMetricsRequest. If the long running
// operation was successful, then this message is returned by the
// google.longrunning.Operations.response field if the operation was successful.
message ExportAnalyticsMetricsResponse {
  // A sample of errors encountered while processing the request.
  repeated google.rpc.Status error_samples = 1;

  // This field is never set.
  ExportErrorsConfig errors_config = 2;

  // Output result indicating where the data were exported to.
  OutputResult output_result = 3;
}

// Output result that stores the information about where the exported data is
// stored.
message OutputResult {
  // The BigQuery location where the result is stored.
  repeated BigQueryOutputResult bigquery_result = 1;

  // The Google Cloud Storage location where the result is stored.
  repeated GcsOutputResult gcs_result = 2;
}

// A BigQuery output result.
message BigQueryOutputResult {
  // The ID of a BigQuery Dataset.
  string dataset_id = 1;

  // The ID of a BigQuery Table.
  string table_id = 2;
}

// A Gcs output result.
message GcsOutputResult {
  // The uri of Gcs output
  string output_uri = 1;
}
