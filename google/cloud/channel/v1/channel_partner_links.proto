// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.channel.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/channel/v1/common.proto";
import "google/protobuf/timestamp.proto";

option go_package = "cloud.google.com/go/channel/apiv1/channelpb;channelpb";
option java_multiple_files = true;
option java_outer_classname = "ChannelPartnerLinksProto";
option java_package = "com.google.cloud.channel.v1";

// The level of granularity the
// [ChannelPartnerLink][google.cloud.channel.v1.ChannelPartnerLink] will
// display.
enum ChannelPartnerLinkView {
  // The default / unset value.
  // The API will default to the BASIC view.
  UNSPECIFIED = 0;

  // Includes all fields except the
  // [ChannelPartnerLink.channel_partner_cloud_identity_info][google.cloud.channel.v1.ChannelPartnerLink.channel_partner_cloud_identity_info].
  BASIC = 1;

  // Includes all fields.
  FULL = 2;
}

// ChannelPartnerLinkState represents state of a channel partner link.
enum ChannelPartnerLinkState {
  // Not used.
  CHANNEL_PARTNER_LINK_STATE_UNSPECIFIED = 0;

  // An invitation has been sent to the reseller to create a channel partner
  // link.
  INVITED = 1;

  // Status when the reseller is active.
  ACTIVE = 2;

  // Status when the reseller has been revoked by the distributor.
  REVOKED = 3;

  // Status when the reseller is suspended by Google or distributor.
  SUSPENDED = 4;
}

// Entity representing a link between distributors and their indirect
// resellers in an n-tier resale channel.
message ChannelPartnerLink {
  option (google.api.resource) = {
    type: "cloudchannel.googleapis.com/ChannelPartnerLink"
    pattern: "accounts/{account}/channelPartnerLinks/{channel_partner_link}"
  };

  // Output only. Resource name for the channel partner link, in the format
  // accounts/{account_id}/channelPartnerLinks/{id}.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Cloud Identity ID of the linked reseller.
  string reseller_cloud_identity_id = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. State of the channel partner link.
  ChannelPartnerLinkState link_state = 3
      [(google.api.field_behavior) = REQUIRED];

  // Output only. URI of the web page where partner accepts the link invitation.
  string invite_link_uri = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp of when the channel partner link is created.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp of when the channel partner link is updated.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Public identifier that a customer must use to generate a
  // transfer token to move to this distributor-reseller combination.
  string public_id = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Cloud Identity info of the channel partner (IR).
  CloudIdentityInfo channel_partner_cloud_identity_info = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
