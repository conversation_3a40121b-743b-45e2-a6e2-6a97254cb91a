// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.automl.v1beta1;


option go_package = "cloud.google.com/go/automl/apiv1beta1/automlpb;automlpb";
option java_multiple_files = true;
option java_package = "com.google.cloud.automl.v1beta1";
option php_namespace = "Google\\Cloud\\AutoMl\\V1beta1";
option ruby_package = "Google::Cloud::AutoML::V1beta1";

// `TypeCode` is used as a part of
// [DataType][google.cloud.automl.v1beta1.DataType].
enum TypeCode {
  // Not specified. Should not be used.
  TYPE_CODE_UNSPECIFIED = 0;

  // Encoded as `number`, or the strings `"NaN"`, `"Infinity"`, or
  // `"-Infinity"`.
  FLOAT64 = 3;

  // Must be between 0AD and 9999AD. Encoded as `string` according to
  // [time_format][google.cloud.automl.v1beta1.DataType.time_format], or, if
  // that format is not set, then in RFC 3339 `date-time` format, where
  // `time-offset` = `"Z"` (e.g. 1985-04-12T23:20:50.52Z).
  TIMESTAMP = 4;

  // Encoded as `string`.
  STRING = 6;

  // Encoded as `list`, where the list elements are represented according to
  //
  // [list_element_type][google.cloud.automl.v1beta1.DataType.list_element_type].
  ARRAY = 8;

  // Encoded as `struct`, where field values are represented according to
  // [struct_type][google.cloud.automl.v1beta1.DataType.struct_type].
  STRUCT = 9;

  // Values of this type are not further understood by AutoML,
  // e.g. AutoML is unable to tell the order of values (as it could with
  // FLOAT64), or is unable to say if one value contains another (as it
  // could with STRING).
  // Encoded as `string` (bytes should be base64-encoded, as described in RFC
  // 4648, section 4).
  CATEGORY = 10;
}

// Indicated the type of data that can be stored in a structured data entity
// (e.g. a table).
message DataType {
  // Details of DataType-s that need additional specification.
  oneof details {
    // If [type_code][google.cloud.automl.v1beta1.DataType.type_code] == [ARRAY][google.cloud.automl.v1beta1.TypeCode.ARRAY],
    // then `list_element_type` is the type of the elements.
    DataType list_element_type = 2;

    // If [type_code][google.cloud.automl.v1beta1.DataType.type_code] == [STRUCT][google.cloud.automl.v1beta1.TypeCode.STRUCT], then `struct_type`
    // provides type information for the struct's fields.
    StructType struct_type = 3;

    // If [type_code][google.cloud.automl.v1beta1.DataType.type_code] == [TIMESTAMP][google.cloud.automl.v1beta1.TypeCode.TIMESTAMP]
    // then `time_format` provides the format in which that time field is
    // expressed. The time_format must either be one of:
    // * `UNIX_SECONDS`
    // * `UNIX_MILLISECONDS`
    // * `UNIX_MICROSECONDS`
    // * `UNIX_NANOSECONDS`
    // (for respectively number of seconds, milliseconds, microseconds and
    // nanoseconds since start of the Unix epoch);
    // or be written in `strftime` syntax. If time_format is not set, then the
    // default format as described on the type_code is used.
    string time_format = 5;
  }

  // Required. The [TypeCode][google.cloud.automl.v1beta1.TypeCode] for this type.
  TypeCode type_code = 1;

  // If true, this DataType can also be `NULL`. In .CSV files `NULL` value is
  // expressed as an empty string.
  bool nullable = 4;
}

// `StructType` defines the DataType-s of a [STRUCT][google.cloud.automl.v1beta1.TypeCode.STRUCT] type.
message StructType {
  // Unordered map of struct field names to their data types.
  // Fields cannot be added or removed via Update. Their names and
  // data types are still mutable.
  map<string, DataType> fields = 1;
}
