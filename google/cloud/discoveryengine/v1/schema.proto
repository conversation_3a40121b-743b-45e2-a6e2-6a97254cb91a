// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.discoveryengine.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/struct.proto";

option csharp_namespace = "Google.Cloud.DiscoveryEngine.V1";
option go_package = "cloud.google.com/go/discoveryengine/apiv1/discoveryenginepb;discoveryenginepb";
option java_multiple_files = true;
option java_outer_classname = "SchemaProto";
option java_package = "com.google.cloud.discoveryengine.v1";
option objc_class_prefix = "DISCOVERYENGINE";
option php_namespace = "Google\\Cloud\\DiscoveryEngine\\V1";
option ruby_package = "Google::Cloud::DiscoveryEngine::V1";

// Defines the structure and layout of a type of document data.
message Schema {
  option (google.api.resource) = {
    type: "discoveryengine.googleapis.com/Schema"
    pattern: "projects/{project}/locations/{location}/dataStores/{data_store}/schemas/{schema}"
    pattern: "projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/schemas/{schema}"
  };

  // Schema representation. One of
  // [struct_schema][google.cloud.discoveryengine.v1.Schema.struct_schema] or
  // [json_schema][google.cloud.discoveryengine.v1.Schema.json_schema] should be
  // provided otherwise an `INVALID_ARGUMENT` error is thrown.
  oneof schema {
    // The structured representation of the schema.
    google.protobuf.Struct struct_schema = 2;

    // The JSON representation of the schema.
    string json_schema = 3;
  }

  // Immutable. The full resource name of the schema, in the format of
  // `projects/{project}/locations/{location}/collections/{collection}/dataStores/{data_store}/schemas/{schema}`.
  //
  // This field must be a UTF-8 encoded string with a length limit of 1024
  // characters.
  string name = 1 [(google.api.field_behavior) = IMMUTABLE];
}
