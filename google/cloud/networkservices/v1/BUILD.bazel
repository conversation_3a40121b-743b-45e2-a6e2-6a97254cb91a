# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# buildifier: disable=load-on-top

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
# buildifier: disable=same-origin-load
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "networkservices_proto",
    srcs = [
        "common.proto",
        "dep.proto",
        "endpoint_policy.proto",
        "gateway.proto",
        "grpc_route.proto",
        "http_route.proto",
        "mesh.proto",
        "network_services.proto",
        "service_binding.proto",
        "tcp_route.proto",
        "tls_route.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:field_info_proto",
        "//google/api:resource_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "networkservices_proto_with_info",
    deps = [
        ":networkservices_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
        "//google/iam/v1:iam_policy_proto",
    ],
)

##############################################################################
# Java
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "networkservices_java_proto",
    deps = [":networkservices_proto"],
)

java_grpc_library(
    name = "networkservices_java_grpc",
    srcs = [":networkservices_proto"],
    deps = [":networkservices_java_proto"],
)

java_gapic_library(
    name = "networkservices_java_gapic",
    srcs = [":networkservices_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "networkservices_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "networkservices_v1.yaml",
    test_deps = [
        ":networkservices_java_grpc",
        "//google/cloud/location:location_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":networkservices_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "networkservices_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.networkservices.v1.DepServiceClientHttpJsonTest",
        "com.google.cloud.networkservices.v1.DepServiceClientTest",
        "com.google.cloud.networkservices.v1.NetworkServicesClientHttpJsonTest",
        "com.google.cloud.networkservices.v1.NetworkServicesClientTest",
    ],
    runtime_deps = [":networkservices_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-networkservices-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":networkservices_java_gapic",
        ":networkservices_java_grpc",
        ":networkservices_java_proto",
        ":networkservices_proto",
    ],
)

##############################################################################
# Go
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "networkservices_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/networkservices/apiv1/networkservicespb",
    protos = [":networkservices_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "networkservices_go_gapic",
    srcs = [":networkservices_proto_with_info"],
    grpc_service_config = "networkservices_grpc_service_config.json",
    importpath = "cloud.google.com/go/networkservices/apiv1;networkservices",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "networkservices_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":networkservices_go_proto",
        "//google/cloud/location:location_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-networkservices-v1-go",
    deps = [
        ":networkservices_go_gapic",
        ":networkservices_go_gapic_srcjar-metadata.srcjar",
        ":networkservices_go_gapic_srcjar-snippets.srcjar",
        ":networkservices_go_gapic_srcjar-test.srcjar",
        ":networkservices_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "networkservices_py_gapic",
    srcs = [":networkservices_proto"],
    grpc_service_config = "networkservices_grpc_service_config.json",
    opt_args = [
        "python-gapic-name=network_services",
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-network-services",
    ],
    rest_numeric_enums = True,
    service_yaml = "networkservices_v1.yaml",
    transport = "grpc+rest",
    deps = [
        "//google/iam/v1:iam_policy_py_proto",
    ],
)

py_test(
    name = "networkservices_py_gapic_test",
    srcs = [
        "networkservices_py_gapic_pytest.py",
        "networkservices_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":networkservices_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "networkservices-v1-py",
    deps = [
        ":networkservices_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "networkservices_php_proto",
    deps = [":networkservices_proto"],
)

php_gapic_library(
    name = "networkservices_php_gapic",
    srcs = [":networkservices_proto_with_info"],
    grpc_service_config = "networkservices_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "networkservices_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":networkservices_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-networkservices-v1-php",
    deps = [
        ":networkservices_php_gapic",
        ":networkservices_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "networkservices_nodejs_gapic",
    package_name = "@google-cloud/networkservices",
    src = ":networkservices_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "networkservices_grpc_service_config.json",
    package = "google.cloud.networkservices.v1",
    rest_numeric_enums = True,
    service_yaml = "networkservices_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "networkservices-v1-nodejs",
    deps = [
        ":networkservices_nodejs_gapic",
        ":networkservices_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "networkservices_ruby_proto",
    deps = [":networkservices_proto"],
)

ruby_grpc_library(
    name = "networkservices_ruby_grpc",
    srcs = [":networkservices_proto"],
    deps = [":networkservices_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "networkservices_ruby_gapic",
    srcs = [":networkservices_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-network_services-v1",
        "ruby-cloud-product-url=https://cloud.google.com/products/networking",
    ],
    grpc_service_config = "networkservices_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "networkservices_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":networkservices_ruby_grpc",
        ":networkservices_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-networkservices-v1-ruby",
    deps = [
        ":networkservices_ruby_gapic",
        ":networkservices_ruby_grpc",
        ":networkservices_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "networkservices_csharp_proto",
    deps = [":networkservices_proto"],
)

csharp_grpc_library(
    name = "networkservices_csharp_grpc",
    srcs = [":networkservices_proto"],
    deps = [":networkservices_csharp_proto"],
)

csharp_gapic_library(
    name = "networkservices_csharp_gapic",
    srcs = [":networkservices_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "networkservices_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "networkservices_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":networkservices_csharp_grpc",
        ":networkservices_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-networkservices-v1-csharp",
    deps = [
        ":networkservices_csharp_gapic",
        ":networkservices_csharp_grpc",
        ":networkservices_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
# buildifier: disable=same-origin-load
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "networkservices_cc_proto",
    deps = [":networkservices_proto"],
)

cc_grpc_library(
    name = "networkservices_cc_grpc",
    srcs = [":networkservices_proto"],
    grpc_only = True,
    deps = [":networkservices_cc_proto"],
)
