// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.video.stitcher.v1;

import "google/protobuf/duration.proto";

option go_package = "cloud.google.com/go/video/stitcher/apiv1/stitcherpb;stitcherpb";
option java_multiple_files = true;
option java_outer_classname = "EventsProto";
option java_package = "com.google.cloud.video.stitcher.v1";

// Describes an event and a trigger URI.
message Event {
  // Describes the event that occurred.
  enum EventType {
    // The event type is unspecified.
    EVENT_TYPE_UNSPECIFIED = 0;

    // First frame of creative ad viewed.
    CREATIVE_VIEW = 1;

    // Creative ad started.
    START = 2;

    // Start of an ad break.
    BREAK_START = 3;

    // End of an ad break.
    BREAK_END = 4;

    // Impression.
    IMPRESSION = 5;

    // First quartile progress.
    FIRST_QUARTILE = 6;

    // Midpoint progress.
    MIDPOINT = 7;

    // Third quartile progress.
    THIRD_QUARTILE = 8;

    // Ad progress completed.
    COMPLETE = 9;

    // Specific progress event with an offset.
    PROGRESS = 10;

    // Player muted.
    MUTE = 11;

    // Player unmuted.
    UNMUTE = 12;

    // Player paused.
    PAUSE = 13;

    // Click event.
    CLICK = 14;

    // Click-through event.
    CLICK_THROUGH = 15;

    // Player rewinding.
    REWIND = 16;

    // Player resumed.
    RESUME = 17;

    // Error event.
    ERROR = 18;

    // Ad expanded to a larger size.
    EXPAND = 21;

    // Ad collapsed to a smaller size.
    COLLAPSE = 22;

    // Non-linear ad closed.
    CLOSE = 24;

    // Linear ad closed.
    CLOSE_LINEAR = 25;

    // Ad skipped.
    SKIP = 26;

    // Accept invitation event.
    ACCEPT_INVITATION = 27;
  }

  // Describes the event that occurred.
  EventType type = 1;

  // The URI to trigger for this event.
  string uri = 2;

  // The ID of the event.
  string id = 3;

  // The offset in seconds if the event type is `PROGRESS`.
  google.protobuf.Duration offset = 4;
}

// Indicates a time in which a list of events should be triggered
// during media playback.
message ProgressEvent {
  // The time when the following tracking events occurs. The time is in
  // seconds relative to the start of the VOD asset.
  google.protobuf.Duration time_offset = 1;

  // The list of progress tracking events for the ad break. These can be of
  // the following IAB types: `BREAK_START`, `BREAK_END`, `IMPRESSION`,
  // `CREATIVE_VIEW`, `START`, `FIRST_QUARTILE`, `MIDPOINT`, `THIRD_QUARTILE`,
  // `COMPLETE`, `PROGRESS`.
  repeated Event events = 2;
}
