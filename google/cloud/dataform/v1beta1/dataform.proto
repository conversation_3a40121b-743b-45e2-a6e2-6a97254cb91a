// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.dataform.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "google/type/interval.proto";

option csharp_namespace = "Google.Cloud.Dataform.V1Beta1";
option go_package = "cloud.google.com/go/dataform/apiv1beta1/dataformpb;dataformpb";
option java_multiple_files = true;
option java_outer_classname = "DataformProto";
option java_package = "com.google.cloud.dataform.v1beta1";
option php_namespace = "Google\\Cloud\\Dataform\\V1beta1";
option ruby_package = "Google::Cloud::Dataform::V1beta1";
option (google.api.resource_definition) = {
  type: "secretmanager.googleapis.com/SecretVersion"
  pattern: "projects/{project}/secrets/{secret}/versions/{version}"
};

// Dataform is a service to develop, create, document, test, and update curated
// tables in BigQuery.
service Dataform {
  option (google.api.default_host) = "dataform.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists Repositories in a given project and location.
  rpc ListRepositories(ListRepositoriesRequest)
      returns (ListRepositoriesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/repositories"
    };
    option (google.api.method_signature) = "parent";
  }

  // Fetches a single Repository.
  rpc GetRepository(GetRepositoryRequest) returns (Repository) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/repositories/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Repository in a given project and location.
  rpc CreateRepository(CreateRepositoryRequest) returns (Repository) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/repositories"
      body: "repository"
    };
    option (google.api.method_signature) = "parent,repository,repository_id";
  }

  // Updates a single Repository.
  rpc UpdateRepository(UpdateRepositoryRequest) returns (Repository) {
    option (google.api.http) = {
      patch: "/v1beta1/{repository.name=projects/*/locations/*/repositories/*}"
      body: "repository"
    };
    option (google.api.method_signature) = "repository,update_mask";
  }

  // Deletes a single Repository.
  rpc DeleteRepository(DeleteRepositoryRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/repositories/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Applies a Git commit to a Repository. The Repository must not have a value
  // for `git_remote_settings.url`.
  rpc CommitRepositoryChanges(CommitRepositoryChangesRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/repositories/*}:commit"
      body: "*"
    };
  }

  // Returns the contents of a file (inside a Repository). The Repository
  // must not have a value for `git_remote_settings.url`.
  rpc ReadRepositoryFile(ReadRepositoryFileRequest)
      returns (ReadRepositoryFileResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/repositories/*}:readFile"
    };
  }

  // Returns the contents of a given Repository directory. The Repository must
  // not have a value for `git_remote_settings.url`.
  rpc QueryRepositoryDirectoryContents(QueryRepositoryDirectoryContentsRequest)
      returns (QueryRepositoryDirectoryContentsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/repositories/*}:queryDirectoryContents"
    };
  }

  // Fetches a Repository's history of commits.  The Repository must not have a
  // value for `git_remote_settings.url`.
  rpc FetchRepositoryHistory(FetchRepositoryHistoryRequest)
      returns (FetchRepositoryHistoryResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/repositories/*}:fetchHistory"
    };
  }

  // Computes a Repository's Git access token status.
  rpc ComputeRepositoryAccessTokenStatus(
      ComputeRepositoryAccessTokenStatusRequest)
      returns (ComputeRepositoryAccessTokenStatusResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/repositories/*}:computeAccessTokenStatus"
    };
  }

  // Fetches a Repository's remote branches.
  rpc FetchRemoteBranches(FetchRemoteBranchesRequest)
      returns (FetchRemoteBranchesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/repositories/*}:fetchRemoteBranches"
    };
  }

  // Lists Workspaces in a given Repository.
  rpc ListWorkspaces(ListWorkspacesRequest) returns (ListWorkspacesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/repositories/*}/workspaces"
    };
    option (google.api.method_signature) = "parent";
  }

  // Fetches a single Workspace.
  rpc GetWorkspace(GetWorkspaceRequest) returns (Workspace) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/repositories/*/workspaces/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Workspace in a given Repository.
  rpc CreateWorkspace(CreateWorkspaceRequest) returns (Workspace) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/repositories/*}/workspaces"
      body: "workspace"
    };
    option (google.api.method_signature) = "parent,workspace,workspace_id";
  }

  // Deletes a single Workspace.
  rpc DeleteWorkspace(DeleteWorkspaceRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/repositories/*/workspaces/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Installs dependency NPM packages (inside a Workspace).
  rpc InstallNpmPackages(InstallNpmPackagesRequest)
      returns (InstallNpmPackagesResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{workspace=projects/*/locations/*/repositories/*/workspaces/*}:installNpmPackages"
      body: "*"
    };
  }

  // Pulls Git commits from the Repository's remote into a Workspace.
  rpc PullGitCommits(PullGitCommitsRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/repositories/*/workspaces/*}:pull"
      body: "*"
    };
  }

  // Pushes Git commits from a Workspace to the Repository's remote.
  rpc PushGitCommits(PushGitCommitsRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/repositories/*/workspaces/*}:push"
      body: "*"
    };
  }

  // Fetches Git statuses for the files in a Workspace.
  rpc FetchFileGitStatuses(FetchFileGitStatusesRequest)
      returns (FetchFileGitStatusesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/repositories/*/workspaces/*}:fetchFileGitStatuses"
    };
  }

  // Fetches Git ahead/behind against a remote branch.
  rpc FetchGitAheadBehind(FetchGitAheadBehindRequest)
      returns (FetchGitAheadBehindResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/repositories/*/workspaces/*}:fetchGitAheadBehind"
    };
  }

  // Applies a Git commit for uncommitted files in a Workspace.
  rpc CommitWorkspaceChanges(CommitWorkspaceChangesRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/repositories/*/workspaces/*}:commit"
      body: "*"
    };
  }

  // Performs a Git reset for uncommitted files in a Workspace.
  rpc ResetWorkspaceChanges(ResetWorkspaceChangesRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/repositories/*/workspaces/*}:reset"
      body: "*"
    };
  }

  // Fetches Git diff for an uncommitted file in a Workspace.
  rpc FetchFileDiff(FetchFileDiffRequest) returns (FetchFileDiffResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{workspace=projects/*/locations/*/repositories/*/workspaces/*}:fetchFileDiff"
    };
  }

  // Returns the contents of a given Workspace directory.
  rpc QueryDirectoryContents(QueryDirectoryContentsRequest)
      returns (QueryDirectoryContentsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{workspace=projects/*/locations/*/repositories/*/workspaces/*}:queryDirectoryContents"
    };
  }

  // Creates a directory inside a Workspace.
  rpc MakeDirectory(MakeDirectoryRequest) returns (MakeDirectoryResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{workspace=projects/*/locations/*/repositories/*/workspaces/*}:makeDirectory"
      body: "*"
    };
  }

  // Deletes a directory (inside a Workspace) and all of its contents.
  rpc RemoveDirectory(RemoveDirectoryRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1beta1/{workspace=projects/*/locations/*/repositories/*/workspaces/*}:removeDirectory"
      body: "*"
    };
  }

  // Moves a directory (inside a Workspace), and all of its contents, to a new
  // location.
  rpc MoveDirectory(MoveDirectoryRequest) returns (MoveDirectoryResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{workspace=projects/*/locations/*/repositories/*/workspaces/*}:moveDirectory"
      body: "*"
    };
  }

  // Returns the contents of a file (inside a Workspace).
  rpc ReadFile(ReadFileRequest) returns (ReadFileResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{workspace=projects/*/locations/*/repositories/*/workspaces/*}:readFile"
    };
  }

  // Deletes a file (inside a Workspace).
  rpc RemoveFile(RemoveFileRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1beta1/{workspace=projects/*/locations/*/repositories/*/workspaces/*}:removeFile"
      body: "*"
    };
  }

  // Moves a file (inside a Workspace) to a new location.
  rpc MoveFile(MoveFileRequest) returns (MoveFileResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{workspace=projects/*/locations/*/repositories/*/workspaces/*}:moveFile"
      body: "*"
    };
  }

  // Writes to a file (inside a Workspace).
  rpc WriteFile(WriteFileRequest) returns (WriteFileResponse) {
    option (google.api.http) = {
      post: "/v1beta1/{workspace=projects/*/locations/*/repositories/*/workspaces/*}:writeFile"
      body: "*"
    };
  }

  // Lists ReleaseConfigs in a given Repository.
  rpc ListReleaseConfigs(ListReleaseConfigsRequest)
      returns (ListReleaseConfigsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/repositories/*}/releaseConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Fetches a single ReleaseConfig.
  rpc GetReleaseConfig(GetReleaseConfigRequest) returns (ReleaseConfig) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/repositories/*/releaseConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new ReleaseConfig in a given Repository.
  rpc CreateReleaseConfig(CreateReleaseConfigRequest) returns (ReleaseConfig) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/repositories/*}/releaseConfigs"
      body: "release_config"
    };
    option (google.api.method_signature) =
        "parent,release_config,release_config_id";
  }

  // Updates a single ReleaseConfig.
  rpc UpdateReleaseConfig(UpdateReleaseConfigRequest) returns (ReleaseConfig) {
    option (google.api.http) = {
      patch: "/v1beta1/{release_config.name=projects/*/locations/*/repositories/*/releaseConfigs/*}"
      body: "release_config"
    };
    option (google.api.method_signature) = "release_config,update_mask";
  }

  // Deletes a single ReleaseConfig.
  rpc DeleteReleaseConfig(DeleteReleaseConfigRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/repositories/*/releaseConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists CompilationResults in a given Repository.
  rpc ListCompilationResults(ListCompilationResultsRequest)
      returns (ListCompilationResultsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/repositories/*}/compilationResults"
    };
    option (google.api.method_signature) = "parent";
  }

  // Fetches a single CompilationResult.
  rpc GetCompilationResult(GetCompilationResultRequest)
      returns (CompilationResult) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/repositories/*/compilationResults/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new CompilationResult in a given project and location.
  rpc CreateCompilationResult(CreateCompilationResultRequest)
      returns (CompilationResult) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/repositories/*}/compilationResults"
      body: "compilation_result"
    };
    option (google.api.method_signature) = "parent,compilation_result";
  }

  // Returns CompilationResultActions in a given CompilationResult.
  rpc QueryCompilationResultActions(QueryCompilationResultActionsRequest)
      returns (QueryCompilationResultActionsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/repositories/*/compilationResults/*}:query"
    };
  }

  // Lists WorkflowConfigs in a given Repository.
  rpc ListWorkflowConfigs(ListWorkflowConfigsRequest)
      returns (ListWorkflowConfigsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/repositories/*}/workflowConfigs"
    };
    option (google.api.method_signature) = "parent";
  }

  // Fetches a single WorkflowConfig.
  rpc GetWorkflowConfig(GetWorkflowConfigRequest) returns (WorkflowConfig) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/repositories/*/workflowConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new WorkflowConfig in a given Repository.
  rpc CreateWorkflowConfig(CreateWorkflowConfigRequest)
      returns (WorkflowConfig) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/repositories/*}/workflowConfigs"
      body: "workflow_config"
    };
    option (google.api.method_signature) =
        "parent,workflow_config,workflow_config_id";
  }

  // Updates a single WorkflowConfig.
  rpc UpdateWorkflowConfig(UpdateWorkflowConfigRequest)
      returns (WorkflowConfig) {
    option (google.api.http) = {
      patch: "/v1beta1/{workflow_config.name=projects/*/locations/*/repositories/*/workflowConfigs/*}"
      body: "workflow_config"
    };
    option (google.api.method_signature) = "workflow_config,update_mask";
  }

  // Deletes a single WorkflowConfig.
  rpc DeleteWorkflowConfig(DeleteWorkflowConfigRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/repositories/*/workflowConfigs/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists WorkflowInvocations in a given Repository.
  rpc ListWorkflowInvocations(ListWorkflowInvocationsRequest)
      returns (ListWorkflowInvocationsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*/repositories/*}/workflowInvocations"
    };
    option (google.api.method_signature) = "parent";
  }

  // Fetches a single WorkflowInvocation.
  rpc GetWorkflowInvocation(GetWorkflowInvocationRequest)
      returns (WorkflowInvocation) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/repositories/*/workflowInvocations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new WorkflowInvocation in a given Repository.
  rpc CreateWorkflowInvocation(CreateWorkflowInvocationRequest)
      returns (WorkflowInvocation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*/repositories/*}/workflowInvocations"
      body: "workflow_invocation"
    };
    option (google.api.method_signature) = "parent,workflow_invocation";
  }

  // Deletes a single WorkflowInvocation.
  rpc DeleteWorkflowInvocation(DeleteWorkflowInvocationRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/repositories/*/workflowInvocations/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Requests cancellation of a running WorkflowInvocation.
  rpc CancelWorkflowInvocation(CancelWorkflowInvocationRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/repositories/*/workflowInvocations/*}:cancel"
      body: "*"
    };
  }

  // Returns WorkflowInvocationActions in a given WorkflowInvocation.
  rpc QueryWorkflowInvocationActions(QueryWorkflowInvocationActionsRequest)
      returns (QueryWorkflowInvocationActionsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/repositories/*/workflowInvocations/*}:query"
    };
  }
}

// Represents a Dataform Git repository.
message Repository {
  option (google.api.resource) = {
    type: "dataform.googleapis.com/Repository"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}"
  };

  // Controls Git remote configuration for a repository.
  message GitRemoteSettings {
    // Configures fields for performing SSH authentication.
    message SshAuthenticationConfig {
      // Required. The name of the Secret Manager secret version to use as a
      // ssh private key for Git operations.
      // Must be in the format `projects/*/secrets/*/versions/*`.
      string user_private_key_secret_version = 1 [
        (google.api.field_behavior) = REQUIRED,
        (google.api.resource_reference) = {
          type: "secretmanager.googleapis.com/SecretVersion"
        }
      ];

      // Required. Content of a public SSH key to verify an identity of a remote
      // Git host.
      string host_public_key = 2 [(google.api.field_behavior) = REQUIRED];
    }

    enum TokenStatus {
      // Default value. This value is unused.
      TOKEN_STATUS_UNSPECIFIED = 0;

      // The token could not be found in Secret Manager (or the Dataform
      // Service Account did not have permission to access it).
      NOT_FOUND = 1;

      // The token could not be used to authenticate against the Git remote.
      INVALID = 2;

      // The token was used successfully to authenticate against the Git remote.
      VALID = 3;
    }

    // Required. The Git remote's URL.
    string url = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The Git remote's default branch name.
    string default_branch = 2 [(google.api.field_behavior) = REQUIRED];

    // Optional. The name of the Secret Manager secret version to use as an
    // authentication token for Git operations. Must be in the format
    // `projects/*/secrets/*/versions/*`.
    string authentication_token_secret_version = 3 [
      (google.api.field_behavior) = OPTIONAL,
      (google.api.resource_reference) = {
        type: "secretmanager.googleapis.com/SecretVersion"
      }
    ];

    // Optional. Authentication fields for remote uris using SSH protocol.
    SshAuthenticationConfig ssh_authentication_config = 5
        [(google.api.field_behavior) = OPTIONAL];

    // Output only. Deprecated: The field does not contain any token status
    // information. Instead use
    // https://cloud.google.com/dataform/reference/rest/v1beta1/projects.locations.repositories/computeAccessTokenStatus
    TokenStatus token_status = 4
        [deprecated = true, (google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Configures workspace compilation overrides for a repository.
  // Primarily used by the UI (`console.cloud.google.com`).
  // `schema_suffix` and `table_prefix` can have a special expression -
  // `${workspaceName}`, which refers to the workspace name from which the
  // compilation results will be created. API callers are expected to resolve
  // the expression in these overrides and provide them explicitly in
  // `code_compilation_config`
  // (https://cloud.google.com/dataform/reference/rest/v1beta1/projects.locations.repositories.compilationResults#codecompilationconfig)
  // when creating workspace-scoped compilation results.
  message WorkspaceCompilationOverrides {
    // Optional. The default database (Google Cloud project ID).
    string default_database = 1 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The suffix that should be appended to all schema (BigQuery
    // dataset ID) names.
    string schema_suffix = 2 [(google.api.field_behavior) = OPTIONAL];

    // Optional. The prefix that should be prepended to all table names.
    string table_prefix = 3 [(google.api.field_behavior) = OPTIONAL];
  }

  // Output only. The repository's name.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The repository's user-friendly name.
  string display_name = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set, configures this repository to be linked to a Git remote.
  GitRemoteSettings git_remote_settings = 2
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The name of the Secret Manager secret version to be used to
  // interpolate variables into the .npmrc file for package installation
  // operations. Must be in the format `projects/*/secrets/*/versions/*`. The
  // file itself must be in a JSON format.
  string npmrc_environment_variables_secret_version = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "secretmanager.googleapis.com/SecretVersion"
    }
  ];

  // Optional. If set, fields of `workspace_compilation_overrides` override the
  // default compilation settings that are specified in dataform.json when
  // creating workspace-scoped compilation results. See documentation for
  // `WorkspaceCompilationOverrides` for more information.
  WorkspaceCompilationOverrides workspace_compilation_overrides = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Repository user labels.
  map<string, string> labels = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Input only. If set to true, the authenticated user will be
  // granted the roles/dataform.admin role on the created repository. To modify
  // access to the created repository later apply setIamPolicy from
  // https://cloud.google.com/dataform/reference/rest#rest-resource:-v1beta1.projects.locations.repositories
  bool set_authenticated_user_admin = 9 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.field_behavior) = INPUT_ONLY
  ];

  // Optional. The service account to run workflow invocations under.
  string service_account = 10 [(google.api.field_behavior) = OPTIONAL];
}

// `ListRepositories` request message.
message ListRepositoriesRequest {
  // Required. The location in which to list repositories. Must be in the format
  // `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Optional. Maximum number of repositories to return. The server may return
  // fewer items than requested. If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListRepositories` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListRepositories`
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This field only supports ordering by `name`. If unspecified, the
  // server will choose the ordering. If specified, the default order is
  // ascending for the `name` field.
  string order_by = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter for the returned list.
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];
}

// `ListRepositories` response message.
message ListRepositoriesResponse {
  // List of repositories.
  repeated Repository repositories = 1;

  // A token which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations which could not be reached.
  repeated string unreachable = 3;
}

// `GetRepository` request message.
message GetRepositoryRequest {
  // Required. The repository's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];
}

// `CreateRepository` request message.
message CreateRepositoryRequest {
  // Required. The location in which to create the repository. Must be in the
  // format `projects/*/locations/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The repository to create.
  Repository repository = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the repository, which will become the final
  // component of the repository's resource name.
  string repository_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// `UpdateRepository` request message.
message UpdateRepositoryRequest {
  // Optional. Specifies the fields to be updated in the repository. If left
  // unset, all fields will be updated.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The repository to update.
  Repository repository = 2 [(google.api.field_behavior) = REQUIRED];
}

// `DeleteRepository` request message.
message DeleteRepositoryRequest {
  // Required. The repository's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];

  // If set to true, any child resources of this repository will also be
  // deleted. (Otherwise, the request will only succeed if the repository has no
  // child resources.)
  bool force = 2;
}

// `CommitRepositoryChanges` request message.
message CommitRepositoryChangesRequest {
  // Represents a single file operation to the repository.
  message FileOperation {
    // Represents the write file operation (for files added or modified).
    message WriteFile {
      // The file's contents.
      bytes contents = 1;
    }

    // Represents the delete file operation.
    message DeleteFile {}

    oneof operation {
      // Represents the write operation.
      WriteFile write_file = 1;

      // Represents the delete operation.
      DeleteFile delete_file = 2;
    }
  }

  // Required. The repository's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];

  // Required. The changes to commit to the repository.
  CommitMetadata commit_metadata = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. The commit SHA which must be the repository's current HEAD before
  // applying this commit; otherwise this request will fail. If unset, no
  // validation on the current HEAD commit SHA is performed.
  string required_head_commit_sha = 4 [(google.api.field_behavior) = OPTIONAL];

  // A map to the path of the file to the operation. The path is the full file
  // path including filename, from repository root.
  map<string, FileOperation> file_operations = 3;
}

// `ReadRepositoryFile` request message.
message ReadRepositoryFileRequest {
  // Required. The repository's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];

  // Optional. The commit SHA for the commit to read from. If unset, the file
  // will be read from HEAD.
  string commit_sha = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. Full file path to read including filename, from repository root.
  string path = 3 [(google.api.field_behavior) = REQUIRED];
}

// `ReadRepositoryFile` response message.
message ReadRepositoryFileResponse {
  // The file's contents.
  bytes contents = 1;
}

// `QueryRepositoryDirectoryContents` request message.
message QueryRepositoryDirectoryContentsRequest {
  // Required. The repository's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];

  // Optional. The Commit SHA for the commit to query from. If unset, the
  // directory will be queried from HEAD.
  string commit_sha = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The directory's full path including directory name, relative to
  // root. If left unset, the root is used.
  string path = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Maximum number of paths to return. The server may return fewer
  // items than requested. If unspecified, the server will pick an appropriate
  // default.
  int32 page_size = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous
  // `QueryRepositoryDirectoryContents` call. Provide this to retrieve the
  // subsequent page.
  //
  // When paginating, all other parameters provided to
  // `QueryRepositoryDirectoryContents` must match the call that provided the
  // page token.
  string page_token = 5 [(google.api.field_behavior) = OPTIONAL];
}

// `QueryRepositoryDirectoryContents` response message.
message QueryRepositoryDirectoryContentsResponse {
  // List of entries in the directory.
  repeated DirectoryEntry directory_entries = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// `FetchRepositoryHistory` request message.
message FetchRepositoryHistoryRequest {
  // Required. The repository's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];

  // Optional. Maximum number of commits to return. The server may return fewer
  // items than requested. If unspecified, the server will pick an appropriate
  // default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `FetchRepositoryHistory`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `FetchRepositoryHistory`
  // must match the call that provided the page token.
  string page_token = 5 [(google.api.field_behavior) = OPTIONAL];
}

// `FetchRepositoryHistory` response message.
message FetchRepositoryHistoryResponse {
  // A list of commit logs, ordered by 'git log' default order.
  repeated CommitLogEntry commits = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Represents a single commit log.
message CommitLogEntry {
  // Commit timestamp.
  google.protobuf.Timestamp commit_time = 1;

  // The commit SHA for this commit log entry.
  string commit_sha = 2;

  // The commit author for this commit log entry.
  CommitAuthor author = 3;

  // The commit message for this commit log entry.
  string commit_message = 4;
}

// Represents a Dataform Git commit.
message CommitMetadata {
  // Required. The commit's author.
  CommitAuthor author = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The commit's message.
  string commit_message = 2 [(google.api.field_behavior) = OPTIONAL];
}

// `ComputeRepositoryAccessTokenStatus` request message.
message ComputeRepositoryAccessTokenStatusRequest {
  // Required. The repository's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];
}

// `ComputeRepositoryAccessTokenStatus` response message.
message ComputeRepositoryAccessTokenStatusResponse {
  // Indicates the status of a Git authentication token.
  enum TokenStatus {
    // Default value. This value is unused.
    TOKEN_STATUS_UNSPECIFIED = 0;

    // The token could not be found in Secret Manager (or the Dataform
    // Service Account did not have permission to access it).
    NOT_FOUND = 1;

    // The token could not be used to authenticate against the Git remote.
    INVALID = 2;

    // The token was used successfully to authenticate against the Git remote.
    VALID = 3;
  }

  // Indicates the status of the Git access token.
  TokenStatus token_status = 1;
}

// `FetchRemoteBranches` request message.
message FetchRemoteBranchesRequest {
  // Required. The repository's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];
}

// `FetchRemoteBranches` response message.
message FetchRemoteBranchesResponse {
  // The remote repository's branch names.
  repeated string branches = 1;
}

// Represents a Dataform Git workspace.
message Workspace {
  option (google.api.resource) = {
    type: "dataform.googleapis.com/Workspace"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/workspaces/{workspace}"
  };

  // Output only. The workspace's name.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// `ListWorkspaces` request message.
message ListWorkspacesRequest {
  // Required. The repository in which to list workspaces. Must be in the
  // format `projects/*/locations/*/repositories/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];

  // Optional. Maximum number of workspaces to return. The server may return
  // fewer items than requested. If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListWorkspaces` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListWorkspaces`
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This field only supports ordering by `name`. If unspecified, the
  // server will choose the ordering. If specified, the default order is
  // ascending for the `name` field.
  string order_by = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter for the returned list.
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];
}

// `ListWorkspaces` response message.
message ListWorkspacesResponse {
  // List of workspaces.
  repeated Workspace workspaces = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations which could not be reached.
  repeated string unreachable = 3;
}

// `GetWorkspace` request message.
message GetWorkspaceRequest {
  // Required. The workspace's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];
}

// `CreateWorkspace` request message.
message CreateWorkspaceRequest {
  // Required. The repository in which to create the workspace. Must be in the
  // format `projects/*/locations/*/repositories/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];

  // Required. The workspace to create.
  Workspace workspace = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the workspace, which will become the final
  // component of the workspace's resource name.
  string workspace_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// `DeleteWorkspace` request message.
message DeleteWorkspaceRequest {
  // Required. The workspace resource's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];
}

// Represents the author of a Git commit.
message CommitAuthor {
  // Required. The commit author's name.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The commit author's email address.
  string email_address = 2 [(google.api.field_behavior) = REQUIRED];
}

// `PullGitCommits` request message.
message PullGitCommitsRequest {
  // Required. The workspace's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];

  // Optional. The name of the branch in the Git remote from which to pull
  // commits. If left unset, the repository's default branch name will be used.
  string remote_branch = 2 [(google.api.field_behavior) = OPTIONAL];

  // Required. The author of any merge commit which may be created as a result
  // of merging fetched Git commits into this workspace.
  CommitAuthor author = 3 [(google.api.field_behavior) = REQUIRED];
}

// `PushGitCommits` request message.
message PushGitCommitsRequest {
  // Required. The workspace's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];

  // Optional. The name of the branch in the Git remote to which commits should
  // be pushed. If left unset, the repository's default branch name will be
  // used.
  string remote_branch = 2 [(google.api.field_behavior) = OPTIONAL];
}

// `FetchFileGitStatuses` request message.
message FetchFileGitStatusesRequest {
  // Required. The workspace's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];
}

// `FetchFileGitStatuses` response message.
message FetchFileGitStatusesResponse {
  // Represents the Git state of a file with uncommitted changes.
  message UncommittedFileChange {
    // Indicates the status of an uncommitted file change.
    enum State {
      // Default value. This value is unused.
      STATE_UNSPECIFIED = 0;

      // The file has been newly added.
      ADDED = 1;

      // The file has been deleted.
      DELETED = 2;

      // The file has been modified.
      MODIFIED = 3;

      // The file contains merge conflicts.
      HAS_CONFLICTS = 4;
    }

    // The file's full path including filename, relative to the workspace root.
    string path = 1;

    // Indicates the status of the file.
    State state = 2;
  }

  // A list of all files which have uncommitted Git changes. There will only be
  // a single entry for any given file.
  repeated UncommittedFileChange uncommitted_file_changes = 1;
}

// `FetchGitAheadBehind` request message.
message FetchGitAheadBehindRequest {
  // Required. The workspace's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];

  // Optional. The name of the branch in the Git remote against which this
  // workspace should be compared. If left unset, the repository's default
  // branch name will be used.
  string remote_branch = 2 [(google.api.field_behavior) = OPTIONAL];
}

// `FetchGitAheadBehind` response message.
message FetchGitAheadBehindResponse {
  // The number of commits in the remote branch that are not in the workspace.
  int32 commits_ahead = 1;

  // The number of commits in the workspace that are not in the remote branch.
  int32 commits_behind = 2;
}

// `CommitWorkspaceChanges` request message.
message CommitWorkspaceChangesRequest {
  // Required. The workspace's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];

  // Required. The commit's author.
  CommitAuthor author = 4 [(google.api.field_behavior) = REQUIRED];

  // Optional. The commit's message.
  string commit_message = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Full file paths to commit including filename, rooted at workspace
  // root. If left empty, all files will be committed.
  repeated string paths = 3 [(google.api.field_behavior) = OPTIONAL];
}

// `ResetWorkspaceChanges` request message.
message ResetWorkspaceChangesRequest {
  // Required. The workspace's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];

  // Optional. Full file paths to reset back to their committed state including
  // filename, rooted at workspace root. If left empty, all files will be reset.
  repeated string paths = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. If set to true, untracked files will be deleted.
  bool clean = 3 [(google.api.field_behavior) = OPTIONAL];
}

// `FetchFileDiff` request message.
message FetchFileDiffRequest {
  // Required. The workspace's name.
  string workspace = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];

  // Required. The file's full path including filename, relative to the
  // workspace root.
  string path = 2 [(google.api.field_behavior) = REQUIRED];
}

// `FetchFileDiff` response message.
message FetchFileDiffResponse {
  // The raw formatted Git diff for the file.
  string formatted_diff = 1;
}

// `QueryDirectoryContents` request message.
message QueryDirectoryContentsRequest {
  // Required. The workspace's name.
  string workspace = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];

  // Optional. The directory's full path including directory name, relative to
  // the workspace root. If left unset, the workspace root is used.
  string path = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Maximum number of paths to return. The server may return fewer
  // items than requested. If unspecified, the server will pick an appropriate
  // default.
  int32 page_size = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `QueryDirectoryContents`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to
  // `QueryDirectoryContents` must match the call that provided the page
  // token.
  string page_token = 4 [(google.api.field_behavior) = OPTIONAL];
}

// `QueryDirectoryContents` response message.
message QueryDirectoryContentsResponse {
  // List of entries in the directory.
  repeated DirectoryEntry directory_entries = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Represents a single entry in a directory.
message DirectoryEntry {
  oneof entry {
    // A file in the directory.
    string file = 1;

    // A child directory in the directory.
    string directory = 2;
  }
}

// `MakeDirectory` request message.
message MakeDirectoryRequest {
  // Required. The workspace's name.
  string workspace = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];

  // Required. The directory's full path including directory name, relative to
  // the workspace root.
  string path = 2 [(google.api.field_behavior) = REQUIRED];
}

// `MakeDirectory` response message.
message MakeDirectoryResponse {}

// `RemoveDirectory` request message.
message RemoveDirectoryRequest {
  // Required. The workspace's name.
  string workspace = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];

  // Required. The directory's full path including directory name, relative to
  // the workspace root.
  string path = 2 [(google.api.field_behavior) = REQUIRED];
}

// `MoveDirectory` request message.
message MoveDirectoryRequest {
  // Required. The workspace's name.
  string workspace = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];

  // Required. The directory's full path including directory name, relative to
  // the workspace root.
  string path = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The new path for the directory including directory name, rooted
  // at workspace root.
  string new_path = 3 [(google.api.field_behavior) = REQUIRED];
}

// `MoveDirectory` response message.
message MoveDirectoryResponse {}

// `ReadFile` request message.
message ReadFileRequest {
  // Required. The workspace's name.
  string workspace = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];

  // Required. The file's full path including filename, relative to the
  // workspace root.
  string path = 2 [(google.api.field_behavior) = REQUIRED];
}

// `ReadFile` response message.
message ReadFileResponse {
  // The file's contents.
  bytes file_contents = 1;
}

// `RemoveFile` request message.
message RemoveFileRequest {
  // Required. The workspace's name.
  string workspace = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];

  // Required. The file's full path including filename, relative to the
  // workspace root.
  string path = 2 [(google.api.field_behavior) = REQUIRED];
}

// `MoveFile` request message.
message MoveFileRequest {
  // Required. The workspace's name.
  string workspace = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];

  // Required. The file's full path including filename, relative to the
  // workspace root.
  string path = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The file's new path including filename, relative to the workspace
  // root.
  string new_path = 3 [(google.api.field_behavior) = REQUIRED];
}

// `MoveFile` response message.
message MoveFileResponse {}

// `WriteFile` request message.
message WriteFileRequest {
  // Required. The workspace's name.
  string workspace = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];

  // Required. The file.
  string path = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The file's contents.
  bytes contents = 3 [(google.api.field_behavior) = REQUIRED];
}

// `WriteFile` response message.
message WriteFileResponse {}

// `InstallNpmPackages` request message.
message InstallNpmPackagesRequest {
  // Required. The workspace's name.
  string workspace = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Workspace"
    }
  ];
}

// `InstallNpmPackages` response message.
message InstallNpmPackagesResponse {}

// Represents a Dataform release configuration.
message ReleaseConfig {
  option (google.api.resource) = {
    type: "dataform.googleapis.com/ReleaseConfig"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/releaseConfigs/{release_config}"
  };

  // A record of an attempt to create a compilation result for this release
  // config.
  message ScheduledReleaseRecord {
    // The timestamp of this release attempt.
    google.protobuf.Timestamp release_time = 1;

    oneof result {
      // The name of the created compilation result, if one was successfully
      // created. Must be in the format
      // `projects/*/locations/*/repositories/*/compilationResults/*`.
      string compilation_result = 2 [(google.api.resource_reference) = {
        type: "dataform.googleapis.com/CompilationResult"
      }];

      // The error status encountered upon this attempt to create the
      // compilation result, if the attempt was unsuccessful.
      google.rpc.Status error_status = 3;
    }
  }

  // Output only. The release config's name.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. Git commit/tag/branch name at which the repository should be
  // compiled. Must exist in the remote repository. Examples:
  // - a commit SHA: `12ade345`
  // - a tag: `tag1`
  // - a branch name: `branch1`
  string git_commitish = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. If set, fields of `code_compilation_config` override the default
  // compilation settings that are specified in dataform.json.
  CodeCompilationConfig code_compilation_config = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Optional schedule (in cron format) for automatic creation of
  // compilation results.
  string cron_schedule = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the time zone to be used when interpreting
  // cron_schedule. Must be a time zone name from the time zone database
  // (https://en.wikipedia.org/wiki/List_of_tz_database_time_zones). If left
  // unspecified, the default is UTC.
  string time_zone = 7 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Records of the 10 most recent scheduled release attempts,
  // ordered in in descending order of `release_time`. Updated whenever
  // automatic creation of a compilation result is triggered by cron_schedule.
  repeated ScheduledReleaseRecord recent_scheduled_release_records = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Optional. The name of the currently released compilation result for this
  // release config. This value is updated when a compilation result is created
  // from this release config, or when this resource is updated by API call
  // (perhaps to roll back to an earlier release). The compilation result must
  // have been created using this release config. Must be in the format
  // `projects/*/locations/*/repositories/*/compilationResults/*`.
  string release_compilation_result = 6 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/CompilationResult"
    }
  ];
}

// `ListReleaseConfigs` request message.
message ListReleaseConfigsRequest {
  // Required. The repository in which to list release configs. Must be in the
  // format `projects/*/locations/*/repositories/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];

  // Optional. Maximum number of release configs to return. The server may
  // return fewer items than requested. If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListReleaseConfigs` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListReleaseConfigs`
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// `ListReleaseConfigs` response message.
message ListReleaseConfigsResponse {
  // List of release configs.
  repeated ReleaseConfig release_configs = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations which could not be reached.
  repeated string unreachable = 3;
}

// `GetReleaseConfig` request message.
message GetReleaseConfigRequest {
  // Required. The release config's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/ReleaseConfig"
    }
  ];
}

// `CreateReleaseConfig` request message.
message CreateReleaseConfigRequest {
  // Required. The repository in which to create the release config. Must be in
  // the format `projects/*/locations/*/repositories/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];

  // Required. The release config to create.
  ReleaseConfig release_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the release config, which will become the final
  // component of the release config's resource name.
  string release_config_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// `UpdateReleaseConfig` request message.
message UpdateReleaseConfigRequest {
  // Optional. Specifies the fields to be updated in the release config. If left
  // unset, all fields will be updated.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The release config to update.
  ReleaseConfig release_config = 2 [(google.api.field_behavior) = REQUIRED];
}

// `DeleteReleaseConfig` request message.
message DeleteReleaseConfigRequest {
  // Required. The release config's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/ReleaseConfig"
    }
  ];
}

// Represents the result of compiling a Dataform project.
message CompilationResult {
  option (google.api.resource) = {
    type: "dataform.googleapis.com/CompilationResult"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/compilationResults/{compilation_result}"
  };

  // An error encountered when attempting to compile a Dataform project.
  message CompilationError {
    // Output only. The error's top level message.
    string message = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The error's full stack trace.
    string stack = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The path of the file where this error occurred, if
    // available, relative to the project root.
    string path = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. The identifier of the action where this error occurred, if
    // available.
    Target action_target = 4 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The compilation result's name.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  oneof source {
    // Immutable. Git commit/tag/branch name at which the repository should be
    // compiled. Must exist in the remote repository. Examples:
    // - a commit SHA: `12ade345`
    // - a tag: `tag1`
    // - a branch name: `branch1`
    string git_commitish = 2 [(google.api.field_behavior) = IMMUTABLE];

    // Immutable. The name of the workspace to compile. Must be in the format
    // `projects/*/locations/*/repositories/*/workspaces/*`.
    string workspace = 3 [
      (google.api.field_behavior) = IMMUTABLE,
      (google.api.resource_reference) = {
        type: "dataform.googleapis.com/Workspace"
      }
    ];

    // Immutable. The name of the release config to compile. The release
    // config's 'current_compilation_result' field will be updated to this
    // compilation result. Must be in the format
    // `projects/*/locations/*/repositories/*/releaseConfigs/*`.
    string release_config = 7 [
      (google.api.field_behavior) = IMMUTABLE,
      (google.api.resource_reference) = {
        type: "dataform.googleapis.com/ReleaseConfig"
      }
    ];
  }

  // Immutable. If set, fields of `code_compilation_config` override the default
  // compilation settings that are specified in dataform.json.
  CodeCompilationConfig code_compilation_config = 4
      [(google.api.field_behavior) = IMMUTABLE];

  // Output only. The fully resolved Git commit SHA of the code that was
  // compiled. Not set for compilation results whose source is a workspace.
  string resolved_git_commit_sha = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The version of `@dataform/core` that was used for compilation.
  string dataform_core_version = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Errors encountered during project compilation.
  repeated CompilationError compilation_errors = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Configures various aspects of Dataform code compilation.
message CodeCompilationConfig {
  // Optional. The default database (Google Cloud project ID).
  string default_database = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The default schema (BigQuery dataset ID).
  string default_schema = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The default BigQuery location to use. Defaults to "US".
  // See the BigQuery docs for a full list of locations:
  // https://cloud.google.com/bigquery/docs/locations.
  string default_location = 8 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The default schema (BigQuery dataset ID) for assertions.
  string assertion_schema = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. User-defined variables that are made available to project code
  // during compilation.
  map<string, string> vars = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The suffix that should be appended to all database (Google Cloud
  // project ID) names.
  string database_suffix = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The suffix that should be appended to all schema (BigQuery
  // dataset ID) names.
  string schema_suffix = 6 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The prefix that should be prepended to all table names.
  string table_prefix = 7 [(google.api.field_behavior) = OPTIONAL];
}

// `ListCompilationResults` request message.
message ListCompilationResultsRequest {
  // Required. The repository in which to list compilation results. Must be in
  // the format `projects/*/locations/*/repositories/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];

  // Optional. Maximum number of compilation results to return. The server may
  // return fewer items than requested. If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListCompilationResults`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListCompilationResults`
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// `ListCompilationResults` response message.
message ListCompilationResultsResponse {
  // List of compilation results.
  repeated CompilationResult compilation_results = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations which could not be reached.
  repeated string unreachable = 3;
}

// `GetCompilationResult` request message.
message GetCompilationResultRequest {
  // Required. The compilation result's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/CompilationResult"
    }
  ];
}

// `CreateCompilationResult` request message.
message CreateCompilationResultRequest {
  // Required. The repository in which to create the compilation result. Must be
  // in the format `projects/*/locations/*/repositories/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];

  // Required. The compilation result to create.
  CompilationResult compilation_result = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Represents an action identifier. If the action writes output, the output
// will be written to the referenced database object.
message Target {
  // The action's database (Google Cloud project ID) .
  string database = 1;

  // The action's schema (BigQuery dataset ID), within `database`.
  string schema = 2;

  // The action's name, within `database` and `schema`.
  string name = 3;
}

// Describes a relation and its columns.
message RelationDescriptor {
  // Describes a column.
  message ColumnDescriptor {
    // The identifier for the column. Each entry in `path` represents one level
    // of nesting.
    repeated string path = 1;

    // A textual description of the column.
    string description = 2;

    // A list of BigQuery policy tags that will be applied to the column.
    repeated string bigquery_policy_tags = 3;
  }

  // A text description of the relation.
  string description = 1;

  // A list of descriptions of columns within the relation.
  repeated ColumnDescriptor columns = 2;

  // A set of BigQuery labels that should be applied to the relation.
  map<string, string> bigquery_labels = 3;
}

// Represents a single Dataform action in a compilation result.
message CompilationResultAction {
  // Represents a database relation.
  message Relation {
    // Indicates the type of this relation.
    enum RelationType {
      // Default value. This value is unused.
      RELATION_TYPE_UNSPECIFIED = 0;

      // The relation is a table.
      TABLE = 1;

      // The relation is a view.
      VIEW = 2;

      // The relation is an incrementalized table.
      INCREMENTAL_TABLE = 3;

      // The relation is a materialized view.
      MATERIALIZED_VIEW = 4;
    }

    // Contains settings for relations of type `INCREMENTAL_TABLE`.
    message IncrementalTableConfig {
      // The SELECT query which returns rows which should be inserted into the
      // relation if it already exists and is not being refreshed.
      string incremental_select_query = 1;

      // Whether this table should be protected from being refreshed.
      bool refresh_disabled = 2;

      // A set of columns or SQL expressions used to define row uniqueness.
      // If any duplicates are discovered (as defined by `unique_key_parts`),
      // only the newly selected rows (as defined by `incremental_select_query`)
      // will be included in the relation.
      repeated string unique_key_parts = 3;

      // A SQL expression conditional used to limit the set of existing rows
      // considered for a merge operation (see `unique_key_parts` for more
      // information).
      string update_partition_filter = 4;

      // SQL statements to be executed before inserting new rows into the
      // relation.
      repeated string incremental_pre_operations = 5;

      // SQL statements to be executed after inserting new rows into the
      // relation.
      repeated string incremental_post_operations = 6;
    }

    // A list of actions that this action depends on.
    repeated Target dependency_targets = 1;

    // Whether this action is disabled (i.e. should not be run).
    bool disabled = 2;

    // Arbitrary, user-defined tags on this action.
    repeated string tags = 3;

    // Descriptor for the relation and its columns.
    RelationDescriptor relation_descriptor = 4;

    // The type of this relation.
    RelationType relation_type = 5;

    // The SELECT query which returns rows which this relation should contain.
    string select_query = 6;

    // SQL statements to be executed before creating the relation.
    repeated string pre_operations = 7;

    // SQL statements to be executed after creating the relation.
    repeated string post_operations = 8;

    // Configures `INCREMENTAL_TABLE` settings for this relation. Only set if
    // `relation_type` is `INCREMENTAL_TABLE`.
    IncrementalTableConfig incremental_table_config = 9;

    // The SQL expression used to partition the relation.
    string partition_expression = 10;

    // A list of columns or SQL expressions used to cluster the table.
    repeated string cluster_expressions = 11;

    // Sets the partition expiration in days.
    int32 partition_expiration_days = 12;

    // Specifies whether queries on this table must include a predicate filter
    // that filters on the partitioning column.
    bool require_partition_filter = 13;

    // Additional options that will be provided as key/value pairs into the
    // options clause of a create table/view statement. See
    // https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language
    // for more information on which options are supported.
    map<string, string> additional_options = 14;
  }

  // Represents a list of arbitrary database operations.
  message Operations {
    // A list of actions that this action depends on.
    repeated Target dependency_targets = 1;

    // Whether this action is disabled (i.e. should not be run).
    bool disabled = 2;

    // Arbitrary, user-defined tags on this action.
    repeated string tags = 3;

    // Descriptor for any output relation and its columns. Only set if
    // `has_output` is true.
    RelationDescriptor relation_descriptor = 6;

    // A list of arbitrary SQL statements that will be executed without
    // alteration.
    repeated string queries = 4;

    // Whether these operations produce an output relation.
    bool has_output = 5;
  }

  // Represents an assertion upon a SQL query which is required return zero
  // rows.
  message Assertion {
    // A list of actions that this action depends on.
    repeated Target dependency_targets = 1;

    // The parent action of this assertion. Only set if this assertion was
    // automatically generated.
    Target parent_action = 5;

    // Whether this action is disabled (i.e. should not be run).
    bool disabled = 2;

    // Arbitrary, user-defined tags on this action.
    repeated string tags = 3;

    // The SELECT query which must return zero rows in order for this assertion
    // to succeed.
    string select_query = 4;

    // Descriptor for the assertion's automatically-generated view and its
    // columns.
    RelationDescriptor relation_descriptor = 6;
  }

  // Represents a relation which is not managed by Dataform but which may be
  // referenced by Dataform actions.
  message Declaration {
    // Descriptor for the relation and its columns. Used as documentation only,
    // i.e. values here will result in no changes to the relation's metadata.
    RelationDescriptor relation_descriptor = 1;
  }

  // This action's identifier. Unique within the compilation result.
  Target target = 1;

  // The action's identifier if the project had been compiled without any
  // overrides configured. Unique within the compilation result.
  Target canonical_target = 2;

  // The full path including filename in which this action is located, relative
  // to the workspace root.
  string file_path = 3;

  oneof compiled_object {
    // The database relation created/updated by this action.
    Relation relation = 4;

    // The database operations executed by this action.
    Operations operations = 5;

    // The assertion executed by this action.
    Assertion assertion = 6;

    // The declaration declared by this action.
    Declaration declaration = 7;
  }
}

// `QueryCompilationResultActions` request message.
message QueryCompilationResultActionsRequest {
  // Required. The compilation result's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/CompilationResult"
    }
  ];

  // Optional. Maximum number of compilation results to return. The server may
  // return fewer items than requested. If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous
  // `QueryCompilationResultActions` call. Provide this to retrieve the
  // subsequent page.
  //
  // When paginating, all other parameters provided to
  // `QueryCompilationResultActions` must match the call that provided the page
  // token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Optional filter for the returned list. Filtering is only
  // currently supported on the `file_path` field.
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// `QueryCompilationResultActions` response message.
message QueryCompilationResultActionsResponse {
  // List of compilation result actions.
  repeated CompilationResultAction compilation_result_actions = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// Represents a Dataform workflow configuration.
message WorkflowConfig {
  option (google.api.resource) = {
    type: "dataform.googleapis.com/WorkflowConfig"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/workflowConfigs/{workflow_config}"
  };

  // A record of an attempt to create a workflow invocation for this workflow
  // config.
  message ScheduledExecutionRecord {
    // The timestamp of this execution attempt.
    google.protobuf.Timestamp execution_time = 1;

    oneof result {
      // The name of the created workflow invocation, if one was successfully
      // created. Must be in the format
      // `projects/*/locations/*/repositories/*/workflowInvocations/*`.
      string workflow_invocation = 2 [(google.api.resource_reference) = {
        type: "dataform.googleapis.com/WorkflowInvocation"
      }];

      // The error status encountered upon this attempt to create the
      // workflow invocation, if the attempt was unsuccessful.
      google.rpc.Status error_status = 3;
    }
  }

  // Output only. The workflow config's name.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The name of the release config whose release_compilation_result
  // should be executed. Must be in the format
  // `projects/*/locations/*/repositories/*/releaseConfigs/*`.
  string release_config = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/ReleaseConfig"
    }
  ];

  // Optional. If left unset, a default InvocationConfig will be used.
  InvocationConfig invocation_config = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. Optional schedule (in cron format) for automatic execution of
  // this workflow config.
  string cron_schedule = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Specifies the time zone to be used when interpreting
  // cron_schedule. Must be a time zone name from the time zone database
  // (https://en.wikipedia.org/wiki/List_of_tz_database_time_zones). If left
  // unspecified, the default is UTC.
  string time_zone = 7 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Records of the 10 most recent scheduled execution attempts,
  // ordered in in descending order of `execution_time`. Updated whenever
  // automatic creation of a workflow invocation is triggered by cron_schedule.
  repeated ScheduledExecutionRecord recent_scheduled_execution_records = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Includes various configuration options for a workflow invocation.
// If both `included_targets` and `included_tags` are unset, all actions
// will be included.
message InvocationConfig {
  // Optional. The set of action identifiers to include.
  repeated Target included_targets = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The set of tags to include.
  repeated string included_tags = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. When set to true, transitive dependencies of included actions
  // will be executed.
  bool transitive_dependencies_included = 3
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. When set to true, transitive dependents of included actions will
  // be executed.
  bool transitive_dependents_included = 4
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. When set to true, any incremental tables will be fully refreshed.
  bool fully_refresh_incremental_tables_enabled = 5
      [(google.api.field_behavior) = OPTIONAL];

  // Optional. The service account to run workflow invocations under.
  string service_account = 6 [(google.api.field_behavior) = OPTIONAL];
}

// `ListWorkflowConfigs` request message.
message ListWorkflowConfigsRequest {
  // Required. The repository in which to list workflow configs. Must be in the
  // format `projects/*/locations/*/repositories/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];

  // Optional. Maximum number of workflow configs to return. The server may
  // return fewer items than requested. If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListWorkflowConfigs` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListWorkflowConfigs`
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// `ListWorkflowConfigs` response message.
message ListWorkflowConfigsResponse {
  // List of workflow configs.
  repeated WorkflowConfig workflow_configs = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations which could not be reached.
  repeated string unreachable = 3;
}

// `GetWorkflowConfig` request message.
message GetWorkflowConfigRequest {
  // Required. The workflow config's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/WorkflowConfig"
    }
  ];
}

// `CreateWorkflowConfig` request message.
message CreateWorkflowConfigRequest {
  // Required. The repository in which to create the workflow config. Must be in
  // the format `projects/*/locations/*/repositories/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];

  // Required. The workflow config to create.
  WorkflowConfig workflow_config = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The ID to use for the workflow config, which will become the
  // final component of the workflow config's resource name.
  string workflow_config_id = 3 [(google.api.field_behavior) = REQUIRED];
}

// `UpdateWorkflowConfig` request message.
message UpdateWorkflowConfigRequest {
  // Optional. Specifies the fields to be updated in the workflow config. If
  // left unset, all fields will be updated.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = OPTIONAL];

  // Required. The workflow config to update.
  WorkflowConfig workflow_config = 2 [(google.api.field_behavior) = REQUIRED];
}

// `DeleteWorkflowConfig` request message.
message DeleteWorkflowConfigRequest {
  // Required. The workflow config's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/WorkflowConfig"
    }
  ];
}

// Represents a single invocation of a compilation result.
message WorkflowInvocation {
  option (google.api.resource) = {
    type: "dataform.googleapis.com/WorkflowInvocation"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/workflowInvocations/{workflow_invocation}"
  };

  // Represents the current state of a workflow invocation.
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // The workflow invocation is currently running.
    RUNNING = 1;

    // The workflow invocation succeeded. A terminal state.
    SUCCEEDED = 2;

    // The workflow invocation was cancelled. A terminal state.
    CANCELLED = 3;

    // The workflow invocation failed. A terminal state.
    FAILED = 4;

    // The workflow invocation is being cancelled, but some actions are still
    // running.
    CANCELING = 5;
  }

  // Output only. The workflow invocation's name.
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  oneof compilation_source {
    // Immutable. The name of the compilation result to use for this invocation.
    // Must be in the format
    // `projects/*/locations/*/repositories/*/compilationResults/*`.
    string compilation_result = 2 [
      (google.api.field_behavior) = IMMUTABLE,
      (google.api.resource_reference) = {
        type: "dataform.googleapis.com/CompilationResult"
      }
    ];

    // Immutable. The name of the workflow config to invoke. Must be in the
    // format `projects/*/locations/*/repositories/*/workflowConfigs/*`.
    string workflow_config = 6 [
      (google.api.field_behavior) = IMMUTABLE,
      (google.api.resource_reference) = {
        type: "dataform.googleapis.com/WorkflowConfig"
      }
    ];
  }

  // Immutable. If left unset, a default InvocationConfig will be used.
  InvocationConfig invocation_config = 3
      [(google.api.field_behavior) = IMMUTABLE];

  // Output only. This workflow invocation's current state.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This workflow invocation's timing details.
  google.type.Interval invocation_timing = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// `ListWorkflowInvocations` request message.
message ListWorkflowInvocationsRequest {
  // Required. The parent resource of the WorkflowInvocation type. Must be in
  // the format `projects/*/locations/*/repositories/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];

  // Optional. Maximum number of workflow invocations to return. The server may
  // return fewer items than requested. If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous `ListWorkflowInvocations`
  // call. Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListWorkflowInvocations`
  // must match the call that provided the page token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. This field only supports ordering by `name`. If unspecified, the
  // server will choose the ordering. If specified, the default order is
  // ascending for the `name` field.
  string order_by = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filter for the returned list.
  string filter = 5 [(google.api.field_behavior) = OPTIONAL];
}

// `ListWorkflowInvocations` response message.
message ListWorkflowInvocationsResponse {
  // List of workflow invocations.
  repeated WorkflowInvocation workflow_invocations = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations which could not be reached.
  repeated string unreachable = 3;
}

// `GetWorkflowInvocation` request message.
message GetWorkflowInvocationRequest {
  // Required. The workflow invocation resource's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/WorkflowInvocation"
    }
  ];
}

// `CreateWorkflowInvocation` request message.
message CreateWorkflowInvocationRequest {
  // Required. The repository in which to create the workflow invocation. Must
  // be in the format `projects/*/locations/*/repositories/*`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/Repository"
    }
  ];

  // Required. The workflow invocation resource to create.
  WorkflowInvocation workflow_invocation = 2
      [(google.api.field_behavior) = REQUIRED];
}

// `DeleteWorkflowInvocation` request message.
message DeleteWorkflowInvocationRequest {
  // Required. The workflow invocation resource's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/WorkflowInvocation"
    }
  ];
}

// `CancelWorkflowInvocation` request message.
message CancelWorkflowInvocationRequest {
  // Required. The workflow invocation resource's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/WorkflowInvocation"
    }
  ];
}

// Represents a single action in a workflow invocation.
message WorkflowInvocationAction {
  // Represents the current state of a workflow invocation action.
  enum State {
    // The action has not yet been considered for invocation.
    PENDING = 0;

    // The action is currently running.
    RUNNING = 1;

    // Execution of the action was skipped because upstream dependencies did not
    // all complete successfully. A terminal state.
    SKIPPED = 2;

    // Execution of the action was disabled as per the configuration of the
    // corresponding compilation result action. A terminal state.
    DISABLED = 3;

    // The action succeeded. A terminal state.
    SUCCEEDED = 4;

    // The action was cancelled. A terminal state.
    CANCELLED = 5;

    // The action failed. A terminal state.
    FAILED = 6;
  }

  // Represents a workflow action that will run against BigQuery.
  message BigQueryAction {
    // Output only. The generated BigQuery SQL script that will be executed.
    string sql_script = 1 [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. This action's identifier. Unique within the workflow
  // invocation.
  Target target = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The action's identifier if the project had been compiled
  // without any overrides configured. Unique within the compilation result.
  Target canonical_target = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This action's current state.
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. If and only if action's state is FAILED a failure reason is
  // set.
  string failure_reason = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. This action's timing details.
  // `start_time` will be set if the action is in [RUNNING, SUCCEEDED,
  // CANCELLED, FAILED] state.
  // `end_time` will be set if the action is in [SUCCEEDED, CANCELLED, FAILED]
  // state.
  google.type.Interval invocation_timing = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The workflow action's bigquery action details.
  BigQueryAction bigquery_action = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// `QueryWorkflowInvocationActions` request message.
message QueryWorkflowInvocationActionsRequest {
  // Required. The workflow invocation's name.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "dataform.googleapis.com/WorkflowInvocation"
    }
  ];

  // Optional. Maximum number of workflow invocations to return. The server may
  // return fewer items than requested. If unspecified, the server will pick an
  // appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Page token received from a previous
  // `QueryWorkflowInvocationActions` call. Provide this to retrieve the
  // subsequent page.
  //
  // When paginating, all other parameters provided to
  // `QueryWorkflowInvocationActions` must match the call that provided the page
  // token.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];
}

// `QueryWorkflowInvocationActions` response message.
message QueryWorkflowInvocationActionsResponse {
  // List of workflow invocation actions.
  repeated WorkflowInvocationAction workflow_invocation_actions = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}
