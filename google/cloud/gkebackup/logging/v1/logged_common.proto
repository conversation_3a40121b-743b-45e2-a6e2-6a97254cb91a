// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.gkebackup.logging.v1;

option go_package = "cloud.google.com/go/gkebackup/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "LoggedCommonProto";
option java_package = "google.cloud.gkebackup.logging.v1";
option csharp_namespace = "Google.Cloud.GkeBackup.Logging.V1";
option php_namespace = "Google\\Cloud\\GkeBackup\\Logging\\V1";
option ruby_package = "Google::Cloud::GkeBackup::Logging::V1";

// Namespaces, list of namespaces
message Namespaces {
  // namespaces
  repeated string namespaces = 1;
}

// NamespacedName
message NamespacedName {
  // the namespace of the resource in Kubernetes
  string namespace = 1;

  // the name of the resource in Kubernetes
  string name = 2;
}

// NamespacedNames
message NamespacedNames {
  // a list of namespaced names in Kubernetes
  repeated NamespacedName namespaced_names = 1;
}

// Encryption key.
// This only contains the key metadata, and no key material.
message EncryptionKey {
  // Google KMS encryption key in the format:
  // projects/<project>/locations/<location>/keyRings/<key-ring>/cryptoKeys/<key>
  string gcp_kms_encryption_key = 1;
}
