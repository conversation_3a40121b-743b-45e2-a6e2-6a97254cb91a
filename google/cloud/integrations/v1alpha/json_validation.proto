// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.integrations.v1alpha;

option csharp_namespace = "Google.Cloud.Integrations.V1Alpha";
option go_package = "cloud.google.com/go/integrations/apiv1alpha/integrationspb;integrationspb";
option java_multiple_files = true;
option java_outer_classname = "JsonValidationProto";
option java_package = "com.google.cloud.integrations.v1alpha";

// Options for how to validate json schemas.
enum JsonValidationOption {
  // As per the default behavior, no validation will be run. Will not override
  // any option set in a Task.
  JSON_VALIDATION_OPTION_UNSPECIFIED = 0;

  // Do not run any validation against JSON schemas.
  SKIP = 1;

  // Validate all potential input JSON parameters against schemas specified in
  // IntegrationParameter.
  PRE_EXECUTION = 2;

  // Validate all potential output JSON parameters against schemas specified in
  // IntegrationParameter.
  POST_EXECUTION = 3;

  // Perform both PRE_EXECUTION and POST_EXECUTION validations.
  PRE_POST_EXECUTION = 4;
}
