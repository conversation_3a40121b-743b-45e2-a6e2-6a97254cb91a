// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.integrations.v1alpha;

option go_package = "cloud.google.com/go/integrations/apiv1alpha/integrationspb;integrationspb";
option java_multiple_files = true;
option java_outer_classname = "ProductProto";
option java_package = "com.google.cloud.integrations.v1alpha";

// Enum Product.
enum Product {
  // Default value.
  PRODUCT_UNSPECIFIED = 0;

  // Integration Platform.
  IP = 1;

  // Apigee.
  APIGEE = 2;

  // Security Command Center.
  SECURITY = 3;
}
