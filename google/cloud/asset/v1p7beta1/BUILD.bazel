# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
    "proto_library_with_info",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

proto_library(
    name = "asset_proto",
    srcs = [
        "asset_service.proto",
        "assets.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/cloud/orgpolicy/v1:orgpolicy_proto",
        "//google/cloud/osconfig/v1:osconfig_proto",
        "//google/iam/v1:policy_proto",
        "//google/identity/accesscontextmanager/v1:accesscontextmanager_proto",
        "//google/longrunning:operations_proto",
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "asset_proto_with_info",
    deps = [
        ":asset_proto",
        "//google/cloud:common_resources_proto",
    ],
)

java_proto_library(
    name = "asset_java_proto",
    deps = [":asset_proto"],
)

java_grpc_library(
    name = "asset_java_grpc",
    srcs = [":asset_proto"],
    deps = [":asset_java_proto"],
)

java_gapic_library(
    name = "asset_java_gapic",
    srcs = [":asset_proto_with_info"],
    grpc_service_config = "cloudasset_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudasset_v1p7beta1.yaml",
    test_deps = [
        ":asset_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc",
    deps = [
        ":asset_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "asset_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.asset.v1p7beta1.AssetServiceClientTest",
    ],
    runtime_deps = [":asset_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-asset-v1p7beta1-java",
    include_samples = True,
    transport = "grpc",
    deps = [
        ":asset_java_gapic",
        ":asset_java_grpc",
        ":asset_java_proto",
        ":asset_proto",
    ],
)

go_proto_library(
    name = "asset_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/asset/apiv1p7beta1/assetpb",
    protos = [":asset_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/cloud/orgpolicy/v1:orgpolicy_go_proto",
        "//google/cloud/osconfig/v1:osconfig_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/identity/accesscontextmanager/v1:accesscontextmanager_go_proto",
        "//google/longrunning:longrunning_go_proto",
    ],
)

go_gapic_library(
    name = "asset_go_gapic",
    srcs = [":asset_proto_with_info"],
    grpc_service_config = "cloudasset_grpc_service_config.json",
    importpath = "cloud.google.com/go/asset/apiv1p7beta1;asset",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "cloudasset_v1p7beta1.yaml",
    transport = "grpc",
    deps = [
        ":asset_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
        "@io_bazel_rules_go//proto/wkt:any_go_proto",
        "@io_bazel_rules_go//proto/wkt:struct_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-asset-v1p7beta1-go",
    deps = [
        ":asset_go_gapic",
        ":asset_go_gapic_srcjar-metadata.srcjar",
        ":asset_go_gapic_srcjar-snippets.srcjar",
        ":asset_go_gapic_srcjar-test.srcjar",
        ":asset_go_proto",
    ],
)

py_gapic_library(
    name = "asset_py_gapic",
    srcs = [":asset_proto"],
    grpc_service_config = "cloudasset_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudasset_v1p7beta1.yaml",
    transport = "grpc",
    deps = [
        "//google/cloud/orgpolicy/v1:orgpolicy_py_proto",
        "//google/iam/v1:policy_py_proto",
        "//google/identity/accesscontextmanager/v1:accesscontextmanager_py_proto",
    ],
)

py_test(
    name = "asset_py_gapic_test",
    srcs = [
        "asset_py_gapic_pytest.py",
        "asset_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":asset_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "asset-v1p7beta1-py",
    deps = [
        ":asset_py_gapic",
    ],
)

php_proto_library(
    name = "asset_php_proto",
    deps = [":asset_proto"],
)

php_gapic_library(
    name = "asset_php_gapic",
    srcs = [":asset_proto_with_info"],
    grpc_service_config = "cloudasset_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudasset_v1p7beta1.yaml",
    transport = "grpc+rest",
    deps = [":asset_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-asset-v1p7beta1-php",
    deps = [
        ":asset_php_gapic",
        ":asset_php_proto",
    ],
)

nodejs_gapic_library(
    name = "asset_nodejs_gapic",
    package_name = "@google-cloud/asset",
    src = ":asset_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "cloudasset_grpc_service_config.json",
    package = "google.cloud.asset.v1p7beta1",
    rest_numeric_enums = True,
    service_yaml = "cloudasset_v1p7beta1.yaml",
    transport = "grpc",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "asset-v1p7beta1-nodejs",
    deps = [
        ":asset_nodejs_gapic",
        ":asset_proto",
        "//google/cloud/orgpolicy/v1:orgpolicy_proto",
        "//google/cloud/osconfig/v1:osconfig_proto",
        "//google/identity/accesscontextmanager/v1:accesscontextmanager_proto",
        "//google/identity/accesscontextmanager/type:type_proto",
    ],
)

ruby_proto_library(
    name = "asset_ruby_proto",
    deps = [":asset_proto"],
)

ruby_grpc_library(
    name = "asset_ruby_grpc",
    srcs = [":asset_proto"],
    deps = [":asset_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "asset_ruby_gapic",
    srcs = [":asset_proto_with_info"],
    extra_protoc_parameters = ["ruby-cloud-gem-name=google-cloud-asset-v1p7beta1"],
    rest_numeric_enums = True,
    service_yaml = "cloudasset_v1p7beta1.yaml",
    deps = [
        ":asset_ruby_grpc",
        ":asset_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-asset-v1p7beta1-ruby",
    deps = [
        ":asset_ruby_gapic",
        ":asset_ruby_grpc",
        ":asset_ruby_proto",
    ],
)

csharp_proto_library(
    name = "asset_csharp_proto",
    deps = [":asset_proto"],
)

csharp_grpc_library(
    name = "asset_csharp_grpc",
    srcs = [":asset_proto"],
    deps = [":asset_csharp_proto"],
)

# Invalid C# namespaces, cannot build.
# csharp_gapic_library(
#     name = "asset_csharp_gapic",
#     srcs = [":asset_proto_with_info"],
#     common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
#     grpc_service_config = "cloudasset_grpc_service_config.json",
#     service_yaml = "cloudasset_v1p7beta1.yaml",
#     deps = [
#         ":asset_csharp_grpc",
#         ":asset_csharp_proto",
#     ],
# )

# # Open Source Packages
# csharp_gapic_assembly_pkg(
#     name = "google-cloud-asset-v1p7beta1-csharp",
#     deps = [
#         ":asset_csharp_gapic",
#         ":asset_csharp_grpc",
#         ":asset_csharp_proto",
#     ],
# )

##############################################################################
# C++
##############################################################################
# Put your C++ rules here
