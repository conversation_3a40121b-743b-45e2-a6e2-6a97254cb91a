// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.notebooks.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/cloud/notebooks/v1beta1/environment.proto";
import "google/cloud/notebooks/v1beta1/instance.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Notebooks.V1Beta1";
option go_package = "cloud.google.com/go/notebooks/apiv1beta1/notebookspb;notebookspb";
option java_multiple_files = true;
option java_outer_classname = "NotebooksProto";
option java_package = "com.google.cloud.notebooks.v1beta1";
option php_namespace = "Google\\Cloud\\Notebooks\\V1beta1";
option ruby_package = "Google::Cloud::Notebooks::V1beta1";

// API v1beta1 service for Cloud AI Platform Notebooks.
service NotebookService {
  option (google.api.default_host) = "notebooks.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/cloud-platform";

  // Lists instances in a given project and location.
  rpc ListInstances(ListInstancesRequest) returns (ListInstancesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/instances"
    };
  }

  // Gets details of a single Instance.
  rpc GetInstance(GetInstanceRequest) returns (Instance) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/instances/*}"
    };
  }

  // Creates a new Instance in a given project and location.
  rpc CreateInstance(CreateInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/instances"
      body: "instance"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Registers an existing legacy notebook instance to the Notebooks API server.
  // Legacy instances are instances created with the legacy Compute Engine
  // calls. They are not manageable by the Notebooks API out of the box. This
  // call makes these instances manageable by the Notebooks API.
  rpc RegisterInstance(RegisterInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/instances:register"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the guest accelerators of a single Instance.
  rpc SetInstanceAccelerator(SetInstanceAcceleratorRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{name=projects/*/locations/*/instances/*}:setAccelerator"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the machine type of a single Instance.
  rpc SetInstanceMachineType(SetInstanceMachineTypeRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{name=projects/*/locations/*/instances/*}:setMachineType"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the labels of an Instance.
  rpc SetInstanceLabels(SetInstanceLabelsRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1beta1/{name=projects/*/locations/*/instances/*}:setLabels"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Instance.
  rpc DeleteInstance(DeleteInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/instances/*}"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Starts a notebook instance.
  rpc StartInstance(StartInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/instances/*}:start"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Stops a notebook instance.
  rpc StopInstance(StopInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/instances/*}:stop"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Resets a notebook instance.
  rpc ResetInstance(ResetInstanceRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/instances/*}:reset"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Allows notebook instances to
  // report their latest instance information to the Notebooks
  // API server. The server will merge the reported information to
  // the instance metadata store. Do not use this method directly.
  rpc ReportInstanceInfo(ReportInstanceInfoRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/instances/*}:report"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Check if a notebook instance is upgradable.
  // Deprecated. Please consider using v1.
  rpc IsInstanceUpgradeable(IsInstanceUpgradeableRequest) returns (IsInstanceUpgradeableResponse) {
    option deprecated = true;
    option (google.api.http) = {
      get: "/v1beta1/{notebook_instance=projects/*/locations/*/instances/*}:isUpgradeable"
    };
  }

  // Upgrades a notebook instance to the latest version.
  // Deprecated. Please consider using v1.
  rpc UpgradeInstance(UpgradeInstanceRequest) returns (google.longrunning.Operation) {
    option deprecated = true;
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/instances/*}:upgrade"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Allows notebook instances to
  // call this endpoint to upgrade themselves. Do not use this method directly.
  // Deprecated. Please consider using v1.
  rpc UpgradeInstanceInternal(UpgradeInstanceInternalRequest) returns (google.longrunning.Operation) {
    option deprecated = true;
    option (google.api.http) = {
      post: "/v1beta1/{name=projects/*/locations/*/instances/*}:upgradeInternal"
      body: "*"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Instance"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists environments in a project.
  rpc ListEnvironments(ListEnvironmentsRequest) returns (ListEnvironmentsResponse) {
    option (google.api.http) = {
      get: "/v1beta1/{parent=projects/*/locations/*}/environments"
    };
  }

  // Gets details of a single Environment.
  rpc GetEnvironment(GetEnvironmentRequest) returns (Environment) {
    option (google.api.http) = {
      get: "/v1beta1/{name=projects/*/locations/*/environments/*}"
    };
  }

  // Creates a new Environment.
  rpc CreateEnvironment(CreateEnvironmentRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1beta1/{parent=projects/*/locations/*}/environments"
      body: "environment"
    };
    option (google.longrunning.operation_info) = {
      response_type: "Environment"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Environment.
  rpc DeleteEnvironment(DeleteEnvironmentRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1beta1/{name=projects/*/locations/*/environments/*}"
    };
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // The time the operation was created.
  google.protobuf.Timestamp create_time = 1;

  // The time the operation finished running.
  google.protobuf.Timestamp end_time = 2;

  // Server-defined resource path for the target of the operation.
  string target = 3;

  // Name of the verb executed by the operation.
  string verb = 4;

  // Human-readable status of the operation, if any.
  string status_message = 5;

  // Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to `Code.CANCELLED`.
  bool requested_cancellation = 6;

  // API version used to start the operation.
  string api_version = 7;

  // API endpoint name of this operation.
  string endpoint = 8;
}

// Request for listing notebook instances.
message ListInstancesRequest {
  // Required. Format:
  // `parent=projects/{project_id}/locations/{location}`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Maximum return size of the list call.
  int32 page_size = 2;

  // A previous returned page token that can be used to continue listing
  // from the last result.
  string page_token = 3;
}

// Response for listing notebook instances.
message ListInstancesResponse {
  // A list of returned instances.
  repeated Instance instances = 1;

  // Page token that can be used to continue listing from the last result in the
  // next list call.
  string next_page_token = 2;

  // Locations that could not be reached. For example,
  // `['us-west1-a', 'us-central1-b']`.
  // A ListInstancesResponse will only contain either instances or unreachables,
  repeated string unreachable = 3;
}

// Request for getting a notebook instance.
message GetInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for creating a notebook instance.
message CreateInstanceRequest {
  // Required. Format:
  // `parent=projects/{project_id}/locations/{location}`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. User-defined unique ID of this instance.
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The instance to be created.
  Instance instance = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for registering a notebook instance.
message RegisterInstanceRequest {
  // Required. Format:
  // `parent=projects/{project_id}/locations/{location}`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. User defined unique ID of this instance. The `instance_id` must
  // be 1 to 63 characters long and contain only lowercase letters,
  // numeric characters, and dashes. The first character must be a lowercase
  // letter and the last character cannot be a dash.
  string instance_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for setting instance accelerator.
message SetInstanceAcceleratorRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Type of this accelerator.
  Instance.AcceleratorType type = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. Count of cores of this accelerator. Note that not all combinations
  // of `type` and `core_count` are valid. Check [GPUs on
  // Compute Engine](https://cloud.google.com/compute/docs/gpus/#gpus-list) to
  // find a valid combination. TPUs are not supported.
  int64 core_count = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for setting instance machine type.
message SetInstanceMachineTypeRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The [Compute Engine machine
  // type](https://cloud.google.com/compute/docs/machine-types).
  string machine_type = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for setting instance labels.
message SetInstanceLabelsRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Labels to apply to this instance.
  // These can be later modified by the setLabels method
  map<string, string> labels = 2;
}

// Request for deleting a notebook instance.
message DeleteInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for starting a notebook instance
message StartInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for stopping a notebook instance
message StopInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for reseting a notebook instance
message ResetInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for notebook instances to report information to Notebooks API.
message ReportInstanceInfoRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The VM hardware token for authenticating the VM.
  // https://cloud.google.com/compute/docs/instances/verifying-instance-identity
  string vm_id = 2 [(google.api.field_behavior) = REQUIRED];

  // The metadata reported to Notebooks API. This will be merged to the instance
  // metadata store
  map<string, string> metadata = 3;
}

// Request for checking if a notebook instance is upgradeable.
message IsInstanceUpgradeableRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string notebook_instance = 1 [(google.api.field_behavior) = REQUIRED];
}

// Response for checking if a notebook instance is upgradeable.
message IsInstanceUpgradeableResponse {
  // If an instance is upgradeable.
  bool upgradeable = 1;

  // The version this instance will be upgraded to if calling the upgrade
  // endpoint. This field will only be populated if field upgradeable is true.
  string upgrade_version = 2;

  // Additional information about upgrade.
  string upgrade_info = 3;

  // The new image self link this instance will be upgraded to if calling the
  // upgrade endpoint. This field will only be populated if field upgradeable
  // is true.
  string upgrade_image = 4;
}

// Request for upgrading a notebook instance
message UpgradeInstanceRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for upgrading a notebook instance from within the VM
message UpgradeInstanceInternalRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The VM hardware token for authenticating the VM.
  // https://cloud.google.com/compute/docs/instances/verifying-instance-identity
  string vm_id = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for listing environments.
message ListEnvironmentsRequest {
  // Required. Format: `projects/{project_id}/locations/{location}`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Maximum return size of the list call.
  int32 page_size = 2;

  // A previous returned page token that can be used to continue listing from
  // the last result.
  string page_token = 3;
}

// Response for listing environments.
message ListEnvironmentsResponse {
  // A list of returned environments.
  repeated Environment environments = 1;

  // A page token that can be used to continue listing from the last result
  // in the next list call.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request for getting a notebook environment.
message GetEnvironmentRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/environments/{environment_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}

// Request for creating a notebook environment.
message CreateEnvironmentRequest {
  // Required. Format: `projects/{project_id}/locations/{location}`
  string parent = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. User-defined unique ID of this environment. The `environment_id` must
  // be 1 to 63 characters long and contain only lowercase letters,
  // numeric characters, and dashes. The first character must be a lowercase
  // letter and the last character cannot be a dash.
  string environment_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The environment to be created.
  Environment environment = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for deleting a notebook environment.
message DeleteEnvironmentRequest {
  // Required. Format:
  // `projects/{project_id}/locations/{location}/environments/{environment_id}`
  string name = 1 [(google.api.field_behavior) = REQUIRED];
}
