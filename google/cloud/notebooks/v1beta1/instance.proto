// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.notebooks.v1beta1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/cloud/notebooks/v1beta1/environment.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Notebooks.V1Beta1";
option go_package = "cloud.google.com/go/notebooks/apiv1beta1/notebookspb;notebookspb";
option java_multiple_files = true;
option java_outer_classname = "InstanceProto";
option java_package = "com.google.cloud.notebooks.v1beta1";
option php_namespace = "Google\\Cloud\\Notebooks\\V1beta1";
option ruby_package = "Google::Cloud::Notebooks::V1beta1";

// Reservation Affinity for consuming Zonal reservation.
message ReservationAffinity {
  // Indicates whether to consume capacity from an reservation or not.
  enum Type {
    // Default type.
    TYPE_UNSPECIFIED = 0;

    // Do not consume from any allocated capacity.
    NO_RESERVATION = 1;

    // Consume any reservation available.
    ANY_RESERVATION = 2;

    // Must consume from a specific reservation. Must specify key value fields
    // for specifying the reservations.
    SPECIFIC_RESERVATION = 3;
  }

  // Optional. Type of reservation to consume
  Type consume_reservation_type = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Corresponds to the label key of reservation resource.
  string key = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Corresponds to the label values of reservation resource.
  repeated string values = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The definition of a notebook instance.
message Instance {
  option (google.api.resource) = {
    type: "notebooks.googleapis.com/Instance"
    pattern: "projects/{project}/instances/{instance}"
  };

  // Definition of the types of hardware accelerators that can be used on this
  // instance.
  enum AcceleratorType {
    // Accelerator type is not specified.
    ACCELERATOR_TYPE_UNSPECIFIED = 0;

    // Accelerator type is Nvidia Tesla K80.
    NVIDIA_TESLA_K80 = 1;

    // Accelerator type is Nvidia Tesla P100.
    NVIDIA_TESLA_P100 = 2;

    // Accelerator type is Nvidia Tesla V100.
    NVIDIA_TESLA_V100 = 3;

    // Accelerator type is Nvidia Tesla P4.
    NVIDIA_TESLA_P4 = 4;

    // Accelerator type is Nvidia Tesla T4.
    NVIDIA_TESLA_T4 = 5;

    // Accelerator type is NVIDIA Tesla T4 Virtual Workstations.
    NVIDIA_TESLA_T4_VWS = 8;

    // Accelerator type is NVIDIA Tesla P100 Virtual Workstations.
    NVIDIA_TESLA_P100_VWS = 9;

    // Accelerator type is NVIDIA Tesla P4 Virtual Workstations.
    NVIDIA_TESLA_P4_VWS = 10;

    // (Coming soon) Accelerator type is TPU V2.
    TPU_V2 = 6;

    // (Coming soon) Accelerator type is TPU V3.
    TPU_V3 = 7;
  }

  // Definition of a hardware accelerator. Note that not all combinations
  // of `type` and `core_count` are valid. Check [GPUs on Compute
  // Engine](https://cloud.google.com/compute/docs/gpus/#gpus-list) to find a
  // valid combination. TPUs are not supported.
  message AcceleratorConfig {
    // Type of this accelerator.
    AcceleratorType type = 1;

    // Count of cores of this accelerator.
    int64 core_count = 2;
  }

  // The definition of the states of this instance.
  enum State {
    // State is not specified.
    STATE_UNSPECIFIED = 0;

    // The control logic is starting the instance.
    STARTING = 1;

    // The control logic is installing required frameworks and registering the
    // instance with notebook proxy
    PROVISIONING = 2;

    // The instance is running.
    ACTIVE = 3;

    // The control logic is stopping the instance.
    STOPPING = 4;

    // The instance is stopped.
    STOPPED = 5;

    // The instance is deleted.
    DELETED = 6;

    // The instance is upgrading.
    UPGRADING = 7;

    // The instance is being created.
    INITIALIZING = 8;

    // The instance is getting registered.
    REGISTERING = 9;

    // The instance is suspending.
    SUSPENDING = 10;

    // The instance is suspended.
    SUSPENDED = 11;
  }

  // Possible disk types for notebook instances.
  enum DiskType {
    // Disk type not set.
    DISK_TYPE_UNSPECIFIED = 0;

    // Standard persistent disk type.
    PD_STANDARD = 1;

    // SSD persistent disk type.
    PD_SSD = 2;

    // Balanced persistent disk type.
    PD_BALANCED = 3;
  }

  // Definition of the disk encryption options.
  enum DiskEncryption {
    // Disk encryption is not specified.
    DISK_ENCRYPTION_UNSPECIFIED = 0;

    // Use Google managed encryption keys to encrypt the boot disk.
    GMEK = 1;

    // Use customer managed encryption keys to encrypt the boot disk.
    CMEK = 2;
  }

  // The type of vNIC driver.
  enum NicType {
    // No type specified. Default should be UNSPECIFIED_NIC_TYPE.
    UNSPECIFIED_NIC_TYPE = 0;

    // VIRTIO. Default in Notebooks DLVM.
    VIRTIO_NET = 1;

    // GVNIC. Alternative to VIRTIO.
    // https://github.com/GoogleCloudPlatform/compute-virtual-ethernet-linux
    GVNIC = 2;
  }

  // Output only. The name of this notebook instance. Format:
  // `projects/{project_id}/locations/{location}/instances/{instance_id}`
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Type of the environment; can be one of VM image, or container image.
  oneof environment {
    // Use a Compute Engine VM image to start the notebook instance.
    VmImage vm_image = 2;

    // Use a container image to start the notebook instance.
    ContainerImage container_image = 3;
  }

  // Path to a Bash script that automatically runs after a notebook instance
  // fully boots up. The path must be a URL or
  // Cloud Storage path (`gs://path-to-file/file-name`).
  string post_startup_script = 4;

  // Output only. The proxy endpoint that is used to access the Jupyter notebook.
  string proxy_uri = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Input only. The owner of this instance after creation. Format: `<EMAIL>`
  //
  // Currently supports one owner only. If not specified, all of the service
  // account users of your VM instance's service account can use
  // the instance.
  repeated string instance_owners = 6 [(google.api.field_behavior) = INPUT_ONLY];

  // The service account on this instance, giving access to other Google
  // Cloud services.
  // You can use any service account within the same project, but you
  // must have the service account user permission to use the instance.
  //
  // If not specified, the [Compute Engine default service
  // account](https://cloud.google.com/compute/docs/access/service-accounts#default_service_account)
  // is used.
  string service_account = 7;

  // Required. The [Compute Engine machine
  // type](https://cloud.google.com/compute/docs/machine-types) of this
  // instance.
  string machine_type = 8 [(google.api.field_behavior) = REQUIRED];

  // The hardware accelerator used on this instance. If you use
  // accelerators, make sure that your configuration has
  // [enough vCPUs and memory to support the `machine_type` you have
  // selected](https://cloud.google.com/compute/docs/gpus/#gpus-list).
  AcceleratorConfig accelerator_config = 9;

  // Output only. The state of this instance.
  State state = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Whether the end user authorizes Google Cloud to install GPU driver
  // on this instance.
  // If this field is empty or set to false, the GPU driver won't be installed.
  // Only applicable to instances with GPUs.
  bool install_gpu_driver = 11;

  // Specify a custom Cloud Storage path where the GPU driver is stored.
  // If not specified, we'll automatically choose from official GPU drivers.
  string custom_gpu_driver_path = 12;

  // Input only. The type of the boot disk attached to this instance, defaults to
  // standard persistent disk (`PD_STANDARD`).
  DiskType boot_disk_type = 13 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. The size of the boot disk in GB attached to this instance, up to a maximum
  // of 64000 GB (64 TB). The minimum recommended value is 100 GB. If not
  // specified, this defaults to 100.
  int64 boot_disk_size_gb = 14 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. The type of the data disk attached to this instance, defaults to
  // standard persistent disk (`PD_STANDARD`).
  DiskType data_disk_type = 25 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. The size of the data disk in GB attached to this instance, up to a maximum
  // of 64000 GB (64 TB). You can choose the size of the data disk based on how
  // big your notebooks and data are. If not specified, this defaults to 100.
  int64 data_disk_size_gb = 26 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. If true, the data disk will not be auto deleted when deleting the instance.
  bool no_remove_data_disk = 27 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. Disk encryption method used on the boot and data disks, defaults to GMEK.
  DiskEncryption disk_encryption = 15 [(google.api.field_behavior) = INPUT_ONLY];

  // Input only. The KMS key used to encrypt the disks, only applicable if disk_encryption
  // is CMEK.
  // Format:
  // `projects/{project_id}/locations/{location}/keyRings/{key_ring_id}/cryptoKeys/{key_id}`
  //
  // Learn more about [using your own encryption
  // keys](https://cloud.google.com/kms/docs/quickstart).
  string kms_key = 16 [(google.api.field_behavior) = INPUT_ONLY];

  // If true, no public IP will be assigned to this instance.
  bool no_public_ip = 17;

  // If true, the notebook instance will not register with the proxy.
  bool no_proxy_access = 18;

  // The name of the VPC that this instance is in.
  // Format:
  // `projects/{project_id}/global/networks/{network_id}`
  string network = 19;

  // The name of the subnet that this instance is in.
  // Format:
  // `projects/{project_id}/regions/{region}/subnetworks/{subnetwork_id}`
  string subnet = 20;

  // Labels to apply to this instance.
  // These can be later modified by the setLabels method.
  map<string, string> labels = 21;

  // Custom metadata to apply to this instance.
  map<string, string> metadata = 22;

  // Optional. The type of vNIC to be used on this interface. This may be gVNIC or
  // VirtioNet.
  NicType nic_type = 28 [(google.api.field_behavior) = OPTIONAL];

  // Optional. The optional reservation affinity. Setting this field will apply
  // the specified [Zonal Compute
  // Reservation](https://cloud.google.com/compute/docs/instances/reserving-zonal-resources)
  // to this notebook instance.
  ReservationAffinity reservation_affinity = 29 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Flag to enable ip forwarding or not, default false/off.
  // https://cloud.google.com/vpc/docs/using-routes#canipforward
  bool can_ip_forward = 31 [(google.api.field_behavior) = OPTIONAL];

  // Output only. Instance creation time.
  google.protobuf.Timestamp create_time = 23 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Instance update time.
  google.protobuf.Timestamp update_time = 24 [(google.api.field_behavior) = OUTPUT_ONLY];
}
