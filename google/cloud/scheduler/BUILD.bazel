# This build file includes a target for the Ruby wrapper library for
# google-cloud-scheduler.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for cloudscheduler.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "cloudscheduler_ruby_wrapper",
    srcs = ["//google/cloud/scheduler/v1:scheduler_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-scheduler",
        "ruby-cloud-env-prefix=SCHEDULER",
        "ruby-cloud-wrapper-of=v1:0.10;v1beta1:0.10",
        "ruby-cloud-product-url=https://cloud.google.com/scheduler",
        "ruby-cloud-api-id=cloudscheduler.googleapis.com",
        "ruby-cloud-api-shortname=cloudscheduler",
        "ruby-cloud-migration-version=2.0",
    ],
    ruby_cloud_description = "Cloud Scheduler is a fully managed enterprise-grade cron job scheduler. It allows you to schedule virtually any job, including batch, big data jobs, cloud infrastructure operations, and more. You can automate everything, including retries in case of failure to reduce manual toil and intervention. Cloud Scheduler even acts as a single pane of glass, allowing you to manage all your automation tasks from one place.",
    ruby_cloud_title = "Cloud Scheduler",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-scheduler-ruby",
    deps = [
        ":cloudscheduler_ruby_wrapper",
    ],
)
