# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "scheduler_proto",
    srcs = [
        "cloudscheduler.proto",
        "job.proto",
        "target.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/rpc:status_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "scheduler_proto_with_info",
    deps = [
        ":scheduler_proto",
        "//google/cloud:common_resources_proto",
        "//google/cloud/location:location_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "scheduler_java_proto",
    deps = [":scheduler_proto"],
)

java_grpc_library(
    name = "scheduler_java_grpc",
    srcs = [":scheduler_proto"],
    deps = [":scheduler_java_proto"],
)

java_gapic_library(
    name = "scheduler_java_gapic",
    srcs = [":scheduler_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "cloudscheduler_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudscheduler_v1beta1.yaml",
    test_deps = [
        ":scheduler_java_grpc",
        "//google/cloud/location:location_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":scheduler_java_proto",
        "//google/api:api_java_proto",
        "//google/cloud/location:location_java_proto",
    ],
)

java_gapic_test(
    name = "scheduler_java_gapic_test_suite",
    test_classes = [
        "com.google.cloud.scheduler.v1beta1.CloudSchedulerClientHttpJsonTest",
        "com.google.cloud.scheduler.v1beta1.CloudSchedulerClientTest",
    ],
    runtime_deps = [":scheduler_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-scheduler-v1beta1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":scheduler_java_gapic",
        ":scheduler_java_grpc",
        ":scheduler_java_proto",
        ":scheduler_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "scheduler_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/scheduler/apiv1beta1/schedulerpb",
    protos = [":scheduler_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/rpc:status_go_proto",
    ],
)

go_gapic_library(
    name = "scheduler_go_gapic",
    srcs = [":scheduler_proto_with_info"],
    grpc_service_config = "cloudscheduler_grpc_service_config.json",
    importpath = "cloud.google.com/go/scheduler/apiv1beta1;scheduler",
    metadata = True,
    release_level = "beta",
    rest_numeric_enums = True,
    service_yaml = "cloudscheduler_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":scheduler_go_proto",
        "//google/cloud/location:location_go_proto",
        "@io_bazel_rules_go//proto/wkt:duration_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-scheduler-v1beta1-go",
    deps = [
        ":scheduler_go_gapic",
        ":scheduler_go_gapic_srcjar-metadata.srcjar",
        ":scheduler_go_gapic_srcjar-snippets.srcjar",
        ":scheduler_go_gapic_srcjar-test.srcjar",
        ":scheduler_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "scheduler_py_gapic",
    srcs = [":scheduler_proto"],
    grpc_service_config = "cloudscheduler_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudscheduler_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
    ],
)

py_test(
    name = "scheduler_py_gapic_test",
    srcs = [
        "scheduler_py_gapic_pytest.py",
        "scheduler_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":scheduler_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "scheduler-v1beta1-py",
    deps = [
        ":scheduler_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "scheduler_php_proto",
    deps = [":scheduler_proto"],
)

php_gapic_library(
    name = "scheduler_php_gapic",
    srcs = [":scheduler_proto_with_info"],
    grpc_service_config = "cloudscheduler_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudscheduler_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [":scheduler_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-scheduler-v1beta1-php",
    deps = [
        ":scheduler_php_gapic",
        ":scheduler_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "scheduler_nodejs_gapic",
    package_name = "@google-cloud/scheduler",
    src = ":scheduler_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "cloudscheduler_grpc_service_config.json",
    main_service = "scheduler",
    package = "google.cloud.scheduler.v1beta1",
    rest_numeric_enums = True,
    service_yaml = "cloudscheduler_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "scheduler-v1beta1-nodejs",
    deps = [
        ":scheduler_nodejs_gapic",
        ":scheduler_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "scheduler_ruby_proto",
    deps = [":scheduler_proto"],
)

ruby_grpc_library(
    name = "scheduler_ruby_grpc",
    srcs = [":scheduler_proto"],
    deps = [":scheduler_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "scheduler_ruby_gapic",
    srcs = [":scheduler_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=cloudscheduler.googleapis.com",
        "ruby-cloud-api-shortname=cloudscheduler",
        "ruby-cloud-env-prefix=SCHEDULER",
        "ruby-cloud-gem-name=google-cloud-scheduler-v1beta1",
        "ruby-cloud-product-url=https://cloud.google.com/scheduler",
    ],
    grpc_service_config = "cloudscheduler_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Cloud Scheduler is a fully managed enterprise-grade cron job scheduler. It allows you to schedule virtually any job, including batch, big data jobs, cloud infrastructure operations, and more. You can automate everything, including retries in case of failure to reduce manual toil and intervention. Cloud Scheduler even acts as a single pane of glass, allowing you to manage all your automation tasks from one place.",
    ruby_cloud_title = "Cloud Scheduler V1beta1",
    service_yaml = "cloudscheduler_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":scheduler_ruby_grpc",
        ":scheduler_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-scheduler-v1beta1-ruby",
    deps = [
        ":scheduler_ruby_gapic",
        ":scheduler_ruby_grpc",
        ":scheduler_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "scheduler_csharp_proto",
    deps = [":scheduler_proto"],
)

csharp_grpc_library(
    name = "scheduler_csharp_grpc",
    srcs = [":scheduler_proto"],
    deps = [":scheduler_csharp_proto"],
)

csharp_gapic_library(
    name = "scheduler_csharp_gapic",
    srcs = [":scheduler_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "cloudscheduler_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudscheduler_v1beta1.yaml",
    transport = "grpc+rest",
    deps = [
        ":scheduler_csharp_grpc",
        ":scheduler_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-scheduler-v1beta1-csharp",
    deps = [
        ":scheduler_csharp_gapic",
        ":scheduler_csharp_grpc",
        ":scheduler_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "scheduler_cc_proto",
    deps = [":scheduler_proto"],
)

cc_grpc_library(
    name = "scheduler_cc_grpc",
    srcs = [":scheduler_proto"],
    grpc_only = True,
    deps = [":scheduler_cc_proto"],
)
