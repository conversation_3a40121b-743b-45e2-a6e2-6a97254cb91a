// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.backupdr.v1;

import "google/api/field_behavior.proto";
import "google/api/field_info.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Cloud.BackupDR.V1";
option go_package = "cloud.google.com/go/backupdr/apiv1/backupdrpb;backupdrpb";
option java_multiple_files = true;
option java_outer_classname = "BackupPlanAssociationProto";
option java_package = "com.google.cloud.backupdr.v1";
option php_namespace = "Google\\Cloud\\BackupDR\\V1";
option ruby_package = "Google::Cloud::BackupDR::V1";

// A BackupPlanAssociation represents a single BackupPlanAssociation which
// contains details like workload, backup plan etc
message BackupPlanAssociation {
  option (google.api.resource) = {
    type: "backupdr.googleapis.com/BackupPlanAssociation"
    pattern: "projects/{project}/locations/{location}/backupPlanAssociations/{backup_plan_association}"
    plural: "backupPlanAssociations"
    singular: "backupPlanAssociation"
  };

  // Enum for State of BackupPlan Association
  enum State {
    // State not set.
    STATE_UNSPECIFIED = 0;

    // The resource is being created.
    CREATING = 1;

    // The resource has been created and is fully usable.
    ACTIVE = 2;

    // The resource is being deleted.
    DELETING = 3;

    // The resource has been created but is not usable.
    INACTIVE = 4;
  }

  // Output only. Identifier. The resource name of BackupPlanAssociation in
  // below format Format :
  // projects/{project}/locations/{location}/backupPlanAssociations/{backupPlanAssociationId}
  string name = 1 [
    (google.api.field_behavior) = IDENTIFIER,
    (google.api.field_behavior) = OUTPUT_ONLY
  ];

  // Required. Immutable. Resource type of workload on which backupplan is
  // applied
  string resource_type = 2 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = REQUIRED
  ];

  // Required. Immutable. Resource name of workload on which backupplan is
  // applied
  string resource = 3 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.field_behavior) = REQUIRED
  ];

  // Required. Resource name of backup plan which needs to be applied on
  // workload. Format:
  // projects/{project}/locations/{location}/backupPlans/{backupPlanId}
  string backup_plan = 4 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "backupdr.googleapis.com/BackupPlan"
    }
  ];

  // Output only. The time when the instance was created.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time when the instance was updated.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The BackupPlanAssociation resource state.
  State state = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The config info related to backup rules.
  repeated RuleConfigInfo rules_config_info = 8
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Resource name of data source which will be used as storage
  // location for backups taken. Format :
  // projects/{project}/locations/{location}/backupVaults/{backupvault}/dataSources/{datasource}
  string data_source = 9 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Message for rules config info.
message RuleConfigInfo {
  // Enum for LastBackupState
  enum LastBackupState {
    // State not set.
    LAST_BACKUP_STATE_UNSPECIFIED = 0;

    // The first backup is pending.
    FIRST_BACKUP_PENDING = 1;

    // The most recent backup could not be run/failed because of the lack of
    // permissions.
    PERMISSION_DENIED = 2;

    // The last backup operation succeeded.
    SUCCEEDED = 3;

    // The last backup operation failed.
    FAILED = 4;
  }

  // Output only. Backup Rule id fetched from backup plan.
  string rule_id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The last backup state for rule.
  LastBackupState last_backup_state = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. google.rpc.Status object to store the last backup error.
  google.rpc.Status last_backup_error = 4
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The point in time when the last successful backup was captured
  // from the source.
  google.protobuf.Timestamp last_successful_backup_consistency_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for creating a backup plan.
message CreateBackupPlanAssociationRequest {
  // Required. The backup plan association project and location in the format
  // `projects/{project_id}/locations/{location}`. In Cloud BackupDR locations
  // map to GCP regions, for example **us-central1**.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "backupdr.googleapis.com/BackupPlanAssociation"
    }
  ];

  // Required. The name of the backup plan association to create. The name must
  // be unique for the specified project and location.
  string backup_plan_association_id = 2
      [(google.api.field_behavior) = REQUIRED];

  // Required. The resource being created
  BackupPlanAssociation backup_plan_association = 3
      [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Request message for List BackupPlanAssociation
message ListBackupPlanAssociationsRequest {
  // Required. The project and location for which to retrieve backup Plan
  // Associations information, in the format
  // `projects/{project_id}/locations/{location}`. In Cloud BackupDR, locations
  // map to GCP regions, for example **us-central1**. To retrieve backup plan
  // associations for all locations, use "-" for the
  // `{location}` value.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      child_type: "backupdr.googleapis.com/BackupPlanAssociation"
    }
  ];

  // Optional. Requested page size. Server may return fewer items than
  // requested. If unspecified, server will pick an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A token identifying a page of results the server should return.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Filtering results
  string filter = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for List BackupPlanAssociation
message ListBackupPlanAssociationsResponse {
  // The list of Backup Plan Associations in the project for the specified
  // location.
  //
  // If the `{location}` value in the request is "-", the response contains a
  // list of instances from all locations. In case any location is unreachable,
  // the response will only return backup plan associations in reachable
  // locations and the 'unreachable' field will be populated with a list of
  // unreachable locations.
  repeated BackupPlanAssociation backup_plan_associations = 1;

  // A token identifying a page of results the server should return.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for getting a BackupPlanAssociation resource.
message GetBackupPlanAssociationRequest {
  // Required. Name of the backup plan association resource, in the format
  // `projects/{project}/locations/{location}/backupPlanAssociations/{backupPlanAssociationId}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "backupdr.googleapis.com/BackupPlanAssociation"
    }
  ];
}

// Request message for deleting a backup plan association.
message DeleteBackupPlanAssociationRequest {
  // Required. Name of the backup plan association resource, in the format
  // `projects/{project}/locations/{location}/backupPlanAssociations/{backupPlanAssociationId}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "backupdr.googleapis.com/BackupPlanAssociation"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Request message for triggering a backup.
message TriggerBackupRequest {
  // Required. Name of the backup plan association resource, in the format
  // `projects/{project}/locations/{location}/backupPlanAssociations/{backupPlanAssociationId}`
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "backupdr.googleapis.com/BackupPlanAssociation"
    }
  ];

  // Required. backup rule_id for which a backup needs to be triggered.
  string rule_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and
  // the request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [
    (google.api.field_info).format = UUID4,
    (google.api.field_behavior) = OPTIONAL
  ];
}
