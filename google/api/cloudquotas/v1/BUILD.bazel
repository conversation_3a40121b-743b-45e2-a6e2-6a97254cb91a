# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "cloudquotas_proto",
    srcs = [
        "cloudquotas.proto",
        "resources.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@com_google_protobuf//:wrappers_proto",
    ],
)

proto_library_with_info(
    name = "cloudquotas_proto_with_info",
    deps = [
        ":cloudquotas_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "cloudquotas_java_proto",
    deps = [":cloudquotas_proto"],
)

java_grpc_library(
    name = "cloudquotas_java_grpc",
    srcs = [":cloudquotas_proto"],
    deps = [":cloudquotas_java_proto"],
)

java_gapic_library(
    name = "cloudquotas_java_gapic",
    srcs = [":cloudquotas_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "cloudquotas_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudquotas_v1.yaml",
    test_deps = [
        ":cloudquotas_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":cloudquotas_java_proto",
        "//google/api:api_java_proto",
    ],
)

java_gapic_test(
    name = "cloudquotas_java_gapic_test_suite",
    test_classes = [
        "com.google.api.cloudquotas.v1.CloudQuotasClientHttpJsonTest",
        "com.google.api.cloudquotas.v1.CloudQuotasClientTest",
    ],
    runtime_deps = [":cloudquotas_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-api-cloudquotas-v1-java",
    transport = "grpc+rest",
    deps = [
        ":cloudquotas_java_gapic",
        ":cloudquotas_java_grpc",
        ":cloudquotas_java_proto",
        ":cloudquotas_proto",
    ],
    include_samples = True,
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "cloudquotas_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/cloudquotas/apiv1/cloudquotaspb",
    protos = [":cloudquotas_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

go_gapic_library(
    name = "cloudquotas_go_gapic",
    srcs = [":cloudquotas_proto_with_info"],
    grpc_service_config = "cloudquotas_v1_grpc_service_config.json",
    importpath = "cloud.google.com/go/cloudquotas/apiv1;cloudquotas",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "cloudquotas_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":cloudquotas_go_proto",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-api-cloudquotas-v1-go",
    deps = [
        ":cloudquotas_go_gapic",
        ":cloudquotas_go_gapic_srcjar-test.srcjar",
        ":cloudquotas_go_gapic_srcjar-metadata.srcjar",
        ":cloudquotas_go_gapic_srcjar-snippets.srcjar",
        ":cloudquotas_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_gapic_library",
    "py_test",
)

py_gapic_library(
    name = "cloudquotas_py_gapic",
    srcs = [":cloudquotas_proto"],
    grpc_service_config = "cloudquotas_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudquotas_v1.yaml",
    transport = "grpc+rest",
    deps = [],
    opt_args = [
        "python-gapic-namespace=google.cloud",
        "warehouse-package-name=google-cloud-quotas",
    ],
)

py_test(
    name = "cloudquotas_py_gapic_test",
    srcs = [
        "cloudquotas_py_gapic_pytest.py",
        "cloudquotas_py_gapic_test.py",
    ],
    legacy_create_init = False,
    deps = [":cloudquotas_py_gapic"],
)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "api-cloudquotas-v1-py",
    deps = [
        ":cloudquotas_py_gapic",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "cloudquotas_php_proto",
    deps = [":cloudquotas_proto"],
)

php_gapic_library(
    name = "cloudquotas_php_gapic",
    srcs = [":cloudquotas_proto_with_info"],
    grpc_service_config = "cloudquotas_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    migration_mode = "NEW_SURFACE_ONLY",
    service_yaml = "cloudquotas_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":cloudquotas_php_proto",
    ],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-api-cloudquotas-v1-php",
    deps = [
        ":cloudquotas_php_gapic",
        ":cloudquotas_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "cloudquotas_nodejs_gapic",
    package_name = "@google-cloud/cloudquotas",
    src = ":cloudquotas_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "cloudquotas_v1_grpc_service_config.json",
    package = "google.api.cloudquotas.v1",
    rest_numeric_enums = True,
    service_yaml = "cloudquotas_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "api-cloudquotas-v1-nodejs",
    deps = [
        ":cloudquotas_nodejs_gapic",
        ":cloudquotas_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_gapic_assembly_pkg",
    "ruby_cloud_gapic_library",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "cloudquotas_ruby_proto",
    deps = [":cloudquotas_proto"],
)

ruby_grpc_library(
    name = "cloudquotas_ruby_grpc",
    srcs = [":cloudquotas_proto"],
    deps = [":cloudquotas_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "cloudquotas_ruby_gapic",
    srcs = [":cloudquotas_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-cloud_quotas-v1",
    ],
    grpc_service_config = "cloudquotas_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudquotas_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":cloudquotas_ruby_grpc",
        ":cloudquotas_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-cloudquotas-v1-ruby",
    deps = [
        ":cloudquotas_ruby_gapic",
        ":cloudquotas_ruby_grpc",
        ":cloudquotas_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "cloudquotas_csharp_proto",
    extra_opts = [],
    deps = [":cloudquotas_proto"],
)

csharp_grpc_library(
    name = "cloudquotas_csharp_grpc",
    srcs = [":cloudquotas_proto"],
    deps = [":cloudquotas_csharp_proto"],
)

csharp_gapic_library(
    name = "cloudquotas_csharp_gapic",
    srcs = [":cloudquotas_proto_with_info"],
    grpc_service_config = "cloudquotas_v1_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "cloudquotas_v1.yaml",
    deps = [
        ":cloudquotas_csharp_grpc",
        ":cloudquotas_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-api-cloudquotas-v1-csharp",
    deps = [
        ":cloudquotas_csharp_gapic",
        ":cloudquotas_csharp_grpc",
        ":cloudquotas_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "cloudquotas_cc_proto",
    deps = [":cloudquotas_proto"],
)

cc_grpc_library(
    name = "cloudquotas_cc_grpc",
    srcs = [":cloudquotas_proto"],
    grpc_only = True,
    deps = [":cloudquotas_cc_proto"],
)
