type: google.api.Service
config_version: 3
name: logging.googleapis.com
title: Cloud Logging API

apis:
- name: google.logging.v2.ConfigServiceV2
- name: google.logging.v2.LoggingServiceV2
- name: google.logging.v2.MetricsServiceV2
- name: google.longrunning.Operations

types:
- name: google.logging.v2.BucketMetadata
- name: google.logging.v2.CopyLogEntriesMetadata
- name: google.logging.v2.CopyLogEntriesResponse
- name: google.logging.v2.LinkMetadata
- name: google.logging.v2.LocationMetadata

documentation:
  summary: Writes log entries and manages your Cloud Logging configuration.
  overview: |-
    # Introduction

    The Cloud Logging service.

backend:
  rules:
  - selector: 'google.logging.v2.ConfigServiceV2.*'
    deadline: 60.0
  - selector: google.logging.v2.ConfigServiceV2.CreateBucket
    deadline: 600.0
  - selector: google.logging.v2.ConfigServiceV2.UpdateBucket
    deadline: 600.0
  - selector: 'google.logging.v2.LoggingServiceV2.*'
    deadline: 60.0
  - selector: google.logging.v2.LoggingServiceV2.ListLogEntries
    deadline: 10.0
  - selector: google.logging.v2.LoggingServiceV2.TailLogEntries
    deadline: 3600.0
  - selector: 'google.logging.v2.MetricsServiceV2.*'
    deadline: 60.0
  - selector: 'google.longrunning.Operations.*'
    deadline: 60.0

http:
  rules:
  - selector: google.longrunning.Operations.CancelOperation
    post: '/v2/{name=*/*/locations/*/operations/*}:cancel'
    body: '*'
    additional_bindings:
    - post: '/v2/{name=projects/*/locations/*/operations/*}:cancel'
      body: '*'
    - post: '/v2/{name=organizations/*/locations/*/operations/*}:cancel'
      body: '*'
    - post: '/v2/{name=folders/*/locations/*/operations/*}:cancel'
      body: '*'
    - post: '/v2/{name=billingAccounts/*/locations/*/operations/*}:cancel'
      body: '*'
  - selector: google.longrunning.Operations.GetOperation
    get: '/v2/{name=*/*/locations/*/operations/*}'
    additional_bindings:
    - get: '/v2/{name=projects/*/locations/*/operations/*}'
    - get: '/v2/{name=organizations/*/locations/*/operations/*}'
    - get: '/v2/{name=folders/*/locations/*/operations/*}'
    - get: '/v2/{name=billingAccounts/*/locations/*/operations/*}'
  - selector: google.longrunning.Operations.ListOperations
    get: '/v2/{name=*/*/locations/*}/operations'
    additional_bindings:
    - get: '/v2/{name=projects/*/locations/*}/operations'
    - get: '/v2/{name=organizations/*/locations/*}/operations'
    - get: '/v2/{name=folders/*/locations/*}/operations'
    - get: '/v2/{name=billingAccounts/*/locations/*}/operations'

authentication:
  rules:
  - selector: 'google.logging.v2.ConfigServiceV2.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/logging.admin
  - selector: google.logging.v2.ConfigServiceV2.GetBucket
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.read
  - selector: google.logging.v2.ConfigServiceV2.GetCmekSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.read
  - selector: google.logging.v2.ConfigServiceV2.GetExclusion
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.read
  - selector: google.logging.v2.ConfigServiceV2.GetLink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.read
  - selector: google.logging.v2.ConfigServiceV2.GetSettings
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.read
  - selector: google.logging.v2.ConfigServiceV2.GetSink
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.read
  - selector: google.logging.v2.ConfigServiceV2.GetView
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.read
  - selector: google.logging.v2.ConfigServiceV2.ListBuckets
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.read
  - selector: google.logging.v2.ConfigServiceV2.ListExclusions
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.read
  - selector: google.logging.v2.ConfigServiceV2.ListLinks
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.read
  - selector: google.logging.v2.ConfigServiceV2.ListSinks
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.read
  - selector: google.logging.v2.ConfigServiceV2.ListViews
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.read
  - selector: 'google.logging.v2.LoggingServiceV2.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.read
  - selector: google.logging.v2.LoggingServiceV2.DeleteLog
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/logging.admin
  - selector: google.logging.v2.LoggingServiceV2.WriteLogEntries
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.write
  - selector: 'google.logging.v2.MetricsServiceV2.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.write
  - selector: google.logging.v2.MetricsServiceV2.GetLogMetric
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.read
  - selector: google.logging.v2.MetricsServiceV2.ListLogMetrics
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.read
  - selector: google.longrunning.Operations.CancelOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/logging.admin
  - selector: google.longrunning.Operations.GetOperation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.read
  - selector: google.longrunning.Operations.ListOperations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform,
        https://www.googleapis.com/auth/cloud-platform.read-only,
        https://www.googleapis.com/auth/logging.admin,
        https://www.googleapis.com/auth/logging.read

publishing:
  documentation_uri: https://cloud.google.com/logging/docs/
