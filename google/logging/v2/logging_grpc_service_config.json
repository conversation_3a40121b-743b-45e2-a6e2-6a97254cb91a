{"methodConfig": [{"name": [{"service": "google.logging.v2.MetricsServiceV2", "method": "CreateLogMetric"}], "timeout": "60s"}, {"name": [{"service": "google.logging.v2.LoggingServiceV2", "method": "DeleteLog"}, {"service": "google.logging.v2.LoggingServiceV2", "method": "WriteLogEntries"}, {"service": "google.logging.v2.LoggingServiceV2", "method": "ListLogEntries"}, {"service": "google.logging.v2.LoggingServiceV2", "method": "ListMonitoredResourceDescriptors"}, {"service": "google.logging.v2.LoggingServiceV2", "method": "ListLogs"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "INTERNAL", "UNAVAILABLE"]}}, {"name": [{"service": "google.logging.v2.LoggingServiceV2", "method": "TailLogEntries"}], "timeout": "3600s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "INTERNAL", "UNAVAILABLE"]}}, {"name": [{"service": "google.logging.v2.ConfigServiceV2", "method": "ListSinks"}, {"service": "google.logging.v2.ConfigServiceV2", "method": "GetSink"}, {"service": "google.logging.v2.ConfigServiceV2", "method": "UpdateSink"}, {"service": "google.logging.v2.ConfigServiceV2", "method": "DeleteSink"}, {"service": "google.logging.v2.ConfigServiceV2", "method": "ListExclusions"}, {"service": "google.logging.v2.ConfigServiceV2", "method": "GetExclusion"}, {"service": "google.logging.v2.ConfigServiceV2", "method": "DeleteExclusion"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "INTERNAL", "UNAVAILABLE"]}}, {"name": [{"service": "google.logging.v2.ConfigServiceV2", "method": "CreateSink"}, {"service": "google.logging.v2.ConfigServiceV2", "method": "CreateExclusion"}, {"service": "google.logging.v2.ConfigServiceV2", "method": "UpdateExclusion"}], "timeout": "120s"}, {"name": [{"service": "google.logging.v2.MetricsServiceV2", "method": "ListLogMetrics"}, {"service": "google.logging.v2.MetricsServiceV2", "method": "GetLogMetric"}, {"service": "google.logging.v2.MetricsServiceV2", "method": "UpdateLogMetric"}, {"service": "google.logging.v2.MetricsServiceV2", "method": "DeleteLogMetric"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "0.100s", "maxBackoff": "60s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["DEADLINE_EXCEEDED", "INTERNAL", "UNAVAILABLE"]}}]}