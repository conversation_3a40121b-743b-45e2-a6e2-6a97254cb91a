# This file was automatically generated by BuildFileGenerator

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "container_proto",
    srcs = [
        "cluster_service.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "@com_google_protobuf//:empty_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "container_java_proto",
    deps = [":container_proto"],
)

java_grpc_library(
    name = "container_java_grpc",
    srcs = [":container_proto"],
    deps = [":container_java_proto"],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_proto_library",
)

go_proto_library(
    name = "container_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "google.golang.org/genproto/googleapis/container/v1alpha1",
    protos = [":container_proto"],
    deps = [
        "//google/api:annotations_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "moved_proto_library",
    "py_grpc_library",
    "py_proto_library",
)

moved_proto_library(
    name = "container_moved_proto",
    srcs = [":container_proto"],
    deps = [
        "//google/api:annotations_proto",
        "@com_google_protobuf//:empty_proto",
    ],
)

py_proto_library(
    name = "container_py_proto",
    deps = [":container_moved_proto"],
)

py_grpc_library(
    name = "container_py_grpc",
    srcs = [":container_moved_proto"],
    deps = [":container_py_proto"],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_proto_library",
)

php_proto_library(
    name = "container_php_proto",
    deps = [":container_proto"],
)

##############################################################################
# Node.js
##############################################################################

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "container_ruby_proto",
    deps = [":container_proto"],
)

ruby_grpc_library(
    name = "container_ruby_grpc",
    srcs = [":container_proto"],
    deps = [":container_ruby_proto"],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "container_csharp_proto",
    deps = [":container_proto"],
)

csharp_grpc_library(
    name = "container_csharp_grpc",
    srcs = [":container_proto"],
    deps = [":container_csharp_proto"],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "container_cc_proto",
    deps = [":container_proto"],
)

cc_grpc_library(
    name = "container_cc_grpc",
    srcs = [":container_proto"],
    grpc_only = True,
    deps = [":container_cc_proto"],
)