# This file was automatically generated by BuildFileGenerator
# https://github.com/googleapis/rules_gapic/tree/master/bazel

# Most of the manual changes to this file will be overwritten.
# It's **only** allowed to change the following rule attribute values:
# - names of *_gapic_assembly_* rules
# - certain parameters of *_gapic_library rules, including but not limited to:
#    * extra_protoc_parameters
#    * extra_protoc_file_parameters
# The complete list of preserved parameters can be found in the source code.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

##############################################################################
# Common
##############################################################################
load("@rules_proto//proto:defs.bzl", "proto_library")
load("@com_google_googleapis_imports//:imports.bzl", "proto_library_with_info")

proto_library(
    name = "accesscontextmanager_proto",
    srcs = [
        "access_context_manager.proto",
        "access_level.proto",
        "access_policy.proto",
        "gcp_user_access_binding.proto",
        "service_perimeter.proto",
    ],
    deps = [
        "//google/api:annotations_proto",
        "//google/api:client_proto",
        "//google/api:field_behavior_proto",
        "//google/api:resource_proto",
        "//google/iam/v1:iam_policy_proto",
        "//google/iam/v1:policy_proto",
        "//google/identity/accesscontextmanager/type:type_proto",
        "//google/longrunning:operations_proto",
        "//google/type:expr_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library_with_info(
    name = "accesscontextmanager_proto_with_info",
    deps = [
        ":accesscontextmanager_proto",
        "//google/cloud:common_resources_proto",
    ],
)

proto_library(
    name = "access_level_proto",
    srcs = [
        "access_level.proto",
    ],
    deps = [
        "//google/api:resource_proto",
        "//google/identity/accesscontextmanager/type:type_proto",
        "//google/type:expr_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library(
    name = "access_policy_proto",
    srcs = [
        "access_policy.proto",
    ],
    deps = [
        "//google/api:resource_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

proto_library(
    name = "service_perimeter_proto",
    srcs = [
        "service_perimeter.proto",
    ],
    deps = [
        "//google/api:resource_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

##############################################################################
# Java
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "java_gapic_assembly_gradle_pkg",
    "java_gapic_library",
    "java_gapic_test",
    "java_grpc_library",
    "java_proto_library",
)

java_proto_library(
    name = "accesscontextmanager_java_proto",
    deps = [":accesscontextmanager_proto"],
)

java_grpc_library(
    name = "accesscontextmanager_java_grpc",
    srcs = [":accesscontextmanager_proto"],
    deps = [":accesscontextmanager_java_proto"],
)

java_gapic_library(
    name = "accesscontextmanager_java_gapic",
    srcs = [":accesscontextmanager_proto_with_info"],
    gapic_yaml = None,
    grpc_service_config = "accesscontextmanager_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "accesscontextmanager_v1.yaml",
    test_deps = [
        ":accesscontextmanager_java_grpc",
        "//google/iam/v1:iam_java_grpc",
    ],
    transport = "grpc+rest",
    deps = [
        ":accesscontextmanager_java_proto",
        "//google/api:api_java_proto",
        "//google/iam/v1:iam_java_proto",
    ],
)

java_gapic_test(
    name = "accesscontextmanager_java_gapic_test_suite",
    test_classes = [
        "com.google.identity.accesscontextmanager.v1.AccessContextManagerClientHttpJsonTest",
        "com.google.identity.accesscontextmanager.v1.AccessContextManagerClientTest",
    ],
    runtime_deps = [":accesscontextmanager_java_gapic_test"],
)

# Open Source Packages
java_gapic_assembly_gradle_pkg(
    name = "google-cloud-accesscontextmanager-v1-java",
    include_samples = True,
    transport = "grpc+rest",
    deps = [
        ":accesscontextmanager_java_gapic",
        ":accesscontextmanager_java_grpc",
        ":accesscontextmanager_java_proto",
        ":accesscontextmanager_proto",
    ],
)

##############################################################################
# Go
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "go_gapic_assembly_pkg",
    "go_gapic_library",
    "go_proto_library",
)

go_proto_library(
    name = "accesscontextmanager_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "cloud.google.com/go/accesscontextmanager/apiv1/accesscontextmanagerpb",
    protos = [":accesscontextmanager_proto"],
    deps = [
        "//google/api:annotations_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/identity/accesscontextmanager/type:type_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "//google/type:expr_go_proto",
    ],
)

go_gapic_library(
    name = "accesscontextmanager_go_gapic",
    srcs = [":accesscontextmanager_proto_with_info"],
    grpc_service_config = "accesscontextmanager_grpc_service_config.json",
    importpath = "cloud.google.com/go/accesscontextmanager/apiv1;accesscontextmanager",
    metadata = True,
    release_level = "ga",
    rest_numeric_enums = True,
    service_yaml = "accesscontextmanager_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":accesscontextmanager_go_proto",
        "//google/iam/v1:iam_go_proto",
        "//google/longrunning:longrunning_go_proto",
        "@com_google_cloud_go_longrunning//:go_default_library",
        "@com_google_cloud_go_longrunning//autogen:go_default_library",
    ],
)

# Open Source Packages
go_gapic_assembly_pkg(
    name = "gapi-cloud-identity-accesscontextmanager-v1-go",
    deps = [
        ":accesscontextmanager_go_gapic",
        ":accesscontextmanager_go_gapic_srcjar-metadata.srcjar",
        ":accesscontextmanager_go_gapic_srcjar-snippets.srcjar",
        ":accesscontextmanager_go_gapic_srcjar-test.srcjar",
        ":accesscontextmanager_go_proto",
    ],
)

##############################################################################
# Python
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "py_gapic_assembly_pkg",
    "py_proto_library",
)

py_proto_library(
    name = "accesscontextmanager_py_proto",
    deps = ["accesscontextmanager_proto"],
)

# accesscontextmanager_py_gapic is currently unused
# See https://github.com/googleapis/google-cloud-python/issues/13364
# and https://github.com/googleapis/google-cloud-python/issues/13365
# py_gapic_library(
#     name = "accesscontextmanager_py_gapic",
#     srcs = [":accesscontextmanager_proto"],
#     grpc_service_config = "accesscontextmanager_grpc_service_config.json",
#     rest_numeric_enums = True,
#     service_yaml = "accesscontextmanager_v1.yaml",
#     transport = "grpc+rest",
#     deps = [
#         "//google/iam/v1:iam_policy_py_proto",
#     ],
# )

# Uncomment once https://github.com/googleapis/gapic-generator-python/issues/1376 is fixed
#py_test(
#    name = "accesscontextmanager_py_gapic_test",
#    srcs = [
#        "accesscontextmanager_py_gapic_pytest.py",
#        "accesscontextmanager_py_gapic_test.py",
#    ],
#    legacy_create_init = False,
#    deps = [":accesscontextmanager_py_gapic"],
#)

# Open Source Packages
py_gapic_assembly_pkg(
    name = "identity-accesscontextmanager-v1-py",
    deps = [
        ":accesscontextmanager_py_proto",
    ],
)

##############################################################################
# PHP
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "php_gapic_assembly_pkg",
    "php_gapic_library",
    "php_proto_library",
)

php_proto_library(
    name = "accesscontextmanager_php_proto",
    deps = [":accesscontextmanager_proto"],
)

php_gapic_library(
    name = "accesscontextmanager_php_gapic",
    srcs = [":accesscontextmanager_proto_with_info"],
    grpc_service_config = "accesscontextmanager_grpc_service_config.json",
    migration_mode = "NEW_SURFACE_ONLY",
    rest_numeric_enums = True,
    service_yaml = "accesscontextmanager_v1.yaml",
    transport = "grpc+rest",
    deps = [":accesscontextmanager_php_proto"],
)

# Open Source Packages
php_gapic_assembly_pkg(
    name = "google-cloud-identity-accesscontextmanager-v1-php",
    deps = [
        ":accesscontextmanager_php_gapic",
        ":accesscontextmanager_php_proto",
    ],
)

##############################################################################
# Node.js
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "nodejs_gapic_assembly_pkg",
    "nodejs_gapic_library",
)

nodejs_gapic_library(
    name = "accesscontextmanager_nodejs_gapic",
    package_name = "@google-cloud/access-context-manager",
    src = ":accesscontextmanager_proto_with_info",
    extra_protoc_parameters = ["metadata"],
    grpc_service_config = "accesscontextmanager_grpc_service_config.json",
    package = "google.identity.accesscontextmanager.v1",
    rest_numeric_enums = True,
    service_yaml = "accesscontextmanager_v1.yaml",
    transport = "grpc+rest",
    deps = [],
)

nodejs_gapic_assembly_pkg(
    name = "identity-accesscontextmanager-v1-nodejs",
    deps = [
        ":accesscontextmanager_nodejs_gapic",
        ":accesscontextmanager_proto",
        "//google/identity/accesscontextmanager/type:type_proto",
    ],
)

##############################################################################
# Ruby
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
    "ruby_grpc_library",
    "ruby_proto_library",
)

ruby_proto_library(
    name = "accesscontextmanager_ruby_proto",
    deps = [
        ":accesscontextmanager_proto",
        "//google/identity/accesscontextmanager/type:type_proto",
    ],
)

ruby_grpc_library(
    name = "accesscontextmanager_ruby_grpc",
    srcs = [":accesscontextmanager_proto"],
    deps = [":accesscontextmanager_ruby_proto"],
)

ruby_cloud_gapic_library(
    name = "accesscontextmanager_ruby_gapic",
    srcs = [":accesscontextmanager_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-api-id=accesscontextmanager.googleapis.com",
        "ruby-cloud-api-shortname=accesscontextmanager",
        "ruby-cloud-gem-name=google-identity-access_context_manager-v1",
        "ruby-cloud-product-url=https://cloud.google.com/access-context-manager/",
    ],
    grpc_service_config = "accesscontextmanager_grpc_service_config.json",
    rest_numeric_enums = True,
    ruby_cloud_description = "Access Context Manager allows enterprises to configure access levels which map to a policy defined on request attributes.",
    ruby_cloud_title = "Access Context Manager V1",
    service_yaml = "accesscontextmanager_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":accesscontextmanager_ruby_grpc",
        ":accesscontextmanager_ruby_proto",
    ],
)

# Open Source Packages
ruby_gapic_assembly_pkg(
    name = "google-cloud-identity-accesscontextmanager-v1-ruby",
    deps = [
        ":accesscontextmanager_ruby_gapic",
        ":accesscontextmanager_ruby_grpc",
        ":accesscontextmanager_ruby_proto",
    ],
)

##############################################################################
# C#
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "csharp_gapic_assembly_pkg",
    "csharp_gapic_library",
    "csharp_grpc_library",
    "csharp_proto_library",
)

csharp_proto_library(
    name = "accesscontextmanager_csharp_proto",
    deps = [":accesscontextmanager_proto"],
)

csharp_grpc_library(
    name = "accesscontextmanager_csharp_grpc",
    srcs = [":accesscontextmanager_proto"],
    deps = [":accesscontextmanager_csharp_proto"],
)

csharp_gapic_library(
    name = "accesscontextmanager_csharp_gapic",
    srcs = [":accesscontextmanager_proto_with_info"],
    common_resources_config = "@gax_dotnet//:Google.Api.Gax/ResourceNames/CommonResourcesConfig.json",
    grpc_service_config = "accesscontextmanager_grpc_service_config.json",
    rest_numeric_enums = True,
    service_yaml = "accesscontextmanager_v1.yaml",
    transport = "grpc+rest",
    deps = [
        ":accesscontextmanager_csharp_grpc",
        ":accesscontextmanager_csharp_proto",
    ],
)

# Open Source Packages
csharp_gapic_assembly_pkg(
    name = "google-cloud-identity-accesscontextmanager-v1-csharp",
    deps = [
        ":accesscontextmanager_csharp_gapic",
        ":accesscontextmanager_csharp_grpc",
        ":accesscontextmanager_csharp_proto",
    ],
)

##############################################################################
# C++
##############################################################################
load(
    "@com_google_googleapis_imports//:imports.bzl",
    "cc_grpc_library",
    "cc_proto_library",
)

cc_proto_library(
    name = "accesscontextmanager_cc_proto",
    deps = [":accesscontextmanager_proto"],
)

cc_grpc_library(
    name = "accesscontextmanager_cc_grpc",
    srcs = [":accesscontextmanager_proto"],
    grpc_only = True,
    deps = [":accesscontextmanager_cc_proto"],
)
