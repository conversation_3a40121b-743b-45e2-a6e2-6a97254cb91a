// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.spanner.admin.instance.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.Spanner.Admin.Instance.V1";
option go_package = "cloud.google.com/go/spanner/admin/instance/apiv1/instancepb;instancepb";
option java_multiple_files = true;
option java_outer_classname = "CommonProto";
option java_package = "com.google.spanner.admin.instance.v1";
option php_namespace = "Google\\Cloud\\Spanner\\Admin\\Instance\\V1";
option ruby_package = "Google::Cloud::Spanner::Admin::Instance::V1";

// Encapsulates progress related information for a Cloud Spanner long
// running instance operations.
message OperationProgress {
  // Percent completion of the operation.
  // Values are between 0 and 100 inclusive.
  int32 progress_percent = 1;

  // Time the request was received.
  google.protobuf.Timestamp start_time = 2;

  // If set, the time at which this operation failed or was completed
  // successfully.
  google.protobuf.Timestamp end_time = 3;
}

// Indicates the expected fulfillment period of an operation.
enum FulfillmentPeriod {
  // Not specified.
  FULFILLMENT_PERIOD_UNSPECIFIED = 0;

  // Normal fulfillment period. The operation is expected to complete within
  // minutes.
  FULFILLMENT_PERIOD_NORMAL = 1;

  // Extended fulfillment period. It can take up to an hour for the operation
  // to complete.
  FULFILLMENT_PERIOD_EXTENDED = 2;
}

// ReplicaSelection identifies replicas with common properties.
message ReplicaSelection {
  // Required. Name of the location of the replicas (e.g., "us-central1").
  string location = 1 [(google.api.field_behavior) = REQUIRED];
}
