# This build file includes a target for the Ruby wrapper library for
# google-cloud-storage-control.

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

# Generates a Ruby wrapper client for storage-control.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v2 in this case.
ruby_cloud_gapic_library(
    name = "control_ruby_wrapper",
    srcs = ["//google/storage/control/v2:control_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-storage-control",
        "ruby-cloud-wrapper-of=v2:0.0",
    ],
    service_yaml = "//google/storage/control/v2:storage_v2.yaml",
    transport = "grpc",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-storage-control-ruby",
    deps = [
        ":control_ruby_wrapper",
    ],
)
