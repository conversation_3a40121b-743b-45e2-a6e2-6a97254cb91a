{"methodConfig": [{"name": [{"service": "google.apps.meet.v2beta.SpacesService", "method": "GetSpace"}, {"service": "google.apps.meet.v2beta.ConferenceRecordsService", "method": "GetConferenceRecord"}, {"service": "google.apps.meet.v2beta.ConferenceRecordsService", "method": "ListConferenceRecords"}, {"service": "google.apps.meet.v2beta.ConferenceRecordsService", "method": "GetParticipant"}, {"service": "google.apps.meet.v2beta.ConferenceRecordsService", "method": "ListParticipants"}, {"service": "google.apps.meet.v2beta.ConferenceRecordsService", "method": "GetParticipantSession"}, {"service": "google.apps.meet.v2beta.ConferenceRecordsService", "method": "ListParticipantSessions"}, {"service": "google.apps.meet.v2beta.ConferenceRecordsService", "method": "GetRecording"}, {"service": "google.apps.meet.v2beta.ConferenceRecordsService", "method": "ListRecordings"}, {"service": "google.apps.meet.v2beta.ConferenceRecordsService", "method": "GetTranscript"}, {"service": "google.apps.meet.v2beta.ConferenceRecordsService", "method": "ListTranscripts"}, {"service": "google.apps.meet.v2beta.ConferenceRecordsService", "method": "GetTranscriptEntry"}, {"service": "google.apps.meet.v2beta.ConferenceRecordsService", "method": "ListTranscriptEntries"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.apps.meet.v2beta.SpacesService", "method": "CreateSpace"}, {"service": "google.apps.meet.v2beta.SpacesService", "method": "UpdateSpace"}, {"service": "google.apps.meet.v2beta.SpacesService", "method": "EndActiveConference"}, {"service": "google.apps.meet.v2beta.SpacesService", "method": "DeleteSpace"}], "timeout": "60s"}]}