// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.script.type.gmail;

import "google/apps/script/type/addon_widget_set.proto";
import "google/apps/script/type/extension_point.proto";

option csharp_namespace = "Google.Apps.Script.Type.Gmail";
option go_package = "google.golang.org/genproto/googleapis/apps/script/type/gmail";
option java_multiple_files = true;
option java_outer_classname = "GmailAddOnManifestProto";
option java_package = "com.google.apps.script.type.gmail";
option php_namespace = "Google\\Apps\\Script\\Type\\Gmail";
option ruby_package = "Google::Apps::Script::Type::Gmail";

// Properties customizing the appearance and execution of a Gmail add-on.
message GmailAddOnManifest {
  // Defines an endpoint that will be executed in contexts that don't
  // match a declared contextual trigger. Any cards generated by this function
  // will always be available to the user, but may be eclipsed by contextual
  // content when this add-on declares more targeted triggers.
  //
  // If present, this overrides the configuration from
  // `addOns.common.homepageTrigger`.
  google.apps.script.type.HomepageExtensionPoint homepage_trigger = 14;

  // Defines the set of conditions that trigger the add-on.
  repeated ContextualTrigger contextual_triggers = 3;

  // Defines set of [universal
  // actions](/gmail/add-ons/how-tos/universal-actions) for the add-on. The user
  // triggers universal actions from the add-on toolbar menu.
  repeated UniversalAction universal_actions = 4;

  // Defines the compose time trigger for a compose time add-on. This is the
  // trigger that causes an add-on to take action when the user is composing an
  // email.
  // All compose time addons are required to have the
  // gmail.addons.current.action.compose scope even though it might not edit the
  // draft.
  ComposeTrigger compose_trigger = 12;

  // The name of an endpoint that verifies that the add-on has
  // all the required third-party authorizations, by probing the third-party
  // APIs. If the probe fails, the function should throw an exception to
  // initiate the authorization flow. This function is called before each
  // invocation of the add-on, in order to ensure a smooth user experience.
  string authorization_check_function = 7;
}

// An action that is always available in the add-on toolbar menu regardless of
// message context.
message UniversalAction {
  // Required. User-visible text describing the action, for example, "Add a new
  // contact."
  string text = 1;

  // The type of the action determines the behavior of Gmail when the user
  // invokes the action.
  oneof action_type {
    // A link that is opened by Gmail when the user triggers the action.
    string open_link = 2;

    // An endpoint that is called when the user triggers the
    // action. See the [universal actions
    // guide](/gmail/add-ons/how-tos/universal-actions) for details.
    string run_function = 3;
  }
}

// A trigger that activates when user is composing an email.
message ComposeTrigger {
  // An enum defining the level of data access this compose trigger requires.
  enum DraftAccess {
    // Default value when nothing is set for DraftAccess.
    UNSPECIFIED = 0;

    // NONE means compose trigger won't be able to access any data of the draft
    // when a compose addon is triggered.
    NONE = 1;

    // METADATA gives compose trigger the permission to access the metadata of
    // the draft when a compose addon is triggered. This includes the audience
    // list (To/cc list) of a draft message.
    METADATA = 2;
  }

  // Defines the set of actions for compose time add-on. These are actions
  // that user can trigger on a compose time addon.
  repeated google.apps.script.type.MenuItemExtensionPoint actions = 5;

  // Define the level of data access when a compose time addon is triggered.
  DraftAccess draft_access = 4;
}

// Defines a trigger that fires when the open email meets a specific criteria.
// When the trigger fires, it executes a specific endpoint, usually
// in order to create new cards and update the UI.
message ContextualTrigger {
  // The type of trigger determines the conditions Gmail uses to show the
  // add-on.
  oneof trigger {
    // UnconditionalTriggers are executed when any mail message is opened.
    UnconditionalTrigger unconditional = 1;
  }

  // Required. The name of the endpoint to call when a message matches the
  // trigger.
  string on_trigger_function = 4;
}

// A trigger that fires when any email message is opened.
message UnconditionalTrigger {}
