// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.apps.drive.activity.v2;

option csharp_namespace = "Google.Apps.Drive.Activity.V2";
option go_package = "google.golang.org/genproto/googleapis/apps/drive/activity/v2;activity";
option java_multiple_files = true;
option java_outer_classname = "ActorProto";
option java_package = "com.google.apps.drive.activity.v2";
option objc_class_prefix = "GADA";
option php_namespace = "Google\\Apps\\Drive\\Activity\\V2";

// The actor of a Drive activity.
message Actor {
  // The type of actor.
  oneof type {
    // An end user.
    User user = 1;

    // An anonymous user.
    AnonymousUser anonymous = 2;

    // An account acting on behalf of another.
    Impersonation impersonation = 3;

    // A non-user actor (i.e. system triggered).
    SystemEvent system = 4;

    // An administrator.
    Administrator administrator = 5;
  }
}

// Information about an end user.
message User {
  // A known user.
  message KnownUser {
    // The identifier for this user that can be used with the People API to get
    // more information. The format is `people/ACCOUNT_ID`. See
    // https://developers.google.com/people/.
    string person_name = 1;

    // True if this is the user making the request.
    bool is_current_user = 2;
  }

  // A user whose account has since been deleted.
  message DeletedUser {}

  // A user about whom nothing is currently known.
  message UnknownUser {}

  // The type of user, such as known, unknown, and deleted.
  oneof type {
    // A known user.
    KnownUser known_user = 2;

    // A user whose account has since been deleted.
    DeletedUser deleted_user = 3;

    // A user about whom nothing is currently known.
    UnknownUser unknown_user = 4;
  }
}

// Empty message representing an anonymous user or indicating the authenticated
// user should be anonymized.
message AnonymousUser {}

// Information about an impersonation, where an admin acts on behalf of an end
// user. Information about the acting admin is not currently available.
message Impersonation {
  // The impersonated user.
  User impersonated_user = 1;
}

// Event triggered by system operations instead of end users.
message SystemEvent {
  // The types of system events that may trigger activity.
  enum Type {
    // The event type is unspecified.
    TYPE_UNSPECIFIED = 0;

    // The event is a consequence of a user account being deleted.
    USER_DELETION = 1;

    // The event is due to the system automatically purging trash.
    TRASH_AUTO_PURGE = 2;
  }

  // The type of the system event that may triggered activity.
  Type type = 1;
}

// Empty message representing an administrator.
message Administrator {}
