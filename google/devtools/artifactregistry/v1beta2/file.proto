// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.artifactregistry.v1beta2;

import "google/api/resource.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ArtifactRegistry.V1Beta2";
option go_package = "cloud.google.com/go/artifactregistry/apiv1beta2/artifactregistrypb;artifactregistrypb";
option java_multiple_files = true;
option java_outer_classname = "FileProto";
option java_package = "com.google.devtools.artifactregistry.v1beta2";
option php_namespace = "Google\\Cloud\\ArtifactRegistry\\V1beta2";
option ruby_package = "Google::Cloud::ArtifactRegistry::V1beta2";

// A hash of file content.
message Hash {
  // The algorithm used to compute the hash.
  enum HashType {
    // Unspecified.
    HASH_TYPE_UNSPECIFIED = 0;

    // SHA256 hash.
    SHA256 = 1;

    // MD5 hash.
    MD5 = 2;
  }

  // The algorithm used to compute the hash value.
  HashType type = 1;

  // The hash value.
  bytes value = 2;
}

// Files store content that is potentially associated with Packages or Versions.
message File {
  option (google.api.resource) = {
    type: "artifactregistry.googleapis.com/File"
    pattern: "projects/{project}/locations/{location}/repositories/{repository}/files/{file}"
  };

  // The name of the file, for example:
  // "projects/p1/locations/us-central1/repositories/repo1/files/a%2Fb%2Fc.txt".
  // If the file ID part contains slashes, they are escaped.
  string name = 1;

  // The size of the File in bytes.
  int64 size_bytes = 3;

  // The hashes of the file content.
  repeated Hash hashes = 4;

  // The time when the File was created.
  google.protobuf.Timestamp create_time = 5;

  // The time when the File was last updated.
  google.protobuf.Timestamp update_time = 6;

  // The name of the Package or Version that owns this file, if any.
  string owner = 7;
}

// The request to list files.
message ListFilesRequest {
  // The name of the repository whose files will be listed. For example:
  // "projects/p1/locations/us-central1/repositories/repo1
  string parent = 1;

  // An expression for filtering the results of the request. Filter rules are
  // case insensitive. The fields eligible for filtering are:
  //
  //   * `name`
  //   * `owner`
  //
  //  An example of using a filter:
  //
  //   * `name="projects/p1/locations/us-central1/repositories/repo1/files/a/b/*"` --> Files with an
  //   ID starting with "a/b/".
  //   * `owner="projects/p1/locations/us-central1/repositories/repo1/packages/pkg1/versions/1.0"` -->
  //   Files owned by the version `1.0` in package `pkg1`.
  string filter = 4;

  // The maximum number of files to return.
  int32 page_size = 2;

  // The next_page_token value returned from a previous list request, if any.
  string page_token = 3;
}

// The response from listing files.
message ListFilesResponse {
  // The files returned.
  repeated File files = 1;

  // The token to retrieve the next page of files, or empty if there are no
  // more files to return.
  string next_page_token = 2;
}

// The request to retrieve a file.
message GetFileRequest {
  // The name of the file to retrieve.
  string name = 1;
}
