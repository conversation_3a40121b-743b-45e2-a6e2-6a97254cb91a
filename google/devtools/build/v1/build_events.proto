// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.devtools.build.v1;

import "google/devtools/build/v1/build_status.proto";
import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option go_package = "google.golang.org/genproto/googleapis/devtools/build/v1;build";
option java_multiple_files = true;
option java_outer_classname = "BuildEventProto";
option java_package = "com.google.devtools.build.v1";
option php_namespace = "Google\\Cloud\\Build\\V1";

// An event representing some state change that occurred in the build. This
// message does not include field for uniquely identifying an event.
message BuildEvent {
  // Notification that the build system has attempted to run the build tool.
  message InvocationAttemptStarted {
    // The number of the invocation attempt, starting at 1 and increasing by 1
    // for each new attempt. Can be used to determine if there is a later
    // invocation attempt replacing the current one a client is processing.
    int64 attempt_number = 1;

    // Arbitrary details about the invocation attempt.
    google.protobuf.Any details = 2;
  }

  // Notification that an invocation attempt has finished.
  message InvocationAttemptFinished {
    // Final status of the invocation.
    BuildStatus invocation_status = 3;

    // Arbitrary details about the invocation attempt.
    google.protobuf.Any details = 4;
  }

  // Notification that the build request is enqueued.
  message BuildEnqueued {
    // Additional details about the Build.
    google.protobuf.Any details = 1;
  }

  // Notification that the build request has finished, and no further
  // invocations will occur.  Note that this applies to the entire Build.
  // Individual invocations trigger InvocationFinished when they finish.
  message BuildFinished {
    // Final status of the build.
    BuildStatus status = 1;

    // Additional details about the Build.
    google.protobuf.Any details = 2;
  }

  // Textual output written to standard output or standard error.
  message ConsoleOutput {
    // The output stream type.
    ConsoleOutputStream type = 1;

    // The output stream content.
    oneof output {
      // Regular UTF-8 output; normal text.
      string text_output = 2;

      // Used if the output is not UTF-8 text (for example, a binary proto).
      bytes binary_output = 3;
    }
  }

  // Notification of the end of a build event stream published by a build
  // component other than CONTROLLER (See StreamId.BuildComponents).
  message BuildComponentStreamFinished {
    // How did the event stream finish.
    enum FinishType {
      // Unknown or unspecified; callers should never set this value.
      FINISH_TYPE_UNSPECIFIED = 0;

      // Set by the event publisher to indicate a build event stream is
      // finished.
      FINISHED = 1;

      // Set by the WatchBuild RPC server when the publisher of a build event
      // stream stops publishing events without publishing a
      // BuildComponentStreamFinished event whose type equals FINISHED.
      EXPIRED = 2;
    }

    // How the event stream finished.
    FinishType type = 1;
  }

  // This should be precisely the time when this event happened, and not when
  // the event proto was created or sent.
  google.protobuf.Timestamp event_time = 1;

  // //////////////////////////////////////////////////////////////////////////
  // Events that indicate a state change of a build request in the build
  // queue.
  oneof event {
    // An invocation attempt has started.
    InvocationAttemptStarted invocation_attempt_started = 51;

    // An invocation attempt has finished.
    InvocationAttemptFinished invocation_attempt_finished = 52;

    // The build is enqueued.
    BuildEnqueued build_enqueued = 53;

    // The build has finished. Set when the build is terminated.
    BuildFinished build_finished = 55;

    // An event containing printed text.
    ConsoleOutput console_output = 56;

    // Indicates the end of a build event stream (with the same StreamId) from
    // a build component executing the requested build task.
    // *** This field does not indicate the WatchBuild RPC is finished. ***
    BuildComponentStreamFinished component_stream_finished = 59;

    // Structured build event generated by Bazel about its execution progress.
    google.protobuf.Any bazel_event = 60;

    // An event that contains supplemental tool-specific information about
    // build execution.
    google.protobuf.Any build_execution_event = 61;

    // An event that contains supplemental tool-specific information about
    // source fetching.
    google.protobuf.Any source_fetch_event = 62;
  }
}

// Unique identifier for a build event stream.
message StreamId {
  // Which build component generates this event stream. Each build component
  // may generate one event stream.
  enum BuildComponent {
    // Unknown or unspecified; callers should never set this value.
    UNKNOWN_COMPONENT = 0;

    // A component that coordinates builds.
    CONTROLLER = 1;

    // A component that runs executables needed to complete a build.
    WORKER = 2;

    // A component that builds something.
    TOOL = 3;
  }

  // The id of a Build message.
  string build_id = 1;

  // The unique invocation ID within this build.
  // It should be the same as {invocation} (below) during the migration.
  string invocation_id = 6;

  // The component that emitted this event.
  BuildComponent component = 3;
}

// The type of console output stream.
enum ConsoleOutputStream {
  // Unspecified or unknown.
  UNKNOWN = 0;

  // Normal output stream.
  STDOUT = 1;

  // Error output stream.
  STDERR = 2;
}
