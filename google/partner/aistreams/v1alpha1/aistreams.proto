// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.partner.aistreams.v1alpha1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option cc_enable_arenas = true;
option go_package = "google.golang.org/genproto/googleapis/partner/aistreams/v1alpha1;aistreams";
option java_multiple_files = true;
option java_outer_classname = "AIStreamsProto";
option java_package = "com.google.partner.aistreams.v1alpha1";
option php_namespace = "Google\\Cloud\\Partner\\Aistreams\\V1alpha1";

// AIStreams service.
service AIStreams {
  option (google.api.default_host) = "aistreams.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Lists Clusters in a given project and location.
  rpc ListClusters(ListClustersRequest) returns (ListClustersResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*}/clusters"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Cluster.
  rpc GetCluster(GetClusterRequest) returns (Cluster) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/clusters/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Cluster in a given project and location.
  rpc CreateCluster(CreateClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=projects/*/locations/*}/clusters"
      body: "cluster"
    };
    option (google.api.method_signature) = "parent,cluster,cluster_id";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Cluster.
  rpc UpdateCluster(UpdateClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1alpha1/{cluster.name=projects/*/locations/*/clusters/*}"
      body: "cluster"
    };
    option (google.api.method_signature) = "cluster,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Cluster"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Cluster.
  rpc DeleteCluster(DeleteClusterRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1alpha1/{name=projects/*/locations/*/clusters/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }

  // Lists Streams in a given project, location and cluster.
  rpc ListStreams(ListStreamsRequest) returns (ListStreamsResponse) {
    option (google.api.http) = {
      get: "/v1alpha1/{parent=projects/*/locations/*/clusters/*}/streams"
    };
    option (google.api.method_signature) = "parent";
  }

  // Gets details of a single Stream.
  rpc GetStream(GetStreamRequest) returns (Stream) {
    option (google.api.http) = {
      get: "/v1alpha1/{name=projects/*/locations/*/clusters/*/streams/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Creates a new Stream in a given project and location.
  rpc CreateStream(CreateStreamRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v1alpha1/{parent=projects/*/locations/*/clusters/*}/streams"
      body: "stream"
    };
    option (google.api.method_signature) = "parent,stream,stream_id";
    option (google.longrunning.operation_info) = {
      response_type: "Stream"
      metadata_type: "OperationMetadata"
    };
  }

  // Updates the parameters of a single Stream.
  rpc UpdateStream(UpdateStreamRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v1alpha1/{stream.name=projects/*/locations/*/clusters/*/streams/*}"
      body: "stream"
    };
    option (google.api.method_signature) = "stream,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Stream"
      metadata_type: "OperationMetadata"
    };
  }

  // Deletes a single Stream.
  rpc DeleteStream(DeleteStreamRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v1alpha1/{name=projects/*/locations/*/clusters/*/streams/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "google.protobuf.Empty"
      metadata_type: "OperationMetadata"
    };
  }
}

// Cluster resource.
message Cluster {
  option (google.api.resource) = {
    type: "aistreams.googleapis.com/Cluster"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}"
  };

  // The name of the cluster. The format of cluster is:
  // projects/<projectid>/locations/<locationid>/clusters/<clusterid>.
  string name = 1;

  // Output only. The time at which this cluster was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this cluster was updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Labels with user-defined metadata.
  map<string, string> labels = 4;

  // Output only. The certificate for creating the secure connection between the
  // client and the AI Streams data plane.
  string certificate = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The endpoint of the data plane cluster.
  string service_endpoint = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// Request message for 'ListClusters'.
message ListClustersRequest {
  // Required. The parent that owns the collection of Clusters.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Maximum number of Clusters to return.
  int32 page_size = 2;

  // Page token received from a previous `ListClusters` call. Provide this to
  // retrieve the subsequent page. When paginating, all other parameters
  // provided to `ListClusters` must match the call that provided the page
  // token.
  string page_token = 3;

  // Filter request.
  string filter = 4;

  // Order by fields for the result.
  string order_by = 5;
}

// Response message from 'ListClusters'.
message ListClustersResponse {
  // List of clusters.
  repeated Cluster clusters = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for 'GetCluster'.
message GetClusterRequest {
  // Required. The name of the Cluster resource to get.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aistreams.googleapis.com/Cluster"
    }
  ];
}

// Request message for 'CreateCluster'.
message CreateClusterRequest {
  // Required. The parent that owns the collection of Clusters.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "locations.googleapis.com/Location"
    }
  ];

  // Required. The cluster identifier.
  string cluster_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The cluster resource to create.
  Cluster cluster = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for 'UpdateCluster'.
message UpdateClusterRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Cluster resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The Cluster resource to update.
  Cluster cluster = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for 'DeleteCluster'.
message DeleteClusterRequest {
  // Required. The name of cluster to delete.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aistreams.googleapis.com/Cluster"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and the
  // request times out. If you make the request again with the same request ID,
  // the server can check if original operation with the same request ID was
  // received, and if so, will ignore the second request. This prevents clients
  // from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Stream resource.
message Stream {
  option (google.api.resource) = {
    type: "aistreams.googleapis.com/Stream"
    pattern: "projects/{project}/locations/{location}/clusters/{cluster}/streams/{stream}"
  };

  // The name of the stream. The format for the full name is:
  // projects/<projectid>/location/<locationid>/clusters/<clusterid>/streams/<streamid>.
  string name = 1;

  // Output only. The time at which this Stream was created.
  google.protobuf.Timestamp create_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time at which this Stream was updated.
  google.protobuf.Timestamp update_time = 3
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // The labels of the stream.
  map<string, string> labels = 4;
}

// Request message for 'ListStreams'.
message ListStreamsRequest {
  // Required. The parent that owns the collection of the Streams.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aistreams.googleapis.com/Cluster"
    }
  ];

  // Maximum number of Streams to return.
  int32 page_size = 2;

  // Page token received from a previous `ListStreams` call. Provide this to
  // retrieve the subsequent page. When paginating, all other parameters
  // provided to `ListClusters` must match the call that provided the page
  // token.
  string page_token = 3;

  // Filter request.
  string filter = 4;

  // Order by fields for the result.
  string order_by = 5;
}

// Response message from 'ListStreams'.
message ListStreamsResponse {
  // List of the streams.
  repeated Stream streams = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;

  // Locations that could not be reached.
  repeated string unreachable = 3;
}

// Request message for 'GetStream'.
message GetStreamRequest {
  // Required. The name of the stream.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aistreams.googleapis.com/Stream"
    }
  ];
}

// Request message for 'CreateStream'.
message CreateStreamRequest {
  // Required. The parent that owns the collection of streams.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aistreams.googleapis.com/Cluster"
    }
  ];

  // Required. The stream identifier.
  string stream_id = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The stream to create.
  Stream stream = 3 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 4 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for 'UpdateStream'.
message UpdateStreamRequest {
  // Required. Field mask is used to specify the fields to be overwritten in the
  // Stream resource by the update.
  // The fields specified in the update_mask are relative to the resource, not
  // the full request. A field will be overwritten if it is in the mask. If the
  // user does not provide a mask then all fields will be overwritten.
  google.protobuf.FieldMask update_mask = 1
      [(google.api.field_behavior) = REQUIRED];

  // Required. The stream resource to update.
  Stream stream = 2 [(google.api.field_behavior) = REQUIRED];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes since the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 3 [(google.api.field_behavior) = OPTIONAL];
}

// Request message for 'DeleteStream'.
message DeleteStreamRequest {
  // Required. The name of the stream.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "aistreams.googleapis.com/Stream"
    }
  ];

  // Optional. An optional request ID to identify requests. Specify a unique
  // request ID so that if you must retry your request, the server will know to
  // ignore the request if it has already been completed. The server will
  // guarantee that for at least 60 minutes after the first request.
  //
  // For example, consider a situation where you make an initial request and t
  // he request times out. If you make the request again with the same request
  // ID, the server can check if original operation with the same request ID
  // was received, and if so, will ignore the second request. This prevents
  // clients from accidentally creating duplicate commitments.
  //
  // The request ID must be a valid UUID with the exception that zero UUID is
  // not supported (00000000-0000-0000-0000-000000000000).
  string request_id = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Represents the metadata of the long-running operation.
message OperationMetadata {
  // Output only. The time the operation was created.
  google.protobuf.Timestamp create_time = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The time the operation finished running.
  google.protobuf.Timestamp end_time = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Server-defined resource path for the target of the operation.
  string target = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Name of the verb executed by the operation.
  string verb = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Human-readable status of the operation, if any.
  string status_message = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Identifies whether the user has requested cancellation
  // of the operation. Operations that have successfully been cancelled
  // have [Operation.error][] value with a
  // [google.rpc.Status.code][google.rpc.Status.code] of 1, corresponding to
  // `Code.CANCELLED`.
  bool requested_cancellation = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. API version used to start the operation.
  string api_version = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}
