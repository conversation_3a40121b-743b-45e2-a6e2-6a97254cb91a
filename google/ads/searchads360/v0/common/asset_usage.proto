// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.searchads360.v0.common;

import "google/ads/searchads360/v0/enums/served_asset_field_type.proto";

option csharp_namespace = "Google.Ads.SearchAds360.V0.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/searchads360/v0/common;common";
option java_multiple_files = true;
option java_outer_classname = "AssetUsageProto";
option java_package = "com.google.ads.searchads360.v0.common";
option objc_class_prefix = "GASA360";
option php_namespace = "Google\\Ads\\SearchAds360\\V0\\Common";
option ruby_package = "Google::Ads::SearchAds360::V0::Common";

// Proto file describing asset usage.

// Contains the usage information of the asset.
message AssetUsage {
  // Resource name of the asset.
  string asset = 1;

  // The served field type of the asset.
  google.ads.searchads360.v0.enums.ServedAssetFieldTypeEnum.ServedAssetFieldType
      served_asset_field_type = 2;
}
