// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "AdCustomizerErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing ad customizer errors.

// Container for enum describing possible ad customizer errors.
message AdCustomizerErrorEnum {
  // Enum describing possible ad customizer errors.
  enum AdCustomizerError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Invalid date argument in countdown function.
    COUNTDOWN_INVALID_DATE_FORMAT = 2;

    // Countdown end date is in the past.
    COUNTDOWN_DATE_IN_PAST = 3;

    // Invalid locale string in countdown function.
    COUNTDOWN_INVALID_LOCALE = 4;

    // Days-before argument to countdown function is not positive.
    COUNTDOWN_INVALID_START_DAYS_BEFORE = 5;

    // A user list referenced in an IF function does not exist.
    UNKNOWN_USER_LIST = 6;
  }
}
