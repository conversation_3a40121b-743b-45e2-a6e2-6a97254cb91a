// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "CustomerUserAccessErrorProto";
option java_package = "com.google.ads.googleads.v17.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V17::Errors";

// Proto file describing CustomerUserAccess errors.

// Container for enum describing possible CustomerUserAccess errors.
message CustomerUserAccessErrorEnum {
  // Enum describing possible customer user access errors.
  enum CustomerUserAccessError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // There is no user associated with the user id specified.
    INVALID_USER_ID = 2;

    // Unable to remove the access between the user and customer.
    REMOVAL_DISALLOWED = 3;

    // Unable to add or update the access role as specified.
    DISALLOWED_ACCESS_ROLE = 4;

    // The user can't remove itself from an active serving customer if it's the
    // last admin user and the customer doesn't have any owner manager
    LAST_ADMIN_USER_OF_SERVING_CUSTOMER = 5;

    // Last admin user cannot be removed from a manager.
    LAST_ADMIN_USER_OF_MANAGER = 6;
  }
}
