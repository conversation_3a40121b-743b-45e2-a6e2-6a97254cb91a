// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V17.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "CustomPlaceholderFieldProto";
option java_package = "com.google.ads.googleads.v17.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V17::Enums";

// Proto file describing Custom placeholder fields.

// Values for Custom placeholder fields.
// For more information about dynamic remarketing feeds, see
// https://support.google.com/google-ads/answer/6053288.
message CustomPlaceholderFieldEnum {
  // Possible values for Custom placeholder fields.
  enum CustomPlaceholderField {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Data Type: STRING. Required. Combination ID and ID2 must be unique per
    // offer.
    ID = 2;

    // Data Type: STRING. Combination ID and ID2 must be unique per offer.
    ID2 = 3;

    // Data Type: STRING. Required. Main headline with product name to be shown
    // in dynamic ad.
    ITEM_TITLE = 4;

    // Data Type: STRING. Optional text to be shown in the image ad.
    ITEM_SUBTITLE = 5;

    // Data Type: STRING. Optional description of the product to be shown in the
    // ad.
    ITEM_DESCRIPTION = 6;

    // Data Type: STRING. Full address of your offer or service, including
    // postal code. This will be used to identify the closest product to the
    // user when there are multiple offers in the feed that are relevant to the
    // user.
    ITEM_ADDRESS = 7;

    // Data Type: STRING. Price to be shown in the ad.
    // Example: "100.00 USD"
    PRICE = 8;

    // Data Type: STRING. Formatted price to be shown in the ad.
    // Example: "Starting at $100.00 USD", "$80 - $100"
    FORMATTED_PRICE = 9;

    // Data Type: STRING. Sale price to be shown in the ad.
    // Example: "80.00 USD"
    SALE_PRICE = 10;

    // Data Type: STRING. Formatted sale price to be shown in the ad.
    // Example: "On sale for $80.00", "$60 - $80"
    FORMATTED_SALE_PRICE = 11;

    // Data Type: URL. Image to be displayed in the ad. Highly recommended for
    // image ads.
    IMAGE_URL = 12;

    // Data Type: STRING. Used as a recommendation engine signal to serve items
    // in the same category.
    ITEM_CATEGORY = 13;

    // Data Type: URL_LIST. Final URLs for the ad when using Upgraded
    // URLs. User will be redirected to these URLs when they click on an ad, or
    // when they click on a specific product for ads that have multiple
    // products.
    FINAL_URLS = 14;

    // Data Type: URL_LIST. Final mobile URLs for the ad when using Upgraded
    // URLs.
    FINAL_MOBILE_URLS = 15;

    // Data Type: URL. Tracking template for the ad when using Upgraded URLs.
    TRACKING_URL = 16;

    // Data Type: STRING_LIST. Keywords used for product retrieval.
    CONTEXTUAL_KEYWORDS = 17;

    // Data Type: STRING. Android app link. Must be formatted as:
    // android-app://{package_id}/{scheme}/{host_path}.
    // The components are defined as follows:
    // package_id: app ID as specified in Google Play.
    // scheme: the scheme to pass to the application. Can be HTTP, or a custom
    //   scheme.
    // host_path: identifies the specific content within your application.
    ANDROID_APP_LINK = 18;

    // Data Type: STRING_LIST. List of recommended IDs to show together with
    // this item.
    SIMILAR_IDS = 19;

    // Data Type: STRING. iOS app link.
    IOS_APP_LINK = 20;

    // Data Type: INT64. iOS app store ID.
    IOS_APP_STORE_ID = 21;
  }
}
