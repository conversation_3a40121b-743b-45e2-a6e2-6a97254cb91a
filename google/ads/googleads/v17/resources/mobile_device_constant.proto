// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v17.resources;

import "google/ads/googleads/v17/enums/mobile_device_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V17.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v17/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "MobileDeviceConstantProto";
option java_package = "com.google.ads.googleads.v17.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V17\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V17::Resources";

// Proto file describing the mobile device constant resource.

// A mobile device constant.
message MobileDeviceConstant {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/MobileDeviceConstant"
    pattern: "mobileDeviceConstants/{criterion_id}"
  };

  // Output only. The resource name of the mobile device constant.
  // Mobile device constant resource names have the form:
  //
  // `mobileDeviceConstants/{criterion_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/MobileDeviceConstant"
    }
  ];

  // Output only. The ID of the mobile device constant.
  optional int64 id = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The name of the mobile device.
  optional string name = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The manufacturer of the mobile device.
  optional string manufacturer_name = 9
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The operating system of the mobile device.
  optional string operating_system_name = 10
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The type of mobile device.
  google.ads.googleads.v17.enums.MobileDeviceTypeEnum.MobileDeviceType type = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
