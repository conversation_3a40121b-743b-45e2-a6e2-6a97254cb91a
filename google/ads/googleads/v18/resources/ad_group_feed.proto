// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.resources;

import "google/ads/googleads/v18/common/matching_function.proto";
import "google/ads/googleads/v18/enums/feed_link_status.proto";
import "google/ads/googleads/v18/enums/placeholder_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V18.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "AdGroupFeedProto";
option java_package = "com.google.ads.googleads.v18.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V18::Resources";

// Proto file describing the AdGroupFeed resource.

// An ad group feed.
message AdGroupFeed {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/AdGroupFeed"
    pattern: "customers/{customer_id}/adGroupFeeds/{ad_group_id}~{feed_id}"
  };

  // Immutable. The resource name of the ad group feed.
  // Ad group feed resource names have the form:
  //
  // `customers/{customer_id}/adGroupFeeds/{ad_group_id}~{feed_id}
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroupFeed"
    }
  ];

  // Immutable. The feed being linked to the ad group.
  optional string feed = 7 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = { type: "googleads.googleapis.com/Feed" }
  ];

  // Immutable. The ad group being linked to the feed.
  optional string ad_group = 8 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/AdGroup"
    }
  ];

  // Indicates which placeholder types the feed may populate under the connected
  // ad group. Required.
  repeated google.ads.googleads.v18.enums.PlaceholderTypeEnum.PlaceholderType
      placeholder_types = 4;

  // Matching function associated with the AdGroupFeed.
  // The matching function is used to filter the set of feed items selected.
  // Required.
  google.ads.googleads.v18.common.MatchingFunction matching_function = 5;

  // Output only. Status of the ad group feed.
  // This field is read-only.
  google.ads.googleads.v18.enums.FeedLinkStatusEnum.FeedLinkStatus status = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
