// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.errors;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Errors";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/errors;errors";
option java_multiple_files = true;
option java_outer_classname = "CustomConversionGoalErrorProto";
option java_package = "com.google.ads.googleads.v18.errors";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Errors";
option ruby_package = "Google::Ads::GoogleAds::V18::Errors";

// Proto file describing CustomConversionGoal errors.

// Container for enum describing possible custom conversion goal errors.
message CustomConversionGoalErrorEnum {
  // Enum describing possible custom conversion goal errors.
  enum CustomConversionGoalError {
    // Enum unspecified.
    UNSPECIFIED = 0;

    // The received error code is not known in this version.
    UNKNOWN = 1;

    // Cannot find a conversion action with the specified id.
    INVALID_CONVERSION_ACTION = 2;

    // The conversion action is not enabled so it cannot be included in a custom
    // conversion goal.
    CONVERSION_ACTION_NOT_ENABLED = 3;

    // The custom conversion goal cannot be removed because it's linked to a
    // campaign.
    CANNOT_REMOVE_LINKED_CUSTOM_CONVERSION_GOAL = 4;

    // Custom goal with the same name already exists.
    CUSTOM_GOAL_DUPLICATE_NAME = 5;

    // Custom goal with the same conversion action list already exists.
    DUPLICATE_CONVERSION_ACTION_LIST = 6;

    // Conversion types that cannot be biddable should not be included in custom
    // goal.
    NON_BIDDABLE_CONVERSION_ACTION_NOT_ELIGIBLE_FOR_CUSTOM_GOAL = 7;
  }
}
