// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.common;

import "google/ads/googleads/v16/common/criteria.proto";
import "google/ads/googleads/v16/common/feed_common.proto";
import "google/ads/googleads/v16/enums/call_conversion_reporting_state.proto";
import "google/ads/googleads/v16/enums/call_to_action_type.proto";
import "google/ads/googleads/v16/enums/lead_form_call_to_action_type.proto";
import "google/ads/googleads/v16/enums/lead_form_desired_intent.proto";
import "google/ads/googleads/v16/enums/lead_form_field_user_input_type.proto";
import "google/ads/googleads/v16/enums/lead_form_post_submit_call_to_action_type.proto";
import "google/ads/googleads/v16/enums/location_ownership_type.proto";
import "google/ads/googleads/v16/enums/mime_type.proto";
import "google/ads/googleads/v16/enums/mobile_app_vendor.proto";
import "google/ads/googleads/v16/enums/price_extension_price_qualifier.proto";
import "google/ads/googleads/v16/enums/price_extension_price_unit.proto";
import "google/ads/googleads/v16/enums/price_extension_type.proto";
import "google/ads/googleads/v16/enums/promotion_extension_discount_modifier.proto";
import "google/ads/googleads/v16/enums/promotion_extension_occasion.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Common";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/common;common";
option java_multiple_files = true;
option java_outer_classname = "AssetTypesProto";
option java_package = "com.google.ads.googleads.v16.common";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Common";
option ruby_package = "Google::Ads::GoogleAds::V16::Common";

// Proto file containing info messages for specific asset types.

// A YouTube asset.
message YoutubeVideoAsset {
  // YouTube video id. This is the 11 character string value used in the
  // YouTube video URL.
  optional string youtube_video_id = 2;

  // YouTube video title.
  string youtube_video_title = 3;
}

// A MediaBundle asset.
message MediaBundleAsset {
  // Media bundle (ZIP file) asset data. The format of the uploaded ZIP file
  // depends on the ad field where it will be used. For more information on the
  // format, see the documentation of the ad field where you plan on using the
  // MediaBundleAsset. This field is mutate only.
  optional bytes data = 2;
}

// An Image asset.
message ImageAsset {
  // The raw bytes data of an image. This field is mutate only.
  optional bytes data = 5;

  // File size of the image asset in bytes.
  optional int64 file_size = 6;

  // MIME type of the image asset.
  google.ads.googleads.v16.enums.MimeTypeEnum.MimeType mime_type = 3;

  // Metadata for this image at its original size.
  ImageDimension full_size = 4;
}

// Metadata for an image at a certain size, either original or resized.
message ImageDimension {
  // Height of the image.
  optional int64 height_pixels = 4;

  // Width of the image.
  optional int64 width_pixels = 5;

  // A URL that returns the image with this height and width.
  optional string url = 6;
}

// A Text asset.
message TextAsset {
  // Text content of the text asset.
  optional string text = 2;
}

// A Lead Form asset.
message LeadFormAsset {
  // Required. The name of the business being advertised.
  string business_name = 10 [(google.api.field_behavior) = REQUIRED];

  // Required. Pre-defined display text that encourages user to expand the form.
  google.ads.googleads.v16.enums.LeadFormCallToActionTypeEnum
      .LeadFormCallToActionType call_to_action_type = 17
      [(google.api.field_behavior) = REQUIRED];

  // Required. Text giving a clear value proposition of what users expect once
  // they expand the form.
  string call_to_action_description = 18
      [(google.api.field_behavior) = REQUIRED];

  // Required. Headline of the expanded form to describe what the form is asking
  // for or facilitating.
  string headline = 12 [(google.api.field_behavior) = REQUIRED];

  // Required. Detailed description of the expanded form to describe what the
  // form is asking for or facilitating.
  string description = 13 [(google.api.field_behavior) = REQUIRED];

  // Required. Link to a page describing the policy on how the collected data is
  // handled by the advertiser/business.
  string privacy_policy_url = 14 [(google.api.field_behavior) = REQUIRED];

  // Headline of text shown after form submission that describes how the
  // advertiser will follow up with the user.
  optional string post_submit_headline = 15;

  // Detailed description shown after form submission that describes how the
  // advertiser will follow up with the user.
  optional string post_submit_description = 16;

  // Ordered list of input fields. This field can be updated by reordering
  // questions, but not by adding or removing questions.
  repeated LeadFormField fields = 8;

  // Ordered list of custom question fields. This field is subject to a limit of
  // 5 qualifying questions per form.
  repeated LeadFormCustomQuestionField custom_question_fields = 23;

  // Configured methods for collected lead data to be delivered to advertiser.
  // Only one method typed as WebhookDelivery can be configured.
  repeated LeadFormDeliveryMethod delivery_methods = 9;

  // Pre-defined display text that encourages user action after the form is
  // submitted.
  google.ads.googleads.v16.enums.LeadFormPostSubmitCallToActionTypeEnum
      .LeadFormPostSubmitCallToActionType post_submit_call_to_action_type = 19;

  // Asset resource name of the background image.
  // The image dimensions must be exactly 1200x628.
  optional string background_image_asset = 20;

  // Chosen intent for the lead form, for example, more volume or more
  // qualified.
  google.ads.googleads.v16.enums.LeadFormDesiredIntentEnum.LeadFormDesiredIntent
      desired_intent = 21;

  // Custom disclosure shown along with Google disclaimer on the lead form.
  // Accessible to allowed customers only.
  optional string custom_disclosure = 22;
}

// One input field instance within a form.
message LeadFormField {
  // Describes the input type, which may be a predefined type such as "full
  // name" or a pre-vetted question like "What kind of vehicle do you have?".
  google.ads.googleads.v16.enums.LeadFormFieldUserInputTypeEnum
      .LeadFormFieldUserInputType input_type = 1;

  // Defines answer configuration that this form field accepts. If oneof is not
  // set, this is a free-text answer.
  oneof answers {
    // Answer configuration for a single choice question. Can be set only for
    // pre-vetted question fields. Minimum of 2 answers required and maximum of
    // 12 allowed.
    LeadFormSingleChoiceAnswers single_choice_answers = 2;

    // Answer configuration for location question. If true, campaign/account
    // level location data (state, city, business name etc) will be rendered on
    // the Lead Form.
    // Starting V13.1, has_location_answer can only be set for "What is your
    // preferred dealership?" question, for advertisers with Location Assets
    // setup at campaign/account level.
    bool has_location_answer = 3;
  }
}

// One custom question input field instance within a form.
message LeadFormCustomQuestionField {
  // The exact custom question field text (for example, "What kind of vehicle
  // do you have?").
  string custom_question_text = 1;

  // Defines answer configuration that this form field accepts. If
  // oneof is not set, this is a free-text answer.
  oneof answers {
    // Answer configuration for a single choice question.
    // Minimum of 2 answers and maximum of 12 allowed.
    LeadFormSingleChoiceAnswers single_choice_answers = 2;

    // Answer configuration for location question. If true, campaign/account
    // level location data (state, city, business name etc) will be rendered on
    // the Lead Form.
    // Starting V13.1, has_location_answer can only be set for "What is your
    // preferred dealership?" question, for advertisers with Location Assets
    // setup at campaign/account level.
    bool has_location_answer = 3;
  }
}

// Defines possible answers for a single choice question, usually presented as
// a single-choice drop-down list.
message LeadFormSingleChoiceAnswers {
  // List of choices for a single question field. The order of entries defines
  // UI order. Minimum of 2 answers required and maximum of 12 allowed.
  repeated string answers = 1;
}

// A configuration of how leads are delivered to the advertiser.
message LeadFormDeliveryMethod {
  // Various subtypes of delivery.
  oneof delivery_details {
    // Webhook method of delivery.
    WebhookDelivery webhook = 1;
  }
}

// Google notifies the advertiser of leads by making HTTP calls to an
// endpoint they specify. The requests contain JSON matching a schema that
// Google publishes as part of form ads documentation.
message WebhookDelivery {
  // Webhook url specified by advertiser to send the lead.
  optional string advertiser_webhook_url = 4;

  // Anti-spoofing secret set by the advertiser as part of the webhook payload.
  optional string google_secret = 5;

  // The schema version that this delivery instance will use.
  optional int64 payload_schema_version = 6;
}

// A Book on Google asset. Used to redirect user to book through Google.
// Book on Google will change the redirect url to book directly through
// Google.
message BookOnGoogleAsset {}

// A Promotion asset.
message PromotionAsset {
  // Required. A freeform description of what the promotion is targeting.
  string promotion_target = 1 [(google.api.field_behavior) = REQUIRED];

  // A modifier for qualification of the discount.
  google.ads.googleads.v16.enums.PromotionExtensionDiscountModifierEnum
      .PromotionExtensionDiscountModifier discount_modifier = 2;

  // Start date of when the promotion is eligible to be redeemed, in yyyy-MM-dd
  // format.
  string redemption_start_date = 7;

  // Last date of when the promotion is eligible to be redeemed, in yyyy-MM-dd
  // format.
  string redemption_end_date = 8;

  // The occasion the promotion was intended for.
  // If an occasion is set, the redemption window will need to fall within the
  // date range associated with the occasion.
  google.ads.googleads.v16.enums.PromotionExtensionOccasionEnum
      .PromotionExtensionOccasion occasion = 9;

  // The language of the promotion.
  // Represented as BCP 47 language tag.
  string language_code = 10;

  // Start date of when this asset is effective and can begin serving, in
  // yyyy-MM-dd format.
  string start_date = 11;

  // Last date of when this asset is effective and still serving, in yyyy-MM-dd
  // format.
  string end_date = 12;

  // List of non-overlapping schedules specifying all time intervals for which
  // the asset may serve. There can be a maximum of 6 schedules per day, 42 in
  // total.
  repeated AdScheduleInfo ad_schedule_targets = 13;

  // Discount type, can be percentage off or amount off.
  oneof discount_type {
    // Percentage off discount in the promotion. 1,000,000 = 100%.
    // Either this or money_amount_off is required.
    int64 percent_off = 3;

    // Money amount off for discount in the promotion.
    // Either this or percent_off is required.
    Money money_amount_off = 4;
  }

  // Promotion trigger. Can be by promotion code or promo by eligible order
  // amount.
  oneof promotion_trigger {
    // A code the user should use in order to be eligible for the promotion.
    string promotion_code = 5;

    // The amount the total order needs to be for the user to be eligible for
    // the promotion.
    Money orders_over_amount = 6;
  }
}

// A Callout asset.
message CalloutAsset {
  // Required. The callout text.
  // The length of this string should be between 1 and 25, inclusive.
  string callout_text = 1 [(google.api.field_behavior) = REQUIRED];

  // Start date of when this asset is effective and can begin serving, in
  // yyyy-MM-dd format.
  string start_date = 2;

  // Last date of when this asset is effective and still serving, in yyyy-MM-dd
  // format.
  string end_date = 3;

  // List of non-overlapping schedules specifying all time intervals for which
  // the asset may serve. There can be a maximum of 6 schedules per day, 42 in
  // total.
  repeated AdScheduleInfo ad_schedule_targets = 4;
}

// A Structured Snippet asset.
message StructuredSnippetAsset {
  // Required. The header of the snippet.
  // This string should be one of the predefined values at
  // https://developers.google.com/google-ads/api/reference/data/structured-snippet-headers
  string header = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The values in the snippet.
  // The size of this collection should be between 3 and 10, inclusive.
  // The length of each value should be between 1 and 25 characters, inclusive.
  repeated string values = 2 [(google.api.field_behavior) = REQUIRED];
}

// A Sitelink asset.
message SitelinkAsset {
  // Required. URL display text for the sitelink.
  // The length of this string should be between 1 and 25, inclusive.
  string link_text = 1 [(google.api.field_behavior) = REQUIRED];

  // First line of the description for the sitelink.
  // If set, the length should be between 1 and 35, inclusive, and description2
  // must also be set.
  string description1 = 2;

  // Second line of the description for the sitelink.
  // If set, the length should be between 1 and 35, inclusive, and description1
  // must also be set.
  string description2 = 3;

  // Start date of when this asset is effective and can begin serving, in
  // yyyy-MM-dd format.
  string start_date = 4;

  // Last date of when this asset is effective and still serving, in yyyy-MM-dd
  // format.
  string end_date = 5;

  // List of non-overlapping schedules specifying all time intervals for which
  // the asset may serve. There can be a maximum of 6 schedules per day, 42 in
  // total.
  repeated AdScheduleInfo ad_schedule_targets = 6;
}

// A Page Feed asset.
message PageFeedAsset {
  // Required. The webpage that advertisers want to target.
  string page_url = 1 [(google.api.field_behavior) = REQUIRED];

  // Labels used to group the page urls.
  repeated string labels = 2;
}

// A Dynamic Education asset.
message DynamicEducationAsset {
  // Required. Program ID which can be any sequence of letters and digits, and
  // must be unique and match the values of remarketing tag. Required.
  string program_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Location ID which can be any sequence of letters and digits and must be
  // unique.
  string location_id = 2;

  // Required. Program name, for example, Nursing. Required.
  string program_name = 3 [(google.api.field_behavior) = REQUIRED];

  // Subject of study, for example, Health.
  string subject = 4;

  // Program description, for example, Nursing Certification.
  string program_description = 5;

  // School name, for example, Mountain View School of Nursing.
  string school_name = 6;

  // School address which can be specified in one of the following formats.
  // (1) City, state, code, country, for example, Mountain View, CA, USA.
  // (2) Full address, for example, 123 Boulevard St, Mountain View, CA 94043.
  // (3) Latitude-longitude in the DDD format, for example, 41.40338, 2.17403
  string address = 7;

  // Contextual keywords, for example, Nursing certification, Health, Mountain
  // View.
  repeated string contextual_keywords = 8;

  // Android deep link, for example,
  // android-app://com.example.android/http/example.com/gizmos?1234.
  string android_app_link = 9;

  // Similar program IDs.
  repeated string similar_program_ids = 10;

  // iOS deep link, for example, exampleApp://content/page.
  string ios_app_link = 11;

  // iOS app store ID. This is used to check if the user has the app installed
  // on their device before deep linking. If this field is set, then the
  // ios_app_link field must also be present.
  int64 ios_app_store_id = 12;

  // Thumbnail image url, for example, http://www.example.com/thumbnail.png. The
  // thumbnail image will not be uploaded as image asset.
  string thumbnail_image_url = 13;

  // Image url, for example, http://www.example.com/image.png. The image will
  // not be uploaded as image asset.
  string image_url = 14;
}

// An asset representing a mobile app.
message MobileAppAsset {
  // Required. A string that uniquely identifies a mobile application. It should
  // just contain the platform native id, like "com.android.ebay" for Android or
  // "12345689" for iOS.
  string app_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The application store that distributes this specific app.
  google.ads.googleads.v16.enums.MobileAppVendorEnum.MobileAppVendor app_store =
      2 [(google.api.field_behavior) = REQUIRED];

  // Required. The visible text displayed when the link is rendered in an ad.
  // The length of this string should be between 1 and 25, inclusive.
  string link_text = 3 [(google.api.field_behavior) = REQUIRED];

  // Start date of when this asset is effective and can begin serving, in
  // yyyy-MM-dd format.
  string start_date = 4;

  // Last date of when this asset is effective and still serving, in yyyy-MM-dd
  // format.
  string end_date = 5;
}

// An asset representing a hotel callout.
message HotelCalloutAsset {
  // Required. The text of the hotel callout asset.
  // The length of this string should be between 1 and 25, inclusive.
  string text = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The language of the hotel callout.
  // Represented as BCP 47 language tag.
  string language_code = 2 [(google.api.field_behavior) = REQUIRED];
}

// A Call asset.
message CallAsset {
  // Required. Two-letter country code of the phone number. Examples: 'US',
  // 'us'.
  string country_code = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The advertiser's raw phone number. Examples: '**********',
  // '(123)456-7890'
  string phone_number = 2 [(google.api.field_behavior) = REQUIRED];

  // Indicates whether this CallAsset should use its own call conversion
  // setting, follow the account level setting, or disable call conversion.
  google.ads.googleads.v16.enums.CallConversionReportingStateEnum
      .CallConversionReportingState call_conversion_reporting_state = 3;

  // The conversion action to attribute a call conversion to. If not set, the
  // default conversion action is used. This field only has effect if
  // call_conversion_reporting_state is set to
  // USE_RESOURCE_LEVEL_CALL_CONVERSION_ACTION.
  string call_conversion_action = 4 [(google.api.resource_reference) = {
    type: "googleads.googleapis.com/ConversionAction"
  }];

  // List of non-overlapping schedules specifying all time intervals for which
  // the asset may serve. There can be a maximum of 6 schedules per day, 42 in
  // total.
  repeated AdScheduleInfo ad_schedule_targets = 5;
}

// An asset representing a list of price offers.
message PriceAsset {
  // Required. The type of the price asset.
  google.ads.googleads.v16.enums.PriceExtensionTypeEnum.PriceExtensionType
      type = 1 [(google.api.field_behavior) = REQUIRED];

  // The price qualifier of the price asset.
  google.ads.googleads.v16.enums.PriceExtensionPriceQualifierEnum
      .PriceExtensionPriceQualifier price_qualifier = 2;

  // Required. The language of the price asset.
  // Represented as BCP 47 language tag.
  string language_code = 3 [(google.api.field_behavior) = REQUIRED];

  // The price offerings of the price asset.
  // The size of this collection should be between 3 and 8, inclusive.
  repeated PriceOffering price_offerings = 4;
}

// A single price offering within a PriceAsset.
message PriceOffering {
  // Required. The header of the price offering.
  // The length of this string should be between 1 and 25, inclusive.
  string header = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The description of the price offering.
  // The length of this string should be between 1 and 25, inclusive.
  string description = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The price value of the price offering.
  Money price = 3 [(google.api.field_behavior) = REQUIRED];

  // The price unit of the price offering.
  google.ads.googleads.v16.enums.PriceExtensionPriceUnitEnum
      .PriceExtensionPriceUnit unit = 4;

  // Required. The final URL after all cross domain redirects.
  string final_url = 5 [(google.api.field_behavior) = REQUIRED];

  // The final mobile URL after all cross domain redirects.
  string final_mobile_url = 6;
}

// A call to action asset.
message CallToActionAsset {
  // Call to action.
  google.ads.googleads.v16.enums.CallToActionTypeEnum.CallToActionType
      call_to_action = 1;
}

// A dynamic real estate asset.
message DynamicRealEstateAsset {
  // Required. Listing ID which can be any sequence of letters and digits, and
  // must be unique and match the values of remarketing tag. Required.
  string listing_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Listing name, for example, Boulevard Bungalow. Required.
  string listing_name = 2 [(google.api.field_behavior) = REQUIRED];

  // City name, for example, Mountain View, California.
  string city_name = 3;

  // Description, for example, 3 beds, 2 baths, 1568 sq. ft.
  string description = 4;

  // Address which can be specified in one of the following formats.
  // (1) City, state, code, country, for example, Mountain View, CA, USA.
  // (2) Full address, for example, 123 Boulevard St, Mountain View, CA 94043.
  // (3) Latitude-longitude in the DDD format, for example, 41.40338, 2.17403
  string address = 5;

  // Price which can be number followed by the alphabetic currency code,
  // ISO 4217 standard. Use '.' as the decimal mark, for example, 200,000.00
  // USD.
  string price = 6;

  // Image URL, for example, http://www.example.com/image.png. The image will
  // not be uploaded as image asset.
  string image_url = 7;

  // Property type, for example, House.
  string property_type = 8;

  // Listing type, for example, For sale.
  string listing_type = 9;

  // Contextual keywords, for example, For sale; Houses for sale.
  repeated string contextual_keywords = 10;

  // Formatted price which can be any characters. If set, this attribute will be
  // used instead of 'price', for example, Starting at $200,000.00.
  string formatted_price = 11;

  // Android deep link, for example,
  // android-app://com.example.android/http/example.com/gizmos?1234.
  string android_app_link = 12;

  // iOS deep link, for example, exampleApp://content/page.
  string ios_app_link = 13;

  // iOS app store ID. This is used to check if the user has the app installed
  // on their device before deep linking. If this field is set, then the
  // ios_app_link field must also be present.
  int64 ios_app_store_id = 14;

  // Similar listing IDs.
  repeated string similar_listing_ids = 15;
}

// A dynamic custom asset.
message DynamicCustomAsset {
  // Required. ID which can be any sequence of letters and digits, and must be
  // unique and match the values of remarketing tag, for example, sedan.
  // Required.
  string id = 1 [(google.api.field_behavior) = REQUIRED];

  // ID2 which can be any sequence of letters and digits, for example, red. ID
  // sequence (ID + ID2) must be unique.
  string id2 = 2;

  // Required. Item title, for example, Mid-size sedan. Required.
  string item_title = 3 [(google.api.field_behavior) = REQUIRED];

  // Item subtitle, for example, At your Mountain View dealership.
  string item_subtitle = 4;

  // Item description, for example, Best selling mid-size car.
  string item_description = 5;

  // Item address which can be specified in one of the following formats.
  // (1) City, state, code, country, for example, Mountain View, CA, USA.
  // (2) Full address, for example, 123 Boulevard St, Mountain View, CA 94043.
  // (3) Latitude-longitude in the DDD format, for example, 41.40338, 2.17403
  string item_address = 6;

  // Item category, for example, Sedans.
  string item_category = 7;

  // Price which can be number followed by the alphabetic currency code,
  // ISO 4217 standard. Use '.' as the decimal mark, for example, 20,000.00 USD.
  string price = 8;

  // Sale price which can be number followed by the alphabetic currency code,
  // ISO 4217 standard. Use '.' as the decimal mark, for example, 15,000.00 USD.
  // Must be less than the 'price' field.
  string sale_price = 9;

  // Formatted price which can be any characters. If set, this attribute will be
  // used instead of 'price', for example, Starting at $20,000.00.
  string formatted_price = 10;

  // Formatted sale price which can be any characters. If set, this attribute
  // will be used instead of 'sale price', for example, On sale for $15,000.00.
  string formatted_sale_price = 11;

  // Image URL, for example, http://www.example.com/image.png. The image will
  // not be uploaded as image asset.
  string image_url = 12;

  // Contextual keywords, for example, Sedans, 4 door sedans.
  repeated string contextual_keywords = 13;

  // Android deep link, for example,
  // android-app://com.example.android/http/example.com/gizmos?1234.
  string android_app_link = 14;

  // iOS deep link, for example, exampleApp://content/page.
  string ios_app_link = 16;

  // iOS app store ID. This is used to check if the user has the app installed
  // on their device before deep linking. If this field is set, then the
  // ios_app_link field must also be present.
  int64 ios_app_store_id = 17;

  // Similar IDs.
  repeated string similar_ids = 15;
}

// A dynamic hotels and rentals asset.
message DynamicHotelsAndRentalsAsset {
  // Required. Property ID which can be any sequence of letters and digits, and
  // must be unique and match the values of remarketing tag. Required.
  string property_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Property name, for example, Mountain View Hotel. Required.
  string property_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Image URL, for example, http://www.example.com/image.png. The image will
  // not be uploaded as image asset.
  string image_url = 3;

  // Destination name, for example, Downtown Mountain View.
  string destination_name = 4;

  // Description, for example, Close to SJC Airport.
  string description = 5;

  // Price which can be number followed by the alphabetic currency code,
  // ISO 4217 standard. Use '.' as the decimal mark, for example, 100.00 USD.
  string price = 6;

  // ISO 4217 standard. Use '.' as the decimal mark, for example, 80.00 USD.
  // Must be less than the 'price' field.
  string sale_price = 7;

  // Star rating. Must be a number between 1 to 5, inclusive.
  int64 star_rating = 8;

  // Category, for example, Hotel suite.
  string category = 9;

  // Contextual keywords, for example, Mountain View "Hotels", South Bay hotels.
  repeated string contextual_keywords = 10;

  // Address which can be specified in one of the following formats.
  // (1) City, state, code, country, for example, Mountain View, CA, USA.
  // (2) Full address, for example, 123 Boulevard St, Mountain View, CA 94043.
  // (3) Latitude-longitude in the DDD format, for example, 41.40338, 2.17403
  string address = 11;

  // Android deep link, for example,
  // android-app://com.example.android/http/example.com/gizmos?1234.
  string android_app_link = 12;

  // iOS deep link, for example, exampleApp://content/page.
  string ios_app_link = 13;

  // iOS app store ID. This is used to check if the user has the app installed
  // on their device before deep linking. If this field is set, then the
  // ios_app_link field must also be present.
  int64 ios_app_store_id = 14;

  // Formatted price which can be any characters. If set, this attribute will be
  // used instead of 'price', for example, Starting at $100.00.
  string formatted_price = 15;

  // Formatted sale price which can be any characters. If set, this attribute
  // will be used instead of 'sale price', for example, On sale for $80.00.
  string formatted_sale_price = 16;

  // Similar property IDs.
  repeated string similar_property_ids = 17;
}

// A dynamic flights asset.
message DynamicFlightsAsset {
  // Required. Destination ID which can be any sequence of letters and digits,
  // and must be unique and match the values of remarketing tag. Required.
  string destination_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Origin ID which can be any sequence of letters and digits. The ID sequence
  // (destination ID + origin ID) must be unique.
  string origin_id = 2;

  // Required. Flight description, for example, Book your ticket. Required.
  string flight_description = 3 [(google.api.field_behavior) = REQUIRED];

  // Image URL, for example, http://www.example.com/image.png. The image will
  // not be uploaded as image asset.
  string image_url = 4;

  // Destination name, for example, Paris.
  string destination_name = 5;

  // Origin name, for example, London.
  string origin_name = 6;

  // Flight price which can be number followed by the alphabetic currency code,
  // ISO 4217 standard. Use '.' as the decimal mark, for example, 100.00 USD.
  string flight_price = 7;

  // Flight sale price which can be number followed by the alphabetic currency
  // code, ISO 4217 standard. Use '.' as the decimal mark, for example, 80.00
  // USD. Must be less than the 'flight_price' field.
  string flight_sale_price = 8;

  // Formatted price which can be any characters. If set, this attribute will be
  // used instead of 'price', for example, Starting at $100.00.
  string formatted_price = 9;

  // Formatted sale price which can be any characters. If set, this attribute
  // will be used instead of 'sale price', for example, On sale for $80.00.
  string formatted_sale_price = 10;

  // Android deep link, for example,
  // android-app://com.example.android/http/example.com/gizmos?1234.
  string android_app_link = 11;

  // iOS deep link, for example, exampleApp://content/page.
  string ios_app_link = 12;

  // iOS app store ID. This is used to check if the user has the app installed
  // on their device before deep linking. If this field is set, then the
  // ios_app_link field must also be present.
  int64 ios_app_store_id = 13;

  // Similar destination IDs, for example, PAR,LON.
  repeated string similar_destination_ids = 14;

  // A custom field which can be multiple key to values mapping separated by
  // delimiters (",", "|" and ":"), in the forms of
  // "<KEY_1>: <VALUE_1>, <VALUE_2>, ... ,<VALUE_N> | <KEY_2>: <VALUE_1>, ...
  // ,<VALUE_N> | ... | <KEY_N>: <VALUE_1>, ... ,<VALUE_N>" for example, wifi:
  // most | aircraft: 320, 77W | flights: 42 | legroom: 32".
  string custom_mapping = 15;
}

// A Discovery Carousel Card asset.
message DiscoveryCarouselCardAsset {
  // Asset resource name of the associated 1.91:1 marketing image. This and/or
  // square marketing image asset is required.
  string marketing_image_asset = 1;

  // Asset resource name of the associated square marketing image. This
  // and/or a marketing image asset is required.
  string square_marketing_image_asset = 2;

  // Asset resource name of the associated 4:5 portrait marketing image.
  string portrait_marketing_image_asset = 3;

  // Required. Headline of the carousel card.
  string headline = 4 [(google.api.field_behavior) = REQUIRED];

  // Call to action text.
  string call_to_action_text = 5;
}

// A dynamic travel asset.
message DynamicTravelAsset {
  // Required. Destination ID which can be any sequence of letters and digits,
  // and must be unique and match the values of remarketing tag. Required.
  string destination_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Origin ID which can be any sequence of letters and digits. The ID sequence
  // (destination ID + origin ID) must be unique.
  string origin_id = 2;

  // Required. Title, for example, Book your train ticket. Required.
  string title = 3 [(google.api.field_behavior) = REQUIRED];

  // Destination name, for example, Paris.
  string destination_name = 4;

  // Destination address which can be specified in one of the following formats.
  // (1) City, state, code, country, for example, Mountain View, CA, USA.
  // (2) Full address, for example, 123 Boulevard St, Mountain View, CA 94043.
  // (3) Latitude-longitude in the DDD format, for example, 41.40338, 2.17403.
  string destination_address = 5;

  // Origin name, for example, London.
  string origin_name = 6;

  // Price which can be a number followed by the alphabetic currency code,
  // ISO 4217 standard. Use '.' as the decimal mark, for example, 100.00 USD.
  string price = 7;

  // Sale price which can be a number followed by the alphabetic currency
  // code, ISO 4217 standard. Use '.' as the decimal mark, for example, 80.00
  // USD. Must be less than the 'price' field.
  string sale_price = 8;

  // Formatted price which can be any characters. If set, this attribute will be
  // used instead of 'price', for example, Starting at $100.00.
  string formatted_price = 9;

  // Formatted sale price which can be any characters. If set, this attribute
  // will be used instead of 'sale price', for example, On sale for $80.00.
  string formatted_sale_price = 10;

  // Category, for example, Express.
  string category = 11;

  // Contextual keywords, for example, Paris trains.
  repeated string contextual_keywords = 12;

  // Similar destination IDs, for example, NYC.
  repeated string similar_destination_ids = 13;

  // Image URL, for example, http://www.example.com/image.png. The image will
  // not be uploaded as image asset.
  string image_url = 14;

  // Android deep link, for example,
  // android-app://com.example.android/http/example.com/gizmos?1234.
  string android_app_link = 15;

  // iOS deep link, for example, exampleApp://content/page.
  string ios_app_link = 16;

  // iOS app store ID. This is used to check if the user has the app installed
  // on their device before deep linking. If this field is set, then the
  // ios_app_link field must also be present.
  int64 ios_app_store_id = 17;
}

// A dynamic local asset.
message DynamicLocalAsset {
  // Required. Deal ID which can be any sequence of letters and digits, and must
  // be unique and match the values of remarketing tag. Required.
  string deal_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Deal name, for example, 50% off at Mountain View Grocers.
  // Required.
  string deal_name = 2 [(google.api.field_behavior) = REQUIRED];

  // Subtitle, for example, Groceries.
  string subtitle = 3;

  // Description, for example, Save on your weekly bill.
  string description = 4;

  // Price which can be a number followed by the alphabetic currency code,
  // ISO 4217 standard. Use '.' as the decimal mark, for example, 100.00 USD.
  string price = 5;

  // Sale price which can be number followed by the alphabetic currency code,
  // ISO 4217 standard. Use '.' as the decimal mark, for example, 80.00 USD.
  // Must be less than the 'price' field.
  string sale_price = 6;

  // Image URL, for example, http://www.example.com/image.png. The image will
  // not be uploaded as image asset.
  string image_url = 7;

  // Address which can be specified in one of the following formats.
  // (1) City, state, code, country, for example, Mountain View, CA, USA.
  // (2) Full address, for example, 123 Boulevard St, Mountain View, CA 94043.
  // (3) Latitude-longitude in the DDD format, for example, 41.40338, 2.17403.
  string address = 8;

  // Category, for example, Food.
  string category = 9;

  // Contextual keywords, for example, Save groceries coupons.
  repeated string contextual_keywords = 10;

  // Formatted price which can be any characters. If set, this attribute will be
  // used instead of 'price', for example, Starting at $100.00.
  string formatted_price = 11;

  // Formatted sale price which can be any characters. If set, this attribute
  // will be used instead of 'sale price', for example, On sale for $80.00.
  string formatted_sale_price = 12;

  // Android deep link, for example,
  // android-app://com.example.android/http/example.com/gizmos?1234.
  string android_app_link = 13;

  // Similar deal IDs, for example, 1275.
  repeated string similar_deal_ids = 14;

  // iOS deep link, for example, exampleApp://content/page.
  string ios_app_link = 15;

  // iOS app store ID. This is used to check if the user has the app installed
  // on their device before deep linking. If this field is set, then the
  // ios_app_link field must also be present.
  int64 ios_app_store_id = 16;
}

// A dynamic jobs asset.
message DynamicJobsAsset {
  // Required. Job ID which can be any sequence of letters and digits, and must
  // be unique and match the values of remarketing tag. Required.
  string job_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Location ID which can be any sequence of letters and digits. The ID
  // sequence (job ID + location ID) must be unique.
  string location_id = 2;

  // Required. Job title, for example, Software engineer. Required.
  string job_title = 3 [(google.api.field_behavior) = REQUIRED];

  // Job subtitle, for example, Level II.
  string job_subtitle = 4;

  // Description, for example, Apply your technical skills.
  string description = 5;

  // Image URL, for example, http://www.example.com/image.png. The image will
  // not be uploaded as image asset.
  string image_url = 6;

  // Job category, for example, Technical.
  string job_category = 7;

  // Contextual keywords, for example, Software engineering job.
  repeated string contextual_keywords = 8;

  // Address which can be specified in one of the following formats.
  // (1) City, state, code, country, for example, Mountain View, CA, USA.
  // (2) Full address, for example, 123 Boulevard St, Mountain View, CA 94043.
  // (3) Latitude-longitude in the DDD format, for example, 41.40338, 2.17403.
  string address = 9;

  // Salary, for example, $100,000.
  string salary = 10;

  // Android deep link, for example,
  // android-app://com.example.android/http/example.com/gizmos?1234.
  string android_app_link = 11;

  // Similar job IDs, for example, 1275.
  repeated string similar_job_ids = 12;

  // iOS deep link, for example, exampleApp://content/page.
  string ios_app_link = 13;

  // iOS app store ID. This is used to check if the user has the app installed
  // on their device before deep linking. If this field is set, then the
  // ios_app_link field must also be present.
  int64 ios_app_store_id = 14;
}

// A location asset.
message LocationAsset {
  // Place IDs uniquely identify a place in the Google Places database and on
  // Google Maps.
  // This field is unique for a given customer ID and asset type. See
  // https://developers.google.com/places/web-service/place-id to learn more
  // about Place ID.
  string place_id = 1;

  // The list of business locations for the customer.
  // This will only be returned if the Location Asset is syncing from the
  // Business Profile account. It is possible to have multiple Business Profile
  // listings under the same account that point to the same Place ID.
  repeated BusinessProfileLocation business_profile_locations = 2;

  // The type of location ownership.
  // If the type is BUSINESS_OWNER, it will be served as a location extension.
  // If the type is AFFILIATE, it will be served as an affiliate location.
  google.ads.googleads.v16.enums.LocationOwnershipTypeEnum.LocationOwnershipType
      location_ownership_type = 3;
}

// Business Profile location data synced from the linked Business Profile
// account.
message BusinessProfileLocation {
  // Advertiser specified label for the location on the Business Profile
  // account. This is synced from the Business Profile account.
  repeated string labels = 1;

  // Business Profile store code of this location. This is synced from the
  // Business Profile account.
  string store_code = 2;

  // Listing ID of this Business Profile location. This is synced from the
  // linked Business Profile account.
  int64 listing_id = 3;
}

// A hotel property asset.
message HotelPropertyAsset {
  // Place IDs uniquely identify a place in the Google Places database and on
  // Google Maps. See https://developers.google.com/places/web-service/place-id
  // to learn more.
  string place_id = 1;

  // Address of the hotel. Read-only.
  string hotel_address = 2;

  // Name of the hotel. Read-only.
  string hotel_name = 3;
}
