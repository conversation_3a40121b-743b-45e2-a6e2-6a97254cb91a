// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/shared_set_status.proto";
import "google/ads/googleads/v16/enums/shared_set_type.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "SharedSetProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the SharedSet resource.

// SharedSets are used for sharing criterion exclusions across multiple
// campaigns.
message SharedSet {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/SharedSet"
    pattern: "customers/{customer_id}/sharedSets/{shared_set_id}"
  };

  // Immutable. The resource name of the shared set.
  // Shared set resource names have the form:
  //
  // `customers/{customer_id}/sharedSets/{shared_set_id}`
  string resource_name = 1 [
    (google.api.field_behavior) = IMMUTABLE,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/SharedSet"
    }
  ];

  // Output only. The ID of this shared set. Read only.
  optional int64 id = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Immutable. The type of this shared set: each shared set holds only a single
  // kind of resource. Required. Immutable.
  google.ads.googleads.v16.enums.SharedSetTypeEnum.SharedSetType type = 3
      [(google.api.field_behavior) = IMMUTABLE];

  // The name of this shared set. Required.
  // Shared Sets must have names that are unique among active shared sets of
  // the same type.
  // The length of this string should be between 1 and 255 UTF-8 bytes,
  // inclusive.
  optional string name = 9;

  // Output only. The status of this shared set. Read only.
  google.ads.googleads.v16.enums.SharedSetStatusEnum.SharedSetStatus status = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of shared criteria within this shared set. Read
  // only.
  optional int64 member_count = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. The number of campaigns associated with this shared set. Read
  // only.
  optional int64 reference_count = 11
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
