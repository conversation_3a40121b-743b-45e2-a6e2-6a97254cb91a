// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.resources;

import "google/ads/googleads/v16/enums/sk_ad_network_coarse_conversion_value.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Resources";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/resources;resources";
option java_multiple_files = true;
option java_outer_classname = "CustomerSkAdNetworkConversionValueSchemaProto";
option java_package = "com.google.ads.googleads.v16.resources";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Resources";
option ruby_package = "Google::Ads::GoogleAds::V16::Resources";

// Proto file describing the SkAdNetworkConversionVauleSchema resource.

// A CustomerSkAdNetworkConversionValueSchema.
message CustomerSkAdNetworkConversionValueSchema {
  option (google.api.resource) = {
    type: "googleads.googleapis.com/CustomerSkAdNetworkConversionValueSchema"
    pattern: "customers/{customer_id}/customerSkAdNetworkConversionValueSchemas/{account_link_id}"
  };

  // The CustomerLink specific SkAdNetworkConversionValueSchema.
  message SkAdNetworkConversionValueSchema {
    // Mappings for fine grained conversion value.
    message FineGrainedConversionValueMappings {
      // Output only. Fine grained conversion value. Valid values are in the
      // inclusive range [0,63].
      int32 fine_grained_conversion_value = 1
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. Conversion events the fine grained conversion value maps
      // to.
      ConversionValueMapping conversion_value_mapping = 2
          [(google.api.field_behavior) = OUTPUT_ONLY];
    }

    // Mappings for each postback in multiple conversion windows.
    message PostbackMapping {
      // Output only. 0-based index that indicates the order of postback. Valid
      // values are in the inclusive range [0,2].
      int32 postback_sequence_index = 1
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. Conversion value mappings for all coarse grained
      // conversion values.
      CoarseGrainedConversionValueMappings
          coarse_grained_conversion_value_mappings = 2
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Event or conversion value used for locking conversion window.
      oneof lock_window_trigger {
        // Output only. Coarse grained conversion value that triggers conversion
        // window lock.
        google.ads.googleads.v16.enums.SkAdNetworkCoarseConversionValueEnum
            .SkAdNetworkCoarseConversionValue
                lock_window_coarse_conversion_value = 3
            [(google.api.field_behavior) = OUTPUT_ONLY];

        // Output only. Fine grained conversion value that triggers conversion
        // window lock.
        int32 lock_window_fine_conversion_value = 4
            [(google.api.field_behavior) = OUTPUT_ONLY];

        // Output only. Event name that triggers conversion window lock.
        string lock_window_event = 5
            [(google.api.field_behavior) = OUTPUT_ONLY];
      }
    }

    // Mappings for coarse grained conversion values.
    message CoarseGrainedConversionValueMappings {
      // Output only. Mapping for "low" coarse conversion value.
      ConversionValueMapping low_conversion_value_mapping = 1
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. Mapping for "medium" coarse conversion value.
      ConversionValueMapping medium_conversion_value_mapping = 2
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. Mapping for "high" coarse conversion value.
      ConversionValueMapping high_conversion_value_mapping = 3
          [(google.api.field_behavior) = OUTPUT_ONLY];
    }

    // Represents mapping from one conversion value to one or more conversion
    // events.
    message ConversionValueMapping {
      // Output only. The minimum of the time range in which a user was last
      // active during the measurement window.
      int64 min_time_post_install_hours = 1
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The maximum of the time range in which a user was last
      // active during the measurement window.
      int64 max_time_post_install_hours = 2
          [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The conversion value may be mapped to multiple events with
      // various attributes.
      repeated Event mapped_events = 3
          [(google.api.field_behavior) = OUTPUT_ONLY];
    }

    // Defines a Google conversion event that the conversion value is mapped to.
    message Event {
      // Defines a range for revenue values.
      message RevenueRange {
        // Output only. For revenue ranges, the minimum value in `currency_code`
        // for which this conversion value would be updated. A value of 0 will
        // be treated as unset.
        double min_event_revenue = 3
            [(google.api.field_behavior) = OUTPUT_ONLY];

        // Output only. For revenue ranges, the maximum value in `currency_code`
        // for which this conversion value would be updated. A value of 0 will
        // be treated as unset.
        double max_event_revenue = 4
            [(google.api.field_behavior) = OUTPUT_ONLY];
      }

      // Defines a range for event counter values.
      message EventOccurrenceRange {
        // Output only. For event counter ranges, the minimum of the defined
        // range. A value of 0 will be treated as unset.
        int64 min_event_count = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

        // Output only. For event counter ranges, the maximum of the defined
        // range. A value of 0 will be treated as unset.
        int64 max_event_count = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
      }

      // Output only. Google event name represented by this conversion value.
      string mapped_event_name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

      // Output only. The reported currency for the event_revenue. ISO 4217
      // three-letter currency code, for example, "USD"
      string currency_code = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

      // Either a range or specific value for event revenue.
      oneof revenue_rate {
        // Output only. The event revenue range.
        RevenueRange event_revenue_range = 3
            [(google.api.field_behavior) = OUTPUT_ONLY];

        // Output only. The specific event revenue value.
        double event_revenue_value = 4
            [(google.api.field_behavior) = OUTPUT_ONLY];
      }

      // Either a range or specific value for event counter.
      oneof event_rate {
        // Output only. The event counter range.
        EventOccurrenceRange event_occurrence_range = 5
            [(google.api.field_behavior) = OUTPUT_ONLY];

        // Output only. For specific event counter values.
        int64 event_counter = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
      }
    }

    // Required. Output only. Apple App Store app ID.
    string app_id = 1 [
      (google.api.field_behavior) = REQUIRED,
      (google.api.field_behavior) = OUTPUT_ONLY
    ];

    // Output only. A time window (measured in hours) post-install, after which
    // the App Attribution Partner or advertiser stops calling
    // [updateConversionValue]
    // (https://developer.apple.com/documentation/storekit/skadnetwork/3566697-updateconversionvalue).
    int32 measurement_window_hours = 2
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Fine grained conversion value mappings.
    // For SkAdNetwork versions >= 4.0 that support multiple conversion
    // windows, fine grained conversion value mappings are only applicable to
    // the first postback.
    repeated FineGrainedConversionValueMappings
        fine_grained_conversion_value_mappings = 3
        [(google.api.field_behavior) = OUTPUT_ONLY];

    // Output only. Per-postback conversion value mappings for postbacks in
    // multiple conversion windows. Only applicable for SkAdNetwork versions
    // >= 4.0.
    repeated PostbackMapping postback_mappings = 4
        [(google.api.field_behavior) = OUTPUT_ONLY];
  }

  // Output only. The resource name of the schema.
  // CustomerSkAdNetworkConversionValueSchema resource names have the form:
  // customers/{customer_id}/customerSkAdNetworkConversionValueSchemas/{account_link_id}
  string resource_name = 1 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.resource_reference) = {
      type: "googleads.googleapis.com/CustomerSkAdNetworkConversionValueSchema"
    }
  ];

  // Output only. The schema for the specified resource.
  SkAdNetworkConversionValueSchema schema = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
