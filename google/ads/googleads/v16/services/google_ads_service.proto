// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.services;

import "google/ads/googleads/v16/common/metrics.proto";
import "google/ads/googleads/v16/common/segments.proto";
import "google/ads/googleads/v16/enums/response_content_type.proto";
import "google/ads/googleads/v16/enums/summary_row_setting.proto";
import "google/ads/googleads/v16/resources/accessible_bidding_strategy.proto";
import "google/ads/googleads/v16/resources/account_budget.proto";
import "google/ads/googleads/v16/resources/account_budget_proposal.proto";
import "google/ads/googleads/v16/resources/account_link.proto";
import "google/ads/googleads/v16/resources/ad_group.proto";
import "google/ads/googleads/v16/resources/ad_group_ad.proto";
import "google/ads/googleads/v16/resources/ad_group_ad_asset_combination_view.proto";
import "google/ads/googleads/v16/resources/ad_group_ad_asset_view.proto";
import "google/ads/googleads/v16/resources/ad_group_ad_label.proto";
import "google/ads/googleads/v16/resources/ad_group_asset.proto";
import "google/ads/googleads/v16/resources/ad_group_asset_set.proto";
import "google/ads/googleads/v16/resources/ad_group_audience_view.proto";
import "google/ads/googleads/v16/resources/ad_group_bid_modifier.proto";
import "google/ads/googleads/v16/resources/ad_group_criterion.proto";
import "google/ads/googleads/v16/resources/ad_group_criterion_customizer.proto";
import "google/ads/googleads/v16/resources/ad_group_criterion_label.proto";
import "google/ads/googleads/v16/resources/ad_group_criterion_simulation.proto";
import "google/ads/googleads/v16/resources/ad_group_customizer.proto";
import "google/ads/googleads/v16/resources/ad_group_extension_setting.proto";
import "google/ads/googleads/v16/resources/ad_group_feed.proto";
import "google/ads/googleads/v16/resources/ad_group_label.proto";
import "google/ads/googleads/v16/resources/ad_group_simulation.proto";
import "google/ads/googleads/v16/resources/ad_parameter.proto";
import "google/ads/googleads/v16/resources/ad_schedule_view.proto";
import "google/ads/googleads/v16/resources/age_range_view.proto";
import "google/ads/googleads/v16/resources/android_privacy_shared_key_google_ad_group.proto";
import "google/ads/googleads/v16/resources/android_privacy_shared_key_google_campaign.proto";
import "google/ads/googleads/v16/resources/android_privacy_shared_key_google_network_type.proto";
import "google/ads/googleads/v16/resources/asset.proto";
import "google/ads/googleads/v16/resources/asset_field_type_view.proto";
import "google/ads/googleads/v16/resources/asset_group.proto";
import "google/ads/googleads/v16/resources/asset_group_asset.proto";
import "google/ads/googleads/v16/resources/asset_group_listing_group_filter.proto";
import "google/ads/googleads/v16/resources/asset_group_product_group_view.proto";
import "google/ads/googleads/v16/resources/asset_group_signal.proto";
import "google/ads/googleads/v16/resources/asset_group_top_combination_view.proto";
import "google/ads/googleads/v16/resources/asset_set.proto";
import "google/ads/googleads/v16/resources/asset_set_asset.proto";
import "google/ads/googleads/v16/resources/asset_set_type_view.proto";
import "google/ads/googleads/v16/resources/audience.proto";
import "google/ads/googleads/v16/resources/batch_job.proto";
import "google/ads/googleads/v16/resources/bidding_data_exclusion.proto";
import "google/ads/googleads/v16/resources/bidding_seasonality_adjustment.proto";
import "google/ads/googleads/v16/resources/bidding_strategy.proto";
import "google/ads/googleads/v16/resources/bidding_strategy_simulation.proto";
import "google/ads/googleads/v16/resources/billing_setup.proto";
import "google/ads/googleads/v16/resources/call_view.proto";
import "google/ads/googleads/v16/resources/campaign.proto";
import "google/ads/googleads/v16/resources/campaign_asset.proto";
import "google/ads/googleads/v16/resources/campaign_asset_set.proto";
import "google/ads/googleads/v16/resources/campaign_audience_view.proto";
import "google/ads/googleads/v16/resources/campaign_bid_modifier.proto";
import "google/ads/googleads/v16/resources/campaign_budget.proto";
import "google/ads/googleads/v16/resources/campaign_conversion_goal.proto";
import "google/ads/googleads/v16/resources/campaign_criterion.proto";
import "google/ads/googleads/v16/resources/campaign_customizer.proto";
import "google/ads/googleads/v16/resources/campaign_draft.proto";
import "google/ads/googleads/v16/resources/campaign_extension_setting.proto";
import "google/ads/googleads/v16/resources/campaign_feed.proto";
import "google/ads/googleads/v16/resources/campaign_group.proto";
import "google/ads/googleads/v16/resources/campaign_label.proto";
import "google/ads/googleads/v16/resources/campaign_lifecycle_goal.proto";
import "google/ads/googleads/v16/resources/campaign_search_term_insight.proto";
import "google/ads/googleads/v16/resources/campaign_shared_set.proto";
import "google/ads/googleads/v16/resources/campaign_simulation.proto";
import "google/ads/googleads/v16/resources/carrier_constant.proto";
import "google/ads/googleads/v16/resources/change_event.proto";
import "google/ads/googleads/v16/resources/change_status.proto";
import "google/ads/googleads/v16/resources/click_view.proto";
import "google/ads/googleads/v16/resources/combined_audience.proto";
import "google/ads/googleads/v16/resources/conversion_action.proto";
import "google/ads/googleads/v16/resources/conversion_custom_variable.proto";
import "google/ads/googleads/v16/resources/conversion_goal_campaign_config.proto";
import "google/ads/googleads/v16/resources/conversion_value_rule.proto";
import "google/ads/googleads/v16/resources/conversion_value_rule_set.proto";
import "google/ads/googleads/v16/resources/currency_constant.proto";
import "google/ads/googleads/v16/resources/custom_audience.proto";
import "google/ads/googleads/v16/resources/custom_conversion_goal.proto";
import "google/ads/googleads/v16/resources/custom_interest.proto";
import "google/ads/googleads/v16/resources/customer.proto";
import "google/ads/googleads/v16/resources/customer_asset.proto";
import "google/ads/googleads/v16/resources/customer_asset_set.proto";
import "google/ads/googleads/v16/resources/customer_client.proto";
import "google/ads/googleads/v16/resources/customer_client_link.proto";
import "google/ads/googleads/v16/resources/customer_conversion_goal.proto";
import "google/ads/googleads/v16/resources/customer_customizer.proto";
import "google/ads/googleads/v16/resources/customer_extension_setting.proto";
import "google/ads/googleads/v16/resources/customer_feed.proto";
import "google/ads/googleads/v16/resources/customer_label.proto";
import "google/ads/googleads/v16/resources/customer_lifecycle_goal.proto";
import "google/ads/googleads/v16/resources/customer_manager_link.proto";
import "google/ads/googleads/v16/resources/customer_negative_criterion.proto";
import "google/ads/googleads/v16/resources/customer_search_term_insight.proto";
import "google/ads/googleads/v16/resources/customer_user_access.proto";
import "google/ads/googleads/v16/resources/customer_user_access_invitation.proto";
import "google/ads/googleads/v16/resources/customizer_attribute.proto";
import "google/ads/googleads/v16/resources/detail_placement_view.proto";
import "google/ads/googleads/v16/resources/detailed_demographic.proto";
import "google/ads/googleads/v16/resources/display_keyword_view.proto";
import "google/ads/googleads/v16/resources/distance_view.proto";
import "google/ads/googleads/v16/resources/domain_category.proto";
import "google/ads/googleads/v16/resources/dynamic_search_ads_search_term_view.proto";
import "google/ads/googleads/v16/resources/expanded_landing_page_view.proto";
import "google/ads/googleads/v16/resources/experiment.proto";
import "google/ads/googleads/v16/resources/experiment_arm.proto";
import "google/ads/googleads/v16/resources/extension_feed_item.proto";
import "google/ads/googleads/v16/resources/feed.proto";
import "google/ads/googleads/v16/resources/feed_item.proto";
import "google/ads/googleads/v16/resources/feed_item_set.proto";
import "google/ads/googleads/v16/resources/feed_item_set_link.proto";
import "google/ads/googleads/v16/resources/feed_item_target.proto";
import "google/ads/googleads/v16/resources/feed_mapping.proto";
import "google/ads/googleads/v16/resources/feed_placeholder_view.proto";
import "google/ads/googleads/v16/resources/gender_view.proto";
import "google/ads/googleads/v16/resources/geo_target_constant.proto";
import "google/ads/googleads/v16/resources/geographic_view.proto";
import "google/ads/googleads/v16/resources/group_placement_view.proto";
import "google/ads/googleads/v16/resources/hotel_group_view.proto";
import "google/ads/googleads/v16/resources/hotel_performance_view.proto";
import "google/ads/googleads/v16/resources/hotel_reconciliation.proto";
import "google/ads/googleads/v16/resources/income_range_view.proto";
import "google/ads/googleads/v16/resources/keyword_plan.proto";
import "google/ads/googleads/v16/resources/keyword_plan_ad_group.proto";
import "google/ads/googleads/v16/resources/keyword_plan_ad_group_keyword.proto";
import "google/ads/googleads/v16/resources/keyword_plan_campaign.proto";
import "google/ads/googleads/v16/resources/keyword_plan_campaign_keyword.proto";
import "google/ads/googleads/v16/resources/keyword_theme_constant.proto";
import "google/ads/googleads/v16/resources/keyword_view.proto";
import "google/ads/googleads/v16/resources/label.proto";
import "google/ads/googleads/v16/resources/landing_page_view.proto";
import "google/ads/googleads/v16/resources/language_constant.proto";
import "google/ads/googleads/v16/resources/lead_form_submission_data.proto";
import "google/ads/googleads/v16/resources/life_event.proto";
import "google/ads/googleads/v16/resources/local_services_employee.proto";
import "google/ads/googleads/v16/resources/local_services_lead.proto";
import "google/ads/googleads/v16/resources/local_services_lead_conversation.proto";
import "google/ads/googleads/v16/resources/local_services_verification_artifact.proto";
import "google/ads/googleads/v16/resources/location_view.proto";
import "google/ads/googleads/v16/resources/managed_placement_view.proto";
import "google/ads/googleads/v16/resources/media_file.proto";
import "google/ads/googleads/v16/resources/mobile_app_category_constant.proto";
import "google/ads/googleads/v16/resources/mobile_device_constant.proto";
import "google/ads/googleads/v16/resources/offline_conversion_upload_client_summary.proto";
import "google/ads/googleads/v16/resources/offline_user_data_job.proto";
import "google/ads/googleads/v16/resources/operating_system_version_constant.proto";
import "google/ads/googleads/v16/resources/paid_organic_search_term_view.proto";
import "google/ads/googleads/v16/resources/parental_status_view.proto";
import "google/ads/googleads/v16/resources/per_store_view.proto";
import "google/ads/googleads/v16/resources/product_category_constant.proto";
import "google/ads/googleads/v16/resources/product_group_view.proto";
import "google/ads/googleads/v16/resources/product_link.proto";
import "google/ads/googleads/v16/resources/product_link_invitation.proto";
import "google/ads/googleads/v16/resources/qualifying_question.proto";
import "google/ads/googleads/v16/resources/recommendation.proto";
import "google/ads/googleads/v16/resources/recommendation_subscription.proto";
import "google/ads/googleads/v16/resources/remarketing_action.proto";
import "google/ads/googleads/v16/resources/search_term_view.proto";
import "google/ads/googleads/v16/resources/shared_criterion.proto";
import "google/ads/googleads/v16/resources/shared_set.proto";
import "google/ads/googleads/v16/resources/shopping_performance_view.proto";
import "google/ads/googleads/v16/resources/smart_campaign_search_term_view.proto";
import "google/ads/googleads/v16/resources/smart_campaign_setting.proto";
import "google/ads/googleads/v16/resources/third_party_app_analytics_link.proto";
import "google/ads/googleads/v16/resources/topic_constant.proto";
import "google/ads/googleads/v16/resources/topic_view.proto";
import "google/ads/googleads/v16/resources/travel_activity_group_view.proto";
import "google/ads/googleads/v16/resources/travel_activity_performance_view.proto";
import "google/ads/googleads/v16/resources/user_interest.proto";
import "google/ads/googleads/v16/resources/user_list.proto";
import "google/ads/googleads/v16/resources/user_location_view.proto";
import "google/ads/googleads/v16/resources/video.proto";
import "google/ads/googleads/v16/resources/webpage_view.proto";
import "google/ads/googleads/v16/services/ad_group_ad_label_service.proto";
import "google/ads/googleads/v16/services/ad_group_ad_service.proto";
import "google/ads/googleads/v16/services/ad_group_asset_service.proto";
import "google/ads/googleads/v16/services/ad_group_bid_modifier_service.proto";
import "google/ads/googleads/v16/services/ad_group_criterion_customizer_service.proto";
import "google/ads/googleads/v16/services/ad_group_criterion_label_service.proto";
import "google/ads/googleads/v16/services/ad_group_criterion_service.proto";
import "google/ads/googleads/v16/services/ad_group_customizer_service.proto";
import "google/ads/googleads/v16/services/ad_group_extension_setting_service.proto";
import "google/ads/googleads/v16/services/ad_group_feed_service.proto";
import "google/ads/googleads/v16/services/ad_group_label_service.proto";
import "google/ads/googleads/v16/services/ad_group_service.proto";
import "google/ads/googleads/v16/services/ad_parameter_service.proto";
import "google/ads/googleads/v16/services/ad_service.proto";
import "google/ads/googleads/v16/services/asset_group_asset_service.proto";
import "google/ads/googleads/v16/services/asset_group_listing_group_filter_service.proto";
import "google/ads/googleads/v16/services/asset_group_service.proto";
import "google/ads/googleads/v16/services/asset_group_signal_service.proto";
import "google/ads/googleads/v16/services/asset_service.proto";
import "google/ads/googleads/v16/services/asset_set_asset_service.proto";
import "google/ads/googleads/v16/services/asset_set_service.proto";
import "google/ads/googleads/v16/services/audience_service.proto";
import "google/ads/googleads/v16/services/bidding_data_exclusion_service.proto";
import "google/ads/googleads/v16/services/bidding_seasonality_adjustment_service.proto";
import "google/ads/googleads/v16/services/bidding_strategy_service.proto";
import "google/ads/googleads/v16/services/campaign_asset_service.proto";
import "google/ads/googleads/v16/services/campaign_asset_set_service.proto";
import "google/ads/googleads/v16/services/campaign_bid_modifier_service.proto";
import "google/ads/googleads/v16/services/campaign_budget_service.proto";
import "google/ads/googleads/v16/services/campaign_conversion_goal_service.proto";
import "google/ads/googleads/v16/services/campaign_criterion_service.proto";
import "google/ads/googleads/v16/services/campaign_customizer_service.proto";
import "google/ads/googleads/v16/services/campaign_draft_service.proto";
import "google/ads/googleads/v16/services/campaign_extension_setting_service.proto";
import "google/ads/googleads/v16/services/campaign_feed_service.proto";
import "google/ads/googleads/v16/services/campaign_group_service.proto";
import "google/ads/googleads/v16/services/campaign_label_service.proto";
import "google/ads/googleads/v16/services/campaign_service.proto";
import "google/ads/googleads/v16/services/campaign_shared_set_service.proto";
import "google/ads/googleads/v16/services/conversion_action_service.proto";
import "google/ads/googleads/v16/services/conversion_custom_variable_service.proto";
import "google/ads/googleads/v16/services/conversion_goal_campaign_config_service.proto";
import "google/ads/googleads/v16/services/conversion_value_rule_service.proto";
import "google/ads/googleads/v16/services/conversion_value_rule_set_service.proto";
import "google/ads/googleads/v16/services/custom_conversion_goal_service.proto";
import "google/ads/googleads/v16/services/customer_asset_service.proto";
import "google/ads/googleads/v16/services/customer_conversion_goal_service.proto";
import "google/ads/googleads/v16/services/customer_customizer_service.proto";
import "google/ads/googleads/v16/services/customer_extension_setting_service.proto";
import "google/ads/googleads/v16/services/customer_feed_service.proto";
import "google/ads/googleads/v16/services/customer_label_service.proto";
import "google/ads/googleads/v16/services/customer_negative_criterion_service.proto";
import "google/ads/googleads/v16/services/customer_service.proto";
import "google/ads/googleads/v16/services/customizer_attribute_service.proto";
import "google/ads/googleads/v16/services/experiment_arm_service.proto";
import "google/ads/googleads/v16/services/experiment_service.proto";
import "google/ads/googleads/v16/services/extension_feed_item_service.proto";
import "google/ads/googleads/v16/services/feed_item_service.proto";
import "google/ads/googleads/v16/services/feed_item_set_link_service.proto";
import "google/ads/googleads/v16/services/feed_item_set_service.proto";
import "google/ads/googleads/v16/services/feed_item_target_service.proto";
import "google/ads/googleads/v16/services/feed_mapping_service.proto";
import "google/ads/googleads/v16/services/feed_service.proto";
import "google/ads/googleads/v16/services/keyword_plan_ad_group_keyword_service.proto";
import "google/ads/googleads/v16/services/keyword_plan_ad_group_service.proto";
import "google/ads/googleads/v16/services/keyword_plan_campaign_keyword_service.proto";
import "google/ads/googleads/v16/services/keyword_plan_campaign_service.proto";
import "google/ads/googleads/v16/services/keyword_plan_service.proto";
import "google/ads/googleads/v16/services/label_service.proto";
import "google/ads/googleads/v16/services/recommendation_subscription_service.proto";
import "google/ads/googleads/v16/services/remarketing_action_service.proto";
import "google/ads/googleads/v16/services/shared_criterion_service.proto";
import "google/ads/googleads/v16/services/shared_set_service.proto";
import "google/ads/googleads/v16/services/smart_campaign_setting_service.proto";
import "google/ads/googleads/v16/services/user_list_service.proto";
import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/field_mask.proto";
import "google/rpc/status.proto";

option csharp_namespace = "Google.Ads.GoogleAds.V16.Services";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/services;services";
option java_multiple_files = true;
option java_outer_classname = "GoogleAdsServiceProto";
option java_package = "com.google.ads.googleads.v16.services";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Services";
option ruby_package = "Google::Ads::GoogleAds::V16::Services";

// Proto file describing the GoogleAdsService.

// Service to fetch data and metrics across resources.
service GoogleAdsService {
  option (google.api.default_host) = "googleads.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/adwords";

  // Returns all rows that match the search query.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [ChangeEventError]()
  //   [ChangeStatusError]()
  //   [ClickViewError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QueryError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc Search(SearchGoogleAdsRequest) returns (SearchGoogleAdsResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/googleAds:search"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,query";
  }

  // Returns all rows that match the search stream query.
  //
  // List of thrown errors:
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [ChangeEventError]()
  //   [ChangeStatusError]()
  //   [ClickViewError]()
  //   [HeaderError]()
  //   [InternalError]()
  //   [QueryError]()
  //   [QuotaError]()
  //   [RequestError]()
  rpc SearchStream(SearchGoogleAdsStreamRequest)
      returns (stream SearchGoogleAdsStreamResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/googleAds:searchStream"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,query";
  }

  // Creates, updates, or removes resources. This method supports atomic
  // transactions with multiple types of resources. For example, you can
  // atomically create a campaign and a campaign budget, or perform up to
  // thousands of mutates atomically.
  //
  // This method is essentially a wrapper around a series of mutate methods. The
  // only features it offers over calling those methods directly are:
  //
  // - Atomic transactions
  // - Temp resource names (described below)
  // - Somewhat reduced latency over making a series of mutate calls
  //
  // Note: Only resources that support atomic transactions are included, so this
  // method can't replace all calls to individual services.
  //
  // ## Atomic Transaction Benefits
  //
  // Atomicity makes error handling much easier. If you're making a series of
  // changes and one fails, it can leave your account in an inconsistent state.
  // With atomicity, you either reach the chosen state directly, or the request
  // fails and you can retry.
  //
  // ## Temp Resource Names
  //
  // Temp resource names are a special type of resource name used to create a
  // resource and reference that resource in the same request. For example, if a
  // campaign budget is created with `resource_name` equal to
  // `customers/123/campaignBudgets/-1`, that resource name can be reused in
  // the `Campaign.budget` field in the same request. That way, the two
  // resources are created and linked atomically.
  //
  // To create a temp resource name, put a negative number in the part of the
  // name that the server would normally allocate.
  //
  // Note:
  //
  // - Resources must be created with a temp name before the name can be reused.
  //   For example, the previous CampaignBudget+Campaign example would fail if
  //   the mutate order was reversed.
  // - Temp names are not remembered across requests.
  // - There's no limit to the number of temp names in a request.
  // - Each temp name must use a unique negative number, even if the resource
  //   types differ.
  //
  // ## Latency
  //
  // It's important to group mutates by resource type or the request may time
  // out and fail. Latency is roughly equal to a series of calls to individual
  // mutate methods, where each change in resource type is a new call. For
  // example, mutating 10 campaigns then 10 ad groups is like 2 calls, while
  // mutating 1 campaign, 1 ad group, 1 campaign, 1 ad group is like 4 calls.
  //
  // List of thrown errors:
  //   [AdCustomizerError]()
  //   [AdError]()
  //   [AdGroupAdError]()
  //   [AdGroupCriterionError]()
  //   [AdGroupError]()
  //   [AssetError]()
  //   [AuthenticationError]()
  //   [AuthorizationError]()
  //   [BiddingError]()
  //   [CampaignBudgetError]()
  //   [CampaignCriterionError]()
  //   [CampaignError]()
  //   [CampaignExperimentError]()
  //   [CampaignSharedSetError]()
  //   [CollectionSizeError]()
  //   [ContextError]()
  //   [ConversionActionError]()
  //   [CriterionError]()
  //   [CustomerFeedError]()
  //   [DatabaseError]()
  //   [DateError]()
  //   [DateRangeError]()
  //   [DistinctError]()
  //   [ExtensionFeedItemError]()
  //   [ExtensionSettingError]()
  //   [FeedAttributeReferenceError]()
  //   [FeedError]()
  //   [FeedItemError]()
  //   [FeedItemSetError]()
  //   [FieldError]()
  //   [FieldMaskError]()
  //   [FunctionParsingError]()
  //   [HeaderError]()
  //   [ImageError]()
  //   [InternalError]()
  //   [KeywordPlanAdGroupKeywordError]()
  //   [KeywordPlanCampaignError]()
  //   [KeywordPlanError]()
  //   [LabelError]()
  //   [ListOperationError]()
  //   [MediaUploadError]()
  //   [MutateError]()
  //   [NewResourceCreationError]()
  //   [NullError]()
  //   [OperationAccessDeniedError]()
  //   [PolicyFindingError]()
  //   [PolicyViolationError]()
  //   [QuotaError]()
  //   [RangeError]()
  //   [RequestError]()
  //   [ResourceCountLimitExceededError]()
  //   [SettingError]()
  //   [SharedSetError]()
  //   [SizeLimitError]()
  //   [StringFormatError]()
  //   [StringLengthError]()
  //   [UrlFieldError]()
  //   [UserListError]()
  //   [YoutubeVideoRegistrationError]()
  rpc Mutate(MutateGoogleAdsRequest) returns (MutateGoogleAdsResponse) {
    option (google.api.http) = {
      post: "/v16/customers/{customer_id=*}/googleAds:mutate"
      body: "*"
    };
    option (google.api.method_signature) = "customer_id,mutate_operations";
  }
}

// Request message for
// [GoogleAdsService.Search][google.ads.googleads.v16.services.GoogleAdsService.Search].
message SearchGoogleAdsRequest {
  // Required. The ID of the customer being queried.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The query string.
  string query = 2 [(google.api.field_behavior) = REQUIRED];

  // Token of the page to retrieve. If not specified, the first
  // page of results will be returned. Use the value obtained from
  // `next_page_token` in the previous response in order to request
  // the next page of results.
  string page_token = 3;

  // Number of elements to retrieve in a single page.
  // When too large a page is requested, the server may decide to
  // further limit the number of returned resources.
  int32 page_size = 4;

  // If true, the request is validated but not executed.
  bool validate_only = 5;

  // If true, the total number of results that match the query ignoring the
  // LIMIT clause will be included in the response.
  // Default is false.
  bool return_total_results_count = 7;

  // Determines whether a summary row will be returned. By default, summary row
  // is not returned. If requested, the summary row will be sent in a response
  // by itself after all other query results are returned.
  google.ads.googleads.v16.enums.SummaryRowSettingEnum.SummaryRowSetting
      summary_row_setting = 8;
}

// Response message for
// [GoogleAdsService.Search][google.ads.googleads.v16.services.GoogleAdsService.Search].
message SearchGoogleAdsResponse {
  // The list of rows that matched the query.
  repeated GoogleAdsRow results = 1;

  // Pagination token used to retrieve the next page of results.
  // Pass the content of this string as the `page_token` attribute of
  // the next request. `next_page_token` is not returned for the last
  // page.
  string next_page_token = 2;

  // Total number of results that match the query ignoring the LIMIT
  // clause.
  int64 total_results_count = 3;

  // FieldMask that represents what fields were requested by the user.
  google.protobuf.FieldMask field_mask = 5;

  // Summary row that contains summary of metrics in results.
  // Summary of metrics means aggregation of metrics across all results,
  // here aggregation could be sum, average, rate, etc.
  GoogleAdsRow summary_row = 6;

  // The amount of resources consumed to serve the query.
  int64 query_resource_consumption = 8;
}

// Request message for
// [GoogleAdsService.SearchStream][google.ads.googleads.v16.services.GoogleAdsService.SearchStream].
message SearchGoogleAdsStreamRequest {
  // Required. The ID of the customer being queried.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The query string.
  string query = 2 [(google.api.field_behavior) = REQUIRED];

  // Determines whether a summary row will be returned. By default, summary row
  // is not returned. If requested, the summary row will be sent in a response
  // by itself after all other query results are returned.
  google.ads.googleads.v16.enums.SummaryRowSettingEnum.SummaryRowSetting
      summary_row_setting = 3;
}

// Response message for
// [GoogleAdsService.SearchStream][google.ads.googleads.v16.services.GoogleAdsService.SearchStream].
message SearchGoogleAdsStreamResponse {
  // The list of rows that matched the query.
  repeated GoogleAdsRow results = 1;

  // FieldMask that represents what fields were requested by the user.
  google.protobuf.FieldMask field_mask = 2;

  // Summary row that contains summary of metrics in results.
  // Summary of metrics means aggregation of metrics across all results,
  // here aggregation could be sum, average, rate, etc.
  GoogleAdsRow summary_row = 3;

  // The unique id of the request that is used for debugging purposes.
  string request_id = 4;

  // The amount of resources consumed to serve the query.
  // query_resource_consumption for the Summary row and non-Summary responses
  // are returned separately in their respective rows.
  // query_resource_consumption for non-Summary responses is returned in the
  // final batch of results.
  int64 query_resource_consumption = 6;
}

// A returned row from the query.
message GoogleAdsRow {
  // The account budget in the query.
  google.ads.googleads.v16.resources.AccountBudget account_budget = 42;

  // The account budget proposal referenced in the query.
  google.ads.googleads.v16.resources.AccountBudgetProposal
      account_budget_proposal = 43;

  // The AccountLink referenced in the query.
  google.ads.googleads.v16.resources.AccountLink account_link = 143;

  // The ad group referenced in the query.
  google.ads.googleads.v16.resources.AdGroup ad_group = 3;

  // The ad referenced in the query.
  google.ads.googleads.v16.resources.AdGroupAd ad_group_ad = 16;

  // The ad group ad asset combination view in the query.
  google.ads.googleads.v16.resources.AdGroupAdAssetCombinationView
      ad_group_ad_asset_combination_view = 193;

  // The ad group ad asset view in the query.
  google.ads.googleads.v16.resources.AdGroupAdAssetView ad_group_ad_asset_view =
      131;

  // The ad group ad label referenced in the query.
  google.ads.googleads.v16.resources.AdGroupAdLabel ad_group_ad_label = 120;

  // The ad group asset referenced in the query.
  google.ads.googleads.v16.resources.AdGroupAsset ad_group_asset = 154;

  // The ad group asset set referenced in the query.
  google.ads.googleads.v16.resources.AdGroupAssetSet ad_group_asset_set = 196;

  // The ad group audience view referenced in the query.
  google.ads.googleads.v16.resources.AdGroupAudienceView
      ad_group_audience_view = 57;

  // The bid modifier referenced in the query.
  google.ads.googleads.v16.resources.AdGroupBidModifier ad_group_bid_modifier =
      24;

  // The criterion referenced in the query.
  google.ads.googleads.v16.resources.AdGroupCriterion ad_group_criterion = 17;

  // The ad group criterion customizer referenced in the query.
  google.ads.googleads.v16.resources.AdGroupCriterionCustomizer
      ad_group_criterion_customizer = 187;

  // The ad group criterion label referenced in the query.
  google.ads.googleads.v16.resources.AdGroupCriterionLabel
      ad_group_criterion_label = 121;

  // The ad group criterion simulation referenced in the query.
  google.ads.googleads.v16.resources.AdGroupCriterionSimulation
      ad_group_criterion_simulation = 110;

  // The ad group customizer referenced in the query.
  google.ads.googleads.v16.resources.AdGroupCustomizer ad_group_customizer =
      185;

  // The ad group extension setting referenced in the query.
  google.ads.googleads.v16.resources.AdGroupExtensionSetting
      ad_group_extension_setting = 112;

  // The ad group feed referenced in the query.
  google.ads.googleads.v16.resources.AdGroupFeed ad_group_feed = 67;

  // The ad group label referenced in the query.
  google.ads.googleads.v16.resources.AdGroupLabel ad_group_label = 115;

  // The ad group simulation referenced in the query.
  google.ads.googleads.v16.resources.AdGroupSimulation ad_group_simulation =
      107;

  // The ad parameter referenced in the query.
  google.ads.googleads.v16.resources.AdParameter ad_parameter = 130;

  // The age range view referenced in the query.
  google.ads.googleads.v16.resources.AgeRangeView age_range_view = 48;

  // The ad schedule view referenced in the query.
  google.ads.googleads.v16.resources.AdScheduleView ad_schedule_view = 89;

  // The domain category referenced in the query.
  google.ads.googleads.v16.resources.DomainCategory domain_category = 91;

  // The asset referenced in the query.
  google.ads.googleads.v16.resources.Asset asset = 105;

  // The asset field type view referenced in the query.
  google.ads.googleads.v16.resources.AssetFieldTypeView asset_field_type_view =
      168;

  // The asset group asset referenced in the query.
  google.ads.googleads.v16.resources.AssetGroupAsset asset_group_asset = 173;

  // The asset group signal referenced in the query.
  google.ads.googleads.v16.resources.AssetGroupSignal asset_group_signal = 191;

  // The asset group listing group filter referenced in the query.
  google.ads.googleads.v16.resources.AssetGroupListingGroupFilter
      asset_group_listing_group_filter = 182;

  // The asset group product group view referenced in the query.
  google.ads.googleads.v16.resources.AssetGroupProductGroupView
      asset_group_product_group_view = 189;

  // The asset group top combination view referenced in the query.
  google.ads.googleads.v16.resources.AssetGroupTopCombinationView
      asset_group_top_combination_view = 199;

  // The asset group referenced in the query.
  google.ads.googleads.v16.resources.AssetGroup asset_group = 172;

  // The asset set asset referenced in the query.
  google.ads.googleads.v16.resources.AssetSetAsset asset_set_asset = 180;

  // The asset set referenced in the query.
  google.ads.googleads.v16.resources.AssetSet asset_set = 179;

  // The asset set type view referenced in the query.
  google.ads.googleads.v16.resources.AssetSetTypeView asset_set_type_view = 197;

  // The batch job referenced in the query.
  google.ads.googleads.v16.resources.BatchJob batch_job = 139;

  // The bidding data exclusion referenced in the query.
  google.ads.googleads.v16.resources.BiddingDataExclusion
      bidding_data_exclusion = 159;

  // The bidding seasonality adjustment referenced in the query.
  google.ads.googleads.v16.resources.BiddingSeasonalityAdjustment
      bidding_seasonality_adjustment = 160;

  // The bidding strategy referenced in the query.
  google.ads.googleads.v16.resources.BiddingStrategy bidding_strategy = 18;

  // The bidding strategy simulation referenced in the query.
  google.ads.googleads.v16.resources.BiddingStrategySimulation
      bidding_strategy_simulation = 158;

  // The billing setup referenced in the query.
  google.ads.googleads.v16.resources.BillingSetup billing_setup = 41;

  // The call view referenced in the query.
  google.ads.googleads.v16.resources.CallView call_view = 152;

  // The campaign budget referenced in the query.
  google.ads.googleads.v16.resources.CampaignBudget campaign_budget = 19;

  // The campaign referenced in the query.
  google.ads.googleads.v16.resources.Campaign campaign = 2;

  // The campaign asset referenced in the query.
  google.ads.googleads.v16.resources.CampaignAsset campaign_asset = 142;

  // The campaign asset set referenced in the query.
  google.ads.googleads.v16.resources.CampaignAssetSet campaign_asset_set = 181;

  // The campaign audience view referenced in the query.
  google.ads.googleads.v16.resources.CampaignAudienceView
      campaign_audience_view = 69;

  // The campaign bid modifier referenced in the query.
  google.ads.googleads.v16.resources.CampaignBidModifier campaign_bid_modifier =
      26;

  // The CampaignConversionGoal referenced in the query.
  google.ads.googleads.v16.resources.CampaignConversionGoal
      campaign_conversion_goal = 175;

  // The campaign criterion referenced in the query.
  google.ads.googleads.v16.resources.CampaignCriterion campaign_criterion = 20;

  // The campaign customizer referenced in the query.
  google.ads.googleads.v16.resources.CampaignCustomizer campaign_customizer =
      186;

  // The campaign draft referenced in the query.
  google.ads.googleads.v16.resources.CampaignDraft campaign_draft = 49;

  // The campaign extension setting referenced in the query.
  google.ads.googleads.v16.resources.CampaignExtensionSetting
      campaign_extension_setting = 113;

  // The campaign feed referenced in the query.
  google.ads.googleads.v16.resources.CampaignFeed campaign_feed = 63;

  // Campaign Group referenced in AWQL query.
  google.ads.googleads.v16.resources.CampaignGroup campaign_group = 25;

  // The campaign label referenced in the query.
  google.ads.googleads.v16.resources.CampaignLabel campaign_label = 108;

  // The campaign lifecycle goal referenced in the query.
  google.ads.googleads.v16.resources.CampaignLifecycleGoal
      campaign_lifecycle_goal = 213;

  // The campaign search term insight referenced in the query.
  google.ads.googleads.v16.resources.CampaignSearchTermInsight
      campaign_search_term_insight = 204;

  // Campaign Shared Set referenced in AWQL query.
  google.ads.googleads.v16.resources.CampaignSharedSet campaign_shared_set = 30;

  // The campaign simulation referenced in the query.
  google.ads.googleads.v16.resources.CampaignSimulation campaign_simulation =
      157;

  // The carrier constant referenced in the query.
  google.ads.googleads.v16.resources.CarrierConstant carrier_constant = 66;

  // The ChangeEvent referenced in the query.
  google.ads.googleads.v16.resources.ChangeEvent change_event = 145;

  // The ChangeStatus referenced in the query.
  google.ads.googleads.v16.resources.ChangeStatus change_status = 37;

  // The CombinedAudience referenced in the query.
  google.ads.googleads.v16.resources.CombinedAudience combined_audience = 148;

  // The Audience referenced in the query.
  google.ads.googleads.v16.resources.Audience audience = 190;

  // The conversion action referenced in the query.
  google.ads.googleads.v16.resources.ConversionAction conversion_action = 103;

  // The conversion custom variable referenced in the query.
  google.ads.googleads.v16.resources.ConversionCustomVariable
      conversion_custom_variable = 153;

  // The ConversionGoalCampaignConfig referenced in the query.
  google.ads.googleads.v16.resources.ConversionGoalCampaignConfig
      conversion_goal_campaign_config = 177;

  // The conversion value rule referenced in the query.
  google.ads.googleads.v16.resources.ConversionValueRule conversion_value_rule =
      164;

  // The conversion value rule set referenced in the query.
  google.ads.googleads.v16.resources.ConversionValueRuleSet
      conversion_value_rule_set = 165;

  // The ClickView referenced in the query.
  google.ads.googleads.v16.resources.ClickView click_view = 122;

  // The currency constant referenced in the query.
  google.ads.googleads.v16.resources.CurrencyConstant currency_constant = 134;

  // The CustomAudience referenced in the query.
  google.ads.googleads.v16.resources.CustomAudience custom_audience = 147;

  // The CustomConversionGoal referenced in the query.
  google.ads.googleads.v16.resources.CustomConversionGoal
      custom_conversion_goal = 176;

  // The CustomInterest referenced in the query.
  google.ads.googleads.v16.resources.CustomInterest custom_interest = 104;

  // The customer referenced in the query.
  google.ads.googleads.v16.resources.Customer customer = 1;

  // The customer asset referenced in the query.
  google.ads.googleads.v16.resources.CustomerAsset customer_asset = 155;

  // The customer asset set referenced in the query.
  google.ads.googleads.v16.resources.CustomerAssetSet customer_asset_set = 195;

  // The accessible bidding strategy referenced in the query.
  google.ads.googleads.v16.resources.AccessibleBiddingStrategy
      accessible_bidding_strategy = 169;

  // The customer customizer referenced in the query.
  google.ads.googleads.v16.resources.CustomerCustomizer customer_customizer =
      184;

  // The CustomerManagerLink referenced in the query.
  google.ads.googleads.v16.resources.CustomerManagerLink customer_manager_link =
      61;

  // The CustomerClientLink referenced in the query.
  google.ads.googleads.v16.resources.CustomerClientLink customer_client_link =
      62;

  // The CustomerClient referenced in the query.
  google.ads.googleads.v16.resources.CustomerClient customer_client = 70;

  // The CustomerConversionGoal referenced in the query.
  google.ads.googleads.v16.resources.CustomerConversionGoal
      customer_conversion_goal = 174;

  // The customer extension setting referenced in the query.
  google.ads.googleads.v16.resources.CustomerExtensionSetting
      customer_extension_setting = 114;

  // The customer feed referenced in the query.
  google.ads.googleads.v16.resources.CustomerFeed customer_feed = 64;

  // The customer label referenced in the query.
  google.ads.googleads.v16.resources.CustomerLabel customer_label = 124;

  // The customer lifecycle goal referenced in the query.
  google.ads.googleads.v16.resources.CustomerLifecycleGoal
      customer_lifecycle_goal = 212;

  // The customer negative criterion referenced in the query.
  google.ads.googleads.v16.resources.CustomerNegativeCriterion
      customer_negative_criterion = 88;

  // The customer search term insight referenced in the query.
  google.ads.googleads.v16.resources.CustomerSearchTermInsight
      customer_search_term_insight = 205;

  // The CustomerUserAccess referenced in the query.
  google.ads.googleads.v16.resources.CustomerUserAccess customer_user_access =
      146;

  // The CustomerUserAccessInvitation referenced in the query.
  google.ads.googleads.v16.resources.CustomerUserAccessInvitation
      customer_user_access_invitation = 150;

  // The customizer attribute referenced in the query.
  google.ads.googleads.v16.resources.CustomizerAttribute customizer_attribute =
      178;

  // The detail placement view referenced in the query.
  google.ads.googleads.v16.resources.DetailPlacementView detail_placement_view =
      118;

  // The detailed demographic referenced in the query.
  google.ads.googleads.v16.resources.DetailedDemographic detailed_demographic =
      166;

  // The display keyword view referenced in the query.
  google.ads.googleads.v16.resources.DisplayKeywordView display_keyword_view =
      47;

  // The distance view referenced in the query.
  google.ads.googleads.v16.resources.DistanceView distance_view = 132;

  // The dynamic search ads search term view referenced in the query.
  google.ads.googleads.v16.resources.DynamicSearchAdsSearchTermView
      dynamic_search_ads_search_term_view = 106;

  // The expanded landing page view referenced in the query.
  google.ads.googleads.v16.resources.ExpandedLandingPageView
      expanded_landing_page_view = 128;

  // The extension feed item referenced in the query.
  google.ads.googleads.v16.resources.ExtensionFeedItem extension_feed_item = 85;

  // The feed referenced in the query.
  google.ads.googleads.v16.resources.Feed feed = 46;

  // The feed item referenced in the query.
  google.ads.googleads.v16.resources.FeedItem feed_item = 50;

  // The feed item set referenced in the query.
  google.ads.googleads.v16.resources.FeedItemSet feed_item_set = 149;

  // The feed item set link referenced in the query.
  google.ads.googleads.v16.resources.FeedItemSetLink feed_item_set_link = 151;

  // The feed item target referenced in the query.
  google.ads.googleads.v16.resources.FeedItemTarget feed_item_target = 116;

  // The feed mapping referenced in the query.
  google.ads.googleads.v16.resources.FeedMapping feed_mapping = 58;

  // The feed placeholder view referenced in the query.
  google.ads.googleads.v16.resources.FeedPlaceholderView feed_placeholder_view =
      97;

  // The gender view referenced in the query.
  google.ads.googleads.v16.resources.GenderView gender_view = 40;

  // The geo target constant referenced in the query.
  google.ads.googleads.v16.resources.GeoTargetConstant geo_target_constant = 23;

  // The geographic view referenced in the query.
  google.ads.googleads.v16.resources.GeographicView geographic_view = 125;

  // The group placement view referenced in the query.
  google.ads.googleads.v16.resources.GroupPlacementView group_placement_view =
      119;

  // The hotel group view referenced in the query.
  google.ads.googleads.v16.resources.HotelGroupView hotel_group_view = 51;

  // The hotel performance view referenced in the query.
  google.ads.googleads.v16.resources.HotelPerformanceView
      hotel_performance_view = 71;

  // The hotel reconciliation referenced in the query.
  google.ads.googleads.v16.resources.HotelReconciliation hotel_reconciliation =
      188;

  // The income range view referenced in the query.
  google.ads.googleads.v16.resources.IncomeRangeView income_range_view = 138;

  // The keyword view referenced in the query.
  google.ads.googleads.v16.resources.KeywordView keyword_view = 21;

  // The keyword plan referenced in the query.
  google.ads.googleads.v16.resources.KeywordPlan keyword_plan = 32;

  // The keyword plan campaign referenced in the query.
  google.ads.googleads.v16.resources.KeywordPlanCampaign keyword_plan_campaign =
      33;

  // The keyword plan campaign keyword referenced in the query.
  google.ads.googleads.v16.resources.KeywordPlanCampaignKeyword
      keyword_plan_campaign_keyword = 140;

  // The keyword plan ad group referenced in the query.
  google.ads.googleads.v16.resources.KeywordPlanAdGroup keyword_plan_ad_group =
      35;

  // The keyword plan ad group referenced in the query.
  google.ads.googleads.v16.resources.KeywordPlanAdGroupKeyword
      keyword_plan_ad_group_keyword = 141;

  // The keyword theme constant referenced in the query.
  google.ads.googleads.v16.resources.KeywordThemeConstant
      keyword_theme_constant = 163;

  // The label referenced in the query.
  google.ads.googleads.v16.resources.Label label = 52;

  // The landing page view referenced in the query.
  google.ads.googleads.v16.resources.LandingPageView landing_page_view = 126;

  // The language constant referenced in the query.
  google.ads.googleads.v16.resources.LanguageConstant language_constant = 55;

  // The location view referenced in the query.
  google.ads.googleads.v16.resources.LocationView location_view = 123;

  // The managed placement view referenced in the query.
  google.ads.googleads.v16.resources.ManagedPlacementView
      managed_placement_view = 53;

  // The media file referenced in the query.
  google.ads.googleads.v16.resources.MediaFile media_file = 90;

  // The local services employee referenced in the query.
  google.ads.googleads.v16.resources.LocalServicesEmployee
      local_services_employee = 223;

  // The local services verification artifact referenced in the query.
  google.ads.googleads.v16.resources.LocalServicesVerificationArtifact
      local_services_verification_artifact = 211;

  // The mobile app category constant referenced in the query.
  google.ads.googleads.v16.resources.MobileAppCategoryConstant
      mobile_app_category_constant = 87;

  // The mobile device constant referenced in the query.
  google.ads.googleads.v16.resources.MobileDeviceConstant
      mobile_device_constant = 98;

  // Offline conversion upload client summary.
  google.ads.googleads.v16.resources.OfflineConversionUploadClientSummary
      offline_conversion_upload_client_summary = 216;

  // The offline user data job referenced in the query.
  google.ads.googleads.v16.resources.OfflineUserDataJob offline_user_data_job =
      137;

  // The operating system version constant referenced in the query.
  google.ads.googleads.v16.resources.OperatingSystemVersionConstant
      operating_system_version_constant = 86;

  // The paid organic search term view referenced in the query.
  google.ads.googleads.v16.resources.PaidOrganicSearchTermView
      paid_organic_search_term_view = 129;

  // The qualifying question referenced in the query.
  google.ads.googleads.v16.resources.QualifyingQuestion qualifying_question =
      202;

  // The parental status view referenced in the query.
  google.ads.googleads.v16.resources.ParentalStatusView parental_status_view =
      45;

  // The per store view referenced in the query.
  google.ads.googleads.v16.resources.PerStoreView per_store_view = 198;

  // The product category referenced in the query.
  google.ads.googleads.v16.resources.ProductCategoryConstant
      product_category_constant = 208;

  // The product group view referenced in the query.
  google.ads.googleads.v16.resources.ProductGroupView product_group_view = 54;

  // The product link referenced in the query.
  google.ads.googleads.v16.resources.ProductLink product_link = 194;

  // The product link invitation in the query.
  google.ads.googleads.v16.resources.ProductLinkInvitation
      product_link_invitation = 209;

  // The recommendation referenced in the query.
  google.ads.googleads.v16.resources.Recommendation recommendation = 22;

  // The recommendation subscription referenced in the query.
  google.ads.googleads.v16.resources.RecommendationSubscription
      recommendation_subscription = 220;

  // The search term view referenced in the query.
  google.ads.googleads.v16.resources.SearchTermView search_term_view = 68;

  // The shared set referenced in the query.
  google.ads.googleads.v16.resources.SharedCriterion shared_criterion = 29;

  // The shared set referenced in the query.
  google.ads.googleads.v16.resources.SharedSet shared_set = 27;

  // The Smart campaign setting referenced in the query.
  google.ads.googleads.v16.resources.SmartCampaignSetting
      smart_campaign_setting = 167;

  // The shopping performance view referenced in the query.
  google.ads.googleads.v16.resources.ShoppingPerformanceView
      shopping_performance_view = 117;

  // The Smart campaign search term view referenced in the query.
  google.ads.googleads.v16.resources.SmartCampaignSearchTermView
      smart_campaign_search_term_view = 170;

  // The AccountLink referenced in the query.
  google.ads.googleads.v16.resources.ThirdPartyAppAnalyticsLink
      third_party_app_analytics_link = 144;

  // The topic view referenced in the query.
  google.ads.googleads.v16.resources.TopicView topic_view = 44;

  // The travel activity group view referenced in the query.
  google.ads.googleads.v16.resources.TravelActivityGroupView
      travel_activity_group_view = 201;

  // The travel activity performance view referenced in the query.
  google.ads.googleads.v16.resources.TravelActivityPerformanceView
      travel_activity_performance_view = 200;

  // The experiment referenced in the query.
  google.ads.googleads.v16.resources.Experiment experiment = 133;

  // The experiment arm referenced in the query.
  google.ads.googleads.v16.resources.ExperimentArm experiment_arm = 183;

  // The user interest referenced in the query.
  google.ads.googleads.v16.resources.UserInterest user_interest = 59;

  // The life event referenced in the query.
  google.ads.googleads.v16.resources.LifeEvent life_event = 161;

  // The user list referenced in the query.
  google.ads.googleads.v16.resources.UserList user_list = 38;

  // The user location view referenced in the query.
  google.ads.googleads.v16.resources.UserLocationView user_location_view = 135;

  // The remarketing action referenced in the query.
  google.ads.googleads.v16.resources.RemarketingAction remarketing_action = 60;

  // The topic constant referenced in the query.
  google.ads.googleads.v16.resources.TopicConstant topic_constant = 31;

  // The video referenced in the query.
  google.ads.googleads.v16.resources.Video video = 39;

  // The webpage view referenced in the query.
  google.ads.googleads.v16.resources.WebpageView webpage_view = 162;

  // The lead form user submission referenced in the query.
  google.ads.googleads.v16.resources.LeadFormSubmissionData
      lead_form_submission_data = 192;

  // The local services lead referenced in the query.
  google.ads.googleads.v16.resources.LocalServicesLead local_services_lead =
      210;

  // The local services lead conversationreferenced in the query.
  google.ads.googleads.v16.resources.LocalServicesLeadConversation
      local_services_lead_conversation = 214;

  // The android privacy shared key google ad group referenced in the query.
  google.ads.googleads.v16.resources.AndroidPrivacySharedKeyGoogleAdGroup
      android_privacy_shared_key_google_ad_group = 217;

  // The android privacy shared key google campaign referenced in the query.
  google.ads.googleads.v16.resources.AndroidPrivacySharedKeyGoogleCampaign
      android_privacy_shared_key_google_campaign = 218;

  // The android privacy shared key google network type referenced in the query.
  google.ads.googleads.v16.resources.AndroidPrivacySharedKeyGoogleNetworkType
      android_privacy_shared_key_google_network_type = 219;

  // The metrics.
  google.ads.googleads.v16.common.Metrics metrics = 4;

  // The segments.
  google.ads.googleads.v16.common.Segments segments = 102;
}

// Request message for
// [GoogleAdsService.Mutate][google.ads.googleads.v16.services.GoogleAdsService.Mutate].
message MutateGoogleAdsRequest {
  // Required. The ID of the customer whose resources are being modified.
  string customer_id = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The list of operations to perform on individual resources.
  repeated MutateOperation mutate_operations = 2
      [(google.api.field_behavior) = REQUIRED];

  // If true, successful operations will be carried out and invalid
  // operations will return errors. If false, all operations will be carried
  // out in one transaction if and only if they are all valid.
  // Default is false.
  bool partial_failure = 3;

  // If true, the request is validated but not executed. Only errors are
  // returned, not results.
  bool validate_only = 4;

  // The response content type setting. Determines whether the mutable resource
  // or just the resource name should be returned post mutation. The mutable
  // resource will only be returned if the resource has the appropriate response
  // field. For example, MutateCampaignResult.campaign.
  google.ads.googleads.v16.enums.ResponseContentTypeEnum.ResponseContentType
      response_content_type = 5;
}

// Response message for
// [GoogleAdsService.Mutate][google.ads.googleads.v16.services.GoogleAdsService.Mutate].
message MutateGoogleAdsResponse {
  // Errors that pertain to operation failures in the partial failure mode.
  // Returned only when partial_failure = true and all errors occur inside the
  // operations. If any errors occur outside the operations (for example, auth
  // errors), we return an RPC level error.
  google.rpc.Status partial_failure_error = 3;

  // All responses for the mutate.
  repeated MutateOperationResponse mutate_operation_responses = 1;
}

// A single operation (create, update, remove) on a resource.
message MutateOperation {
  // The mutate operation.
  oneof operation {
    // An ad group ad label mutate operation.
    AdGroupAdLabelOperation ad_group_ad_label_operation = 17;

    // An ad group ad mutate operation.
    AdGroupAdOperation ad_group_ad_operation = 1;

    // An ad group asset mutate operation.
    AdGroupAssetOperation ad_group_asset_operation = 56;

    // An ad group bid modifier mutate operation.
    AdGroupBidModifierOperation ad_group_bid_modifier_operation = 2;

    // An ad group criterion customizer mutate operation.
    AdGroupCriterionCustomizerOperation
        ad_group_criterion_customizer_operation = 77;

    // An ad group criterion label mutate operation.
    AdGroupCriterionLabelOperation ad_group_criterion_label_operation = 18;

    // An ad group criterion mutate operation.
    AdGroupCriterionOperation ad_group_criterion_operation = 3;

    // An ad group customizer mutate operation.
    AdGroupCustomizerOperation ad_group_customizer_operation = 75;

    // An ad group extension setting mutate operation.
    AdGroupExtensionSettingOperation ad_group_extension_setting_operation = 19;

    // An ad group feed mutate operation.
    AdGroupFeedOperation ad_group_feed_operation = 20;

    // An ad group label mutate operation.
    AdGroupLabelOperation ad_group_label_operation = 21;

    // An ad group mutate operation.
    AdGroupOperation ad_group_operation = 5;

    // An ad mutate operation.
    AdOperation ad_operation = 49;

    // An ad parameter mutate operation.
    AdParameterOperation ad_parameter_operation = 22;

    // An asset mutate operation.
    AssetOperation asset_operation = 23;

    // An asset group asset mutate operation.
    AssetGroupAssetOperation asset_group_asset_operation = 65;

    // An asset group listing group filter mutate operation.
    AssetGroupListingGroupFilterOperation
        asset_group_listing_group_filter_operation = 78;

    // An asset group signal mutate operation.
    AssetGroupSignalOperation asset_group_signal_operation = 80;

    // An asset group mutate operation.
    AssetGroupOperation asset_group_operation = 62;

    // An asset set asset mutate operation.
    AssetSetAssetOperation asset_set_asset_operation = 71;

    // An asset set mutate operation.
    AssetSetOperation asset_set_operation = 72;

    // An audience mutate operation.
    AudienceOperation audience_operation = 81;

    // A bidding data exclusion mutate operation.
    BiddingDataExclusionOperation bidding_data_exclusion_operation = 58;

    // A bidding seasonality adjustment mutate operation.
    BiddingSeasonalityAdjustmentOperation
        bidding_seasonality_adjustment_operation = 59;

    // A bidding strategy mutate operation.
    BiddingStrategyOperation bidding_strategy_operation = 6;

    // A campaign asset mutate operation.
    CampaignAssetOperation campaign_asset_operation = 52;

    // A campaign asset mutate operation.
    CampaignAssetSetOperation campaign_asset_set_operation = 73;

    // A campaign bid modifier mutate operation.
    CampaignBidModifierOperation campaign_bid_modifier_operation = 7;

    // A campaign budget mutate operation.
    CampaignBudgetOperation campaign_budget_operation = 8;

    // A campaign conversion goal mutate operation.
    CampaignConversionGoalOperation campaign_conversion_goal_operation = 67;

    // A campaign criterion mutate operation.
    CampaignCriterionOperation campaign_criterion_operation = 13;

    // A campaign customizer mutate operation.
    CampaignCustomizerOperation campaign_customizer_operation = 76;

    // A campaign draft mutate operation.
    CampaignDraftOperation campaign_draft_operation = 24;

    // A campaign extension setting mutate operation.
    CampaignExtensionSettingOperation campaign_extension_setting_operation = 26;

    // A campaign feed mutate operation.
    CampaignFeedOperation campaign_feed_operation = 27;

    // A campaign group mutate operation.
    CampaignGroupOperation campaign_group_operation = 9;

    // A campaign label mutate operation.
    CampaignLabelOperation campaign_label_operation = 28;

    // A campaign mutate operation.
    CampaignOperation campaign_operation = 10;

    // A campaign shared set mutate operation.
    CampaignSharedSetOperation campaign_shared_set_operation = 11;

    // A conversion action mutate operation.
    ConversionActionOperation conversion_action_operation = 12;

    // A conversion custom variable mutate operation.
    ConversionCustomVariableOperation conversion_custom_variable_operation = 55;

    // A conversion goal campaign config mutate operation.
    ConversionGoalCampaignConfigOperation
        conversion_goal_campaign_config_operation = 69;

    // A conversion value rule mutate operation.
    ConversionValueRuleOperation conversion_value_rule_operation = 63;

    // A conversion value rule set mutate operation.
    ConversionValueRuleSetOperation conversion_value_rule_set_operation = 64;

    // A custom conversion goal mutate operation.
    CustomConversionGoalOperation custom_conversion_goal_operation = 68;

    // A customer asset mutate operation.
    CustomerAssetOperation customer_asset_operation = 57;

    // A customer conversion goal mutate operation.
    CustomerConversionGoalOperation customer_conversion_goal_operation = 66;

    // A customer customizer mutate operation.
    CustomerCustomizerOperation customer_customizer_operation = 79;

    // A customer extension setting mutate operation.
    CustomerExtensionSettingOperation customer_extension_setting_operation = 30;

    // A customer feed mutate operation.
    CustomerFeedOperation customer_feed_operation = 31;

    // A customer label mutate operation.
    CustomerLabelOperation customer_label_operation = 32;

    // A customer negative criterion mutate operation.
    CustomerNegativeCriterionOperation customer_negative_criterion_operation =
        34;

    // A customer mutate operation.
    CustomerOperation customer_operation = 35;

    // A customizer attribute mutate operation.
    CustomizerAttributeOperation customizer_attribute_operation = 70;

    // An experiment mutate operation.
    ExperimentOperation experiment_operation = 82;

    // An experiment arm mutate operation.
    ExperimentArmOperation experiment_arm_operation = 83;

    // An extension feed item mutate operation.
    ExtensionFeedItemOperation extension_feed_item_operation = 36;

    // A feed item mutate operation.
    FeedItemOperation feed_item_operation = 37;

    // A feed item set mutate operation.
    FeedItemSetOperation feed_item_set_operation = 53;

    // A feed item set link mutate operation.
    FeedItemSetLinkOperation feed_item_set_link_operation = 54;

    // A feed item target mutate operation.
    FeedItemTargetOperation feed_item_target_operation = 38;

    // A feed mapping mutate operation.
    FeedMappingOperation feed_mapping_operation = 39;

    // A feed mutate operation.
    FeedOperation feed_operation = 40;

    // A keyword plan ad group operation.
    KeywordPlanAdGroupOperation keyword_plan_ad_group_operation = 44;

    // A keyword plan ad group keyword operation.
    KeywordPlanAdGroupKeywordOperation keyword_plan_ad_group_keyword_operation =
        50;

    // A keyword plan campaign keyword operation.
    KeywordPlanCampaignKeywordOperation
        keyword_plan_campaign_keyword_operation = 51;

    // A keyword plan campaign operation.
    KeywordPlanCampaignOperation keyword_plan_campaign_operation = 45;

    // A keyword plan operation.
    KeywordPlanOperation keyword_plan_operation = 48;

    // A label mutate operation.
    LabelOperation label_operation = 41;

    // A recommendation subscription mutate operation.
    RecommendationSubscriptionOperation recommendation_subscription_operation =
        86;

    // A remarketing action mutate operation.
    RemarketingActionOperation remarketing_action_operation = 43;

    // A shared criterion mutate operation.
    SharedCriterionOperation shared_criterion_operation = 14;

    // A shared set mutate operation.
    SharedSetOperation shared_set_operation = 15;

    // A Smart campaign setting mutate operation.
    SmartCampaignSettingOperation smart_campaign_setting_operation = 61;

    // A user list mutate operation.
    UserListOperation user_list_operation = 16;
  }
}

// Response message for the resource mutate.
message MutateOperationResponse {
  // The mutate response.
  oneof response {
    // The result for the ad group ad label mutate.
    MutateAdGroupAdLabelResult ad_group_ad_label_result = 17;

    // The result for the ad group ad mutate.
    MutateAdGroupAdResult ad_group_ad_result = 1;

    // The result for the ad group asset mutate.
    MutateAdGroupAssetResult ad_group_asset_result = 56;

    // The result for the ad group bid modifier mutate.
    MutateAdGroupBidModifierResult ad_group_bid_modifier_result = 2;

    // The result for the ad group criterion customizer mutate.
    MutateAdGroupCriterionCustomizerResult
        ad_group_criterion_customizer_result = 77;

    // The result for the ad group criterion label mutate.
    MutateAdGroupCriterionLabelResult ad_group_criterion_label_result = 18;

    // The result for the ad group criterion mutate.
    MutateAdGroupCriterionResult ad_group_criterion_result = 3;

    // The result for the ad group customizer mutate.
    MutateAdGroupCustomizerResult ad_group_customizer_result = 75;

    // The result for the ad group extension setting mutate.
    MutateAdGroupExtensionSettingResult ad_group_extension_setting_result = 19;

    // The result for the ad group feed mutate.
    MutateAdGroupFeedResult ad_group_feed_result = 20;

    // The result for the ad group label mutate.
    MutateAdGroupLabelResult ad_group_label_result = 21;

    // The result for the ad group mutate.
    MutateAdGroupResult ad_group_result = 5;

    // The result for the ad parameter mutate.
    MutateAdParameterResult ad_parameter_result = 22;

    // The result for the ad mutate.
    MutateAdResult ad_result = 49;

    // The result for the asset mutate.
    MutateAssetResult asset_result = 23;

    // The result for the asset group asset mutate.
    MutateAssetGroupAssetResult asset_group_asset_result = 65;

    // The result for the asset group listing group filter mutate.
    MutateAssetGroupListingGroupFilterResult
        asset_group_listing_group_filter_result = 78;

    // The result for the asset group signal mutate.
    MutateAssetGroupSignalResult asset_group_signal_result = 79;

    // The result for the asset group mutate.
    MutateAssetGroupResult asset_group_result = 62;

    // The result for the asset set asset mutate.
    MutateAssetSetAssetResult asset_set_asset_result = 71;

    // The result for the asset set mutate.
    MutateAssetSetResult asset_set_result = 72;

    // The result for the audience mutate.
    MutateAudienceResult audience_result = 80;

    // The result for the bidding data exclusion mutate.
    MutateBiddingDataExclusionsResult bidding_data_exclusion_result = 58;

    // The result for the bidding seasonality adjustment mutate.
    MutateBiddingSeasonalityAdjustmentsResult
        bidding_seasonality_adjustment_result = 59;

    // The result for the bidding strategy mutate.
    MutateBiddingStrategyResult bidding_strategy_result = 6;

    // The result for the campaign asset mutate.
    MutateCampaignAssetResult campaign_asset_result = 52;

    // The result for the campaign asset set mutate.
    MutateCampaignAssetSetResult campaign_asset_set_result = 73;

    // The result for the campaign bid modifier mutate.
    MutateCampaignBidModifierResult campaign_bid_modifier_result = 7;

    // The result for the campaign budget mutate.
    MutateCampaignBudgetResult campaign_budget_result = 8;

    // The result for the campaign conversion goal mutate.
    MutateCampaignConversionGoalResult campaign_conversion_goal_result = 67;

    // The result for the campaign criterion mutate.
    MutateCampaignCriterionResult campaign_criterion_result = 13;

    // The result for the campaign customizer mutate.
    MutateCampaignCustomizerResult campaign_customizer_result = 76;

    // The result for the campaign draft mutate.
    MutateCampaignDraftResult campaign_draft_result = 24;

    // The result for the campaign extension setting mutate.
    MutateCampaignExtensionSettingResult campaign_extension_setting_result = 26;

    // The result for the campaign feed mutate.
    MutateCampaignFeedResult campaign_feed_result = 27;

    // The result for the campaign group mutate.
    MutateCampaignGroupResult campaign_group_result = 9;

    // The result for the campaign label mutate.
    MutateCampaignLabelResult campaign_label_result = 28;

    // The result for the campaign mutate.
    MutateCampaignResult campaign_result = 10;

    // The result for the campaign shared set mutate.
    MutateCampaignSharedSetResult campaign_shared_set_result = 11;

    // The result for the conversion action mutate.
    MutateConversionActionResult conversion_action_result = 12;

    // The result for the conversion custom variable mutate.
    MutateConversionCustomVariableResult conversion_custom_variable_result = 55;

    // The result for the conversion goal campaign config mutate.
    MutateConversionGoalCampaignConfigResult
        conversion_goal_campaign_config_result = 69;

    // The result for the conversion value rule mutate.
    MutateConversionValueRuleResult conversion_value_rule_result = 63;

    // The result for the conversion value rule set mutate.
    MutateConversionValueRuleSetResult conversion_value_rule_set_result = 64;

    // The result for the custom conversion goal mutate.
    MutateCustomConversionGoalResult custom_conversion_goal_result = 68;

    // The result for the customer asset mutate.
    MutateCustomerAssetResult customer_asset_result = 57;

    // The result for the customer conversion goal mutate.
    MutateCustomerConversionGoalResult customer_conversion_goal_result = 66;

    // The result for the customer customizer mutate.
    MutateCustomerCustomizerResult customer_customizer_result = 74;

    // The result for the customer extension setting mutate.
    MutateCustomerExtensionSettingResult customer_extension_setting_result = 30;

    // The result for the customer feed mutate.
    MutateCustomerFeedResult customer_feed_result = 31;

    // The result for the customer label mutate.
    MutateCustomerLabelResult customer_label_result = 32;

    // The result for the customer negative criterion mutate.
    MutateCustomerNegativeCriteriaResult customer_negative_criterion_result =
        34;

    // The result for the customer mutate.
    MutateCustomerResult customer_result = 35;

    // The result for the customizer attribute mutate.
    MutateCustomizerAttributeResult customizer_attribute_result = 70;

    // The result for the experiment mutate.
    MutateExperimentResult experiment_result = 81;

    // The result for the experiment arm mutate.
    MutateExperimentArmResult experiment_arm_result = 82;

    // The result for the extension feed item mutate.
    MutateExtensionFeedItemResult extension_feed_item_result = 36;

    // The result for the feed item mutate.
    MutateFeedItemResult feed_item_result = 37;

    // The result for the feed item set mutate.
    MutateFeedItemSetResult feed_item_set_result = 53;

    // The result for the feed item set link mutate.
    MutateFeedItemSetLinkResult feed_item_set_link_result = 54;

    // The result for the feed item target mutate.
    MutateFeedItemTargetResult feed_item_target_result = 38;

    // The result for the feed mapping mutate.
    MutateFeedMappingResult feed_mapping_result = 39;

    // The result for the feed mutate.
    MutateFeedResult feed_result = 40;

    // The result for the keyword plan ad group mutate.
    MutateKeywordPlanAdGroupResult keyword_plan_ad_group_result = 44;

    // The result for the keyword plan campaign mutate.
    MutateKeywordPlanCampaignResult keyword_plan_campaign_result = 45;

    // The result for the keyword plan ad group keyword mutate.
    MutateKeywordPlanAdGroupKeywordResult keyword_plan_ad_group_keyword_result =
        50;

    // The result for the keyword plan campaign keyword mutate.
    MutateKeywordPlanCampaignKeywordResult
        keyword_plan_campaign_keyword_result = 51;

    // The result for the keyword plan mutate.
    MutateKeywordPlansResult keyword_plan_result = 48;

    // The result for the label mutate.
    MutateLabelResult label_result = 41;

    // The result for the recommendation subscription mutate.
    MutateRecommendationSubscriptionResult recommendation_subscription_result =
        85;

    // The result for the remarketing action mutate.
    MutateRemarketingActionResult remarketing_action_result = 43;

    // The result for the shared criterion mutate.
    MutateSharedCriterionResult shared_criterion_result = 14;

    // The result for the shared set mutate.
    MutateSharedSetResult shared_set_result = 15;

    // The result for the Smart campaign setting mutate.
    MutateSmartCampaignSettingResult smart_campaign_setting_result = 61;

    // The result for the user list mutate.
    MutateUserListResult user_list_result = 16;
  }
}
