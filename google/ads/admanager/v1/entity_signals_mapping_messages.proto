// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.admanager.v1;

import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Ads.AdManager.V1";
option go_package = "google.golang.org/genproto/googleapis/ads/admanager/v1;admanager";
option java_multiple_files = true;
option java_outer_classname = "EntitySignalsMappingMessagesProto";
option java_package = "com.google.ads.admanager.v1";
option php_namespace = "Google\\Ads\\AdManager\\V1";
option ruby_package = "Google::Ads::AdManager::V1";

// The `EntitySignalsMapping` resource.
message EntitySignalsMapping {
  option (google.api.resource) = {
    type: "admanager.googleapis.com/EntitySignalsMapping"
    pattern: "networks/{network_code}/entitySignalsMappings/{entity_signals_mapping}"
    plural: "entitySignalsMappings"
    singular: "entitySignalsMapping"
  };

  // The entity that the mapping belongs to.
  oneof entity {
    // ID of an AudienceSegment that this mapping belongs to.
    int64 audience_segment_id = 3;

    // ID of a ContentBundle that this mapping belongs to.
    int64 content_bundle_id = 4;

    // ID of a CustomValue that this mapping belongs to.
    int64 custom_targeting_value_id = 5;
  }

  // Identifier. The resource name of the `EntitySignalsMapping`.
  // Format:
  // `networks/{network_code}/entitySignalsMappings/{entity_signals_mapping_id}`
  string name = 1 [(google.api.field_behavior) = IDENTIFIER];

  // Output only. `EntitySignalsMapping` ID.
  int64 entity_signals_mapping_id = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The IDs of the categories that are associated with the
  // referencing entity.
  repeated int64 taxonomy_category_ids = 6
      [(google.api.field_behavior) = REQUIRED];
}
