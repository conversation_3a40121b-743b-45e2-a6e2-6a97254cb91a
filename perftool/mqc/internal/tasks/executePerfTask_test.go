package tasks

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"reflect"
	"testing"
	"time"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/threading"
	"github.com/zeromicro/go-zero/rest/httpc"
	"go.uber.org/zap"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/log"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/base"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	grpcclient "gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes/grpc"
	httpclient "gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes/http"
	ttclient "gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/clienttypes/tt"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/svc"
)

var goPath string

func TestMain(m *testing.M) {
	goPath = os.Getenv("GOPATH")
	_ = os.Setenv("LOG_LEVEL", "debug")

	ttclient.Init()
	grpcclient.Init()
	httpclient.Init()

	m.Run()
}

func TestPerfTestTaskProcessor_ProcessTask(t *testing.T) {
	var c config.Config
	conf.MustLoad("../../etc/perftool.yaml", &c, conf.UseEnv())
	if err := c.ServiceConf.SetUp(); err != nil {
		t.Fatal(err)
	}
	w := log.NewZapWriter(c.Log, zap.AddCaller(), zap.AddCallerSkip(1), zap.Development())
	log.SetWriter(w)

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Minute)
	defer cancel()

	svcCtx := svc.NewServiceContext(c)
	proc.AddShutdownListener(
		func() {
			close(svcCtx.ExitChannel)
		},
	)
	threading.GoSafe(
		func() {
			for {
				select {
				case <-svcCtx.TasksChannel:
					t.Log("got a perf task")
				case <-svcCtx.FinishChannel:
					t.Log("the perf task completed")
				case <-svcCtx.ExitChannel:
					t.Log("got an exit signal")
					return
				case <-ctx.Done():
					t.Logf("got a done signal, error: %+v", ctx.Err())
					return
				}
			}
		},
	)

	processor := NewPerfTestTaskProcessor(svcCtx)

	type args struct {
		ctx  context.Context
		task *base.Task
	}
	tests := []struct {
		name    string
		args    args
		want    []byte
		wantErr bool
	}{
		{
			name: "constant rate limit by times",
			args: args{
				ctx: ctx,
				task: &base.Task{
					Typename: constants.MQTaskTypePerfToolExecutePerfTest,
					Payload: protobuf.MarshalJSONIgnoreError(
						&commonpb.PerfTestTaskInfo{
							ProjectId:      "project_id:Kqllt5-9fA-I5UOdhjA5d",
							TaskId:         "task_id:1",
							ExecuteId:      "execute_id:1111",
							SuiteExecuteId: "execute_id:111",
							PlanExecuteId:  "execute_id:11",
							TriggerMode:    commonpb.TriggerMode_MANUAL,
							ExecuteMode:    commonpb.PerfTaskExecutionMode_BY_TIMES,
							Protocol:       commonpb.Protocol_PROTOCOL_TT,
							ProtobufTargets: []*commonpb.ProtobufTarget{
								{
									Path: "../../../../../../TTProjects/app",
								},
							},
							GeneralConfig: &commonpb.GeneralConfig{
								ProjectId:   "project_id:Kqllt5-9fA-I5UOdhjA5d",
								ConfigId:    "general_config_id:1",
								Name:        "通用配置 - 测试",
								Description: "",
								BaseUrl:     "tcp://lvs.52tt.com",
								Verify:      false,
								Variables: []*commonpb.GeneralConfigVar{
									{
										Key:   "client_version",
										Value: "6.52.5",
									},
								},
							},
							Keepalive: &commonpb.PerfKeepalive{
								Auth: &commonpb.PerfKeepalive_AuthConfig{
									RateLimit: &commonpb.RateLimit{
										TargetRps: 2,
									},
								},
								Heartbeat: &commonpb.PerfKeepalive_HeartbeatConfig{
									RateLimit: &commonpb.RateLimit{
										TargetRps: 5,
									},
									Interval: 60,
								},
							},
							PerfCasePath: "../../../testdata/tt_perf_case.yaml",
							PerfDataPath: "../../../testdata/tt_perf_data_prod.csv",
							Duration:     0,
							Times:        1,
							PerfCase: &commonpb.PerfCaseContentV2{
								SetupSteps: []*commonpb.PerfCaseStepV2{
									{
										Name: "进入房间-50858",
										RateLimits: []*commonpb.RateLimitV2{
											{
												TargetRps: 100,
											},
										},
										Method: "ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2",
										Body:   `{ "channel_id": {{int .my_channel_id}} }`,
										Exports: []*commonpb.PerfCaseStepV2_Export{
											{
												Name:       "channel_name",
												Expression: "$.channel_info.channel_info.channel_name",
											},
										},
										Sleep: "100ms",
									},
								},
								SerialSteps: []*commonpb.PerfCaseStepV2{
									{
										Name: "获取房间成员列表-426",
										RateLimits: []*commonpb.RateLimitV2{
											{
												TargetRps: 110,
											},
										},
										Method: "ga.api.channel_rank.ChannelRankLogic.ChannelGetMemberList",
										Body: `{
    "base_req": {},
    "channel_id": {{int .my_channel_id}},
    "begin_id": 0,
    "req_cnt": 50
}`,
										Sleep: "5s",
									},
								},
								ParallelSteps: []*commonpb.PerfCaseStepV2{
									{
										Name: "获取我管理的房间列表-447",
										RateLimits: []*commonpb.RateLimitV2{
											{
												TargetRps: 100,
											},
										},
										Method: "ga.api.channel_logic_go.ChannelLogicGo.GetUserAdminChannelList",
										Body:   `{ "admin_role": 1 }`,
										Sleep:  "10s",
									},
								},
								TeardownSteps: []*commonpb.PerfCaseStepV2{
									{
										Name: "离开房间-424",
										RateLimits: []*commonpb.RateLimitV2{
											{
												TargetRps: 120,
											},
										},
										Method: "ga.api.channel_core.ChannelCoreLogic.ChannelQuit",
										Body:   `{ "channel_id": {{int .my_channel_id}} }`,
										Sleep:  "120ms",
									},
								},
							},
							RateLimits: &commonpb.PerfRateLimits{
								Auth: &commonpb.PerfRateLimits_AuthConfig{
									RateLimits: []*commonpb.RateLimitV2{
										{
											TargetRps:      2,
											InitialRps:     2,
											ChangeDuration: "0s",
											TargetDuration: "5m",
										},
									},
								},
								Heartbeat: &commonpb.PerfRateLimits_HeartbeatConfig{
									RateLimits: []*commonpb.RateLimitV2{
										{
											TargetRps:      5,
											InitialRps:     5,
											ChangeDuration: "0s",
											TargetDuration: "5m",
										},
									},
									Interval: 60,
								},
							},
							Timeout:   120,
							TotalOfVu: 5,
							TotalOfLg: 1,
						},
					),
					Timeout: 3 * time.Minute,
				},
			},
			want:    []byte(constants.SUCCESS),
			wantErr: false,
		},
		{
			name: "constant rate limit by duration",
			args: args{
				ctx: ctx,
				task: &base.Task{
					Typename: constants.MQTaskTypePerfToolExecutePerfTest,
					Payload: protobuf.MarshalJSONIgnoreError(
						&commonpb.PerfTestTaskInfo{
							ProjectId:      "project_id:Kqllt5-9fA-I5UOdhjA5d",
							TaskId:         "task_id:2",
							ExecuteId:      "execute_id:2111",
							SuiteExecuteId: "execute_id:211",
							PlanExecuteId:  "execute_id:21",
							TriggerMode:    commonpb.TriggerMode_MANUAL,
							ExecuteMode:    commonpb.PerfTaskExecutionMode_BY_DURATION,
							Protocol:       commonpb.Protocol_PROTOCOL_TT,
							ProtobufTargets: []*commonpb.ProtobufTarget{
								{
									Path: "../../../../../../TTProjects/app",
								},
							},
							GeneralConfig: &commonpb.GeneralConfig{
								ProjectId:   "project_id:Kqllt5-9fA-I5UOdhjA5d",
								ConfigId:    "general_config_id:1",
								Name:        "通用配置 - 测试",
								Description: "",
								BaseUrl:     "tcp://lvs.52tt.com",
								Verify:      false,
								Variables: []*commonpb.GeneralConfigVar{
									{
										Key:   "client_version",
										Value: "6.52.5",
									},
								},
							},
							Keepalive: &commonpb.PerfKeepalive{
								Auth: &commonpb.PerfKeepalive_AuthConfig{
									RateLimit: &commonpb.RateLimit{
										TargetRps: 2,
									},
								},
								Heartbeat: &commonpb.PerfKeepalive_HeartbeatConfig{
									RateLimit: &commonpb.RateLimit{
										TargetRps: 5,
									},
									Interval: 60,
								},
							},
							PerfCasePath: "../../../testdata/tt_perf_case.yaml",
							PerfDataPath: "../../../testdata/tt_perf_data_test.csv",
							Duration:     30,
							Times:        0,
							Timeout:      120,
							TotalOfVu:    5,
							TotalOfLg:    1,
						},
					),
					Timeout: 3 * time.Minute,
				},
			},
			want:    []byte(constants.SUCCESS),
			wantErr: false,
		},
		{
			name: "common grpc perf task",
			args: args{
				ctx: ctx,
				task: &base.Task{
					Typename: constants.MQTaskTypePerfToolExecutePerfTest,
					Payload: protobuf.MarshalJSONIgnoreError(
						&commonpb.PerfTestTaskInfo{
							ProjectId:      "project_id:Kqllt5-9fA-I5UOdhjA5d",
							TaskId:         "task_id:3",
							ExecuteId:      "execute_id:3111",
							SuiteExecuteId: "execute_id:311",
							PlanExecuteId:  "execute_id:31",
							TriggerMode:    commonpb.TriggerMode_MANUAL,
							ExecuteMode:    commonpb.PerfTaskExecutionMode_BY_TIMES,
							Protocol:       commonpb.Protocol_PROTOCOL_GRPC,
							ProtobufTargets: []*commonpb.ProtobufTarget{
								{
									Path: "../../../../protos",
									ImportPaths: []string{
										filepath.Join(
											goPath, "/pkg/mod/github.com/envoyproxy/protoc-gen-validate@v1.0.4",
										),
										"../../../../dep_protos/protovalidate/proto/protovalidate",
										"../../../../../qet-backend-common/protos",
										"../../../../../qet-backend-middleware/protos",
									},
								},
							},
							GeneralConfig: &commonpb.GeneralConfig{
								ProjectId:   "project_id:Kqllt5-9fA-I5UOdhjA5d",
								ConfigId:    "general_config_id:3",
								Name:        "通用配置 - K本地",
								Description: "",
								BaseUrl:     "grpc://127.0.0.1:20211",
								Verify:      false,
								Variables: []*commonpb.GeneralConfigVar{
									{
										Key:   "no_tls",
										Value: "true",
									},
								},
							},
							Keepalive: &commonpb.PerfKeepalive{
								Auth: &commonpb.PerfKeepalive_AuthConfig{
									RateLimit: &commonpb.RateLimit{
										TargetRps: 2,
									},
								},
								Heartbeat: &commonpb.PerfKeepalive_HeartbeatConfig{
									RateLimit: &commonpb.RateLimit{
										TargetRps: 5,
									},
									Interval: 60,
								},
							},
							PerfCasePath: "../../../testdata/grpc_perf_case.yaml",
							PerfDataPath: "../../../testdata/grpc_perf_data.csv",
							Duration:     30,
							Times:        3,
							Timeout:      120,
							TotalOfVu:    5,
							TotalOfLg:    1,
						},
					),
					Timeout: 3 * time.Minute,
				},
			},
			want:    []byte(constants.SUCCESS),
			wantErr: false,
		},
		{
			name: "tt http protocol",
			args: args{
				ctx: ctx,
				task: &base.Task{
					Typename: constants.MQTaskTypePerfToolExecutePerfTest,
					Payload: protobuf.MarshalJSONIgnoreError(
						&commonpb.PerfTestTaskInfo{
							ProjectId:      "project_id:Kqllt5-9fA-I5UOdhjA5d",
							TaskId:         "task_id:4",
							ExecuteId:      "execute_id:4111",
							SuiteExecuteId: "execute_id:411",
							PlanExecuteId:  "execute_id:41",
							TriggerMode:    commonpb.TriggerMode_MANUAL,
							ExecuteMode:    commonpb.PerfTaskExecutionMode_BY_TIMES,
							Protocol:       commonpb.Protocol_PROTOCOL_TT,
							ProtobufTargets: []*commonpb.ProtobufTarget{
								{
									Path: "../../../../../../TTProjects/app",
								},
							},
							GeneralConfig: &commonpb.GeneralConfig{
								ProjectId:   "project_id:Kqllt5-9fA-I5UOdhjA5d",
								ConfigId:    "general_config_id:1",
								Name:        "通用配置 - 测试",
								Description: "",
								BaseUrl:     "tcp://testing-login.ttyuyin.com:8080",
								Verify:      false,
								Variables: []*commonpb.GeneralConfigVar{
									{
										Key:   "client_version",
										Value: "6.52.5",
									},
								},
							},
							Keepalive: &commonpb.PerfKeepalive{
								Auth: &commonpb.PerfKeepalive_AuthConfig{
									RateLimit: &commonpb.RateLimit{
										TargetRps: 2,
									},
								},
								Heartbeat: &commonpb.PerfKeepalive_HeartbeatConfig{
									RateLimit: &commonpb.RateLimit{
										TargetRps: 5,
									},
									Interval: 60,
								},
							},
							PerfCasePath: "../../../testdata/tt_http_perf_case.yaml",
							PerfDataPath: "../../../testdata/tt_http_perf_data_test.csv",
							Duration:     30,
							Times:        1,
							PerfCase: &commonpb.PerfCaseContentV2{
								SerialSteps: []*commonpb.PerfCaseStepV2{
									{
										Name: "getGuildRank",
										RateLimits: []*commonpb.RateLimitV2{
											{
												TargetRps: 10,
											},
										},
										Url:    "https://node-unify.52tt.com/activity-production/wwxs-2024/activity.Activity/getGuildRank",
										Method: "GET",
										Sleep:  "2s",
									},
								},
							},
							RateLimits: &commonpb.PerfRateLimits{
								Auth: &commonpb.PerfRateLimits_AuthConfig{
									RateLimits: []*commonpb.RateLimitV2{
										{
											TargetRps:      50,
											InitialRps:     50,
											ChangeDuration: "0s",
											TargetDuration: "5m",
										},
									},
								},
								Heartbeat: &commonpb.PerfRateLimits_HeartbeatConfig{
									RateLimits: []*commonpb.RateLimitV2{
										{
											TargetRps:      100,
											InitialRps:     100,
											ChangeDuration: "0s",
											TargetDuration: "5m",
										},
									},
									Interval: 60,
								},
							},
							Timeout:   120,
							TotalOfVu: 5,
							TotalOfLg: 1,
						},
					),
					Timeout: 3 * time.Minute,
				},
			},
			want:    []byte(constants.SUCCESS),
			wantErr: false,
		},
		{
			name: "common http perf task",
			args: args{
				ctx: ctx,
				task: &base.Task{
					Typename: constants.MQTaskTypePerfToolExecutePerfTest,
					Payload: protobuf.MarshalJSONIgnoreError(
						&commonpb.PerfTestTaskInfo{
							ProjectId:      "project_id:Kqllt5-9fA-I5UOdhjA5d",
							TaskId:         "task_id:5",
							ExecuteId:      "execute_id:5111",
							SuiteExecuteId: "execute_id:511",
							PlanExecuteId:  "execute_id:51",
							TriggerMode:    commonpb.TriggerMode_MANUAL,
							ExecuteMode:    commonpb.PerfTaskExecutionMode_BY_TIMES,
							Protocol:       commonpb.Protocol_PROTOCOL_HTTP,
							GeneralConfig: &commonpb.GeneralConfig{
								ProjectId:   "project_id:Kqllt5-9fA-I5UOdhjA5d",
								ConfigId:    "general_config_id:4",
								Name:        "通用配置 - httpbin",
								Description: "",
								BaseUrl:     "http://***********:8080",
								Verify:      false,
								Variables:   []*commonpb.GeneralConfigVar{},
							},
							Keepalive: &commonpb.PerfKeepalive{
								Auth: &commonpb.PerfKeepalive_AuthConfig{
									RateLimit: &commonpb.RateLimit{
										TargetRps: 2,
									},
								},
								Heartbeat: &commonpb.PerfKeepalive_HeartbeatConfig{
									RateLimit: &commonpb.RateLimit{
										TargetRps: 5,
									},
									Interval: 60,
								},
							},
							PerfCasePath: "../../../testdata/http_perf_case.yaml",
							PerfDataPath: "../../../testdata/http_perf_data.csv",
							Duration:     30,
							Times:        3,
							Timeout:      120,
							TotalOfVu:    5,
							TotalOfLg:    1,
						},
					),
					Timeout: 3 * time.Minute,
				},
			},
			want:    []byte(constants.SUCCESS),
			wantErr: false,
		},
		{
			name: "tt auth",
			args: args{
				ctx: ctx,
				task: &base.Task{
					Typename: constants.MQTaskTypePerfToolExecutePerfTest,
					Payload: protobuf.MarshalJSONIgnoreError(
						&commonpb.PerfTestTaskInfo{
							ProjectId:      "project_id:Kqllt5-9fA-I5UOdhjA5d",
							TaskId:         "task_id:6",
							ExecuteId:      "execute_id:6111",
							SuiteExecuteId: "execute_id:611",
							PlanExecuteId:  "execute_id:61",
							TriggerMode:    commonpb.TriggerMode_MANUAL,
							ExecuteMode:    commonpb.PerfTaskExecutionMode_BY_TIMES,
							Protocol:       commonpb.Protocol_PROTOCOL_TT,
							ProtobufTargets: []*commonpb.ProtobufTarget{
								{
									Path: "../../../../../../TTProjects/app",
								},
							},
							GeneralConfig: &commonpb.GeneralConfig{
								ProjectId:   "project_id:Kqllt5-9fA-I5UOdhjA5d",
								ConfigId:    "general_config_id:1",
								Name:        "通用配置 - 测试",
								Description: "",
								BaseUrl:     "tcp://lvs.52tt.com",
								Verify:      false,
								Variables: []*commonpb.GeneralConfigVar{
									{
										Key:   "client_version",
										Value: "6.52.5",
									},
								},
							},
							Keepalive: &commonpb.PerfKeepalive{
								Auth: &commonpb.PerfKeepalive_AuthConfig{
									RateLimit: &commonpb.RateLimit{
										TargetRps: 2,
									},
								},
								Heartbeat: &commonpb.PerfKeepalive_HeartbeatConfig{
									RateLimit: &commonpb.RateLimit{
										TargetRps: 5,
									},
									Interval: 60,
								},
							},
							PerfCasePath: "../../../testdata/tt_auth_perf_case.yaml",
							PerfDataPath: "../../../testdata/tt_auth_perf_data_prod.csv",
							Duration:     0,
							Times:        2,
							Timeout:      120,
							TotalOfVu:    5,
							TotalOfLg:    1,
						},
					),
					Timeout: 3 * time.Minute,
				},
			},
			want:    []byte(constants.SUCCESS),
			wantErr: false,
		},
		{
			name: "hqy perf case",
			args: args{
				ctx: ctx,
				task: &base.Task{
					Typename: constants.MQTaskTypePerfToolExecutePerfTest,
					Payload: protobuf.MarshalJSONIgnoreError(
						&commonpb.PerfTestTaskInfo{
							ProjectId:      "project_id:Kqllt5-9fA-I5UOdhjA5d",
							TaskId:         "task_id:7",
							ExecuteId:      "execute_id:7111",
							SuiteExecuteId: "execute_id:711",
							PlanExecuteId:  "execute_id:71",
							TriggerMode:    commonpb.TriggerMode_MANUAL,
							ExecuteMode:    commonpb.PerfTaskExecutionMode_BY_TIMES,
							Protocol:       commonpb.Protocol_PROTOCOL_TT,
							ProtobufTargets: []*commonpb.ProtobufTarget{
								{
									Path: "../../../../../../TTProjects/app",
								},
							},
							GeneralConfig: &commonpb.GeneralConfig{
								ProjectId:   "project_id:Kqllt5-9fA-I5UOdhjA5d",
								ConfigId:    "general_config_id:1",
								Name:        "通用配置 - 测试",
								Description: "",
								BaseUrl:     "tcp://lvs.52tt.com",
								Verify:      false,
								Variables: []*commonpb.GeneralConfigVar{
									{
										Key:   "client_version",
										Value: "6.52.5",
									},
								},
							},
							Keepalive: &commonpb.PerfKeepalive{
								Auth: &commonpb.PerfKeepalive_AuthConfig{
									RateLimit: &commonpb.RateLimit{
										TargetRps: 2,
									},
								},
								Heartbeat: &commonpb.PerfKeepalive_HeartbeatConfig{
									RateLimit: &commonpb.RateLimit{
										TargetRps: 5,
									},
									Interval: 60,
								},
							},
							PerfCasePath: "../../../testdata/hqy_perf_case.yaml",
							PerfDataPath: "../../../testdata/hqy_perf_data_prod.csv",
							Duration:     0,
							Times:        1,
							Timeout:      120,
							TotalOfVu:    5,
							TotalOfLg:    1,
						},
					),
					Timeout: 3 * time.Minute,
				},
			},
			want:    []byte(constants.SUCCESS),
			wantErr: false,
		},
		{
			name: "tt auth client by tcp",
			args: args{
				ctx: ctx,
				task: &base.Task{
					Typename: constants.MQTaskTypePerfToolExecutePerfTest,
					Payload: protobuf.MarshalJSONIgnoreError(
						&commonpb.PerfTestTaskInfo{
							ProjectId:      "project_id:Kqllt5-9fA-I5UOdhjA5d",
							TaskId:         "task_id:8",
							ExecuteId:      "execute_id:8111",
							SuiteExecuteId: "execute_id:811",
							PlanExecuteId:  "execute_id:81",
							TriggerMode:    commonpb.TriggerMode_MANUAL,
							ExecuteMode:    commonpb.PerfTaskExecutionMode_BY_TIMES,
							Protocol:       commonpb.Protocol_PROTOCOL_TT_AUTH,
							ProtobufTargets: []*commonpb.ProtobufTarget{
								{
									Path: "../../../../../../TTProjects/app",
								},
							},
							GeneralConfig: &commonpb.GeneralConfig{
								ProjectId:   "project_id:Kqllt5-9fA-I5UOdhjA5d",
								ConfigId:    "general_config_id:1",
								Name:        "通用配置 - 测试",
								Description: "",
								BaseUrl:     "tcp://lvs.52tt.com",
								Verify:      false,
								Variables: []*commonpb.GeneralConfigVar{
									{
										Key:   "client_version",
										Value: "6.52.5",
									},
								},
							},
							PerfDataPath: "../../../testdata/tt_perf_data_prod.csv",
							Duration:     0,
							Times:        3,
							PerfCase: &commonpb.PerfCaseContentV2{
								SerialSteps: []*commonpb.PerfCaseStepV2{
									{
										Name:       "登录",
										RateLimits: nil,
										Method:     "ga.api.auth.AuthLogic.Auth",
										Body: `{
	"base_req": {
		"app_id": 0,
		"market_id": 0
	},
	"user_phone": "{{ .username }}",
	"pwd": "{{ md5 .password }}",
	"login_type": 1,
	"login_account_type": 4
}`,
										Sleep:      "10s",
										Key:        "",
										Service:    "",
										Namespace:  "",
										Cmd:        10,
										GrpcPath:   "/ga.api.auth.AuthLogic/Auth",
										Deprecated: false,
									},
								},
							},
							RateLimits: &commonpb.PerfRateLimits{
								Auth: &commonpb.PerfRateLimits_AuthConfig{
									RateLimits: []*commonpb.RateLimitV2{
										{
											TargetRps:      50,
											InitialRps:     50,
											ChangeDuration: "0s",
											TargetDuration: "5m",
										},
									},
								},
								Heartbeat: &commonpb.PerfRateLimits_HeartbeatConfig{
									RateLimits: []*commonpb.RateLimitV2{
										{
											TargetRps:      100,
											InitialRps:     100,
											ChangeDuration: "0s",
											TargetDuration: "5m",
										},
									},
									Interval: 60,
								},
							},
							Timeout:   120,
							TotalOfVu: 5,
							TotalOfLg: 1,
						},
					),
					Timeout: 3 * time.Minute,
				},
			},
			want:    []byte(constants.SUCCESS),
			wantErr: false,
		},
		{
			name: "tt auth client by grpc",
			args: args{
				ctx: ctx,
				task: &base.Task{
					Typename: constants.MQTaskTypePerfToolExecutePerfTest,
					Payload: protobuf.MarshalJSONIgnoreError(
						&commonpb.PerfTestTaskInfo{
							ProjectId:      "project_id:Kqllt5-9fA-I5UOdhjA5d",
							TaskId:         "task_id:9",
							ExecuteId:      "execute_id:9111",
							SuiteExecuteId: "execute_id:911",
							PlanExecuteId:  "execute_id:91",
							TriggerMode:    commonpb.TriggerMode_MANUAL,
							ExecuteMode:    commonpb.PerfTaskExecutionMode_BY_TIMES,
							Protocol:       commonpb.Protocol_PROTOCOL_TT_AUTH,
							ProtobufTargets: []*commonpb.ProtobufTarget{
								{
									Path: "../../../../../../TTProjects/app",
								},
								{
									Path: "../../../../../../TTProjects/stellaris-api",
									ImportPaths: []string{
										"../../../../../../GitHubProjects/googleapis",
									},
								},
							},
							GeneralConfig: &commonpb.GeneralConfig{
								ProjectId:   "project_id:Kqllt5-9fA-I5UOdhjA5d",
								ConfigId:    "general_config_id:2",
								Name:        "通用配置 - 测试 - pc_lfg",
								Description: "",
								BaseUrl:     "grpc://testing-lfg-apiv2.ttyuyin.com:443",
								Verify:      false,
								Variables: []*commonpb.GeneralConfigVar{
									{
										Key:   "client_type",
										Value: "9",
									},
									{
										Key:   "client_version",
										Value: "1.1.7",
									},
									{
										Key:   "bundle_id",
										Value: "com.quwan.lfg",
									},
								},
							},
							PerfDataPath: "../../../testdata/tt_perf_data_test.csv",
							Duration:     0,
							Times:        3,
							PerfCase: &commonpb.PerfCaseContentV2{
								SerialSteps: []*commonpb.PerfCaseStepV2{
									{
										Name:       "登录",
										RateLimits: nil,
										Method:     "quwan.stellaris.authentication.v1.AuthenticationService.SignIn",
										Body: `{
	"password_credential": {
		"identifier": "{{ .username }}",
		"password": "{{ md5 .password }}"
	}
}`,
										Sleep:      "10s",
										Key:        "",
										Service:    "",
										Namespace:  "",
										Cmd:        10,
										GrpcPath:   "/quwan.stellaris.authentication.v1.AuthenticationService/SignIn",
										Deprecated: false,
									},
								},
							},
							RateLimits: &commonpb.PerfRateLimits{
								Auth: &commonpb.PerfRateLimits_AuthConfig{
									RateLimits: []*commonpb.RateLimitV2{
										{
											TargetRps:      50,
											InitialRps:     50,
											ChangeDuration: "0s",
											TargetDuration: "5m",
										},
									},
								},
								Heartbeat: &commonpb.PerfRateLimits_HeartbeatConfig{
									RateLimits: []*commonpb.RateLimitV2{
										{
											TargetRps:      100,
											InitialRps:     100,
											ChangeDuration: "0s",
											TargetDuration: "5m",
										},
									},
									Interval: 60,
								},
							},
							Timeout:   60,
							TotalOfVu: 5,
							TotalOfLg: 1,
						},
					),
					Timeout: 3 * time.Minute,
				},
			},
			want:    []byte(constants.SUCCESS),
			wantErr: false,
		},
		{
			name: "pc lfg",
			args: args{
				ctx: ctx,
				task: &base.Task{
					Typename: constants.MQTaskTypePerfToolExecutePerfTest,
					Payload: protobuf.MarshalJSONIgnoreError(
						&commonpb.PerfTestTaskInfo{
							ProjectId:      "project_id:Kqllt5-9fA-I5UOdhjA5d",
							TaskId:         "task_id:10",
							ExecuteId:      "execute_id:10111",
							SuiteExecuteId: "execute_id:1011",
							PlanExecuteId:  "execute_id:101",
							TriggerMode:    commonpb.TriggerMode_MANUAL,
							ExecuteMode:    commonpb.PerfTaskExecutionMode_BY_TIMES,
							Protocol:       commonpb.Protocol_PROTOCOL_TT,
							ProtobufTargets: []*commonpb.ProtobufTarget{
								{
									Path: "../../../../../../TTProjects/app",
								},
								{
									Path: "../../../../../../TTProjects/stellaris-api",
									ImportPaths: []string{
										"../../../../../../GitHubProjects/googleapis",
									},
								},
							},
							GeneralConfig: &commonpb.GeneralConfig{
								ProjectId:   "project_id:Kqllt5-9fA-I5UOdhjA5d",
								ConfigId:    "general_config_id:2",
								Name:        "通用配置 - 测试 - pc_lfg",
								Description: "",
								BaseUrl:     "grpc://testing-lfg-apiv2.ttyuyin.com:443", // 生产: grpc://lfg-apiv2.52tt.com:443
								Verify:      false,
								Variables: []*commonpb.GeneralConfigVar{
									{
										Key:   "client_type",
										Value: "9",
									},
									{
										Key:   "client_version",
										Value: "1.1.7",
									},
									{
										Key:   "bundle_id",
										Value: "com.quwan.lfg",
									},
								},
							},
							PerfDataPath: "../../../testdata/tt_perf_data_test.csv",
							Duration:     0,
							Times:        3,
							PerfCase: &commonpb.PerfCaseContentV2{
								SetupSteps: []*commonpb.PerfCaseStepV2{
									{
										Name: "进入房间-50858",
										RateLimits: []*commonpb.RateLimitV2{
											{
												TargetRps: 100,
											},
										},
										Method: "ga.api.channel_core.ChannelCoreLogic.ChannelEnterV2",
										Body:   `{ "channel_id": {{int .my_channel_id}} }`,
										Exports: []*commonpb.PerfCaseStepV2_Export{
											{
												Name:       "channel_name",
												Expression: "$.channel_info.channel_info.channel_name",
											},
										},
										Sleep: "100ms",
									},
								},
								SerialSteps: []*commonpb.PerfCaseStepV2{
									{
										Name: "获取房间成员列表-426",
										RateLimits: []*commonpb.RateLimitV2{
											{
												TargetRps: 100,
											},
										},
										Method: "ga.api.channel_rank.ChannelRankLogic.ChannelGetMemberList",
										Body: `{
    "base_req": {},
    "channel_id": {{int .my_channel_id}},
    "begin_id": 0,
    "req_cnt": 50
}`,
										Sleep: "5s",
									},
								},
								ParallelSteps: []*commonpb.PerfCaseStepV2{
									{
										Name: "获取我管理的房间列表-447",
										RateLimits: []*commonpb.RateLimitV2{
											{
												TargetRps: 100,
											},
										},
										Method: "ga.api.channel_logic_go.ChannelLogicGo.GetUserAdminChannelList",
										Body:   `{ "admin_role": 1 }`,
										Sleep:  "10s",
									},
								},
								TeardownSteps: []*commonpb.PerfCaseStepV2{
									{
										Name: "离开房间-424",
										RateLimits: []*commonpb.RateLimitV2{
											{
												TargetRps: 120,
											},
										},
										Method: "ga.api.channel_core.ChannelCoreLogic.ChannelQuit",
										Body:   `{ "channel_id": {{int .my_channel_id}} }`,
										Sleep:  "120ms",
									},
								},
							},
							RateLimits: &commonpb.PerfRateLimits{
								Auth: &commonpb.PerfRateLimits_AuthConfig{
									RateLimits: []*commonpb.RateLimitV2{
										{
											TargetRps:      50,
											InitialRps:     50,
											ChangeDuration: "0s",
											TargetDuration: "5m",
										},
									},
								},
								Heartbeat: &commonpb.PerfRateLimits_HeartbeatConfig{
									RateLimits: []*commonpb.RateLimitV2{
										{
											TargetRps:      100,
											InitialRps:     100,
											ChangeDuration: "0s",
											TargetDuration: "5m",
										},
									},
									Interval: 60,
								},
							},
							Timeout:   120,
							TotalOfVu: 5,
							TotalOfLg: 1,
						},
					),
					Timeout: 3 * time.Minute,
				},
			},
			want:    []byte(constants.SUCCESS),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got, err := processor.ProcessTask(tt.args.ctx, tt.args.task)
				if (err != nil) != tt.wantErr {
					t.Errorf("ProcessTask() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("ProcessTask() got = %v, want %v", got, tt.want)
				}
			},
		)
	}

	dumpMetrics(t, svcCtx)
}

func dumpMetrics(t *testing.T, svcCtx *svc.ServiceContext) {
	resp, err := httpc.Do(
		context.Background(),
		http.MethodGet,
		fmt.Sprintf(
			"http://%s:%d%s", svcCtx.Config.DevServer.Host, svcCtx.Config.DevServer.Port,
			svcCtx.Config.DevServer.MetricsPath,
		),
		nil,
	)
	if err != nil {
		t.Errorf("failed to dump metrics, error: %+v", err)
		return
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Errorf("failed to read response body, error: %+v", err)
		return
	}

	t.Logf("Dump Metrics:\n%s", body)
}
