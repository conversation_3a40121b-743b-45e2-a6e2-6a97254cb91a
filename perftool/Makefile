
SERVICE_NAME = $(shell basename $(dir $(abspath $(lastword $(MAKEFILE_LIST)))))
MAKEFILE_DIR := $(patsubst %/,%,$(dir $(abspath $(lastword $(MAKEFILE_LIST)))))
BIN_DIR := ${MAKEFILE_DIR}/bin

ifeq (, $(GITLAB_GROUP))
GITLAB_GROUP := "gitlab.ttyuyin.com/TestDevelopment"
endif

ifeq (, $(VERSION))
VERSION := $(shell [ -f "../VERSION" ] && cat ../VERSION || echo "0.0.1")
endif

ifeq (, $(VERSION_PACKAGE))
VERSION_PACKAGE := $(GITLAB_GROUP)/qet-backend-common/version
endif

ifeq (, $(LDFLAGS))
$(shell cd $(MAKEFILE_DIR)/..)

GIT_BRANCH := $(shell git rev-parse --abbrev-ref HEAD)
GIT_TAG := $(shell [ "`git describe --tags --abbrev=0 2>/dev/null`" != "" ] && git describe --tags --abbrev=0 || git log --pretty=format:'%h' -n 1)
GIT_COMMIT := $(shell git rev-parse --short HEAD)
BUILD_DATETIME := $(shell TZ=Asia/Shanghai date +'%F %T %Z')
BUILD_GO_VERSION := $(shell go env GOVERSION)
PLATFORM := $(shell uname)

GCFLAGS := "-N -l"
LDFLAGS := "-s -w -X \"${VERSION_PACKAGE}.Version=${VERSION}\" -X \"${VERSION_PACKAGE}.GitBranch=${GIT_BRANCH}\" -X \"${VERSION_PACKAGE}.GitTag=${GIT_TAG}\" -X \"${VERSION_PACKAGE}.GitCommit=${GIT_COMMIT}\" -X \"${VERSION_PACKAGE}.BuildDatetime=${BUILD_DATETIME}\" -X \"${VERSION_PACKAGE}.BuildGoVersion=${BUILD_GO_VERSION}\""

$(shell cd $(MAKEFILE_DIR))
endif

# check if `gofumpt` command exists
GOFUMPT_EXISTS := $(shell command -v gofumpt >/dev/null 2>&1 && echo 1 || echo 0)

GO_FORMAT_CMD := gofmt -s -w
ifeq (1, $(GOFUMPT_EXISTS))
GO_FORMAT_CMD = gofumpt -l -w -extra
endif

define cmd-build
	@# args:
	@# $(1): name
	@# $(2): os
	@# $(3): arch

	@$(eval PRE_ENV := GOOS=$(2))
	@$(eval PRE_ENV := $(if $(filter "",$(3)),$(PRE_ENV),$(PRE_ENV) GOARCH=$(3)))
	@$(eval TARGET := $(if $(filter "",$(1)),$(SERVICE_NAME).$(2),$(SERVICE_NAME).$(1).$(2)_$(3)))
	@$(eval SOURCE := $(if $(filter "",$(1)),$(SERVICE_NAME).go,$(1)/$(SERVICE_NAME).go))
	@echo "build $(TARGET) by $(PRE_ENV)"
	@$(PRE_ENV) go build -o $(BIN_DIR)/$(TARGET) -gcflags $(GCFLAGS) -ldflags $(LDFLAGS) $(MAKEFILE_DIR)/$(SOURCE)
endef

.PHONY: echo
echo:
	@echo "SERVICE_NAME: $(SERVICE_NAME)"
	@echo "MAKEFILE_DIR: $(MAKEFILE_DIR)"
	@echo "BIN_DIR: $(BIN_DIR)"
	@echo "GITLAB_GROUP: $(GITLAB_GROUP)"
	@echo "VERSION: $(VERSION)"
	@echo "VERSION_PACKAGE: $(VERSION_PACKAGE)"
	@echo "GIT_BRANCH: $(GIT_BRANCH)"
	@echo "GIT_TAG: $(GIT_TAG)"
	@echo "GIT_COMMIT: $(GIT_COMMIT)"
	@echo "BUILD_DATETIME: $(BUILD_DATETIME)"
	@echo "BUILD_GO_VERSION: $(BUILD_GO_VERSION)"
	@echo "PLATFORM: $(PLATFORM)"
	@echo "GCFLAGS: $(GCFLAGS)"
	@echo "LDFLAGS: $(LDFLAGS)"
	@echo "GO_FORMAT_CMD: $(GO_FORMAT_CMD)"
	@echo ""

.PHONY: fmt
fmt:
	@go list -f {{.Dir}} $(MAKEFILE_DIR)/... | xargs $(GO_FORMAT_CMD)
	@goimports -l -w -local $(GITLAB_GROUP) $(MAKEFILE_DIR)

.PHONY: lint
lint:
	@golangci-lint run -c $(MAKEFILE_DIR)/../.golangci.yaml

.PHONY: build
build: mqc

.PHONY: mqc
mqc: mqc-mac-arm64 mqc-mac-amd64 mqc-linux

.PHONY: mqc-mac
mqc-mac:
	$(call cmd-build,mqc,darwin,"")

.PHONY: mqc-mac-arm64
mqc-mac-arm64:
	$(call cmd-build,mqc,darwin,amr64)

.PHONY: mqc-mac-amd64
mqc-mac-amd64:
	$(call cmd-build,mqc,darwin,amd64)

.PHONY: mqc-linux
mqc-linux:
	$(call cmd-build,mqc,linux,amd64)

.PHONY: all
all: all-mac-arm64 all-mac-amd64 all-linux all-win

.PHONY: all-mac
all-mac:
	$(call cmd-build,"",darwin,"")

.PHONY: all-mac-arm64
all-mac-arm64:
	$(call cmd-build,"",darwin,arm64)

.PHONY: all-mac-amd64
all-mac-amd64:
	$(call cmd-build,"",darwin,amd64)

.PHONY: all-linux
all-linux:
	$(call cmd-build,"",linux,amd64)

.PHONY: all-win
all-win:
	$(call cmd-build,"",windows,amd64)
